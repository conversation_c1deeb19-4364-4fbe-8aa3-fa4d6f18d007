"use client";

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@ui/lib/utils';

interface EditableLabelProps {
  defaultValue?: string;
  placeholder?: string;
  onSubmit?: (value: string) => void;
  maxLength?: number;
  className?: string;
  'data-testid'?: string;
}

export function EditableLabel({
  defaultValue = '',
  placeholder = 'Untitled',
  onSubmit,
  maxLength,
  className,
  'data-testid': dataTestId,
}: EditableLabelProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(defaultValue);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleStartEdit = () => {
    setIsEditing(true);
  };

  const handleFinishEdit = () => {
    const trimmedValue = value.trim();
    if (trimmedValue.length === 0) {
      setValue(defaultValue);
    } else {
      onSubmit?.(trimmedValue);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setValue(defaultValue);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleFinishEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleBlur = () => {
    handleFinishEdit();
  };

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        maxLength={maxLength}
        className={cn(
          "font-semibold text-base px-2 py-1 border border-input rounded-3xl focus:outline-none focus:ring-1 focus:ring-ring bg-background",
          className
        )}
        data-testid={dataTestId}
      />
    );
  }

  return (
    <div
      className={cn(
        "font-semibold text-base cursor-pointer hover:bg-muted/50 rounded-3xl px-2 py-1 transition-colors",
        className
      )}
      onClick={handleStartEdit}
      data-testid={dataTestId}
    >
      {value || placeholder}
    </div>
  );
}