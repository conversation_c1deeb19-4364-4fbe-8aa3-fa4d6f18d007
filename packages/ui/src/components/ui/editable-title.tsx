"use client";

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@ui/lib/utils';
import { Button } from '@ui/components/ui/button';

interface EditableTitleProps {
  defaultValue?: string;
  placeholder?: string;
  onSubmit?: (value: string) => void;
  maxLength?: number;
  className?: string;
  'data-testid'?: string;
}

export function EditableTitle({
  defaultValue = '',
  placeholder = 'Untitled',
  onSubmit,
  maxLength,
  className,
  'data-testid': dataTestId,
}: EditableTitleProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(defaultValue);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleStartEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setValue(defaultValue);
    setIsEditing(false);
  };

  const handleSave = () => {
    const trimmedValue = value.trim();
    if (trimmedValue.length === 0) {
      setValue(defaultValue);
      setIsEditing(false);
      return;
    }
    
    onSubmit?.(trimmedValue);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleInputBlur = (e: React.FocusEvent) => {
    // Don't auto-save if user clicked on Save or Cancel buttons
    const relatedTarget = e.relatedTarget as HTMLElement;
    if (relatedTarget?.closest('[data-editable-action]')) {
      return;
    }
    handleSave();
  };

  if (isEditing) {
    return (
      <div className={cn("flex items-center gap-2", className)} data-testid={dataTestId}>
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleInputBlur}
          maxLength={maxLength}
          className="font-semibold text-base px-2 py-1 border border-input rounded focus:outline-none focus:ring-1 focus:ring-ring"
        />
        <div className="flex items-center gap-1">
          <Button 
            size="sm" 
            variant="default" 
            onClick={handleSave}
            data-editable-action="save"
          >
            Save
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleCancel}
            data-editable-action="cancel"
          >
            Cancel
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "font-semibold text-base cursor-pointer hover:bg-muted/50 rounded px-2 py-1 transition-colors",
        className
      )}
      onClick={handleStartEdit}
      data-testid={dataTestId}
    >
      {value || placeholder}
    </div>
  );
}