set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_trigger_func()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    status_column TEXT := 'status';
    record_id_column TEXT := 'id';
    eko_id_column TEXT := 'eko_id';
    issue_column TEXT := 'issue';
    has_status_column BOOLEAN;
    has_eko_id_column BOOLEAN;
    has_issue_column BOOLEAN;
    rec_id INTEGER;
    eko_id_val eko_id;
    old_status_val TEXT;
    new_status_val TEXT;
    issue_val TEXT;
    current_user_name TEXT;
    current_run_id INTEGER;
BEGIN
    -- Check if the table has status, eko_id, and issue columns
    has_status_column := column_exists(TG_TABLE_NAME, status_column);
    has_eko_id_column := column_exists(TG_TABLE_NAME, eko_id_column);
    has_issue_column := column_exists(TG_TABLE_NAME, issue_column);

    -- Determine the record ID
    EXECUTE format('SELECT ($1).%I', record_id_column)
        INTO rec_id
        USING CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;

    -- Get eko_id if the column exists
    IF has_eko_id_column THEN
        EXECUTE format('SELECT ($1).%I', eko_id_column)
            INTO eko_id_val
            USING CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
    END IF;

    -- Get status values if the column exists
    IF has_status_column THEN
        IF TG_OP IN ('UPDATE', 'DELETE') THEN
            EXECUTE format('SELECT ($1).%I', status_column)
                INTO old_status_val
                USING OLD;
        END IF;
        IF TG_OP IN ('INSERT', 'UPDATE') THEN
            EXECUTE format('SELECT ($1).%I', status_column)
                INTO new_status_val
                USING NEW;
        END IF;
    END IF;

    -- Get the current issue value if the column exists
    IF has_issue_column THEN
        EXECUTE format('SELECT ($1).%I', issue_column)
            INTO issue_val
            USING CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
    END IF;

    -- Get the current user
    SELECT session_user INTO current_user_name;

    -- Get the latest run_id
    SELECT get_latest_run_id() INTO current_run_id;

    -- Handle different operations
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (
            table_name, operation, record_id, eko_id, new_data, new_status, issue, run_id, changed_by
        ) VALUES (
                     TG_TABLE_NAME, 'insert', rec_id, eko_id_val, row_to_json(NEW),
                     new_status_val, issue_val, current_run_id, current_user_name
                 );
    ELSIF TG_OP = 'UPDATE' THEN
            INSERT INTO audit_log (
                table_name, operation, record_id, eko_id, old_data, new_data,
                old_status, new_status, issue, run_id, changed_by
            ) VALUES (
                         TG_TABLE_NAME, CASE
                                            WHEN new_status_val != old_status_val and new_status_val ='authorized' THEN 'authorize'
                                            WHEN new_status_val != old_status_val and new_status_val ='deleted' THEN 'logical_delete'
                                            WHEN new_status_val != old_status_val and new_status_val ='revoked' THEN 'revoke'
                                            WHEN new_status_val != old_status_val and new_status_val ='pending' THEN 'pending'
                                            ELSE 'update'
                    END::audit_operation, rec_id, eko_id_val, row_to_json(OLD), row_to_json(NEW),
                         old_status_val, new_status_val, issue_val, current_run_id, current_user_name
                     );
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (
            table_name, operation, record_id, eko_id, old_data, old_status, issue, run_id, changed_by
        ) VALUES (
                     TG_TABLE_NAME, 'delete', rec_id, eko_id_val, row_to_json(OLD),
                     old_status_val, issue_val, current_run_id, current_user_name
                 );
    END IF;
    RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.column_exists(tbl text, col text)
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = tbl AND column_name = col
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_quota_for_current_user()
 RETURNS TABLE(id bigint, created_at timestamp with time zone, item_type quota_item_type, quantity real, period text, customer uuid, organisation bigint, scope quota_scope, profile_id uuid, org_id bigint)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        quota.id,
        quota.created_at,
        quota.item_type,
        quota.quantity,
        quota.period,
        quota.customer,
        quota.organisation,
        quota.scope,
        profiles.id AS profile_id,
        org.id AS org_id
    FROM acc_quota quota
    LEFT JOIN acc_organisations org ON (
        quota.scope = 'org'::quota_scope 
        AND quota.organisation = org.id
        AND org.id IN (
            SELECT p.organisation 
            FROM profiles p 
            WHERE p.id = auth.uid()
        )
    )
    LEFT JOIN profiles ON (
        (quota.scope = 'individual'::quota_scope AND quota.customer = profiles.id)
        OR (org.id IS NOT NULL AND org.id = profiles.organisation)
    )
    WHERE profiles.id = auth.uid()
       OR (quota.scope = 'individual'::quota_scope AND quota.customer = auth.uid());
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$BEGIN
    INSERT INTO public.profiles (id, email, username, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;$function$
;

CREATE OR REPLACE FUNCTION public.is_admin()
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  RETURN COALESCE(
    (SELECT is_admin FROM profiles WHERE id = auth.uid()),
    FALSE
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.name_to_ekoid(input_str text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    lower_str text;
    replaced_str text;
    final_str text;
BEGIN
    lower_str := lower(input_str);
    replaced_str := replace(replace(lower_str, '%', 'pc'), '/', '.');
    final_str := '';
    
    FOR i IN 1..length(replaced_str) LOOP
        IF NOT (substring(replaced_str from i for 1) ~ '[a-zA-Z0-9.]' OR substring(replaced_str from i for 1) IN ('p', 'c')) THEN
            final_str := final_str || '_';
        ELSE
            final_str := final_str || substring(replaced_str from i for 1);
        END IF;
    END LOOP;
    
    RETURN 'eko:sec:name:' || final_str;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.populate_columns_from_metadata()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.title = NEW.metadata->>'title';
    NEW.publish_date = NEW.metadata->>'publish_date';
    NEW.public_url = NEW.metadata->>'url';
    NEW.extract = NEW.metadata->>'extract';
    NEW.research_categories := limit_array_size(ARRAY(select jsonb_array_elements_text((NEW.metadata->>'researchCategories')::jsonb)),3);
    NEW.doughnut_levels := limit_array_size(ARRAY(select jsonb_array_elements_text((NEW.metadata->>'doughnutLevels')::jsonb)),2);
    NEW.doughnut_segments := limit_array_size(ARRAY(select jsonb_array_elements_text((NEW.metadata->>'doughnutSegments')::jsonb)),3);
    NEW.authors := limit_array_size(ARRAY(select jsonb_array_elements_text((NEW.metadata->>'authors')::jsonb)),50);
    NEW.credibility = (NEW.metadata->>'credibility')::int;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.remove_suffix(company_name text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    suffixes TEXT[] := ARRAY['A/S', 'AB', 'AG', 'AO', 'AG & Co', 'AG &', 'AG & CO.', 'AG & CO. KG', 'AG & CO. KGaA',
        'AG & KG', 'AG & KGaA', 'AG & PARTNER', 'ATE', 'ASA', 'B.V.', 'BV', 'Class A', 'Class B',
        'Class C', 'Class D', 'Class E', 'Class F', 'Class G', 'CO', 'Co', 'Co.', 'Company', 'Corp',
        'Corporation', 'DAC', 'GmbH', 'Inc', 'Inc.', 'Incorporated', 'KGaA', 'Limited', 'LLC', 'LLP',
        'LP', 'Ltd', 'Ltd.', 'N.V.', 'NV', 'Plc', 'PC', 'plc', 'PLC', 'Pty Ltd', 'Pty', 'Pty. Ltd.',
        'S.A.', 'S.A.B. de C.V.', 'SAB de CV', 'S.A.B.', 'S.A.P.I.', 'NV/SA', 'SDI', 'SpA', 'S.L.',
        'S.p.A.', 'SA', 'SABESP', 'SE', 'Tbk PT', 'U.A.'];
    cleaned_name TEXT := company_name;
    suffix_pattern TEXT;
BEGIN
    -- Remove leading and trailing spaces
    cleaned_name := TRIM(cleaned_name);

    -- Replace multiple spaces with a single space
    cleaned_name := REGEXP_REPLACE(cleaned_name, '\s+', ' ', 'g');

    -- Create the suffix pattern for matching, escaping special regex characters
    suffix_pattern := '\s+(' || array_to_string(array(
                                                        SELECT REPLACE(REPLACE(REPLACE(REPLACE(suffix, '.', '\.'), '&', '\&'), '(', '\('), ')', '\)')
                                                        FROM unnest(suffixes) suffix
                                                ), '|') || ')\.?$';

    -- Loop until no more suffixes are removed
    LOOP
        cleaned_name := REGEXP_REPLACE(cleaned_name, suffix_pattern, '', 'i');
        cleaned_name := REGEXP_REPLACE(cleaned_name, '''s$', '', 'i');
        cleaned_name := REGEXP_REPLACE(cleaned_name, '[ .,;&\n\t/)]$', '', 'g');
        cleaned_name := TRIM(cleaned_name);

        EXIT WHEN NOT cleaned_name ~* suffix_pattern;
    END LOOP;

    RETURN cleaned_name;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$function$
;


