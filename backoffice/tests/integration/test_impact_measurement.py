"""
Tests for the impact measurement service.

This module tests the impact measurement service including:
1. Basic impact measurement functionality
2. LLM integration for real impact assessment
3. Evaluation framework with cross-checks
4. Bias detection and consistency validation
"""
import unittest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from eko.analysis_v2.effects.impact.service import ImpactMeasurementService
from eko.analysis_v2.effects.impact.models import EventImpactMeasurement, DimensionAssessment, LLMImpactResponse
from eko.analysis_v2.effects.impact.models.assessment import (
    HarmImpactAssessment, BenefitImpactAssessment,
    AnimalHarmImpact, HumanHarmImpact, EnvironmentHarmImpact,
    AnimalBenefitImpact, HumanBenefitImpact, EnvironmentBenefitImpact
)
from eko.analysis_v2.effects.impact.models import TemporalBreakdown, ScaleFactors
from eko.llm import LLMModel


class TestImpactMeasurement(unittest.TestCase):
    """Test suite for impact measurement service."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create service
        self.service = ImpactMeasurementService()
    
    def tearDown(self):
        """Clean up after tests."""
        pass
    
    def test_service_initialization(self):
        """Test that the service initializes correctly."""
        self.assertIsNotNone(self.service)

    def test_basic_event_measurement(self):
        """Test measuring impact of a basic event."""
        event_description = "A company plants 1,000 trees in a local forest to offset carbon emissions."
        
        measurement = self.service.measure_impact(event_description)
        
        # Check basic structure
        self.assertIsInstance(measurement, EventImpactMeasurement)
        self.assertEqual(measurement.event_description, event_description)
        self.assertIsNotNone(measurement.event_id)
        self.assertIsNotNone(measurement.event_summary)
        self.assertIsNotNone(measurement.assessed_at)
        
        # Check impact assessments exist
        self.assertIsInstance(measurement.harm_assessment, HarmImpactAssessment)
        self.assertIsInstance(measurement.benefit_assessment, BenefitImpactAssessment)
        
        # Check dimension assessments - animals, humans, environment are now impact objects with assessment field
        for assessment in [measurement.harm_assessment, measurement.benefit_assessment]:
            self.assertIsInstance(assessment.animals.assessment, DimensionAssessment)
            self.assertIsInstance(assessment.humans.assessment, DimensionAssessment)
            self.assertIsInstance(assessment.environment.assessment, DimensionAssessment)
        
        # Check scores are within valid range - access through the assessment field
        for dimension in [measurement.harm_assessment.animals.assessment, measurement.harm_assessment.humans.assessment, 
                         measurement.harm_assessment.environment.assessment, measurement.benefit_assessment.animals.assessment,
                         measurement.benefit_assessment.humans.assessment, measurement.benefit_assessment.environment.assessment]:
            self.assertGreaterEqual(dimension.score, 0.0)
            self.assertLessEqual(dimension.score, 1.0)
            self.assertIn(dimension.confidence, ['high', 'medium', 'low'])
        
        # Check aggregate scores
        self.assertGreaterEqual(measurement.harm_score, 0.0)
        self.assertLessEqual(measurement.harm_score, 1.0)
        self.assertGreaterEqual(measurement.benefit_score, 0.0)
        self.assertLessEqual(measurement.benefit_score, 1.0)
        
        # For tree planting, expect positive environmental benefits
        self.assertGreater(measurement.benefit_assessment.environment.assessment.score, 0.0)
    
    def test_negative_impact_event(self):
        """Test measuring impact of a negative event."""
        event_description = "A factory releases toxic chemicals into a nearby river, contaminating the water supply."
        
        measurement = self.service.measure_impact(event_description)
        
        # Expect significant environmental harm
        self.assertGreater(measurement.harm_assessment.environment.assessment.score, 0.5)
        
        # Expect human harm due to water contamination
        self.assertGreater(measurement.harm_assessment.humans.assessment.score, 0.3)
        
        # Net impact should be negative (more harm than benefit)
        self.assertLess(measurement.net_impact_score, 0.0)
    
    def test_climate_change_impact(self):
        """Test measurement of a climate change related event."""
        event_description = "An oil company opens a new offshore drilling platform, extracting 100,000 barrels per day."
        
        measurement = self.service.measure_impact(event_description)
        
        # Should detect significant climate harm across all dimensions
        self.assertGreater(measurement.harm_assessment.environment.assessment.score, 0.7)
        self.assertGreater(measurement.harm_assessment.animals.assessment.score, 0.5)
        self.assertGreater(measurement.harm_assessment.humans.assessment.score, 0.5)
        
        # Should have low benefit scores
        self.assertLess(measurement.benefit_assessment.environment.assessment.score, 0.15)
    
    def test_temporal_breakdown(self):
        """Test that temporal breakdowns are properly filled."""
        event_description = "A pharmaceutical company releases a new medication that reduces diabetes symptoms."
        
        measurement = self.service.measure_impact(event_description)
        
        # Check that all temporal breakdowns have content
        for assessment in [measurement.harm_assessment, measurement.benefit_assessment]:
            for dimension in [assessment.animals, assessment.humans, assessment.environment]:
                temporal = dimension.assessment.temporal_breakdown
                self.assertIsInstance(temporal, TemporalBreakdown)
                self.assertTrue(temporal.immediate.strip())
                self.assertTrue(temporal.medium_term.strip())
                self.assertTrue(temporal.long_term.strip())
    
    def test_uncertainties_and_ethics(self):
        """Test that uncertainties and ethical considerations are identified."""
        event_description = "A biotech company begins genetic modification of mosquitoes to reduce malaria transmission."
        
        measurement = self.service.measure_impact(event_description)
        
        # Complex biotechnology should have uncertainties
        self.assertGreater(len(measurement.key_uncertainties), 0)
        
        # Genetic modification should have ethical considerations
        self.assertGreater(len(measurement.ethical_considerations), 0)
    
    def test_evaluation_framework(self):
        """Test the evaluation framework cross-checks."""
        event_description = "A mining company destroys 10,000 acres of rainforest to extract lithium for electric car batteries."
        
        measurement = self.service.measure_impact(event_description)
        evaluation = self.evaluation.evaluate_measurement(measurement)
        
        # Check evaluation structure
        self.assertIsInstance(evaluation, EventImpactEvaluation)
        self.assertIsNotNone(evaluation.measurement_id)
        self.assertIsNotNone(evaluation.consistency_checks)
        self.assertIsNotNone(evaluation.confidence_analysis)
        self.assertIsNotNone(evaluation.bias_detection)
        self.assertIsNotNone(evaluation.completeness_score)
        self.assertIsNotNone(evaluation.overall_quality)
        
        # Check consistency checks
        consistency = evaluation.consistency_checks
        self.assertIsInstance(consistency.score_alignment, bool)
        self.assertIsInstance(consistency.temporal_consistency, bool)
        self.assertIsInstance(consistency.dimensional_balance, bool)
        self.assertIsInstance(consistency.issues, list)
        
        # Check confidence analysis
        confidence = evaluation.confidence_analysis
        self.assertIsNotNone(confidence.confidence_distribution)
        self.assertIsNotNone(confidence.avg_confidence)
        self.assertIsNotNone(confidence.low_confidence_high_impact)
        
        # Check bias detection
        bias = evaluation.bias_detection
        self.assertIsInstance(bias.anthropocentric_bias, bool)
        self.assertIsInstance(bias.optimism_bias, bool)
        self.assertIsInstance(bias.recency_bias, bool)
        self.assertIsInstance(bias.detected_issues, list)
        
        # Check completeness score
        self.assertGreaterEqual(evaluation.completeness_score, 0.0)
        self.assertLessEqual(evaluation.completeness_score, 1.0)
        
        # Check overall quality
        self.assertIn(evaluation.overall_quality, ["poor", "fair", "good", "excellent"])
    
    def test_bias_detection_anthropocentric(self):
        """Test detection of anthropocentric bias."""
        # Create a measurement with only human impacts
        measurement = self._create_test_measurement(
            harm_humans=0.8,
            harm_animals=0.0,
            harm_environment=0.0,
            benefit_humans=0.2,
            benefit_animals=0.0,
            benefit_environment=0.0
        )
        
        evaluation = self.evaluation.evaluate_measurement(measurement)
        bias = evaluation.bias_detection
        
        self.assertTrue(bias.anthropocentric_bias)
        self.assertIn("anthropocentric bias", " ".join(bias.detected_issues).lower())
    
    def test_bias_detection_optimism(self):
        """Test detection of optimism bias."""
        # Create a measurement with excessive benefits vs. harms
        measurement = self._create_test_measurement(
            harm_humans=0.1,
            harm_animals=0.1,
            harm_environment=0.1,
            benefit_humans=0.9,
            benefit_animals=0.9,
            benefit_environment=0.9
        )
        
        evaluation = self.evaluation.evaluate_measurement(measurement)
        bias = evaluation.bias_detection
        
        self.assertTrue(bias.optimism_bias)
        self.assertIn("optimism bias", " ".join(bias.detected_issues).lower())
    
    def test_consistency_checks(self):
        """Test consistency checking functionality."""
        # Create a measurement with high impact but low confidence
        measurement = self._create_test_measurement(
            harm_humans=0.9,
            confidence_humans="low"
        )
        
        evaluation = self.evaluation.evaluate_measurement(measurement)
        consistency = evaluation.consistency_checks
        
        self.assertFalse(consistency.score_alignment)
        self.assertGreater(len(consistency.issues), 0)
    
    def test_input_validation(self):
        """Test input validation."""
        # Test empty description
        with self.assertRaises(ValueError):
            self.service.measure_impact("")
        
        # Test None description
        with self.assertRaises(ValueError):
            self.service.measure_impact(None)
        
        # Test whitespace-only description
        with self.assertRaises(ValueError):
            self.service.measure_impact("   ")
    
    def test_real_world_example_renewable_energy(self):
        """Test with a real-world renewable energy example."""
        event_description = """
        Tesla announces plans to build a massive solar panel manufacturing facility in Nevada. 
        The 500-acre facility will produce solar panels capable of generating 2 GW of clean energy annually 
        and create 3,000 permanent jobs. Construction will require clearing desert land and will consume 
        significant water resources in an arid region.
        """
        
        measurement = self.service.measure_impact(event_description)
        
        # Should detect environmental benefits from renewable energy
        self.assertGreater(measurement.benefit_assessment.environment.assessment.score, 0.5)
        
        # Should detect human benefits from job creation
        self.assertGreater(measurement.benefit_assessment.humans.assessment.score, 0.3)
        
        # Should detect some environmental harm from land clearing
        self.assertGreater(measurement.harm_assessment.environment.assessment.score, 0.1)
        
        # Net impact should be positive (benefits outweigh harms)
        self.assertGreater(measurement.net_impact_score, 0.0)
    
    def test_real_world_example_deforestation(self):
        """Test with a real-world deforestation example."""
        event_description = """
        A palm oil company clears 50,000 hectares of Indonesian rainforest to establish new plantations. 
        The operation displaces indigenous communities and destroys habitat for endangered orangutans. 
        The company claims this will provide jobs for 5,000 local workers and increase Indonesia's palm oil exports.
        """
        
        measurement = self.service.measure_impact(event_description)
        
        # Should detect massive environmental harm
        self.assertGreater(measurement.harm_assessment.environment.assessment.score, 0.8)
        
        # Should detect significant animal harm (orangutans)
        self.assertGreater(measurement.harm_assessment.animals.assessment.score, 0.7)
        
        # Should detect human harm (indigenous displacement)
        self.assertGreater(measurement.harm_assessment.humans.assessment.score, 0.5)
        
        # Benefits should be much lower than harms
        self.assertLess(measurement.benefit_score, measurement.harm_score)
        
        # Should identify uncertainties and ethical issues
        self.assertGreater(len(measurement.key_uncertainties), 0)
        self.assertGreater(len(measurement.ethical_considerations), 0)
    
    def test_completeness_calculation(self):
        """Test completeness score calculation."""
        event_description = "A company implements a comprehensive environmental restoration project."
        
        measurement = self.service.measure_impact(event_description)
        evaluation = self.evaluation.evaluate_measurement(measurement)
        
        # Should have reasonable completeness
        self.assertGreaterEqual(evaluation.completeness_score, 0.5)
        
        # All temporal breakdowns should have content
        for assessment in [measurement.harm_assessment, measurement.benefit_assessment]:
            for dimension in [assessment.animals, assessment.humans, assessment.environment]:
                self.assertTrue(dimension.assessment.temporal_breakdown.immediate.strip())
                self.assertTrue(dimension.assessment.temporal_breakdown.medium_term.strip())
                self.assertTrue(dimension.assessment.temporal_breakdown.long_term.strip())
    
    def test_multiple_events_consistency(self):
        """Test that similar events get consistent assessments."""
        event1 = "A company plants 1,000 trees to offset carbon emissions. "
        event2 = "An organization plants 1,500 native trees to restore degraded forest land. "
        
        measurement1, evaluation1 = self.service.measure_impact(event1)
        measurement2, evaluation2 = self.service.measure_impact(event2)
        
        # Both should have environmental benefits
        self.assertGreater(measurement1.benefit_assessment.environment.assessment.score, 0.1)
        self.assertGreater(measurement2.benefit_assessment.environment.assessment.score, 0.15)
        
        # Scores should be in similar ranges (within 0.5 difference)
        # Note: These events have meaningful differences - native species restoration 
        # vs generic offset planting - so allowing larger variance
        env_diff = abs(measurement1.benefit_assessment.environment.assessment.score - 
                      measurement2.benefit_assessment.environment.assessment.score)
        self.assertLess(env_diff, 0.5)
    
    def _create_test_measurement(self, harm_humans=0.5, harm_animals=0.5, harm_environment=0.5,
                               benefit_humans=0.5, benefit_animals=0.5, benefit_environment=0.5,
                               confidence_humans="medium", confidence_animals="medium", 
                               confidence_environment="medium") -> EventImpactMeasurement:
        """Create a test measurement with specified values."""
        temporal = TemporalBreakdown(
            immediate="Test immediate impact",
            medium_term="Test medium-term impact", 
            long_term="Test long-term impact"
        )
        
        # Create comprehensive scale factors for testing that satisfy all validation requirements
        scale_factors = ScaleFactors(
            proximity_to_tipping_point="Test proximity",
            # Reversibility questions - at least one must be True
            is_irreversible=False,
            takes_centuries_to_reverse=False,
            requires_significant_effort_to_reverse=True,
            reversible_within_years=False,
            reversible_within_months=False,
            reversible_within_weeks=False,
            fully_reversible_immediately=False,
            # Scale of impact questions - at least one must be True
            affects_none=False,
            affects_single_individual=False,
            affects_multiple_individuals=True,
            affects_many_beings=False,
            affects_large_population=False,
            affects_country_ecosystem=False,
            affects_species_biome=False,
            affects_all_global=False,
            # Directness questions - at least one must be True
            third_party_action=False,
            entity_had_minor_influence=False,
            entity_influenced_outcome=True,
            entity_action_led_to_impact=False,
            entity_decision_caused_impact=False,
            direct_action_by_entity=False,
            entity_sole_direct_cause=False,
            # Authenticity questions - at least one must be True
            pure_marketing_greenwashing=False,
            mostly_regulatory_compliance=False,
            primarily_business_driven=True,
            balanced_genuine_business=False,
            mostly_genuine_some_business=False,
            genuine_commitment=False,
            purely_altruistic=False,
            # Deliberateness questions - at least one must be True
            completely_accidental=False,
            foreseeable_not_intended=False,
            knew_consequences_acted_anyway=True,
            intended_action_predictable_outcome=False,
            planned_with_awareness=False,
            fully_intentional=False,
            deliberately_planned_for_impact=False,
            # Contribution questions - at least one must be True
            not_contributing=False,
            very_minor_contributor=False,
            minor_contributor=True,
            significant_contributor=False,
            major_contributor=False,
            dominant_contributor=False,
            sole_contributor=False,
            duration="medium",
            # Degree questions - at least one must be True
            no_harm=False,
            minor_harm=False,
            moderate_harm=True,
            severe_harm=False,
            severe_permanent_damage=False,
            near_total_destruction=False,
            total_destruction_death=False,
            # Probability questions - at least one must be True
            will_not_happen=False,
            very_unlikely=False,
            unlikely=False,
            even_chance=False,
            highly_likely=False,
            virtually_certain=False,
            has_already_happened=True,
        )
        
        harm_assessment = HarmImpactAssessment(
            animals=AnimalHarmImpact(
                assessment=DimensionAssessment(
                    score=harm_animals,
                    reasoning="Test reasoning",
                    confidence=confidence_animals,
                    temporal_breakdown=temporal,
                    scale_factors=scale_factors
                )
            ),
            humans=HumanHarmImpact(
                assessment=DimensionAssessment(
                    score=harm_humans,
                    reasoning="Test reasoning",
                    confidence=confidence_humans,
                    temporal_breakdown=temporal,
                    scale_factors=scale_factors
                )
            ),
            environment=EnvironmentHarmImpact(
                assessment=DimensionAssessment(
                    score=harm_environment,
                    reasoning="Test reasoning", 
                    confidence=confidence_environment,
                    temporal_breakdown=temporal,
                    scale_factors=scale_factors
                )
            )
        )
        
        benefit_assessment = BenefitImpactAssessment(
            animals=AnimalBenefitImpact(
                assessment=DimensionAssessment(
                    score=benefit_animals,
                    reasoning="Test reasoning",
                    confidence=confidence_animals,
                    temporal_breakdown=temporal,
                    scale_factors=scale_factors
                )
            ),
            humans=HumanBenefitImpact(
                assessment=DimensionAssessment(
                    score=benefit_humans,
                    reasoning="Test reasoning",
                    confidence=confidence_humans,
                    temporal_breakdown=temporal,
                    scale_factors=scale_factors
                )
            ),
            environment=EnvironmentBenefitImpact(
                assessment=DimensionAssessment(
                    score=benefit_environment,
                    reasoning="Test reasoning",
                    confidence=confidence_environment,
                    temporal_breakdown=temporal,
                    scale_factors=scale_factors
                )
            )
        )
        
        return EventImpactMeasurement(
            event_id="test-id",
            event_summary="Test event",
            event_description="Test event description",
            harm_assessment=harm_assessment,
            benefit_assessment=benefit_assessment,
            assessed_at=datetime.utcnow().isoformat() + "Z",
            model_used="test-model",
            prompt_version="1.0"
        )


if __name__ == '__main__':
    unittest.main()
