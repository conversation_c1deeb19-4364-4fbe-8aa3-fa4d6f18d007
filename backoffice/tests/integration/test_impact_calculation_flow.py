"""
Integration tests for impact calculation details flow from backend to frontend.

This module tests:
1. Calculation details are properly captured during impact measurement
2. Data flows correctly through the xfer_ tables
3. The complete pipeline from impact measurement to customer database
"""
import unittest
from datetime import datetime
from typing import Dict, List, Any
from unittest.mock import Mock, patch, MagicMock
import json

from eko.analysis_v2.effects.impact.models import EventImpactMeasurement
from eko.analysis_v2.effects.impact.service import ImpactMeasurementService
from eko.analysis_v2.effects.effect_flags_helpers import create_effect_flags_for_entity
from eko.models.vector.derived.effect import EffectFlagModel
from eko.models.xfer.xfer_effect_flag import XferEffectFlagModel
from eko.db.data.xfer import XferData
from eko.db import get_bo_conn, get_cus_conn
from psycopg import Connection


class TestImpactCalculationFlow(unittest.TestCase):
    """Test the complete flow of impact calculation details from backend to frontend."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = ImpactMeasurementService()
    
    def test_calculation_details_in_measurement(self):
        """Test that calculation details are properly captured in impact measurements."""
        event_description = "A company reduces carbon emissions by 50% through renewable energy adoption"
        
        measurement = self.service.measure_impact(event_description)
        
        # Verify measurement structure
        self.assertIsInstance(measurement, EventImpactMeasurement)
        self.assertIsNotNone(measurement.calculation_details)
        
        # Check calculation details structure
        calc_details = measurement.calculation_details
        self.assertIn('harm', calc_details)
        self.assertIn('benefit', calc_details)
        
        # Verify harm calculation details
        for dimension in ['animals', 'humans', 'environment']:
            if dimension in calc_details['harm']:
                harm_calc = calc_details['harm'][dimension]
                self.assertIsInstance(harm_calc, CalculationDetails)
                self.assertIsNotNone(harm_calc.final_score)
                self.assertIsInstance(harm_calc.triggered_indicators, list)
                self.assertIsInstance(harm_calc.scale_adjustment, (int, float))
                self.assertIsInstance(harm_calc.confidence_factors, list)
        
        # Verify benefit calculation details
        for dimension in ['animals', 'humans', 'environment']:
            if dimension in calc_details['benefit']:
                benefit_calc = calc_details['benefit'][dimension]
                self.assertIsInstance(benefit_calc, CalculationDetails)
                self.assertIsNotNone(benefit_calc.final_score)
                # Should have climate benefit for renewable energy
                if dimension == 'environment':
                    self.assertTrue(benefit_calc.has_climate_benefit)
    
    def test_effect_flag_includes_calculation_details(self):
        """Test that effect flags include complete impact measurement with calculation details."""
        # Create a mock effect flag with impact measurement
        measurement = self.service.measure_impact(
            "A factory implements water recycling, reducing water usage by 80%"
        )
        
        # Create effect flag model
        effect_flag = EffectFlagModel(
            id=1,
            entity_id=1,
            flag_type="green",
            title="Water Conservation Initiative",
            short_title="Water Saving",
            year=2024,
            start_year=2023,
            end_year=2025,
            score=85.0,
            impact=80,
            confidence=75,
            credibility=90,
            summary="Significant water conservation through recycling",
            analysis="Detailed analysis of water conservation efforts",
            domains=["environment", "sustainability"],
            citations=[],
            statements=[],
            impact_measurement=measurement,
            model_sections={"TREVR": "environmental_protection"},
            full_demise_centroid=None,
            is_disclosure_only=False
        )
        
        # Verify the flag contains measurement with calculation details
        self.assertIsNotNone(effect_flag.impact_measurement)
        self.assertIsNotNone(effect_flag.impact_measurement.calculation_details)
        self.assertIn('benefit', effect_flag.impact_measurement.calculation_details)
    
    @patch('eko.db.data.xfer.get_cus_conn')
    def test_xfer_conversion_preserves_calculation_details(self, mock_get_cus_conn):
        """Test that conversion to XferEffectFlagModel preserves calculation details."""
        # Create mock connection
        mock_conn = Mock(spec=Connection)
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.__enter__ = Mock(return_value=mock_cursor)
        mock_cursor.__exit__ = Mock(return_value=None)
        
        # Create measurement with calculation details
        measurement = self.service.measure_impact(
            "Company eliminates single-use plastics from operations"
        )
        
        # Create effect flag
        effect_flag = EffectFlagModel(
            id=1,
            entity_id=1,
            flag_type="green",
            title="Plastic Elimination",
            short_title="No Plastic",
            year=2024,
            score=90.0,
            impact=85,
            confidence=80,
            credibility=85,
            impact_measurement=measurement
        )
        
        # Mock customer connection
        mock_cus_conn = Mock()
        mock_cus_cursor = Mock()
        mock_get_cus_conn.return_value.__enter__.return_value = mock_cus_conn
        mock_cus_conn.cursor.return_value = mock_cus_cursor
        mock_cus_cursor.__enter__ = Mock(return_value=mock_cus_cursor)
        mock_cus_cursor.__exit__ = Mock(return_value=None)
        
        # Convert and persist
        XferData.convert_and_persist_xfer_flags(
            mock_conn,
            [effect_flag],
            run_id=123,
            entity_xid="TEST_ENTITY"
        )
        
        # Verify the INSERT call was made
        self.assertTrue(mock_cus_cursor.execute.called)
        
        # Get the SQL and parameters from the call
        call_args = mock_cus_cursor.execute.call_args
        sql = call_args[0][0]
        params = call_args[0][1]
        
        # Verify model data was passed
        model_data = params[3]  # Fourth parameter is model data
        
        # Parse the JSON model data
        model_json = json.loads(model_data)
        
        # Verify calculation details are preserved
        self.assertIn('impact_value_analysis', model_json)
        self.assertIn('impact_measurement', model_json['impact_value_analysis'])
        
        impact_measurement = model_json['impact_value_analysis']['impact_measurement']
        self.assertIn('calculation_details', impact_measurement)
        self.assertIsNotNone(impact_measurement['calculation_details'])
    
    def test_calculation_details_indicators(self):
        """Test that triggered indicators are properly captured."""
        # Test with high-impact environmental event
        event_description = """
        Oil spill contaminates 500 square kilometers of ocean, 
        killing marine life and destroying coastal ecosystems
        """
        
        measurement = self.service.measure_impact(event_description)
        
        # Check environmental harm calculation details
        env_harm = measurement.calculation_details['harm']['environment']
        
        # Should have triggered indicators for oil spill
        self.assertGreater(len(env_harm.triggered_indicators), 0)
        
        # Check for specific indicators (these would be defined in the actual implementation)
        indicator_names = [ind.indicator for ind in env_harm.triggered_indicators]
        
        # Verify high final score due to severity
        self.assertGreater(env_harm.final_score, 0.7)
        
        # Should have climate harm flag
        self.assertTrue(env_harm.has_climate_harm)
    
    def test_scale_adjustment_in_calculations(self):
        """Test that scale adjustments are properly calculated."""
        # Small scale positive action
        small_event = "Local community plants 100 trees in neighborhood park"
        small_measurement = self.service.measure_impact(small_event)
        
        # Large scale positive action
        large_event = "Government reforestation program plants 10 million trees across the country"
        large_measurement = self.service.measure_impact(large_event)
        
        # Compare scale adjustments
        small_scale = small_measurement.calculation_details['benefit']['environment'].scale_adjustment
        large_scale = large_measurement.calculation_details['benefit']['environment'].scale_adjustment
        
        # Large scale should have higher adjustment
        self.assertGreater(large_scale, small_scale)
    
    def test_confidence_factors_in_calculations(self):
        """Test that confidence factors are properly tracked."""
        # Uncertain/speculative event
        uncertain_event = "Company announces vague plans to maybe reduce emissions sometime in the future"
        measurement = self.service.measure_impact(uncertain_event)
        
        # Check confidence factors
        if 'benefit' in measurement.calculation_details:
            for dimension in ['environment', 'humans', 'animals']:
                if dimension in measurement.calculation_details['benefit']:
                    calc = measurement.calculation_details['benefit'][dimension]
                    # Should have confidence factors noting uncertainty
                    self.assertGreater(len(calc.confidence_factors), 0)
    
    def test_default_severity_flags(self):
        """Test that default severity usage is properly flagged."""
        # Generic negative event without specific details
        generic_event = "Company causes some environmental damage"
        measurement = self.service.measure_impact(generic_event)
        
        # Check if default severity was used
        env_harm = measurement.calculation_details['harm']['environment']
        
        # The system should flag when it had to use defaults
        if hasattr(env_harm, 'used_default_severity'):
            self.assertIsInstance(env_harm.used_default_severity, bool)
    
    @patch('eko.db.sync.psycopg.connect')
    def test_xfer_table_sync_preserves_structure(self, mock_connect):
        """Test that syncing xfer tables preserves the calculation details structure."""
        # Mock connections
        source_conn = Mock()
        target_conn = Mock()
        mock_connect.side_effect = [source_conn, target_conn]
        
        # Mock cursors with proper COPY protocol
        source_cursor = Mock()
        target_cursor = Mock()
        source_conn.cursor.return_value = source_cursor
        target_conn.cursor.return_value = target_cursor
        
        # Mock COPY operations
        source_copy = Mock()
        target_copy = Mock()
        source_cursor.copy.return_value = source_copy
        target_cursor.copy.return_value = target_copy
        
        # Simulate data transfer
        test_data = b"test_binary_data_with_json"
        source_copy.__iter__ = Mock(return_value=iter([test_data]))
        source_copy.__enter__ = Mock(return_value=source_copy)
        source_copy.__exit__ = Mock(return_value=None)
        target_copy.__enter__ = Mock(return_value=target_copy)
        target_copy.__exit__ = Mock(return_value=None)
        
        # Import and run sync
        from eko.db.sync import transfer_table_data
        
        source_params = {
            'dbname': 'analytics_db',
            'user': 'test_user',
            'password': 'test_pass',
            'host': 'localhost',
            'port': '5432'
        }
        
        target_params = {
            'dbname': 'customer_db',
            'user': 'test_user',
            'password': 'test_pass',
            'host': 'localhost',
            'port': '5432'
        }
        
        # Sync the table
        transfer_table_data(source_params, target_params, ["xfer_flags"])
        
        # Verify COPY commands were called correctly
        source_cursor.copy.assert_called_with("COPY xfer_flags TO STDOUT (FORMAT BINARY)")
        target_cursor.copy.assert_called_with("COPY xfer_flags FROM STDIN (FORMAT BINARY)")
        
        # Verify data was written
        target_copy.write.assert_called_with(test_data)


if __name__ == '__main__':
    unittest.main()
