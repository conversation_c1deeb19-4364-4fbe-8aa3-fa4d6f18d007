"""
Integration tests for CLI commands that should trigger score calculation.

This module tests that the main CLI analysis commands properly trigger score calculation
and that score calculation failures cause the entire analysis to fail (fail-fast behavior).
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from cli.analysis_v2 import ana
from eko.db.data.xfer import XferData


class TestCLIScoreIntegration:
    """Test CLI commands that should trigger score calculation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()

    @patch('cli.analysis_v2.get_virtual_entity_for_analytics')
    @patch('cli.analysis_v2.get_bo_conn')
    @patch('cli.analysis_v2.RunData')
    @patch('cli.analysis_v2.XferData.sync_score_to_xfer_v2')
    def test_selective_highlighting_command_calls_score_sync(
        self, mock_score_sync, mock_run_data, mock_get_bo_conn, mock_get_virtual_entity
    ):
        """Test that selective-highlighting command calls score sync."""
        # Mock virtual entity
        mock_entity = Mock()
        mock_entity.short_id = "TEST_ENTITY"
        mock_entity.base_entities = [Mock()]
        mock_get_virtual_entity.return_value = mock_entity
        
        # Mock database connection
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        
        # Mock run creation
        mock_run = Mock()
        mock_run.id = 123
        mock_run_data.create.return_value = mock_run
        mock_run_data.mark_completed.return_value = True
        
        # Mock successful score sync
        mock_score_sync.return_value = ["TEST_ENTITY"]
        
        # Mock other dependencies
        with patch('cli.analysis_v2.run_selective_highlighting'), \
             patch('cli.analysis_v2.XferData.sync_run'), \
             patch('cli.analysis_v2.XferData.sync_virtual_entities'), \
             patch('cli.analysis_v2.XferData.sync_cherry_to_xfer_v2'):
            
            # Execute the command
            result = self.runner.invoke(ana, ['selective-highlighting', '--entity', 'test-entity'])
            
            # Verify score sync was called
            mock_score_sync.assert_called_once_with(mock_conn, 123)
            assert result.exit_code == 0

    @patch('cli.analysis_v2.get_virtual_entity_for_analytics')
    @patch('cli.analysis_v2.get_bo_conn')
    @patch('cli.analysis_v2.RunData')
    @patch('cli.analysis_v2.XferData.sync_score_to_xfer_v2')
    def test_selective_highlighting_command_fails_on_score_sync_error(
        self, mock_score_sync, mock_run_data, mock_get_bo_conn, mock_get_virtual_entity
    ):
        """Test that selective-highlighting command fails when score sync fails."""
        # Mock virtual entity
        mock_entity = Mock()
        mock_entity.short_id = "TEST_ENTITY"
        mock_entity.base_entities = [Mock()]
        mock_get_virtual_entity.return_value = mock_entity
        
        # Mock database connection
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        
        # Mock run creation
        mock_run = Mock()
        mock_run.id = 123
        mock_run_data.create.return_value = mock_run
        mock_run_data.get_by_id.return_value = mock_run
        mock_run_data.mark_completed.return_value = True
        mock_run_data.mark_failed.return_value = True
        
        # Mock score sync failure
        mock_score_sync.side_effect = Exception("Score sync failed")
        
        # Mock other dependencies
        with patch('cli.analysis_v2.run_selective_highlighting'), \
             patch('cli.analysis_v2.XferData.sync_run'), \
             patch('cli.analysis_v2.XferData.sync_virtual_entities'), \
             patch('cli.analysis_v2.XferData.sync_cherry_to_xfer_v2'), \
             patch('cli.analysis_v2.console'):
            
            # Execute the command
            result = self.runner.invoke(ana, ['selective-highlighting', '--entity', 'test-entity'])
            
            # Verify score sync was called and run was marked as failed
            mock_score_sync.assert_called_once_with(mock_conn, 123)
            mock_run_data.mark_failed.assert_called_once()
            # Command should complete (exit code 0) but run should be marked as failed
            assert result.exit_code == 0

    @patch('cli.analysis_v2.get_virtual_entity_for_analytics')
    @patch('cli.analysis_v2.get_bo_conn')
    @patch('cli.analysis_v2.RunData')
    @patch('cli.analysis_v2.do_analysis')
    def test_full_command_calls_score_sync_via_do_analysis(
        self, mock_do_analysis, mock_run_data, mock_get_bo_conn, mock_get_virtual_entity
    ):
        """Test that full command calls score sync via do_analysis."""
        # Mock virtual entity
        mock_entity = Mock()
        mock_entity.short_id = "TEST_ENTITY"
        mock_entity.base_entities = [Mock()]
        mock_get_virtual_entity.return_value = mock_entity
        
        # Mock database connection
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        
        # Mock run creation
        mock_run = Mock()
        mock_run.id = 123
        mock_run_data.create.return_value = mock_run
        mock_run_data.mark_completed.return_value = True
        
        # Mock successful do_analysis (which should call score sync internally)
        mock_do_analysis.return_value = None
        
        # Mock other dependencies
        with patch('cli.analysis_v2.XferData.sync_run'), \
             patch('cli.analysis_v2.XferData.sync_model_sections_to_xfer_v2'):
            
            # Execute the command
            result = self.runner.invoke(ana, ['full', '--entity', 'test-entity'])
            
            # Verify do_analysis was called (which should call score sync internally)
            mock_do_analysis.assert_called_once()
            assert result.exit_code == 0

    @patch('cli.analysis_v2.get_virtual_entity_for_analytics')
    @patch('cli.analysis_v2.get_bo_conn')
    @patch('cli.analysis_v2.RunData')
    @patch('cli.analysis_v2.do_analysis')
    def test_full_command_fails_on_do_analysis_error(
        self, mock_do_analysis, mock_run_data, mock_get_bo_conn, mock_get_virtual_entity
    ):
        """Test that full command fails when do_analysis fails (including score sync failures)."""
        # Mock virtual entity
        mock_entity = Mock()
        mock_entity.short_id = "TEST_ENTITY"
        mock_entity.base_entities = [Mock()]
        mock_get_virtual_entity.return_value = mock_entity
        
        # Mock database connection
        mock_conn = Mock()
        mock_get_bo_conn.return_value.__enter__.return_value = mock_conn
        
        # Mock run creation
        mock_run = Mock()
        mock_run.id = 123
        mock_run_data.create.return_value = mock_run
        mock_run_data.mark_completed.return_value = True
        mock_run_data.mark_failed.return_value = True
        
        # Mock do_analysis failure (could be from score sync or other components)
        mock_do_analysis.side_effect = Exception("Analysis failed (including score sync)")
        
        # Mock other dependencies
        with patch('cli.analysis_v2.XferData.sync_run'), \
             patch('cli.analysis_v2.XferData.sync_model_sections_to_xfer_v2'), \
             patch('cli.analysis_v2.console'):
            
            # Execute the command
            result = self.runner.invoke(ana, ['full', '--entity', 'test-entity'])
            
            # Verify do_analysis was called and run was marked as failed
            mock_do_analysis.assert_called_once()
            mock_run_data.mark_failed.assert_called_once()
            # Command should complete (exit code 0) but run should be marked as failed
            assert result.exit_code == 0


class TestDoAnalysisScoreSync:
    """Test the do_analysis function specifically for score sync behavior."""

    @patch('cli.analysis_v2.XferData.sync_score_to_xfer_v2')
    @patch('cli.analysis_v2.create_effect_flags')
    @patch('cli.analysis_v2.run_claims')
    @patch('cli.analysis_v2.run_promises')
    @patch('cli.analysis_v2.run_selective_highlighting')
    @patch('cli.analysis_v2.run_prediction')
    @patch('cli.analysis_v2.XferData.sync_virtual_entities')
    @patch('cli.analysis_v2.XferData.sync_model_sections_to_xfer_v2')
    @patch('cli.analysis_v2.run_cms_publish')
    @patch('cli.analysis_v2.get_traceability_tracker')
    def test_do_analysis_calls_score_sync(
        self, mock_tracker, mock_cms, mock_sync_sections, mock_sync_entities,
        mock_prediction, mock_selective, mock_promises, mock_claims, 
        mock_create_flags, mock_score_sync
    ):
        """Test that do_analysis calls score sync."""
        from cli.analysis_v2 import do_analysis
        
        # Mock dependencies
        mock_conn = Mock()
        mock_run = Mock()
        mock_run.id = 123
        mock_entity = Mock()
        mock_expanded_entity = Mock()
        
        # Mock successful score sync
        mock_score_sync.return_value = ["TEST_ENTITY"]
        
        # Execute do_analysis
        do_analysis(
            conn=mock_conn,
            end_year=2024,
            entities=[mock_entity],
            entity="test-entity",
            expanded_entity=mock_expanded_entity,
            max_workers=4,
            run=mock_run,
            start_year=2014
        )
        
        # Verify score sync was called
        mock_score_sync.assert_called_once_with(mock_conn, 123)

    @patch('cli.analysis_v2.XferData.sync_score_to_xfer_v2')
    @patch('cli.analysis_v2.create_effect_flags')
    @patch('cli.analysis_v2.run_claims')
    @patch('cli.analysis_v2.run_promises')
    @patch('cli.analysis_v2.run_selective_highlighting')
    @patch('cli.analysis_v2.run_prediction')
    @patch('cli.analysis_v2.XferData.sync_virtual_entities')
    def test_do_analysis_propagates_score_sync_failure(
        self, mock_sync_entities, mock_prediction, mock_selective, 
        mock_promises, mock_claims, mock_create_flags, mock_score_sync
    ):
        """Test that do_analysis propagates score sync failures."""
        from cli.analysis_v2 import do_analysis
        
        # Mock dependencies
        mock_conn = Mock()
        mock_run = Mock()
        mock_run.id = 123
        mock_entity = Mock()
        mock_expanded_entity = Mock()
        
        # Mock score sync failure
        mock_score_sync.side_effect = Exception("Score sync failed in do_analysis")
        
        # Execute do_analysis and verify it raises the exception
        with pytest.raises(Exception) as exc_info:
            do_analysis(
                conn=mock_conn,
                end_year=2024,
                entities=[mock_entity],
                entity="test-entity",
                expanded_entity=mock_expanded_entity,
                max_workers=4,
                run=mock_run,
                start_year=2014
            )
        
        assert "Score sync failed in do_analysis" in str(exc_info.value)
        mock_score_sync.assert_called_once_with(mock_conn, 123)
