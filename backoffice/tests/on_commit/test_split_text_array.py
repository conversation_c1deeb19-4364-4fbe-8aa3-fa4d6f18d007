import unittest

from eko.nlp.util import split_text_array


class TestUtilFunctions(unittest.TestCase):

    def test_split_text_array(self):
        text_array = ["This is a test.", "Another test.", "Yet another test."]
        max_tokens = 3
        expected = [["This is a test."], ["Another test."], ["Yet another test."]]
        self.assertEqual(split_text_array(text_array, max_tokens), expected)

        text_array = ["Short text.", "This is a bit longer text.", "Short again."]
        max_chars = 10
        expected = [["Short text.", "This is a bit longer text."], ["Short again."]]
        self.assertEqual(split_text_array(text_array, max_tokens), expected)

        text_array = ["One more test.", "And another one.", "Final test."]
        max_chars = 20
        expected = [["One more test.", "And another one.", "Final test."]]
        self.assertEqual(split_text_array(text_array, max_tokens), expected)

if __name__ == '__main__':
    unittest.main()
