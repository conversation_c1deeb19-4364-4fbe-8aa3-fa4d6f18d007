"""
Tests for effect flag helpers, specifically the merge_flag_group function.

This module tests the merge_flag_group function to ensure that:
1. Merging flags in different orders produces the same result
2. All relevant data is properly combined
3. Edge cases are handled correctly
"""
import unittest
import uuid
from typing import List, Optional
from unittest.mock import patch

from eko.analysis_v2.effects.effect_flags_helpers import merge_flag_group
from eko.analysis_v2.effects.impact.models.assessment import (
    DimensionAssessment, HarmImpactAssessment, BenefitImpactAssessment,
    AnimalHarmImpact, HumanHarmImpact, EnvironmentHarmImpact,
    AnimalBenefitImpact, HumanBenefitImpact, EnvironmentBenefitImpact
)
from eko.analysis_v2.effects.impact.models.base import TemporalBreakdown
from eko.analysis_v2.effects.impact.models.scale_factors import ScaleFactors
from eko.analysis_v2.effects.impact.models.measurement import EventImpactMeasurement
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.effect import EffectFlagModel
from eko.models.vector.derived.effect_type import EffectType
from eko.models.vector.derived.categories import EffectCategory


class TestMergeFlagGroup(unittest.TestCase):
    """Test suite for merge_flag_group function."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test DEMISE models
        self.demise1 = DEMISEModel.model_construct()
        self.demise1.domain.environment.climate = 0.8
        self.demise1.domain.industry.manufacturing = 0.6

        self.demise2 = DEMISEModel.model_construct()
        self.demise2.domain.environment.climate = 0.7
        self.demise2.domain.society.employment = 0.5

        self.demise3 = DEMISEModel.model_construct()
        self.demise3.domain.governance.transparency = 0.9
        self.demise3.domain.industry.technology = 0.4

    def create_test_impact_measurement(self) -> EventImpactMeasurement:
        """Create a test EventImpactMeasurement."""
        temporal = TemporalBreakdown(
            immediate="Immediate impact",
            medium_term="Medium term impact", 
            long_term="Long term impact"
        )
        
        scale_factors = ScaleFactors(
            proximity_to_tipping_point="Test proximity",
            # Reversibility questions
            is_irreversible=False,
            takes_centuries_to_reverse=False,
            requires_significant_effort_to_reverse=True,
            reversible_within_years=False,
            reversible_within_months=False,
            reversible_within_weeks=False,
            fully_reversible_immediately=False,
            # Scale of impact questions
            affects_single_individual=False,
            affects_multiple_individuals=True,
            affects_many_beings=False,
            affects_large_population=False,
            affects_country_ecosystem=False,
            affects_species_biome=False,
            affects_all_global=False,
            # Directness questions
            third_party_action=False,
            entity_had_minor_influence=False,
            entity_influenced_outcome=True,
            entity_action_led_to_impact=False,
            entity_decision_caused_impact=False,
            direct_action_by_entity=False,
            entity_sole_direct_cause=False,
            # Authenticity questions
            pure_marketing_greenwashing=False,
            mostly_regulatory_compliance=False,
            primarily_business_driven=True,
            balanced_genuine_business=False,
            mostly_genuine_some_business=False,
            genuine_commitment=False,
            purely_altruistic=False,
            # Deliberateness questions
            completely_accidental=False,
            foreseeable_not_intended=False,
            knew_consequences_acted_anyway=True,
            intended_action_predictable_outcome=False,
            planned_with_awareness=False,
            fully_intentional=False,
            deliberately_planned_for_impact=False,
            # Contribution questions
            not_contributing=False,
            very_minor_contributor=False,
            minor_contributor=True,
            significant_contributor=False,
            major_contributor=False,
            dominant_contributor=False,
            sole_contributor=False,
            # Duration
            duration="medium",
            # Degree questions
            no_impact=False,
            minor_impact=False,
            moderate_impact=True,
            major_impact=False,
            severe_impact=False,
            extreme_impact=False,
            existential_impact=False,
            # Probability questions
            will_not_happen=False,
            very_unlikely=False,
            unlikely=False,
            even_chance=False,
            highly_likely=False,
            virtually_certain=False,
            has_already_happened=True,
        )
        
        dimension = DimensionAssessment(
            reasoning="Test reasoning",
            temporal_breakdown=temporal,
            scale_factors=scale_factors
        )
        # Set score and confidence after creation
        dimension.score = 0.5
        dimension.confidence = "medium"
        
        # Create harm and benefit assessments with the new structure
        harm_assessment = HarmImpactAssessment(
            animals=AnimalHarmImpact(assessment=dimension),
            humans=HumanHarmImpact(assessment=dimension),
            environment=EnvironmentHarmImpact(assessment=dimension)
        )
        
        benefit_assessment = BenefitImpactAssessment(
            animals=AnimalBenefitImpact(assessment=dimension),
            humans=HumanBenefitImpact(assessment=dimension),
            environment=EnvironmentBenefitImpact(assessment=dimension)
        )
        
        return EventImpactMeasurement(
            event_summary="Test event",
            event_description="Test event description",
            harm_assessment=harm_assessment,
            benefit_assessment=benefit_assessment,
            key_uncertainties=[],
            ethical_considerations=[],
            assessed_at="2023-01-01T00:00:00Z",
            model_used="test-model",
            prompt_version="1.0"
        )


    def create_test_flag(self,
                        flag_id: int,
                        title: str,
                        impact: int,
                        confidence: int,
                        credibility: int,
                        effect_model_ids: Optional[List[int]] = None,
                        statement_ids: Optional[List[int]] = None,
                        domains: Optional[List[str]] = None,
                        demise: Optional[DEMISEModel] = None) -> EffectFlagModel:
        """Create a test EffectFlagModel with specified parameters."""

        if effect_model_ids is None:
            effect_model_ids = [flag_id]
        if statement_ids is None:
            statement_ids = [flag_id]
        if domains is None:
            domains = [f"domain_{flag_id}"]
        if demise is None:
            demise = DEMISEModel.model_construct()

        # Analysis text with proper citations for testing
        # Each flag gets unique citation IDs to ensure they pass the validation
        analysis_text = f"Analysis for {title} shows significant impact [^{1000 + flag_id}]. Additional evidence supports this finding [^{2000 + flag_id}]."

        # Use model_construct to bypass validation
        return EffectFlagModel.model_construct(
            id=flag_id,
            trace_id=str(uuid.uuid4()),
            run_id=1,
            title=title,
            short_title=title[:10],
            summary=f"Summary for {title}",
            reason=f"Reason for {title}",
            analysis=analysis_text,
            impact=impact,
            confidence=confidence,
            credibility=credibility,
            start_year=2023,
            end_year=2023,
            effect_type=EffectType.RED,
            category=EffectCategory.ECOLOGICAL,
            effect_model_ids=effect_model_ids,
            statement_ids=statement_ids,
            statements=[],
            domains=domains,
            citations=[],
            full_demise_centroid=demise,
            is_disclosure_only=False,
            entity_name="Test Entity",
            entity_ids=[1],
            virtual_entity_id=1,
            virtual_entity_short_id="TEST",
            impact_measurement=self.create_test_impact_measurement()
        )

    def test_empty_list_raises_error(self):
        """Test that merging an empty list raises ValueError."""
        with self.assertRaises(ValueError) as context:
            merge_flag_group([])

        self.assertIn("Cannot merge empty list of flags", str(context.exception))

    @patch('eko.analysis_v2.effects.effect_flags_helpers.combine_text')
    def test_single_flag_returns_same_flag(self, mock_combine_text):
        """Test that merging a single flag returns essentially the same flag."""
        # Mock combine_text to return text with citations
        mock_combine_text.return_value = "Analysis for Single Flag shows significant impact [^1001]. Additional evidence supports this finding [^2001]."

        flag = self.create_test_flag(
            flag_id=1,
            title="Single Flag",
            impact=80,
            confidence=90,
            credibility=85
        )

        result = merge_flag_group([flag])

        # Should have same core properties but new trace_id and no id
        self.assertIsNone(result.id)
        self.assertNotEqual(result.trace_id, flag.trace_id)
        self.assertEqual(result.title, flag.title)
        self.assertEqual(result.impact, flag.impact)
        self.assertEqual(result.confidence, flag.confidence)
        self.assertEqual(result.credibility, flag.credibility)

    @patch('eko.analysis_v2.effects.effect_flags_helpers.combine_text')
    def test_merge_order_independence(self, mock_combine_text):
        """Test that merging flags in different orders produces the same result."""
        # Mock combine_text to return text with citations
        mock_combine_text.return_value = "Combined analysis shows significant impact [^1001]. Additional evidence supports this finding [^2001]."

        # Create three flags with different scores to test sorting behavior
        flag1 = self.create_test_flag(
            flag_id=1,
            title="Flag A",  # Title affects sorting as secondary key
            impact=80,
            confidence=90,
            credibility=85,
            effect_model_ids=[1, 2],
            statement_ids=[1],
            domains=["environment"],
            demise=self.demise1
        )

        flag2 = self.create_test_flag(
            flag_id=2,
            title="Flag B",
            impact=90,  # Higher impact should make this the primary flag
            confidence=85,
            credibility=90,
            effect_model_ids=[3, 4],
            statement_ids=[2],
            domains=["society"],
            demise=self.demise2
        )

        flag3 = self.create_test_flag(
            flag_id=3,
            title="Flag C",
            impact=70,
            confidence=75,
            credibility=80,
            effect_model_ids=[5],
            statement_ids=[3],
            domains=["governance"],
            demise=self.demise3
        )

        # Test different orderings
        order1 = [flag1, flag2, flag3]
        order2 = [flag3, flag1, flag2]
        order3 = [flag2, flag3, flag1]

        result1 = merge_flag_group(order1)
        result2 = merge_flag_group(order2)
        result3 = merge_flag_group(order3)

        # All results should have the same primary flag properties
        # (the function sorts by composite score, so flag2 should be primary)
        self.assertEqual(result1.title, result2.title)
        self.assertEqual(result2.title, result3.title)
        self.assertEqual(result1.impact, result2.impact)
        self.assertEqual(result2.impact, result3.impact)

        # Combined fields should be the same regardless of order
        self.assertEqual(set(result1.effect_model_ids), set(result2.effect_model_ids))
        self.assertEqual(set(result2.effect_model_ids), set(result3.effect_model_ids))

        self.assertEqual(set(result1.statement_ids), set(result2.statement_ids))
        self.assertEqual(set(result2.statement_ids), set(result3.statement_ids))

        self.assertEqual(set(result1.domains), set(result2.domains))
        self.assertEqual(set(result2.domains), set(result3.domains))

    @patch('eko.analysis_v2.effects.effect_flags_helpers.combine_text')
    def test_data_combination(self, mock_combine_text):
        """Test that data is properly combined when merging flags."""
        # Mock combine_text to return text with citations
        mock_combine_text.return_value = "Combined analysis shows significant impact [^1001]. Additional evidence supports this finding [^2001]."

        flag1 = self.create_test_flag(
            flag_id=1,
            title="Flag 1",
            impact=80,
            confidence=90,
            credibility=85,
            effect_model_ids=[1, 2],
            statement_ids=[1, 2],
            domains=["environment", "society"]
        )

        flag2 = self.create_test_flag(
            flag_id=2,
            title="Flag 2",
            impact=90,
            confidence=85,
            credibility=90,
            effect_model_ids=[2, 3],  # Overlapping with flag1
            statement_ids=[2, 3],    # Overlapping with flag1
            domains=["society", "governance"]  # Overlapping with flag1
        )

        result = merge_flag_group([flag1, flag2])

        # Check combined effect_model_ids (should be unique)
        expected_effect_ids = [1, 2, 3]
        self.assertEqual(sorted(result.effect_model_ids), expected_effect_ids)

        # Check combined statement_ids (should be unique)
        expected_statement_ids = [1, 2, 3]
        self.assertEqual(sorted(result.statement_ids), expected_statement_ids)

        # Check combined domains (should be unique and sorted)
        expected_domains = ["environment", "governance", "society"]
        self.assertEqual(sorted(result.domains), expected_domains)

    @patch('eko.analysis_v2.effects.effect_flags_helpers.combine_text')
    def test_year_range_calculation(self, mock_combine_text):
        """Test that start_year and end_year are calculated correctly."""
        # Mock combine_text to return text with citations
        mock_combine_text.return_value = "Combined analysis shows significant impact [^1001]. Additional evidence supports this finding [^2001]."

        flag1 = self.create_test_flag(
            flag_id=1,
            title="Flag 1",
            impact=80,
            confidence=90,
            credibility=85
        )
        flag1.start_year = 2020
        flag1.end_year = 2022

        flag2 = self.create_test_flag(
            flag_id=2,
            title="Flag 2",
            impact=90,
            confidence=85,
            credibility=90
        )
        flag2.start_year = 2021
        flag2.end_year = 2024

        result = merge_flag_group([flag1, flag2])

        # Should take the earliest start_year and latest end_year
        self.assertEqual(result.start_year, 2020)
        self.assertEqual(result.end_year, 2024)

    @patch('eko.analysis_v2.effects.effect_flags_helpers.combine_text')
    def test_disclosure_only_flag_calculation(self, mock_combine_text):
        """Test that is_disclosure_only is calculated correctly."""
        # Mock combine_text to return text with citations
        mock_combine_text.return_value = "Combined analysis shows significant impact [^1001]. Additional evidence supports this finding [^2001]."

        flag1 = self.create_test_flag(
            flag_id=1,
            title="Flag 1",
            impact=80,
            confidence=90,
            credibility=85
        )
        flag1.is_disclosure_only = True

        flag2 = self.create_test_flag(
            flag_id=2,
            title="Flag 2",
            impact=90,
            confidence=85,
            credibility=90
        )
        flag2.is_disclosure_only = False

        # If any flag is not disclosure-only, result should be False
        result = merge_flag_group([flag1, flag2])
        self.assertFalse(result.is_disclosure_only)

        # If all flags are disclosure-only, result should be True
        flag2.is_disclosure_only = True
        result = merge_flag_group([flag1, flag2])
        self.assertTrue(result.is_disclosure_only)

    @patch('eko.analysis_v2.effects.effect_flags_helpers.combine_text')
    def test_trace_ids_preservation(self, mock_combine_text):
        """Test that trace IDs from merged flags are preserved."""
        # Mock combine_text to return text with citations
        mock_combine_text.return_value = "Combined analysis shows significant impact [^1001]. Additional evidence supports this finding [^2001]."

        flag1 = self.create_test_flag(
            flag_id=1,
            title="Flag 1",
            impact=80,
            confidence=90,
            credibility=85
        )

        flag2 = self.create_test_flag(
            flag_id=2,
            title="Flag 2",
            impact=90,
            confidence=85,
            credibility=90
        )

        result = merge_flag_group([flag1, flag2])

        # Should preserve trace IDs from both flags
        expected_trace_ids = sorted([flag1.trace_id, flag2.trace_id])
        self.assertEqual(sorted(result.effect_flag_trace_ids), expected_trace_ids)

        # Should preserve flag IDs (excluding None values)
        expected_flag_ids = sorted([1, 2])
        self.assertEqual(sorted(result.effect_flag_ids), expected_flag_ids)

    @patch('eko.analysis_v2.effects.effect_flags_helpers.combine_text')
    def test_sorting_behavior(self, mock_combine_text):
        """Test that flags are sorted correctly by composite score and title."""
        # Mock combine_text to return text with citations
        mock_combine_text.return_value = "Combined analysis shows significant impact [^1001]. Additional evidence supports this finding [^2001]."

        # Create flags with different composite scores
        # Composite score = impact * confidence * credibility

        # Flag with lower composite score but comes first alphabetically
        flag_low_score = self.create_test_flag(
            flag_id=1,
            title="A Flag",  # Alphabetically first
            impact=50,      # 50 * 50 * 50 = 125,000
            confidence=50,
            credibility=50
        )

        # Flag with higher composite score
        flag_high_score = self.create_test_flag(
            flag_id=2,
            title="Z Flag",  # Alphabetically last
            impact=80,       # 80 * 80 * 80 = 512,000
            confidence=80,
            credibility=80
        )

        result = merge_flag_group([flag_low_score, flag_high_score])

        # The flag with higher composite score should be the primary flag
        self.assertEqual(result.title, flag_high_score.title)
        self.assertEqual(result.impact, flag_high_score.impact)

        # Test reverse order to ensure sorting works
        result_reverse = merge_flag_group([flag_high_score, flag_low_score])

        # Should still use the high score flag as primary
        self.assertEqual(result_reverse.title, flag_high_score.title)
        self.assertEqual(result_reverse.impact, flag_high_score.impact)


if __name__ == '__main__':
    unittest.main()
