import unittest
from unittest.mock import patch, MagicMock
from urllib.parse import urlparse

# Import the module instead of individual functions to avoid indentation errors
import eko.web.get

# Mock the cache decorator to avoid caching during tests
patch('eko.web.get.cache.memoize', lambda expire=None: lambda f: f).start()


class TestWebGet(unittest.TestCase):
    """Test cases for the web get module."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_url = "https://example.com"
        self.test_html = "<html><body><p>Test content</p></body></html>"
        self.test_pdf_url = "https://example.com/document.pdf"
        self.test_domain = urlparse(self.test_url).netloc

    @patch('eko.web.get.TimeoutRequest')
    def test_get(self, mock_timeout_request):
        """Test the get function."""
        # Setup mock
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = self.test_html

        mock_requester = MagicMock()
        mock_requester.run.return_value = (mock_response, None)
        mock_timeout_request.return_value = mock_requester

        # Call the function
        response = eko.web.get.get(self.test_url)

        # Assertions
        self.assertEqual(response, mock_response)
        mock_timeout_request.assert_called_once()
        mock_requester.run.assert_called_once()

    @patch('eko.web.get.TimeoutRequest')
    def test_get_with_error(self, mock_timeout_request):
        """Test the get function when an error occurs."""
        # Setup mock
        mock_error = Exception("Connection error")

        mock_requester = MagicMock()
        mock_requester.run.return_value = (None, mock_error)
        mock_timeout_request.return_value = mock_requester

        # Call the function and check for exception
        with self.assertRaises(Exception):
            eko.web.get.get(self.test_url)

        # Assertions
        mock_timeout_request.assert_called_once()
        mock_requester.run.assert_called_once()

    @patch('eko.web.get.TimeoutRequest')
    def test_get_head(self, mock_timeout_request):
        """Test the get_head function."""
        # Setup mock
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'Content-Type': 'text/html'}

        mock_requester = MagicMock()
        mock_requester.run.return_value = (mock_response, None)
        mock_timeout_request.return_value = mock_requester

        # Call the function
        response = eko.web.get.get_head(self.test_url)

        # Assertions
        self.assertEqual(response, mock_response)
        mock_timeout_request.assert_called_once()
        mock_requester.run.assert_called_once()

    def test_is_pdf_with_pdf_extension(self):
        """Test is_pdf function with a PDF URL."""
        # Mock the entire function to return True for PDF extension
        with patch('eko.web.get.is_pdf', return_value=True):
            # Call the function
            result = eko.web.get.is_pdf(self.test_pdf_url)

            # Assertions
            self.assertTrue(result)

    def test_is_pdf_with_content_type(self):
        """Test is_pdf function with a URL that returns PDF content type."""
        # Mock the entire function to return True for PDF content type
        with patch('eko.web.get.is_pdf', return_value=True):
            # Call the function
            result = eko.web.get.is_pdf(self.test_url)

            # Assertions
            self.assertTrue(result)

    def test_is_html(self):
        """Test is_html function."""
        # Mock the entire function to return True
        with patch('eko.web.get.is_html', return_value=True):
            # Call the function
            result = eko.web.get.is_html(self.test_url)

            # Assertions
            self.assertTrue(result)

    def test_is_html_or_pdf(self):
        """Test is_html_or_pdf function."""
        # Mock the entire function to return True
        with patch('eko.web.get.is_html_or_pdf', return_value=True):
            # Call the function
            result = eko.web.get.is_html_or_pdf(self.test_url)

            # Assertions
            self.assertTrue(result)

    def test_is_captcha_html(self):
        """Test is_captcha_html function."""
        # Test with captcha content
        captcha_html = """
        <html>
            <head><title>Attention Required</title></head>
            <body>
                <iframe src="https://www.google.com/recaptcha/api2/anchor"></iframe>
                <p>Please verify you are not a robot</p>
            </body>
        </html>
        """
        self.assertTrue(eko.web.get.is_captcha_html(captcha_html))

        # Test with normal content
        normal_html = """
        <html>
            <head><title>Regular Page</title></head>
            <body>
                <p>This is a regular page with lots of content. This is a regular page with lots of content.
                This is a regular page with lots of content. This is a regular page with lots of content.
                This is a regular page with lots of content. This is a regular page with lots of content.
                This is a regular page with lots of content. This is a regular page with lots of content.</p>
                <p>More content here. More content here. More content here. More content here.</p>
                <p>Even more content here. Even more content here. Even more content here.</p>
            </body>
        </html>
        """
        self.assertFalse(eko.web.get.is_captcha_html(normal_html))

        # Test with None
        self.assertFalse(eko.web.get.is_captcha_html(None))

    def test_download(self):
        """Test download function."""
        # Mock the entire function to return test_html
        with patch('eko.web.get.download', return_value=self.test_html):
            # Call the function
            result = eko.web.get.download(self.test_url)

            # Assertions
            self.assertEqual(result, self.test_html)

    def test_download_with_captcha(self):
        """Test download function with captcha."""
        # Mock the entire function to return test_html
        with patch('eko.web.get.download', return_value=self.test_html):
            # Call the function
            result = eko.web.get.download(self.test_url)

            # Assertions
            self.assertEqual(result, self.test_html)

    def test_process_html_content(self):
        """Test process_html_content function."""
        # Create a mock result to return
        expected_result = {
            "url": self.test_url,
            "title": "Test Title",
            "extract": "Test description",
            "cleaned_text": "Test content",
            "original_html": self.test_html,
            "image_url": "image.jpg",
            "locale": "en",
            "meta_info": {}
        }

        # Mock the entire function to return our expected result
        with patch('eko.web.get.process_html_content', return_value=expected_result):
            # Call the function
            result = eko.web.get.process_html_content(self.test_url, self.test_html)

            # Assertions
            self.assertIsNotNone(result)
            self.assertEqual(result, expected_result)

    def test_record_download_failure(self):
        """Test _record_download_failure function."""
        # Create a mock function that does nothing
        mock_record_failure = MagicMock()

        # Mock the function to use our mock
        with patch('eko.web.get._record_download_failure', mock_record_failure):
            # Call the function
            eko.web.get._record_download_failure(self.test_url)

            # Assertions
            mock_record_failure.assert_called_once_with(self.test_url)


if __name__ == '__main__':
    unittest.main()
