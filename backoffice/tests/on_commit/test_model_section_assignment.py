"""
Tests for model section assignment functionality.

This module tests the assign_model_sections function to ensure that:
1. Model sections are correctly assigned to effect flags using LLM
2. Validation works correctly for valid and invalid assignments
3. Edge cases are handled properly
4. The function returns the flag with model_sections populated
"""
import unittest
import uuid
from typing import Dict, List
from unittest.mock import patch, MagicMock

from eko.analysis_v2.effects.model_section_assignment import assign_model_sections, ModelSectionAssignmentResponse
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.effect import EffectFlagModel
from eko.models.vector.derived.effect_type import EffectType
from eko.models.vector.derived.categories import EffectCategory


class TestAssignModelSections(unittest.TestCase):
    """Test suite for assign_model_sections function."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test DEMISE model
        self.demise = DEMISEModel.model_construct()
        self.demise.domain.environment.climate = 0.8
        self.demise.domain.industry.manufacturing = 0.6

        # Create sample model sections data
        self.model_sections = {
            "sdg": [
                {
                    "section": "sdg_1",
                    "title": "No Poverty",
                    "description": "End poverty in all its forms everywhere",
                    "level": "goal",
                    "icon": "poverty"
                },
                {
                    "section": "sdg_13",
                    "title": "Climate Action",
                    "description": "Take urgent action to combat climate change",
                    "level": "goal",
                    "icon": "climate"
                }
            ],
            "doughnut": [
                {
                    "section": "social_foundation",
                    "title": "Social Foundation",
                    "description": "Meeting basic human needs",
                    "level": "foundation",
                    "icon": "social"
                },
                {
                    "section": "ecological_ceiling",
                    "title": "Ecological Ceiling",
                    "description": "Staying within planetary boundaries",
                    "level": "ceiling",
                    "icon": "ecology"
                }
            ]
        }

    def create_test_flag(self,
                        flag_id: int = 1,
                        title: str = "Test Flag",
                        summary: str = "Test summary",
                        analysis: str = "Test analysis",
                        reason: str = "Test reason",
                        category: EffectCategory = EffectCategory.ECOLOGICAL,
                        effect_type: EffectType = EffectType.RED,
                        domains: List[str] = None) -> EffectFlagModel:
        """Create a test EffectFlagModel with specified parameters."""

        if domains is None:
            domains = ["environment", "climate"]

        return EffectFlagModel.model_construct(
            id=flag_id,
            trace_id=str(uuid.uuid4()),
            run_id=1,
            title=title,
            short_title=title[:10],
            summary=summary,
            analysis=analysis,
            reason=reason,
            category=category,
            effect_type=effect_type,
            impact=80,
            authentic=70,
            contribution=60,
            confidence=90,
            credibility=85,
            start_year=2023,
            end_year=2023,
            statement_ids=[1],
            statements=[],
            domains=domains,
            citations=[],
            full_demise_centroid=self.demise,
            is_disclosure_only=False,
            entity_name="Test Entity",
            entity_ids=[1],
            virtual_entity_id=1,
            virtual_entity_short_id="TEST",
            model_sections={}
        )

    @patch('eko.analysis_v2.effects.model_section_assignment.call_llms_typed')
    def test_successful_assignment(self, mock_call_llms):
        """Test successful model section assignment."""
        # Mock LLM response
        mock_response = ModelSectionAssignmentResponse(
            assignments={
                "sdg": "sdg_13",
                "doughnut": "ecological_ceiling"
            }
        )
        mock_call_llms.return_value = mock_response

        flag = self.create_test_flag(
            title="Climate Impact Flag",
            summary="Company's carbon emissions increased",
            analysis="Analysis shows significant climate impact",
            domains=["environment", "climate"]
        )

        result = assign_model_sections(flag, self.model_sections)

        # Verify the function was called
        mock_call_llms.assert_called_once()

        # Verify the result
        self.assertIsInstance(result, EffectFlagModel)
        self.assertEqual(result.model_sections["sdg"], "sdg_13")
        self.assertEqual(result.model_sections["doughnut"], "ecological_ceiling")
        self.assertEqual(len(result.model_sections), 2)

    @patch('eko.analysis_v2.effects.model_section_assignment.call_llms_typed')
    def test_partial_assignment(self, mock_call_llms):
        """Test assignment where LLM only assigns some models."""
        # Mock LLM response with only one assignment
        mock_response = ModelSectionAssignmentResponse(
            assignments={
                "sdg": "sdg_1"
            }
        )
        mock_call_llms.return_value = mock_response

        flag = self.create_test_flag(
            title="Poverty Impact Flag",
            summary="Company's actions affect poverty levels",
            domains=["society", "economics"]
        )

        result = assign_model_sections(flag, self.model_sections)

        # Should only have the assigned model
        self.assertEqual(result.model_sections["sdg"], "sdg_1")
        self.assertNotIn("doughnut", result.model_sections)
        self.assertEqual(len(result.model_sections), 1)

    @patch('eko.analysis_v2.effects.model_section_assignment.call_llms_typed')
    def test_invalid_section_assignment(self, mock_call_llms):
        """Test handling of invalid section assignments."""
        # Mock LLM response with invalid section
        mock_response = ModelSectionAssignmentResponse(
            assignments={
                "sdg": "invalid_section",
                "doughnut": "ecological_ceiling"
            }
        )
        mock_call_llms.return_value = mock_response

        flag = self.create_test_flag()

        result = assign_model_sections(flag, self.model_sections)

        # Should only include valid assignments
        self.assertNotIn("sdg", result.model_sections)
        self.assertEqual(result.model_sections["doughnut"], "ecological_ceiling")
        self.assertEqual(len(result.model_sections), 1)

    @patch('eko.analysis_v2.effects.model_section_assignment.call_llms_typed')
    def test_empty_model_sections_raises_error(self, mock_call_llms):
        """Test that empty model_sections raises ValueError."""
        flag = self.create_test_flag()

        with self.assertRaises(ValueError) as context:
            assign_model_sections(flag, {})

        self.assertIn("No models found", str(context.exception))
        mock_call_llms.assert_not_called()

    @patch('eko.analysis_v2.effects.model_section_assignment.call_llms_typed')
    def test_llm_prompt_construction(self, mock_call_llms):
        """Test that the LLM prompt is constructed correctly."""
        mock_response = ModelSectionAssignmentResponse(
            assignments={"sdg": "sdg_13"}
        )
        mock_call_llms.return_value = mock_response

        flag = self.create_test_flag(
            title="Climate Flag",
            summary="Climate impact summary",
            analysis="Detailed climate analysis",
            reason="Climate change concern",
            category=EffectCategory.ECOLOGICAL,
            effect_type=EffectType.RED,
            domains=["environment", "climate"]
        )

        assign_model_sections(flag, self.model_sections)

        # Verify call_llms_typed was called with correct parameters
        mock_call_llms.assert_called_once()
        call_args = mock_call_llms.call_args

        # Check that messages were passed
        messages = call_args[0][1]  # Second positional argument
        self.assertEqual(len(messages), 2)
        self.assertEqual(messages[0]["role"], "system")
        self.assertEqual(messages[1]["role"], "user")

        # Check that user prompt contains flag information
        user_prompt = messages[1]["content"]
        self.assertIn("Climate Flag", user_prompt)
        self.assertIn("Climate impact summary", user_prompt)
        self.assertIn("Detailed climate analysis", user_prompt)
        self.assertIn("Climate change concern", user_prompt)
        self.assertIn("ECOLOGICAL", user_prompt)
        self.assertIn("RED", user_prompt)

        # Check that model sections are included
        self.assertIn("sdg", user_prompt)
        self.assertIn("doughnut", user_prompt)
        self.assertIn("No Poverty", user_prompt)
        self.assertIn("Climate Action", user_prompt)

    @patch('eko.analysis_v2.effects.model_section_assignment.call_llms_typed')
    def test_flag_object_preservation(self, mock_call_llms):
        """Test that the original flag object is modified and returned."""
        mock_response = ModelSectionAssignmentResponse(
            assignments={"sdg": "sdg_13"}
        )
        mock_call_llms.return_value = mock_response

        original_flag = self.create_test_flag(title="Original Title")
        original_id = original_flag.id
        original_trace_id = original_flag.trace_id

        result = assign_model_sections(original_flag, self.model_sections)

        # Should be the same object
        self.assertIs(result, original_flag)
        self.assertEqual(result.id, original_id)
        self.assertEqual(result.trace_id, original_trace_id)
        self.assertEqual(result.title, "Original Title")

        # Should have model_sections added
        self.assertEqual(result.model_sections["sdg"], "sdg_13")


if __name__ == '__main__':
    unittest.main()
