"""
Tests for the new feedback-based impact review system.

This module tests that the impact reviewer now provides feedback
on assessment components rather than directly amending scores.
"""
import pytest
from datetime import datetime
from typing import List

from eko.analysis_v2.effects.impact.models.assessment import (
    DimensionAssessment, HarmImpactAssessment, BenefitImpactAssessment,
    AnimalHarmImpact, HumanHarmImpact, EnvironmentHarmImpact,
    AnimalBenefitImpact, HumanBenefitImpact, EnvironmentBenefitImpact
)
from eko.analysis_v2.effects.impact.models.base import TemporalBreakdown
from eko.analysis_v2.effects.impact.models.scale_factors import ScaleFactors
from eko.analysis_v2.effects.impact.models.measurement import EventImpactMeasurement
from eko.analysis_v2.effects.impact.models.review import ReviewDecision
from eko.analysis_v2.effects.impact.models.ass_feedback import AssessmentFeedback
from eko.analysis_v2.effects.impact.models.bias import BiasType
from eko.analysis_v2.effects.impact.review import ImpactReviewer


class TestImpactReviewFeedback:
    """Test the feedback-based review system."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.reviewer = ImpactReviewer()
    
    def create_test_measurement(self) -> EventImpactMeasurement:
        """Create a test EventImpactMeasurement."""
        temporal = TemporalBreakdown(
            immediate="Immediate impact",
            medium_term="Medium term impact", 
            long_term="Long term impact"
        )
        
        scale_factors = ScaleFactors(
            proximity_to_tipping_point="Test proximity",
            # Reversibility questions
            is_irreversible=False,
            takes_centuries_to_reverse=False,
            requires_significant_effort_to_reverse=True,
            reversible_within_years=False,
            reversible_within_months=False,
            reversible_within_weeks=False,
            fully_reversible_immediately=False,
            # Scale of impact questions
            affects_single_individual=False,
            affects_multiple_individuals=True,
            affects_many_beings=False,
            affects_large_population=False,
            affects_country_ecosystem=False,
            affects_species_biome=False,
            affects_all_global=False,
            # Directness questions
            third_party_action=False,
            entity_had_minor_influence=False,
            entity_influenced_outcome=True,
            entity_action_led_to_impact=False,
            entity_decision_caused_impact=False,
            direct_action_by_entity=False,
            entity_sole_direct_cause=False,
            # Authenticity questions
            pure_marketing_greenwashing=False,
            mostly_regulatory_compliance=False,
            primarily_business_driven=True,
            balanced_genuine_business=False,
            mostly_genuine_some_business=False,
            genuine_commitment=False,
            purely_altruistic=False,
            # Deliberateness questions
            completely_accidental=False,
            foreseeable_not_intended=False,
            knew_consequences_acted_anyway=True,
            intended_action_predictable_outcome=False,
            planned_with_awareness=False,
            fully_intentional=False,
            deliberately_planned_for_impact=False,
            # Contribution questions
            not_contributing=False,
            very_minor_contributor=False,
            minor_contributor=True,
            significant_contributor=False,
            major_contributor=False,
            dominant_contributor=False,
            sole_contributor=False,
            duration="medium",
            # Degree questions
            no_impact=False,
            minor_impact=False,
            moderate_impact=True,
            major_impact=False,
            severe_impact=False,
            extreme_impact=False,
            existential_impact=False,
            # Probability questions
            will_not_happen=False,
            very_unlikely=False,
            unlikely=False,
            even_chance=False,
            highly_likely=False,
            virtually_certain=False,
            has_already_happened=True,
        )
        
        dimension = DimensionAssessment(
            reasoning="Test reasoning",
            temporal_breakdown=temporal,
            scale_factors=scale_factors
        )
        # Set score and confidence after creation
        dimension.score = 0.5
        dimension.confidence = "medium"
        
        # Create harm and benefit assessments with the new structure
        harm_assessment = HarmImpactAssessment(
            animals=AnimalHarmImpact(assessment=dimension),
            humans=HumanHarmImpact(assessment=dimension),
            environment=EnvironmentHarmImpact(assessment=dimension)
        )
        
        benefit_assessment = BenefitImpactAssessment(
            animals=AnimalBenefitImpact(assessment=dimension),
            humans=HumanBenefitImpact(assessment=dimension),
            environment=EnvironmentBenefitImpact(assessment=dimension)
        )
        
        return EventImpactMeasurement(
            event_summary="Test event",
            event_description="Test event description",
            harm_assessment=harm_assessment,
            benefit_assessment=benefit_assessment,
            key_uncertainties=[],
            ethical_considerations=[],
            assessed_at=datetime.utcnow().isoformat() + "Z",
            model_used="test-model",
            prompt_version="1.0"
        )
    
    def test_review_decision_has_feedback_not_amendments(self):
        """Test that ReviewDecision uses assessment_feedback instead of score_amendments."""
        feedback = [
            AssessmentFeedback(
                dimension="animals",
                assessment_type="harm",
                component="reasoning",
                issue="Reasoning lacks specificity about animal impacts",
                suggestion="Provide more detailed analysis of specific animal species affected"
            )
        ]
        
        review_decision = ReviewDecision(
            decision="redo",
            reasoning="Assessment needs improvement in animal harm reasoning",
            bias_detected=True,
            bias_types=[BiasType.ANTHROPOCENTRIC_BIAS],
            accuracy_issues=["Insufficient detail on animal impacts"],
            assessment_feedback=feedback,
            confidence_in_review="high"
        )
        
        # Verify the review decision has feedback, not amendments
        assert len(review_decision.assessment_feedback) == 1
        assert review_decision.assessment_feedback[0].dimension == "animals"
        assert review_decision.assessment_feedback[0].component == "reasoning"
        assert "specificity" in review_decision.assessment_feedback[0].issue
        assert "detailed analysis" in review_decision.assessment_feedback[0].suggestion

    def test_feedback_covers_all_components(self):
        """Test that feedback can address all assessment components."""
        components = ["reasoning", "temporal_breakdown", "scale_factors", "general"]
        dimensions = ["animals", "humans", "environment"]
        assessment_types = ["harm", "benefit"]
        
        feedback_list = []
        for component in components:
            for dimension in dimensions:
                for assessment_type in assessment_types:
                    feedback_list.append(
                        AssessmentFeedback(
                            dimension=dimension,
                            assessment_type=assessment_type,
                            component=component,
                            issue=f"Issue with {component} in {dimension} {assessment_type}",
                            suggestion=f"Improve {component} for {dimension} {assessment_type}"
                        )
                    )
        
        # Verify all feedback items are valid
        for feedback in feedback_list:
            assert feedback.dimension in dimensions
            assert feedback.assessment_type in assessment_types
            assert feedback.component in components
            assert len(feedback.issue) > 0
            assert len(feedback.suggestion) > 0
