"""Tests for impact score validation logic."""
from eko.analysis_v2.effects.impact.validation import ImpactScoreValidator


class TestImpactScoreValidator:
    """Test the impact score validation logic."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = ImpactScoreValidator()
    
    def test_score_range_validation_negative_scores(self):
        """Test that negative scores are capped to 0.0."""
        harm_scores = {
            'humans': {
                'score': -0.1,  # Negative score
                'confidence': 'high',
                'calculation_details': {}
            },
            'animals': {
                'score': 0.5,
                'confidence': 'medium',
                'calculation_details': {}
            },
            'environment': {
                'score': 0.3,
                'confidence': 'low',
                'calculation_details': {}
            }
        }
        
        benefit_scores = {
            'humans': {'score': 0.1, 'confidence': 'medium', 'calculation_details': {}},
            'animals': {'score': 0.1, 'confidence': 'medium', 'calculation_details': {}},
            'environment': {'score': 0.1, 'confidence': 'medium', 'calculation_details': {}}
        }
        
        # Validate scores
        capped_harm, capped_benefit, validation_details = self.validator.validate_and_cap_scores(
            harm_scores, benefit_scores
        )
        
        # Check that negative score was capped to 0.0
        assert capped_harm['humans']['score'] == 0.0
        assert validation_details['harm']['humans']['was_capped'] is True
        assert validation_details['harm']['humans']['original_score'] == -0.1
        assert validation_details['harm']['humans']['final_score'] == 0.0
        
        # Other scores should remain unchanged
        assert capped_harm['animals']['score'] == 0.5
        assert capped_harm['environment']['score'] == 0.3
    
    def test_score_range_validation_excessive_scores(self):
        """Test that scores above 1.0 are capped to 1.0."""
        harm_scores = {
            'humans': {
                'score': 0.6,
                'confidence': 'medium',
                'calculation_details': {}
            },
            'animals': {
                'score': 1.2,  # Above 1.0 limit
                'confidence': 'high',
                'calculation_details': {}
            },
            'environment': {
                'score': 0.3,
                'confidence': 'low',
                'calculation_details': {}
            }
        }
        
        benefit_scores = {
            'humans': {'score': 0.1, 'confidence': 'medium', 'calculation_details': {}},
            'animals': {'score': 1.5, 'confidence': 'high', 'calculation_details': {}},  # Above 1.0
            'environment': {'score': 0.1, 'confidence': 'medium', 'calculation_details': {}}
        }
        
        # Validate scores
        capped_harm, capped_benefit, validation_details = self.validator.validate_and_cap_scores(
            harm_scores, benefit_scores
        )
        
        # Check that excessive harm score was capped at 1.0
        assert capped_harm['animals']['score'] == 1.0
        assert validation_details['harm']['animals']['was_capped'] is True
        assert validation_details['harm']['animals']['original_score'] == 1.2
        assert validation_details['harm']['animals']['final_score'] == 1.0
        
        # Check that excessive benefit score was capped at 1.0
        assert capped_benefit['animals']['score'] == 1.0
        assert validation_details['benefit']['animals']['was_capped'] is True
        assert validation_details['benefit']['animals']['original_score'] == 1.5
        assert validation_details['benefit']['animals']['final_score'] == 1.0
        
        # Other scores should remain unchanged
        assert capped_harm['humans']['score'] == 0.6
        assert capped_harm['environment']['score'] == 0.3
    
    def test_no_capping_needed_for_valid_scores(self):
        """Test that valid scores (0.0-1.0) remain unchanged."""
        harm_scores = {
            'humans': {
                'score': 0.8,
                'confidence': 'high',
                'calculation_details': {}
            },
            'animals': {
                'score': 0.6,
                'confidence': 'medium',
                'calculation_details': {}
            },
            'environment': {
                'score': 0.4,
                'confidence': 'low',
                'calculation_details': {}
            }
        }
        
        benefit_scores = {
            'humans': {'score': 0.2, 'confidence': 'medium', 'calculation_details': {}},
            'animals': {'score': 0.3, 'confidence': 'high', 'calculation_details': {}},
            'environment': {'score': 0.1, 'confidence': 'medium', 'calculation_details': {}}
        }
        
        # Validate scores
        capped_harm, capped_benefit, validation_details = self.validator.validate_and_cap_scores(
            harm_scores, benefit_scores
        )
        
        # All scores should remain unchanged
        assert capped_harm['humans']['score'] == 0.8
        assert capped_harm['animals']['score'] == 0.6
        assert capped_harm['environment']['score'] == 0.4
        
        assert capped_benefit['humans']['score'] == 0.2
        assert capped_benefit['animals']['score'] == 0.3
        assert capped_benefit['environment']['score'] == 0.1
        
        # No capping should have occurred
        for dimension in ['humans', 'animals', 'environment']:
            assert validation_details['harm'][dimension]['was_capped'] is False
            assert validation_details['benefit'][dimension]['was_capped'] is False
    
    def test_edge_case_boundary_scores(self):
        """Test boundary values (exactly 0.0 and 1.0)."""
        harm_scores = {
            'humans': {
                'score': 0.0,  # Exact boundary
                'confidence': 'medium',
                'calculation_details': {}
            },
            'animals': {
                'score': 1.0,  # Exact boundary
                'confidence': 'high',
                'calculation_details': {}
            },
            'environment': {
                'score': 0.5,
                'confidence': 'medium',
                'calculation_details': {}
            }
        }
        
        benefit_scores = {
            'humans': {'score': 1.0, 'confidence': 'high', 'calculation_details': {}},  # Exact boundary
            'animals': {'score': 0.0, 'confidence': 'low', 'calculation_details': {}},  # Exact boundary
            'environment': {'score': 0.5, 'confidence': 'medium', 'calculation_details': {}}
        }
        
        # Validate scores
        capped_harm, capped_benefit, validation_details = self.validator.validate_and_cap_scores(
            harm_scores, benefit_scores
        )
        
        # Boundary values should remain unchanged
        assert capped_harm['humans']['score'] == 0.0
        assert capped_harm['animals']['score'] == 1.0
        assert capped_benefit['humans']['score'] == 1.0
        assert capped_benefit['animals']['score'] == 0.0
        
        # No capping should have occurred for boundary values
        for dimension in ['humans', 'animals', 'environment']:
            assert validation_details['harm'][dimension]['was_capped'] is False
            assert validation_details['benefit'][dimension]['was_capped'] is False
    
    def test_mixed_validation_scenarios(self):
        """Test a mix of valid, negative, and excessive scores."""
        harm_scores = {
            'humans': {
                'score': -0.2,  # Negative
                'confidence': 'low',
                'calculation_details': {}
            },
            'animals': {
                'score': 0.7,  # Valid
                'confidence': 'medium',
                'calculation_details': {}
            },
            'environment': {
                'score': 1.3,  # Excessive
                'confidence': 'high',
                'calculation_details': {}
            }
        }
        
        benefit_scores = {
            'humans': {'score': 0.5, 'confidence': 'medium', 'calculation_details': {}},  # Valid
            'animals': {'score': -0.1, 'confidence': 'low', 'calculation_details': {}},  # Negative
            'environment': {'score': 1.1, 'confidence': 'high', 'calculation_details': {}}  # Excessive
        }
        
        # Validate scores
        capped_harm, capped_benefit, validation_details = self.validator.validate_and_cap_scores(
            harm_scores, benefit_scores
        )
        
        # Check harm scores
        assert capped_harm['humans']['score'] == 0.0  # Capped from negative
        assert capped_harm['animals']['score'] == 0.7  # Unchanged (valid)
        assert capped_harm['environment']['score'] == 1.0  # Capped from excessive
        
        # Check benefit scores
        assert capped_benefit['humans']['score'] == 0.5  # Unchanged (valid)
        assert capped_benefit['animals']['score'] == 0.0  # Capped from negative
        assert capped_benefit['environment']['score'] == 1.0  # Capped from excessive
        
        # Check validation details
        assert validation_details['harm']['humans']['was_capped'] is True
        assert validation_details['harm']['animals']['was_capped'] is False
        assert validation_details['harm']['environment']['was_capped'] is True
        
        assert validation_details['benefit']['humans']['was_capped'] is False
        assert validation_details['benefit']['animals']['was_capped'] is True
        assert validation_details['benefit']['environment']['was_capped'] is True