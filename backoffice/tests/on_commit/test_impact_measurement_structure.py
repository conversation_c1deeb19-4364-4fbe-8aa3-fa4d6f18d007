"""
Unit tests for impact measurement structure and calculation details.

This module tests that the EventImpactMeasurement structure properly
supports calculation details storage.
"""
import pytest
from datetime import datetime
from typing import Dict, Any

from eko.analysis_v2.effects.impact.models.assessment import (
    DimensionAssessment, HarmImpactAssessment, BenefitImpactAssessment,
    AnimalHarmImpact, HumanHarmImpact, EnvironmentHarmImpact,
    AnimalBenefitImpact, HumanBenefitImpact, EnvironmentBenefitImpact
)
from eko.analysis_v2.effects.impact.models.base import TemporalBreakdown
from eko.analysis_v2.effects.impact.models.scale_factors import ScaleFactors
from eko.analysis_v2.effects.impact.models.measurement import EventImpactMeasurement
from eko.analysis_v2.effects.impact.service import ImpactMeasurementService

class TestImpactMeasurementStructure:
    """Test the structure and functionality of impact measurement."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.service = ImpactMeasurementService()
    
    def create_test_dimension_assessment(self, score: float = 0.5) -> DimensionAssessment:
        """Create a test DimensionAssessment."""
        assessment = DimensionAssessment(
            reasoning="Test reasoning",
            temporal_breakdown=TemporalBreakdown(
                immediate="Immediate impact", medium_term="Medium term impact", long_term="Long term impact"
            ),
            scale_factors=ScaleFactors(
                proximity_to_tipping_point="Moderate concern",
                # Reversibility questions
                is_irreversible=False,
                takes_centuries_to_reverse=False,
                requires_significant_effort_to_reverse=True,
                reversible_within_years=True,
                reversible_within_months=False,
                reversible_within_weeks=False,
                fully_reversible_immediately=False,
                # Scale of impact questions
                affects_single_individual=False,
                affects_multiple_individuals=True,
                affects_many_beings=False,
                affects_large_population=False,
                affects_country_ecosystem=False,
                affects_species_biome=False,
                affects_all_global=False,
                # Directness questions
                third_party_action=False,
                entity_had_minor_influence=False,
                entity_influenced_outcome=False,
                entity_action_led_to_impact=True,
                entity_decision_caused_impact=False,
                direct_action_by_entity=False,
                entity_sole_direct_cause=False,
                # Authenticity questions
                pure_marketing_greenwashing=False,
                mostly_regulatory_compliance=False,
                primarily_business_driven=False,
                balanced_genuine_business=False,
                mostly_genuine_some_business=True,
                genuine_commitment=False,
                purely_altruistic=False,
                # Deliberateness questions
                completely_accidental=False,
                foreseeable_not_intended=False,
                knew_consequences_acted_anyway=False,
                intended_action_predictable_outcome=True,
                planned_with_awareness=False,
                fully_intentional=False,
                deliberately_planned_for_impact=False,
                # Contribution questions
                not_contributing=False,
                very_minor_contributor=False,
                minor_contributor=False,
                significant_contributor=True,
                major_contributor=False,
                dominant_contributor=False,
                sole_contributor=False,
                duration="medium",
                # Degree questions
                no_impact=False,
                minor_impact=False,
                moderate_impact=True,
                major_impact=False,
                severe_impact=False,
                extreme_impact=False,
                existential_impact=False,
                # Probability questions
                will_not_happen=False,
                very_unlikely=False,
                unlikely=False,
                even_chance=False,
                highly_likely=False,
                virtually_certain=False,
                has_already_happened=True,
            ),
        )
        # Set score and confidence after creation, as they would be set by the calculator
        assessment.score = score
        assessment.confidence = "medium"
        return assessment
    
    def test_event_impact_measurement_with_calculation_details(self):
        """Test that EventImpactMeasurement can store calculation details."""
        # Create test assessments
        harm_assessment = HarmImpactAssessment(
            animals=AnimalHarmImpact(assessment=self.create_test_dimension_assessment(0.7)),
            humans=HumanHarmImpact(assessment=self.create_test_dimension_assessment(0.6)),
            environment=EnvironmentHarmImpact(assessment=self.create_test_dimension_assessment(0.8))
        )
        
        benefit_assessment = BenefitImpactAssessment(
            animals=AnimalBenefitImpact(assessment=self.create_test_dimension_assessment(0.2)),
            humans=HumanBenefitImpact(assessment=self.create_test_dimension_assessment(0.3)),
            environment=EnvironmentBenefitImpact(assessment=self.create_test_dimension_assessment(0.1))
        )
        
        # Create calculation details
        calculation_details = {
            "harm": {
                "animals": {
                    "base_severity": 60,
                    "triggered_indicators": [
                        {"indicator": "habitat_loss", "weight": 0.8},
                        {"indicator": "species_decline", "weight": 0.6}
                    ],
                    "scale_adjustment": 1.17,
                    "has_climate_harm": False,
                    "final_score": 0.7,
                    "confidence_factors": ["Field observations", "Expert assessment"],
                    "used_default_severity": False
                },
                "humans": {
                    "base_severity": 50,
                    "triggered_indicators": [
                        {"indicator": "economic_loss", "weight": 0.7}
                    ],
                    "scale_adjustment": 1.2,
                    "has_climate_harm": False,
                    "final_score": 0.6,
                    "confidence_factors": ["Economic data available"],
                    "used_default_severity": False
                },
                "environment": {
                    "base_severity": 70,
                    "triggered_indicators": [
                        {"indicator": "pollution", "weight": 0.9},
                        {"indicator": "ecosystem_damage", "weight": 0.8}
                    ],
                    "scale_adjustment": 1.14,
                    "has_climate_harm": True,
                    "final_score": 0.8,
                    "confidence_factors": ["Environmental monitoring data"],
                    "used_default_severity": False
                }
            },
            "benefit": {
                "animals": {
                    "base_benefit": 20,
                    "triggered_indicators": [
                        {"indicator": "habitat_restoration", "weight": 0.4}
                    ],
                    "scale_adjustment": 1.0,
                    "has_climate_benefit": False,
                    "final_score": 0.2,
                    "confidence_factors": ["Limited scope of restoration"],
                    "used_default_benefit": False
                },
                "humans": {
                    "base_benefit": 25,
                    "triggered_indicators": [
                        {"indicator": "job_creation", "weight": 0.5}
                    ],
                    "scale_adjustment": 1.2,
                    "has_climate_benefit": False,
                    "final_score": 0.3,
                    "confidence_factors": ["Employment data available"],
                    "used_default_benefit": False
                },
                "environment": {
                    "base_benefit": 10,
                    "triggered_indicators": [],
                    "scale_adjustment": 1.0,
                    "has_climate_benefit": False,
                    "final_score": 0.1,
                    "confidence_factors": ["Minimal environmental benefit"],
                    "used_default_benefit": False
                }
            }
        }
        
        # Create measurement with calculation details
        measurement = EventImpactMeasurement(
            event_id="test-event-001",
            event_summary="Test environmental impact event",
            event_description="A test event for validating calculation details storage",
            harm_assessment=harm_assessment,
            benefit_assessment=benefit_assessment,
            key_uncertainties=["Long-term effects unknown"],
            ethical_considerations=["Impact on vulnerable populations"],
            assessed_at=datetime.utcnow().isoformat() + "Z",
            model_used="test-model",
            prompt_version="1.0",
            calculation_details=calculation_details
        )
        
        # Verify the measurement was created successfully
        assert measurement.event_id == "test-event-001"
        assert measurement.calculation_details is not None
        
        # Verify calculation details structure
        assert "harm" in measurement.calculation_details
        assert "benefit" in measurement.calculation_details
        
        # Verify harm calculations
        harm_calcs = measurement.calculation_details["harm"]
        assert "animals" in harm_calcs
        assert harm_calcs["animals"]["base_severity"] == 60
        assert harm_calcs["animals"]["final_score"] == 0.7
        assert len(harm_calcs["animals"]["triggered_indicators"]) == 2
        
        # Verify benefit calculations
        benefit_calcs = measurement.calculation_details["benefit"]
        assert "environment" in benefit_calcs
        assert benefit_calcs["environment"]["base_benefit"] == 10
        assert benefit_calcs["environment"]["final_score"] == 0.1
    
    def test_measurement_serialization_with_calculation_details(self):
        """Test that measurements with calculation details can be serialized."""
        # Create simple dimension assessments
        dimension = DimensionAssessment(
            reasoning="Test reasoning",
            temporal_breakdown=TemporalBreakdown(
                immediate="Immediate",
                medium_term="Medium",
                long_term="Long"
            ),
            scale_factors=ScaleFactors(
                proximity_to_tipping_point="Low concern",
                # Reversibility questions
                is_irreversible=False,
                takes_centuries_to_reverse=False,
                requires_significant_effort_to_reverse=False,
                reversible_within_years=False,
                reversible_within_months=True,
                reversible_within_weeks=False,
                fully_reversible_immediately=False,
                # Scale of impact questions
                affects_single_individual=False,
                affects_multiple_individuals=False,
                affects_many_beings=False,
                affects_large_population=False,
                affects_country_ecosystem=False,
                affects_species_biome=True,
                affects_all_global=False,
                # Directness questions
                third_party_action=False,
                entity_had_minor_influence=False,
                entity_influenced_outcome=True,
                entity_action_led_to_impact=False,
                entity_decision_caused_impact=False,
                direct_action_by_entity=False,
                entity_sole_direct_cause=False,
                # Authenticity questions
                pure_marketing_greenwashing=False,
                mostly_regulatory_compliance=False,
                primarily_business_driven=False,
                balanced_genuine_business=False,
                mostly_genuine_some_business=True,
                genuine_commitment=False,
                purely_altruistic=False,
                # Deliberateness questions
                completely_accidental=False,
                foreseeable_not_intended=True,
                knew_consequences_acted_anyway=False,
                intended_action_predictable_outcome=False,
                planned_with_awareness=False,
                fully_intentional=False,
                deliberately_planned_for_impact=False,
                # Contribution questions
                not_contributing=False,
                very_minor_contributor=False,
                minor_contributor=True,
                significant_contributor=False,
                major_contributor=False,
                dominant_contributor=False,
                sole_contributor=False,
                duration="short",
                # Degree questions
                no_impact=False,
                minor_impact=True,
                moderate_impact=False,
                major_impact=False,
                severe_impact=False,
                extreme_impact=False,
                existential_impact=False,
                # Probability questions
                will_not_happen=False,
                very_unlikely=False,
                unlikely=False,
                even_chance=False,
                highly_likely=False,
                virtually_certain=False,
                has_already_happened=True,
            )
        )
        
        # Create measurement
        measurement = EventImpactMeasurement(
            event_id="test-002",
            event_summary="Serialization test",
            event_description="Testing serialization",
            harm_assessment=HarmImpactAssessment(
                animals=AnimalHarmImpact(assessment=dimension),
                humans=HumanHarmImpact(assessment=dimension),
                environment=EnvironmentHarmImpact(assessment=dimension)
            ),
            benefit_assessment=BenefitImpactAssessment(
                animals=AnimalBenefitImpact(assessment=dimension),
                humans=HumanBenefitImpact(assessment=dimension),
                environment=EnvironmentBenefitImpact(assessment=dimension)
            ),
            key_uncertainties=[],
            ethical_considerations=[],
            assessed_at=datetime.utcnow().isoformat() + "Z",
            model_used="test-model",
            prompt_version="1.0",
            calculation_details={
                "harm": {
                    "animals": {
                        "base_severity": 50,
                        "final_score": 0.5,
                        "triggered_indicators": []
                    }
                }
            }
        )
        
        # Verify the measurement has calculation details
        assert measurement.calculation_details is not None
        assert measurement.event_id == "test-002"
    
    def test_aggregate_scores_calculation(self):
        """Test that aggregate scores are properly calculated."""
        # Create measurement with known scores
        harm_assessment = HarmImpactAssessment(
            animals=AnimalHarmImpact(assessment=self.create_test_dimension_assessment(0.8)),
            humans=HumanHarmImpact(assessment=self.create_test_dimension_assessment(0.6)),
            environment=EnvironmentHarmImpact(assessment=self.create_test_dimension_assessment(0.9))
        )
        
        benefit_assessment = BenefitImpactAssessment(
            animals=AnimalBenefitImpact(assessment=self.create_test_dimension_assessment(0.1)),
            humans=HumanBenefitImpact(assessment=self.create_test_dimension_assessment(0.2)),
            environment=EnvironmentBenefitImpact(assessment=self.create_test_dimension_assessment(0.15))
        )
        
        measurement = EventImpactMeasurement(
            event_id="test-003",
            event_summary="Score calculation test",
            event_description="Testing aggregate score calculations",
            harm_assessment=harm_assessment,
            benefit_assessment=benefit_assessment,
            key_uncertainties=[],
            ethical_considerations=[],
            assessed_at=datetime.utcnow().isoformat() + "Z",
            model_used="test-model",
            prompt_version="1.0"
        )
        
        # Verify aggregate scores
        expected_harm = (0.8 + 0.6 + 0.9) / 3.0
        expected_benefit = (0.1 + 0.2 + 0.15) / 3.0
        
        assert abs(measurement.harm_score - expected_harm) < 0.001
        assert abs(measurement.benefit_score - expected_benefit) < 0.001
        
        # Net impact should be calculated according to the formula in __init__
        # When harm > benefit: avg(benefits) - max(harms)
        assert measurement.net_impact_score < 0  # Net negative impact
    
    def test_empty_calculation_details(self):
        """Test that measurements work without calculation details."""
        measurement = EventImpactMeasurement(
            event_id="test-004",
            event_summary="No calculation details",
            event_description="Testing without calculation details",
            harm_assessment=HarmImpactAssessment(
                animals=AnimalHarmImpact(assessment=self.create_test_dimension_assessment(0.3)),
                humans=HumanHarmImpact(assessment=self.create_test_dimension_assessment(0.3)),
                environment=EnvironmentHarmImpact(assessment=self.create_test_dimension_assessment(0.3))
            ),
            benefit_assessment=BenefitImpactAssessment(
                animals=AnimalBenefitImpact(assessment=self.create_test_dimension_assessment(0.4)),
                humans=HumanBenefitImpact(assessment=self.create_test_dimension_assessment(0.4)),
                environment=EnvironmentBenefitImpact(assessment=self.create_test_dimension_assessment(0.4))
            ),
            key_uncertainties=[],
            ethical_considerations=[],
            assessed_at=datetime.utcnow().isoformat() + "Z",
            model_used="test-model",
            prompt_version="1.0"
            # No calculation_details provided
        )
        
        # Should work fine without calculation details
        assert measurement.calculation_details is None
        assert measurement.harm_score > 0
        assert measurement.benefit_score > 0
    
    def test_scale_factors_in_dimension_assessment(self):
        """Test that ScaleFactors are properly included in DimensionAssessment."""
        dimension = DimensionAssessment(
            reasoning="Test with scale factors",
            temporal_breakdown=TemporalBreakdown(
                immediate="Immediate",
                medium_term="Medium",
                long_term="Long"
            ),
            scale_factors=ScaleFactors(
                proximity_to_tipping_point="Critical concern",
                # Reversibility questions - nearly irreversible
                is_irreversible=True,
                takes_centuries_to_reverse=False,
                requires_significant_effort_to_reverse=False,
                reversible_within_years=False,
                reversible_within_months=False,
                reversible_within_weeks=False,
                fully_reversible_immediately=False,
                # Scale of impact questions - large scale
                affects_single_individual=False,
                affects_multiple_individuals=False,
                affects_many_beings=False,
                affects_large_population=False,
                affects_country_ecosystem=True,
                affects_species_biome=False,
                affects_all_global=False,
                # Directness questions - direct action
                third_party_action=False,
                entity_had_minor_influence=False,
                entity_influenced_outcome=False,
                entity_action_led_to_impact=False,
                entity_decision_caused_impact=False,
                direct_action_by_entity=True,
                entity_sole_direct_cause=False,
                # Authenticity questions - authentic
                pure_marketing_greenwashing=False,
                mostly_regulatory_compliance=False,
                primarily_business_driven=False,
                balanced_genuine_business=False,
                mostly_genuine_some_business=False,
                genuine_commitment=True,
                purely_altruistic=False,
                # Deliberateness questions - highly deliberate
                completely_accidental=False,
                foreseeable_not_intended=False,
                knew_consequences_acted_anyway=False,
                intended_action_predictable_outcome=False,
                planned_with_awareness=True,
                fully_intentional=False,
                deliberately_planned_for_impact=False,
                # Contribution questions - major contributor
                not_contributing=False,
                very_minor_contributor=False,
                minor_contributor=False,
                significant_contributor=False,
                major_contributor=True,
                dominant_contributor=False,
                sole_contributor=False,
                duration="long",
                # Degree questions - severe damage
                no_impact=False,
                minor_impact=False,
                moderate_impact=False,
                major_impact=False,
                severe_impact=True,
                extreme_impact=False,
                existential_impact=False,
                # Probability questions - already happened
                will_not_happen=False,
                very_unlikely=False,
                unlikely=False,
                even_chance=False,
                highly_likely=False,
                virtually_certain=False,
                has_already_happened=True,
            )
        )
        # Set score and confidence after creation
        dimension.score = 0.95
        dimension.confidence = "high"
        
        # Verify scale factors
        assert dimension.scale_factors.proximity_to_tipping_point == "Critical concern"
        assert dimension.scale_factors.is_irreversible == True
        assert dimension.scale_factors.affects_country_ecosystem == True
        assert dimension.scale_factors.direct_action_by_entity == True
        assert dimension.scale_factors.severe_impact == True
