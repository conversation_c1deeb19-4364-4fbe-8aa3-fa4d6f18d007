"""
Tests for score synchronization functionality.

This module tests the score calculation and synchronization to xfer_score table,
ensuring that the fail-fast behavior works correctly and scores are properly calculated.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from psycopg import Connection
from datetime import datetime

from eko.score.score_flag import UnifiedScoreSync
from eko.models.xfer.xfer_score import XferScoreModel


class TestUnifiedScoreSync:
    """Test the UnifiedScoreSync class for proper score calculation and error handling."""

    def test_sync_score_to_xfer_v2_success(self):
        """Test successful score synchronization."""
        # Mock database connection and cursor
        mock_conn = Mock(spec=Connection)
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.__enter__ = Mock(return_value=mock_cursor)
        mock_cursor.__exit__ = Mock(return_value=None)
        
        # Mock entities with flags
        mock_cursor.fetchall.return_value = [
            (1, "TEST_ENTITY"),  # entity_id, entity_xid
        ]
        
        # Mock effect flags data - need to account for entities query, red flags query, and green flags query
        mock_cursor.fetchall.side_effect = [
            [(1, "TEST_ENTITY")],  # entities
            [(75.5, 2, "Red Test Flag")],  # red flags data
            [(60.0, 1, "Green Test Flag")]  # green flags data
        ]
        
        # Mock customer database connection
        with patch('eko.score.score_flag.get_cus_conn') as mock_get_cus_conn:
            mock_cus_conn = Mock()
            mock_cus_cursor = Mock()
            mock_get_cus_conn.return_value.__enter__.return_value = mock_cus_conn
            mock_cus_conn.cursor.return_value = mock_cus_cursor
            mock_cus_cursor.__enter__ = Mock(return_value=mock_cus_cursor)
            mock_cus_cursor.__exit__ = Mock(return_value=None)
            
            # Execute the sync
            result = UnifiedScoreSync.sync_score_to_xfer_v2(mock_conn, 123)
            
            # Verify results
            assert result == ["TEST_ENTITY"]
            assert mock_cus_cursor.execute.called
            assert mock_cus_conn.commit.called

    def test_sync_score_to_xfer_v2_database_error_fails_fast(self):
        """Test that database errors cause immediate failure (fail-fast behavior)."""
        # Mock database connection that raises an error
        mock_conn = Mock(spec=Connection)
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.__enter__ = Mock(return_value=mock_cursor)
        mock_cursor.__exit__ = Mock(return_value=None)
        
        # Mock entities with flags
        mock_cursor.fetchall.return_value = [
            (1, "TEST_ENTITY"),
        ]
        
        # Mock effect flags data that raises an error
        mock_cursor.fetchall.side_effect = [
            [(1, "TEST_ENTITY")],  # entities
            Exception("Database connection failed")  # red flags query fails
        ]
        
        # Execute and verify it raises the exception
        with pytest.raises(Exception) as exc_info:
            UnifiedScoreSync.sync_score_to_xfer_v2(mock_conn, 123)
        
        assert "Database connection failed" in str(exc_info.value)

    def test_sync_score_to_xfer_v2_customer_db_error_fails_fast(self):
        """Test that customer database errors cause immediate failure."""
        # Mock analytics database connection
        mock_conn = Mock(spec=Connection)
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.__enter__ = Mock(return_value=mock_cursor)
        mock_cursor.__exit__ = Mock(return_value=None)
        
        # Mock successful analytics DB queries
        mock_cursor.fetchall.side_effect = [
            [(1, "TEST_ENTITY")],  # entities
            [(75.5, 2, "Red Test Flag")],  # red flags data
            [(60.0, 1, "Green Test Flag")]  # green flags data
        ]
        
        # Mock customer database connection that fails
        with patch('eko.score.score_flag.get_cus_conn') as mock_get_cus_conn:
            mock_get_cus_conn.side_effect = Exception("Customer DB connection failed")
            
            # Execute and verify it raises the exception
            with pytest.raises(Exception) as exc_info:
                UnifiedScoreSync.sync_score_to_xfer_v2(mock_conn, 123)
            
            assert "Customer DB connection failed" in str(exc_info.value)

    def test_sync_score_to_xfer_v2_no_entities_returns_empty(self):
        """Test that when no entities have flags, empty list is returned."""
        # Mock database connection
        mock_conn = Mock(spec=Connection)
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_cursor.__enter__ = Mock(return_value=mock_cursor)
        mock_cursor.__exit__ = Mock(return_value=None)
        
        # Mock no entities with flags
        mock_cursor.fetchall.return_value = []
        
        # Execute the sync
        result = UnifiedScoreSync.sync_score_to_xfer_v2(mock_conn, 123)
        
        # Verify empty result
        assert result == []


class TestUnifiedScoreSyncIntegration:
    """Integration tests for score sync functionality."""

    @patch('eko.score.score_flag.get_cus_conn')
    def test_xfer_score_model_serialization(self, mock_get_cus_conn):
        """Test that XferScoreModel is properly serialized to JSON."""
        # Create a sample XferScoreModel
        score_model = XferScoreModel(
            entity_xid="TEST_ENTITY",
            score=75,
            rating_text="Good",
            minor_major_text="Minor",
            red_flags_count=2,
            green_flags_count=5,
            red_flags_score=120.5,
            green_flags_score=250.0,
            average_red=60.25,
            average_green=50.0,
            median_red=65.0,
            median_green=45.0,
            created_at=datetime.now().isoformat()
        )
        
        # Verify it can be serialized to JSON
        json_data = score_model.model_dump_json()
        assert isinstance(json_data, str)
        assert "TEST_ENTITY" in json_data
        assert "75" in json_data

    def test_rating_text_calculation(self):
        """Test the rating text calculation logic."""
        assert UnifiedScoreSync._rating_text(90) == "Great"
        assert UnifiedScoreSync._rating_text(80) == "Very Good"
        assert UnifiedScoreSync._rating_text(65) == "Good"
        assert UnifiedScoreSync._rating_text(45) == "Poor"
        assert UnifiedScoreSync._rating_text(25) == "Very Poor"

    def test_severity_label_calculation(self):
        """Test the severity label calculation logic."""
        assert UnifiedScoreSync._severity_label(30) == "Very Serious"  # 70% risk
        assert UnifiedScoreSync._severity_label(50) == "Serious"       # 50% risk
        assert UnifiedScoreSync._severity_label(75) == "Major"         # 25% risk
        assert UnifiedScoreSync._severity_label(85) == "Minor"         # 15% risk
        assert UnifiedScoreSync._severity_label(95) == "Very Minor"    # 5% risk
        assert UnifiedScoreSync._severity_label(96) == "Trivial"       # 4% risk
        assert UnifiedScoreSync._severity_label(99) == "Trivial"       # 1% risk
        
    def test_calculate_unified_score(self):
        """Test the unified score calculation with the new formula."""
        # Test with multiple red flags - should get top 10%
        red_flag_rows = [
            (90.0, 1, "High Impact Flag"),
            (80.0, 1, "Medium High Flag"), 
            (70.0, 1, "Medium Flag"),
            (60.0, 1, "Lower Medium Flag"),
            (50.0, 1, "Lower Flag"),
            (40.0, 1, "Low Flag"),
            (30.0, 1, "Very Low Flag"),
            (20.0, 1, "Minimal Flag"),
            (10.0, 1, "Tiny Flag"),
            (5.0, 1, "Minimal Flag 2")
        ]
        
        # Top 10% = 1 flag (90.0), so score should be 100 - 90 = 10
        result = UnifiedScoreSync._calculate_unified_score(red_flag_rows)
        assert result == 10
        
        # Test with no red flags - should return 100
        result = UnifiedScoreSync._calculate_unified_score([])
        assert result == 100
        
        # Test with single red flag
        result = UnifiedScoreSync._calculate_unified_score([(75.0, 1, "Single Flag")])
        assert result == 25  # 100 - 75
        
        # Test with two flags - top 10% is still 1 flag (the highest)
        result = UnifiedScoreSync._calculate_unified_score([(80.0, 1, "High"), (60.0, 1, "Low")])
        assert result == 20  # 100 - 80
