"""
Unit tests for the DocumentProcessor classes in eko.scrape.reports.
"""
import os
import unittest
from unittest.mock import patch, MagicMock, ANY, mock_open
from typing import List, Literal, cast

import datetime
from bs4 import BeautifulSoup

from eko.models.common import ResearchType
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.scrape.reports import (
    create_document_processor
)
from eko.scrape.pdf_processor import PDFProcessor
from eko.scrape.webpage_processor import WebpageProcessor
from eko.scrape.doc_processor import DocumentProcessor


class TestDocumentProcessor(unittest.TestCase):
    """Tests for the DocumentProcessor base class and its implementations."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock URL
        self.url = "https://example.com"
        self.pdf_url = "https://example.com/test.pdf"

        # Create mock research types - cast to List[ResearchType] to satisfy type checker
        self.research_types = cast(List[ResearchType], ["journalism"])  # ResearchType is a Literal type, not an Enum

        # Create a mock entity name
        self.name = "Test Entity"

        # Create a mock expanded entity
        self.expanded_entity = MagicMock(spec=VirtualEntityExpandedModel)
        self.expanded_entity.title = "Test Entity"
        self.expanded_entity.for_referencing_in_prompts.return_value = "Test Entity"

    @patch('eko.scrape.reports.os.makedirs')
    def test_prepare_file_path_webpage(self, mock_makedirs):
        """Test the _prepare_file_path method for WebpageProcessor."""
        processor = WebpageProcessor(self.url, self.research_types, self.name)
        processor._prepare_file_path()

        # Check that the file path was set correctly
        self.assertIsNotNone(processor.file_path)
        self.assertIsNotNone(processor.filename)

        # Check that the directory was created
        mock_makedirs.assert_called_once_with(ANY, exist_ok=True)

    @patch('eko.scrape.reports.os.makedirs')
    def test_prepare_file_path_pdf(self, mock_makedirs):
        """Test the _prepare_file_path method for PDFProcessor."""
        processor = PDFProcessor(self.pdf_url, self.research_types, self.name)
        processor._prepare_file_path()

        # Check that the file path was set correctly
        self.assertIsNotNone(processor.file_path)
        self.assertIsNotNone(processor.filename)

        # Check that the directory was created
        mock_makedirs.assert_called_once_with(ANY, exist_ok=True)

    def test_download_document_webpage(self):
        """Test the _download_document method for WebpageProcessor."""
        # Create a processor
        processor = WebpageProcessor(self.url, self.research_types, self.name)
        processor._prepare_file_path()

        # Mock the database connection
        mock_conn = MagicMock()

        # Test when file doesn't exist
        with (
            patch("os.path.exists", return_value=False),
            patch("eko.scrape.webpage_processor.download", return_value="<html><body>Test</body></html>"),
            patch("eko.util.hash.sha256_hash_file", return_value="test_hash"),
            patch("builtins.open", mock_open()),
            patch("eko.db.get_bo_conn", return_value=mock_conn),
        ):

            # Call the method
            result = processor._download_document()

            # Manually set the file_hash to match our mock
            processor.file_hash = "test_hash"

            # Check result
            self.assertTrue(result)
            self.assertEqual(processor.content, "<html><body>Test</body></html>")
            self.assertEqual(processor.file_hash, "test_hash")

        # Test when file already exists
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', mock_open(read_data="<html><body>Test</body></html>")), \
             patch('eko.util.hash.sha256_hash_file', return_value="test_hash"), \
             patch('eko.db.get_bo_conn', return_value=mock_conn):

            # Create a new processor
            processor = WebpageProcessor(self.url, self.research_types, self.name)
            processor._prepare_file_path()

            # Call the method
            result = processor._download_document()

            # Manually set the file_hash to match our mock
            processor.file_hash = "test_hash"

            # Check result
            self.assertTrue(result)
            self.assertEqual(processor.content, "<html><body>Test</body></html>")
            self.assertEqual(processor.file_hash, "test_hash")

    def test_download_document_pdf(self):
        """Test the _download_document method for PDFProcessor."""
        # Create a processor
        processor = PDFProcessor(self.pdf_url, self.research_types, self.name)
        processor._prepare_file_path()

        # Mock the database connection
        mock_conn = MagicMock()

        # Create a mock response with the correct Content-Type header
        mock_response = MagicMock()
        mock_response.headers = {'Content-Type': 'application/pdf'}
        mock_response.content = b'PDF content'

        # Test when file doesn't exist
        with patch('os.path.exists', return_value=False), \
             patch('eko.scrape.pdf_processor.download_pdf', return_value=mock_response), \
             patch('eko.util.hash.sha256_hash_file', return_value="test_hash"), \
             patch('eko.db.get_bo_conn', return_value=mock_conn):

            # Call the method
            result = processor._download_document()

            # Manually set the file_hash to match our mock
            processor.file_hash = "test_hash"

            # Check result
            self.assertTrue(result)
            self.assertEqual(processor.file_hash, "test_hash")

        # Test when file already exists
        with patch('os.path.exists', return_value=True), \
             patch('eko.util.hash.sha256_hash_file', return_value="test_hash"), \
             patch('eko.db.get_bo_conn', return_value=mock_conn):

            # Create a new processor
            processor = PDFProcessor(self.pdf_url, self.research_types, self.name)
            processor._prepare_file_path()

            # Call the method
            result = processor._download_document()

            # Manually set the file_hash to match our mock
            processor.file_hash = "test_hash"

            # Check result
            self.assertTrue(result)
            self.assertEqual(processor.file_hash, "test_hash")

    @patch('eko.scrape.reports.is_pdf')
    def test_create_document_processor(self, mock_is_pdf):
        """Test the create_document_processor factory function."""
        # Mock is_pdf to return False for HTML URL and True for PDF URL
        mock_is_pdf.side_effect = lambda url: url.endswith('.pdf')

        # Test with HTML URL
        processor = create_document_processor(self.url, self.research_types, self.name)
        self.assertIsInstance(processor, WebpageProcessor)

        # Test with PDF URL
        processor = create_document_processor(self.pdf_url, self.research_types, self.name)
        self.assertIsInstance(processor, PDFProcessor)

    def test_skip_document_not_related_to_expanded_entity(self):
        """Test that documents not related to an expanded virtual entity are skipped."""
        # Set up mock expanded entity
        expanded_entity = MagicMock(spec=VirtualEntityExpandedModel)
        expanded_entity.title = "Test Virtual Entity"
        expanded_entity.for_referencing_in_prompts.return_value = "company 'Test Virtual Entity' (described as A test company)"

        # Create a WebpageProcessor with the expanded entity
        processor = WebpageProcessor(
            url="https://example.com/test.html",
            research_types=cast(List[ResearchType], ["journalism"]),
            name=None,  # No name, only expanded entity
            expanded_entity=expanded_entity
        )

        # Mock the necessary methods to test _process_content
        processor.content = "<html><body>This document doesn't mention the entity</body></html>"
        processor.file_path = "/tmp/test.html"
        processor.file_hash = "test_hash"

        # Create a mock Goose article with enough text to pass the MIN_ARTICLE_LENGTH check
        mock_article = MagicMock()
        mock_article.title = "Test Article"
        # Create a long enough text to pass the minimum article length check
        mock_article.cleaned_text = "This is a test article" * 100  # Multiply to make it long enough
        mock_article.raw_html = "<html><body>This is a test article</body></html>" * 100
        mock_article.publish_date = None
        mock_article.publish_datetime_utc = None
        mock_article.authors = []
        mock_article.meta_description = "Test description"
        mock_article.meta_keywords = []

        # Mock the is_entity_mentioned method directly on the processor instance
        with patch.object(processor, 'is_entity_mentioned', return_value=False) as mock_is_entity_mentioned, \
             patch('eko.scrape.webpage_processor.Goose') as mock_goose, \
             patch('eko.scrape.webpage_processor.BeautifulSoup') as mock_bs, \
             patch('eko.scrape.webpage_processor.call_llm_boolean', return_value=True), \
             patch('eko.scrape.webpage_processor.langdetect.detect', return_value="en"), \
             patch('eko.scrape.webpage_processor.get_domain_info') as mock_domain_info:

            # Set up mock Goose to return our mock article
            mock_goose_instance = mock_goose.return_value
            mock_goose_instance.extract.return_value = mock_article

            # Set up mock BeautifulSoup
            mock_bs.return_value = MagicMock()

            # Set up mock domain info
            mock_domain_info.return_value = MagicMock(domain="example.com", owning_entity="Example Inc")

            # Call _process_content
            result = processor._process_content()

            # Verify that the document was skipped
            self.assertFalse(result)

            # Verify that is_entity_mentioned was called
            mock_is_entity_mentioned.assert_called_once()
            # Check that the article text was passed to is_entity_mentioned
            self.assertIn("This is a test article", mock_is_entity_mentioned.call_args[0][0])

            # Verify the log message
            # We can't easily check the log message, but we can check that the method returned False

    def test_process_document_related_to_expanded_entity(self):
        """Test that documents related to an expanded virtual entity are processed."""
        # Set up mock expanded entity
        expanded_entity = MagicMock(spec=VirtualEntityExpandedModel)
        expanded_entity.title = "Test Virtual Entity"
        expanded_entity.for_referencing_in_prompts.return_value = "company 'Test Virtual Entity' (described as A test company)"

        # Create a WebpageProcessor with the expanded entity
        processor = WebpageProcessor(
            url="https://example.com/test.html",
            research_types=cast(List[ResearchType], ["journalism"]),
            name=None,  # No name, only expanded entity
            expanded_entity=expanded_entity
        )

        # Mock the necessary methods to test _process_content
        processor.content = "<html><body>This document mentions the Test Virtual Entity</body></html>"
        processor.file_path = "/tmp/test.html"
        processor.file_hash = "test_hash"

        # Create a mock Goose article with enough text to pass the MIN_ARTICLE_LENGTH check
        mock_article = MagicMock()
        mock_article.title = "Test Article"
        # Create a long enough text to pass the minimum article length check
        mock_article.cleaned_text = "This is a test article about Test Virtual Entity" * 100  # Multiply to make it long enough
        mock_article.raw_html = "<html><body>This is a test article about Test Virtual Entity</body></html>" * 100
        mock_article.publish_date = None
        mock_article.publish_datetime_utc = None
        mock_article.authors = []
        mock_article.meta_description = "Test description"
        mock_article.meta_keywords = []

        # Mock the is_entity_mentioned method directly on the processor instance
        with patch.object(processor, 'is_entity_mentioned', return_value=True) as mock_is_entity_mentioned, \
             patch('eko.scrape.webpage_processor.Goose') as mock_goose, \
             patch('eko.scrape.webpage_processor.BeautifulSoup') as mock_bs, \
             patch('eko.scrape.webpage_processor.call_llm_boolean', return_value=True), \
             patch('eko.scrape.webpage_processor.langdetect.detect', return_value="en"), \
             patch('eko.scrape.webpage_processor.get_domain_info') as mock_domain_info:

            # Set up mock Goose to return our mock article
            mock_goose_instance = mock_goose.return_value
            mock_goose_instance.extract.return_value = mock_article

            # Set up mock BeautifulSoup
            mock_bs.return_value = MagicMock()

            # Set up mock domain info
            mock_domain_info.return_value = MagicMock(domain="example.com", owning_entity="Example Inc")

            # Call _process_content
            result = processor._process_content()

            # Verify that the document was processed
            self.assertTrue(result)

            # Verify that is_entity_mentioned was called
            mock_is_entity_mentioned.assert_called_once()
            # Check that the article text was passed to is_entity_mentioned
            self.assertIn("This is a test article about Test Virtual Entity", mock_is_entity_mentioned.call_args[0][0])


if __name__ == '__main__':
    unittest.main()
