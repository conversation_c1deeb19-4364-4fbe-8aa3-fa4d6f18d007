import unittest

from eko.nlp.clean import remove_references, remove_urls


class TestNLPClean(unittest.TestCase):
    full_text = """

<page document_id="sdgfinancialservices-d10af92afc7b1f5175df3e0dd1af2c79.pdf score="85" page_number="21">
**6. CONCLUSION**
THIMBY’s experience builds on and reinforces prior research pointing out areas of continued
growth for the green building industry in order to realize dramatic energy and water savings, in
line with the most recent IPCC reports and 2015 Paris Agreement goals. As THIMBY remains
a work in progress, additional research, replication and policy work is needed to a) demonstrate
longevity of positive energy performance metrics, b) scale up to tiny house communities, and
c) facilitate legal approval and siting opportunities for mobile, off-grid-capable tiny houses. The
demonstrated potential for high energy performance and associated emissions savings in the use
phase of this fossil fuel free home provides significant motivation for this future work. THIMBY
1.0 has already inspired two additional university student-led tiny house design-build projects.
The strategies deployed here for design-build-occupy integration and transparent energy
monitoring for occupants are transferable and can be scaled to larger green building projects.
Replication of all-electric energy systems and off-grid home water systems, in parallel with
safety evaluations and low-cost water quality testing, will go a long way towards advancing
decarbonized residential buildings and enabling the policy changes required to support such
living systems. Allowing off-grid tiny houses to exist and scale could also enhance the ability to
meet California’s increasingly stringent emissions reduction targets, Zero Net Energy building
(ZNE) mandates, and onsite solar requirements for new buildings while addressing affordable
housing challenges coexisting with climate-related threats. Responding to climate change and
mid-century emissions reductions targets requires scalability of proven green building types
in ways that maintain affordable housing availability and promote social sustainability. The
THIMBY project, an energy- and water-efficient home, advances this objective.

**ACKNOWLEDGEMENTS**
This research received significant support from several members of the THIMBY project team,
who contributed to the energy modeling, building design, and technology integration. These
individuals include Tom Webster, Imran Sheikh, and Kenny Gottlieb.
This research did not receive any specific grant from funding agencies in the public, commercial, or not-for-profit sectors.


**REFERENCES**
Alvarez, R. et al. (2018). Assessment of methane emissions from the US oil and gas supply chain. Science. Vol.
261 Iss. 6398 pp. 186–188
Blomgren, E., Bolliger, I., Ma, K., & Schmid, M. (2016). Home Energy Management System for Off-Grid Tiny
_[House. https://ecal.berkeley.edu/files/ce295/projects/S16/schmidmathias_5312024_68719679_THIMBY](https://ecal.berkeley.edu/files/ce295/projects/S16/schmidmathias_5312024_68719679_THIMBY_Final_Report.pdf)_
[_Final_Report.pdf](https://ecal.berkeley.edu/files/ce295/projects/S16/schmidmathias_5312024_68719679_THIMBY_Final_Report.pdf)

Buhayar, N. and Cannon, C. (2019). “How California Became America’s Housing Market Nightmare.” Bloomberg.

[https://www.bloomberg.com/graphics/2019-california-housing-crisis/](https://www.bloomberg.com/graphics/2019-california-housing-crisis/)

Arens, E. et al. (2013). Air movement as an efficient means towards occupant comfort. Prepared by the Center
for the Build Environment at the University of California, Berkeley for the State of California Air Resources
[Board. https://ww3.arb.ca.gov/research/apr/past/10-308.pdf](https://ww3.arb.ca.gov/research/apr/past/10-308.pdf)

California Energy Commission 2011—Title 24 standards
California Energy Commission 2018—Title 24 standards
California Energy Commission. (2018). Funding Opportunities for the Electric Program Investment Charge
[(EPIC) Program. https://www.energy.ca.gov/contracts/epic.html](https://www.energy.ca.gov/contracts/epic.html)

California Energy Commission. (2019). RESIDENTIAL ALTERNATIVE CALCULATION METHOD
REFERENCE MANUAL FOR THE 2019 BUILDING ENERGY EFFICIENCY STANDARDS TITLE


-----

"""


    def test_remove_urls(self):
        text = "Visit https://example.com for more info. Also check http://example.org ."
        expected = "Visit  for more info. Also check  ."
        self.assertEqual(remove_urls(text), expected)

    def test_remove_urls_with_text(self):
        text = "Visit https://example.com for more info. This is a text."
        expected = "Visit  for more info. This is a text."
        self.assertEqual(remove_urls(text), expected)
