"""
Tests for evaluating specific fields in metadata extraction.
"""

import os
import json
import pytest
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional
from pathlib import Path

from eko import eko_var_path
from llm_evals.metadata_eval import MetadataEvaluator
from llm_evals.test_data import get_test_statements


@pytest.fixture
def evaluator(gold_standard_model):
    """Fixture for the metadata evaluator."""
    return MetadataEvaluator(gold_standard_model=gold_standard_model)


@pytest.fixture
def important_fields():
    """Define the most important fields to evaluate."""
    return [
        "company",
        "subject_entities",
        "object_entities",
        "domain",
        "is_impactful_action",
        "impact_value",
        "action",
        "locations",
        "time",
        "quantities",
        "is_environmental",
        "is_social",
        "is_governance",
        "statement_category"
    ]


def test_field_extraction_accuracy(evaluator, favorite_models, important_fields, test_output_dir):
    """
    Test the accuracy of specific fields across models.
    
    Args:
        evaluator: The metadata evaluator
        favorite_models: List of models to test
        important_fields: List of important fields to evaluate
        test_output_dir: Output directory
    """
    # Dictionary to store field-specific results
    field_results = {field: {} for field in important_fields}
    
    # Run evaluations for each model
    for model_name in favorite_models:
        try:
            # Check if results already exist
            model_dir = os.path.join(test_output_dir, model_name.replace("/", "_"))
            results_file = os.path.join(model_dir, "test_results.json")
            
            if os.path.exists(results_file):
                # Load existing results
                with open(results_file, "r") as f:
                    metrics = json.load(f)
            else:
                # Run evaluation
                os.makedirs(model_dir, exist_ok=True)
                metrics = evaluator.evaluate_model(model_name, output_dir=model_dir)
                
                # Save results
                with open(results_file, "w") as f:
                    json.dump(metrics, f, indent=2)
            
            # Extract field accuracy
            if "field_accuracy" in metrics and metrics["field_accuracy"]:
                for field in important_fields:
                    if field in metrics["field_accuracy"]:
                        field_results[field][model_name] = metrics["field_accuracy"][field]
                    else:
                        field_results[field][model_name] = 0.0
            
        except Exception as e:
            print(f"Error evaluating {model_name}: {str(e)}")
    
    # Generate field-specific report
    if field_results:
        _generate_field_report(field_results, test_output_dir)
    
    # Skip the test if no results
    if not any(field_results.values()):
        pytest.skip("No field results were collected")
    
    # Basic assertion
    assert any(field_results.values()), "No field results were collected"


def test_challenging_fields(evaluator, favorite_models, test_output_dir):
    """
    Identify the most challenging fields for each model.
    
    Args:
        evaluator: The metadata evaluator
        favorite_models: List of models to test
        test_output_dir: Output directory
    """
    # Dictionary to store challenging fields for each model
    challenging_fields = {}
    
    # Run evaluations for each model
    for model_name in favorite_models:
        try:
            # Check if results already exist
            model_dir = os.path.join(test_output_dir, model_name.replace("/", "_"))
            results_file = os.path.join(model_dir, "test_results.json")
            
            if os.path.exists(results_file):
                # Load existing results
                with open(results_file, "r") as f:
                    metrics = json.load(f)
                
                # Extract field accuracy
                if "field_accuracy" in metrics and metrics["field_accuracy"]:
                    # Sort fields by accuracy (ascending)
                    sorted_fields = sorted(
                        metrics["field_accuracy"].items(),
                        key=lambda x: x[1]
                    )
                    
                    # Get the 5 most challenging fields (lowest accuracy)
                    challenging_fields[model_name] = sorted_fields[:5]
            
        except Exception as e:
            print(f"Error processing results for {model_name}: {str(e)}")
    
    # Generate report on challenging fields
    if challenging_fields:
        _generate_challenging_fields_report(challenging_fields, test_output_dir)
    
    # Skip the test if no results
    if not challenging_fields:
        pytest.skip("No field results were collected")
    
    # Basic assertion
    assert challenging_fields, "No field results were collected"


def _generate_field_report(field_results: Dict[str, Dict[str, float]], output_dir: str):
    """
    Generate a report on field-specific accuracy.
    
    Args:
        field_results: Dictionary mapping fields to model accuracies
        output_dir: Output directory
    """
    # Create field-specific data
    field_data = []
    
    for field, model_accuracies in field_results.items():
        for model_name, accuracy in model_accuracies.items():
            field_data.append({
                "field": field,
                "model": model_name,
                "accuracy": accuracy
            })
    
    # Create DataFrame
    df = pd.DataFrame(field_data)
    
    # Save to CSV
    csv_file = os.path.join(output_dir, "field_accuracy.csv")
    df.to_csv(csv_file, index=False)
    
    # Generate visualizations
    _generate_field_visualizations(df, output_dir)
    
    # Print field accuracy summary
    print("\nField Accuracy Summary:")
    print("=" * 80)
    
    # Pivot the data for better display
    pivot_df = df.pivot(index="field", columns="model", values="accuracy")
    print(pivot_df.to_string())
    
    print("=" * 80)
    print(f"Full field accuracy report saved to {csv_file}")


def _generate_field_visualizations(df: pd.DataFrame, output_dir: str):
    """
    Generate visualizations for field-specific accuracy.
    
    Args:
        df: DataFrame with field accuracy data
        output_dir: Output directory
    """
    # Create pivot table
    pivot_df = df.pivot(index="field", columns="model", values="accuracy")
    
    # 1. Heatmap of field accuracy across models
    plt.figure(figsize=(14, 10))
    plt.imshow(pivot_df, cmap="YlGnBu", aspect="auto", vmin=0, vmax=1)
    plt.colorbar(label="Accuracy")
    plt.xticks(range(len(pivot_df.columns)), pivot_df.columns, rotation=45, ha="right")
    plt.yticks(range(len(pivot_df.index)), pivot_df.index)
    plt.title("Field Accuracy Across Models")
    
    # Add text annotations
    for i in range(len(pivot_df.index)):
        for j in range(len(pivot_df.columns)):
            value = pivot_df.iloc[i, j]
            if not pd.isna(value):
                plt.text(j, i, f"{value:.2f}", ha="center", va="center", 
                         color="black" if value > 0.5 else "white")
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "field_accuracy_heatmap.png"))
    plt.close()
    
    # 2. Average field accuracy across all models
    avg_field_accuracy = pivot_df.mean(axis=1).sort_values(ascending=False)
    
    plt.figure(figsize=(12, 8))
    avg_field_accuracy.plot(kind="bar", color="skyblue")
    plt.ylim(0, 1)
    plt.title("Average Field Accuracy Across All Models")
    plt.ylabel("Average Accuracy (0-1)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "avg_field_accuracy.png"))
    plt.close()
    
    # 3. Model performance by field
    for field in pivot_df.index:
        field_data = pivot_df.loc[field].sort_values(ascending=False)
        
        plt.figure(figsize=(12, 6))
        field_data.plot(kind="bar", color="lightgreen")
        plt.ylim(0, 1)
        plt.title(f"Model Accuracy for Field: {field}")
        plt.ylabel("Accuracy (0-1)")
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f"field_{field}_accuracy.png"))
        plt.close()


def _generate_challenging_fields_report(challenging_fields: Dict[str, List[tuple]], output_dir: str):
    """
    Generate a report on challenging fields for each model.
    
    Args:
        challenging_fields: Dictionary mapping models to their challenging fields
        output_dir: Output directory
    """
    # Create report file
    report_file = os.path.join(output_dir, "challenging_fields_report.txt")
    
    with open(report_file, "w") as f:
        f.write("Challenging Fields Report\n")
        f.write("=======================\n\n")
        
        for model_name, fields in challenging_fields.items():
            f.write(f"Model: {model_name}\n")
            f.write("-" * 50 + "\n")
            
            for field, accuracy in fields:
                f.write(f"  {field}: {accuracy:.2f}\n")
            
            f.write("\n")
    
    # Print summary
    print("\nChallenging Fields Summary:")
    print("=" * 80)
    
    for model_name, fields in challenging_fields.items():
        print(f"\nModel: {model_name}")
        print("-" * 50)
        
        for field, accuracy in fields:
            print(f"  {field}: {accuracy:.2f}")
    
    print("=" * 80)
    print(f"Full challenging fields report saved to {report_file}")


if __name__ == "__main__":
    # This allows running the tests directly
    pytest.main(["-xvs", __file__])
