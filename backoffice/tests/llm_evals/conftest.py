"""
Pytest configuration for LLM evaluation tests.
"""

import os
import json
import pytest
from typing import Dict, List, Any, Optional
from pathlib import Path

from eko import eko_var_path
from llm_evals.utils import setup_logging


@pytest.fixture(scope="session")
def debug_logging():
    """Enable debug logging for tests."""
    return setup_logging(debug_logging=True)


@pytest.fixture(scope="session")
def favorite_models():
    """
    Define your favorite models to test.
    
    You can customize this list with your preferred models.
    """
    return [
        "vertex_ai/gemini-2.0-flash-lite",
        "vertex_ai/gemini-1.5-flash",
        "claude-3-5-sonnet-20240620",
        "groq/llama3-70b-8192",
        # Add more models as needed
    ]


@pytest.fixture(scope="session")
def gold_standard_model():
    """Define the gold standard model for comparisons."""
    return "gemini-2.5-pro-preview-05-06"


@pytest.fixture(scope="session")
def test_output_dir():
    """Create and return the test output directory."""
    output_dir = os.path.join(eko_var_path, "reports", "llm_evals", "tests")
    os.makedirs(output_dir, exist_ok=True)
    return output_dir


@pytest.fixture(scope="session")
def model_config_file(test_output_dir):
    """
    Path to a JSON file containing model configurations.
    
    This allows you to customize parameters for each model.
    """
    config_path = os.path.join(test_output_dir, "model_config.json")
    
    # Create default config if it doesn't exist
    if not os.path.exists(config_path):
        default_config = {
            "vertex_ai/gemini-2.0-flash-lite": {
                "temperature": 0.1,
                "max_tokens": 4000
            },
            "vertex_ai/gemini-1.5-flash": {
                "temperature": 0.1,
                "max_tokens": 4000
            },
            "claude-3-5-sonnet-20240620": {
                "temperature": 0.1,
                "max_tokens": 4000
            },
            "groq/llama3-70b-8192": {
                "temperature": 0.1,
                "max_tokens": 4000
            }
        }
        
        with open(config_path, "w") as f:
            json.dump(default_config, f, indent=2)
    
    return config_path


@pytest.fixture(scope="session")
def model_config(model_config_file):
    """Load model configurations from the config file."""
    with open(model_config_file, "r") as f:
        return json.load(f)


@pytest.fixture
def parametrized_models(request, favorite_models):
    """
    Fixture to parametrize tests with model names.
    
    Usage:
        @pytest.mark.parametrize("model_name", ["parametrized_models"])
        def test_something(model_name):
            ...
    """
    return favorite_models
