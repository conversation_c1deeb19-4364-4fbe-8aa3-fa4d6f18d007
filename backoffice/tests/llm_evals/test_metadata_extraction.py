"""
Tests for metadata extraction capabilities of different LLM models.
"""

import os
import json
import pytest
from typing import Dict, List, Any, Optional
from pathlib import Path

from eko import eko_var_path
from eko.models.statement_metadata import StatementMetadata
from eko.models.vector.demise.demise_model import DEMISEModel

from llm_evals.metadata_eval import MetadataEvaluator
from llm_evals.test_data import get_test_statements


# Define a fixture for the test statements
@pytest.fixture
def test_statements():
    """Fixture for test statements."""
    return get_test_statements()


# Define a fixture for the output directory
@pytest.fixture
def output_dir():
    """Fixture for output directory."""
    test_output_dir = os.path.join(eko_var_path, "reports", "llm_evals", "tests")
    os.makedirs(test_output_dir, exist_ok=True)
    return test_output_dir


# Define a fixture for the evaluator
@pytest.fixture
def evaluator():
    """Fixture for the metadata evaluator."""
    return MetadataEvaluator(gold_standard_model="gemini-2.5-pro-preview-05-06")


# Define a fixture for the models to test
@pytest.fixture(params=[
    "vertex_ai/gemini-2.0-flash-lite",
    "vertex_ai/gemini-1.5-flash",
    # Add your favorite models here
])
def model_name(request):
    """Fixture for model names to test."""
    return request.param


def test_metadata_extraction(evaluator, model_name, test_statements, output_dir):
    """
    Test metadata extraction for a specific model.
    
    Args:
        evaluator: The metadata evaluator
        model_name: The name of the model to test
        test_statements: The test statements
        output_dir: The output directory
    """
    # Skip test if model is not available
    try:
        # Extract metadata for the first test statement
        statement = test_statements[0]
        metadata, demise = evaluator._extract_metadata(
            model_name,
            statement["statement_text"],
            statement.get("context", ""),
            statement.get("title", ""),
            statement.get("extract", ""),
            statement.get("authors", []),
            statement.get("doc_year", 2023)
        )
        
        # If we get here, the model is available
        assert metadata is not None, f"Metadata extraction failed for {model_name}"
        
    except Exception as e:
        pytest.skip(f"Model {model_name} not available: {str(e)}")
    
    # Run the full evaluation
    model_output_dir = os.path.join(output_dir, model_name.replace("/", "_"))
    os.makedirs(model_output_dir, exist_ok=True)
    
    metrics = evaluator.evaluate_model(model_name, output_dir=model_output_dir)
    
    # Check that the evaluation completed successfully
    assert metrics is not None, f"Evaluation failed for {model_name}"
    assert "overall_accuracy" in metrics, f"No overall accuracy in metrics for {model_name}"
    assert metrics["successful_extractions"] > 0, f"No successful extractions for {model_name}"
    
    # Print the results
    print(f"\nResults for {model_name}:")
    print(f"Overall accuracy: {metrics['overall_accuracy']:.2f}")
    print(f"Successful extractions: {metrics['successful_extractions']}/{metrics['total_statements']}")
    
    # Check field accuracy if available
    if "field_accuracy" in metrics and metrics["field_accuracy"]:
        print("\nField accuracy:")
        for field, accuracy in metrics["field_accuracy"].items():
            print(f"  {field}: {accuracy:.2f}")
    
    # Save the results to a JSON file
    results_file = os.path.join(model_output_dir, "test_results.json")
    with open(results_file, "w") as f:
        json.dump(metrics, f, indent=2)
    
    return metrics


def test_compare_models(evaluator, output_dir):
    """
    Test comparing multiple models.
    
    Args:
        evaluator: The metadata evaluator
        output_dir: The output directory
    """
    # Define the models to compare
    models = [
        "vertex_ai/gemini-2.0-flash-lite",
        "vertex_ai/gemini-1.5-flash",
        # Add your favorite models here
    ]
    
    # Run evaluations for each model
    results = {}
    for model_name in models:
        try:
            model_output_dir = os.path.join(output_dir, model_name.replace("/", "_"))
            os.makedirs(model_output_dir, exist_ok=True)
            
            metrics = evaluator.evaluate_model(model_name, output_dir=model_output_dir)
            results[model_name] = metrics
            
        except Exception as e:
            print(f"Error evaluating {model_name}: {str(e)}")
    
    # Compare the results
    if len(results) >= 2:
        print("\nModel Comparison:")
        print("=" * 50)
        print(f"{'Model':<30} {'Accuracy':<10} {'Success Rate':<15}")
        print("-" * 50)
        
        for model_name, metrics in results.items():
            accuracy = metrics.get("overall_accuracy", 0.0)
            success_rate = metrics.get("successful_extractions", 0) / metrics.get("total_statements", 1)
            print(f"{model_name:<30} {accuracy:<10.2f} {success_rate:<15.2f}")
        
        # Save the comparison results
        comparison_file = os.path.join(output_dir, "model_comparison.json")
        with open(comparison_file, "w") as f:
            json.dump(results, f, indent=2)
    
    return results


if __name__ == "__main__":
    # This allows running the tests directly
    pytest.main(["-xvs", __file__])
