#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run all LLM evaluation tests and generate a comprehensive report.
"""

import os
import sys
import json
import time
import argparse
import subprocess
from pathlib import Path
from datetime import datetime

from eko import eko_var_path


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run LLM evaluation tests")
    parser.add_argument(
        "--models", 
        nargs="+", 
        help="Models to test (default: use models from conftest.py)"
    )
    parser.add_argument(
        "--output-dir", 
        default=None,
        help="Output directory for test results"
    )
    parser.add_argument(
        "--skip-existing", 
        action="store_true",
        help="Skip models that already have results"
    )
    parser.add_argument(
        "--verbose", 
        "-v", 
        action="store_true",
        help="Enable verbose output"
    )
    return parser.parse_args()


def run_tests(models=None, output_dir=None, skip_existing=False, verbose=False):
    """
    Run all LLM evaluation tests.
    
    Args:
        models: List of models to test (default: use models from conftest.py)
        output_dir: Output directory for test results
        skip_existing: Skip models that already have results
        verbose: Enable verbose output
    """
    # Set output directory
    if output_dir is None:
        output_dir = os.path.join(eko_var_path, "reports", "llm_evals", "tests")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Create timestamp for this run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir = os.path.join(output_dir, f"run_{timestamp}")
    os.makedirs(run_dir, exist_ok=True)
    
    # Set up command
    cmd = ["python", "-m", "pytest", "tests/llm_evals"]
    
    if verbose:
        cmd.append("-v")
    
    # Add output directory
    cmd.extend(["--output-dir", run_dir])
    
    # Add models if specified
    if models:
        model_arg = "--models=" + ",".join(models)
        cmd.append(model_arg)
    
    # Add skip_existing flag if specified
    if skip_existing:
        cmd.append("--skip-existing")
    
    # Run the tests
    print(f"Running tests with command: {' '.join(cmd)}")
    start_time = time.time()
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True
        )
        
        # Save output
        with open(os.path.join(run_dir, "test_output.txt"), "w") as f:
            f.write(result.stdout)
        
        if result.stderr:
            with open(os.path.join(run_dir, "test_errors.txt"), "w") as f:
                f.write(result.stderr)
        
        print(result.stdout)
        
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
    except subprocess.CalledProcessError as e:
        print(f"Tests failed with exit code {e.returncode}")
        print(e.stdout)
        print("Errors:")
        print(e.stderr)
        
        # Save output even if tests fail
        with open(os.path.join(run_dir, "test_output.txt"), "w") as f:
            f.write(e.stdout)
        
        with open(os.path.join(run_dir, "test_errors.txt"), "w") as f:
            f.write(e.stderr)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Save run metadata
    metadata = {
        "timestamp": timestamp,
        "duration_seconds": duration,
        "command": " ".join(cmd),
        "models": models,
        "skip_existing": skip_existing
    }
    
    with open(os.path.join(run_dir, "run_metadata.json"), "w") as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\nTests completed in {duration:.2f} seconds")
    print(f"Results saved to {run_dir}")


if __name__ == "__main__":
    # Make sure we're in the right directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir.parent.parent)  # Change to backoffice/src
    
    # Parse arguments and run tests
    args = parse_args()
    run_tests(
        models=args.models,
        output_dir=args.output_dir,
        skip_existing=args.skip_existing,
        verbose=args.verbose
    )
