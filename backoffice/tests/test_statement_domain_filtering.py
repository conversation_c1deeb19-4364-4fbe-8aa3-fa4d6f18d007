import unittest
from unittest.mock import MagicMock, patch

from eko.db.data.statement import StatementData
from eko.models.vector.derived.effect_type import EffectType


class TestStatementDomainFiltering(unittest.TestCase):
    """Test the domain filtering functionality in StatementData.get_statements_by_entity_and_time"""

    @patch('eko.settings.settings')
    def test_domain_filtering_parameters(self, mock_settings):
        """Test that the domain filtering parameters are correctly passed to the SQL query"""
        # Set up mock settings
        mock_settings.statement_min_domain_credibility = 50
        mock_settings.statement_allowed_domain_roles = ["media", "ngo"]
        mock_settings.statement_excluded_domain_categories = ["social"]

        # Set up mock connection and cursor
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor

        # Mock the cursor.fetchall() to return an empty list
        mock_cursor.fetchall.return_value = []

        # Call the method
        StatementData.get_statements_by_entity_and_time(
            mock_conn,
            entity_id=123,
            start_year=2020,
            end_year=2023,
            effect_type=EffectType.GREEN
        )

        # Check that the cursor.execute was called with the correct parameters
        mock_cursor.execute.assert_called_once()
        args, kwargs = mock_cursor.execute.call_args

        # Check that the query contains the domain filtering conditions
        query = args[0]
        self.assertIn("LEFT JOIN kg_domains kd ON d.origin_domain = kd.domain", query)
        self.assertIn("d.origin_domain IS NULL", query)
        self.assertIn("kd.domain IS NULL", query)
        self.assertIn("kd.credibility >= %s", query)
        self.assertIn("kd.domain_role = ANY(%s)", query)
        self.assertIn("(kd.domain_category IS NULL OR NOT (kd.domain_category = ANY(%s)))", query)

        # Check that the parameters were passed correctly
        params = args[1]
        self.assertEqual(params[0], 123)  # entity_id
        self.assertEqual(params[1], 2020)  # start_year
        self.assertEqual(params[2], 2023)  # end_year
        self.assertEqual(params[3], 50)  # statement_min_domain_credibility
        self.assertEqual(params[4], ["media", "ngo"])  # statement_allowed_domain_roles
        self.assertEqual(params[5], ["social"])  # statement_excluded_domain_categories

    @patch('eko.settings.settings')
    def test_red_effect_domain_filtering(self, mock_settings):
        """Test that the domain filtering parameters are correctly passed to the SQL query for RED effects"""
        # Set up mock settings
        mock_settings.statement_min_domain_credibility = 50
        mock_settings.statement_allowed_domain_roles = ["media", "ngo"]
        mock_settings.statement_excluded_domain_categories = ["social"]

        # Set up mock connection and cursor
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor

        # Mock the cursor.fetchall() to return an empty list
        mock_cursor.fetchall.return_value = []

        # Call the method with RED effect type
        StatementData.get_statements_by_entity_and_time(
            mock_conn,
            entity_id=123,
            start_year=2020,
            end_year=2023,
            effect_type=EffectType.RED
        )

        # Check that the cursor.execute was called with the correct parameters
        mock_cursor.execute.assert_called_once()
        args, kwargs = mock_cursor.execute.call_args

        # Check that the query contains the domain filtering conditions
        query = args[0]
        self.assertIn("LEFT JOIN kg_domains kd ON d.origin_domain = kd.domain", query)
        self.assertIn("impact_value < -0.01", query)  # Check for RED effect condition

        # Check that the parameters were passed correctly
        params = args[1]
        self.assertEqual(params[0], 123)  # entity_id
        self.assertEqual(params[1], 2020)  # start_year
        self.assertEqual(params[2], 2023)  # end_year
        self.assertEqual(params[3], 50)  # statement_min_domain_credibility
        self.assertEqual(params[4], ["media", "ngo"])  # statement_allowed_domain_roles
        self.assertEqual(params[5], ["social"])  # statement_excluded_domain_categories


if __name__ == '__main__':
    unittest.main()
