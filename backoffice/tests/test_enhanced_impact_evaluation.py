"""
Tests for the enhanced ImpactEvaluationFramework with ScaleFactors integration.

This test suite validates the new ScaleFactors-based consistency checks,
bias detection, and quality metrics.
"""

import pytest
from eko.analysis_v2.effects.impact import (
    EventImpactMeasurement,
    DimensionAssessment
)
from eko.analysis_v2.effects.impact.models.assessment import (
    HarmImpactAssessment, BenefitImpactAssessment,
    AnimalHarmImpact, HumanHarmImpact, EnvironmentHarmImpact,
    AnimalBenefitImpact, HumanBenefitImpact, EnvironmentBenefitImpact
)
from eko.analysis_v2.effects.impact.evaluation import ImpactEvaluationFramework
from eko.analysis_v2.effects.impact.models import TemporalBreakdown, ScaleFactors


class TestEnhancedImpactEvaluation:
    """Test suite for enhanced impact evaluation with ScaleFactors."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.framework = ImpactEvaluationFramework()
    
    def create_test_scale_factors(self, **overrides):
        """Create test ScaleFactors with optional overrides."""
        defaults = {
            "proximity_to_tipping_point": "Moderate concern",
            # Reversibility questions - at least one must be True
            "requires_significant_effort_to_reverse": True,
            # Scale of impact questions - at least one must be True
            "affects_multiple_individuals": True,
            # Directness questions - at least one must be True
            "entity_influenced_outcome": True,
            # Authenticity questions - at least one must be True
            "primarily_business_driven": True,
            # Deliberateness questions - at least one must be True
            "knew_consequences_acted_anyway": True,
            # Contribution questions - at least one must be True
            "minor_contributor": True,
            "duration": "medium",
            # Scope questions - at least one must be True
            "local_city_impact": True,
            # Degree questions - at least one must be True
            "moderate_harm": True,
            # Probability questions - at least one must be True
            "has_already_happened": True,
        }
        defaults.update(overrides)
        return ScaleFactors(**defaults)
    
    def create_test_dimension(self, score=0.5, confidence="medium", scale_factors_overrides=None):
        """Create test DimensionAssessment."""
        temporal = TemporalBreakdown(
            immediate="Immediate impact",
            medium_term="Medium term impact",
            long_term="Long term impact"
        )
        
        sf_overrides = scale_factors_overrides or {}
        scale_factors = self.create_test_scale_factors(**sf_overrides)
        
        return DimensionAssessment(
            score=score,
            reasoning="Test reasoning",
            confidence=confidence,
            temporal_breakdown=temporal,
            scale_factors=scale_factors
        )
    
    def create_test_measurement(self, harm_overrides=None, benefit_overrides=None):
        """Create test EventImpactMeasurement."""
        harm_sf = harm_overrides or {}
        benefit_sf = benefit_overrides or {}
        
        harm_dim = self.create_test_dimension(score=0.6, scale_factors_overrides=harm_sf)
        benefit_dim = self.create_test_dimension(score=0.3, scale_factors_overrides=benefit_sf)
        
        harm_assessment = HarmImpactAssessment(
            animals=AnimalHarmImpact(assessment=harm_dim),
            humans=HumanHarmImpact(assessment=harm_dim),
            environment=EnvironmentHarmImpact(assessment=harm_dim)
        )
        
        benefit_assessment = BenefitImpactAssessment(
            animals=AnimalBenefitImpact(assessment=benefit_dim),
            humans=HumanBenefitImpact(assessment=benefit_dim),
            environment=EnvironmentBenefitImpact(assessment=benefit_dim)
        )
        
        return EventImpactMeasurement(
            event_id="test-event",
            event_summary="Test event",
            event_description="Test event description",
            harm_assessment=harm_assessment,
            benefit_assessment=benefit_assessment,
            key_uncertainties=["Test uncertainty"],
            ethical_considerations=["Test ethical consideration"],
            assessed_at="2024-01-01T00:00:00Z",
            model_used="test-model",
            prompt_version="1.0"
        )
    
    def test_scale_factors_alignment_detection(self):
        """Test detection of ScaleFactors alignment issues."""
        # High directness with low score should flag issue
        measurement = self.create_test_measurement(
            harm_overrides={"directness": 90}
        )

        # Manually set low scores to trigger the alignment issue
        measurement.harm_assessment.animals.score = 0.2
        measurement.harm_assessment.humans.score = 0.2
        measurement.harm_assessment.environment.score = 0.2

        evaluation = self.framework.evaluate_measurement(measurement)

        assert not evaluation.consistency_checks.scale_factors_alignment
        assert any("High directness" in issue for issue in evaluation.consistency_checks.issues)
    
    def test_contribution_bias_detection(self):
        """Test detection of contribution bias (all-or-nothing thinking)."""
        # High score with low contribution should flag bias
        measurement = self.create_test_measurement(
            harm_overrides={"contribution": 15.0}  # Low contribution but high score (0.6)
        )
        
        # Ensure the scores are set to trigger the bias detection
        # The bias detection checks if score > 0.6 and contribution < 20
        measurement.harm_assessment.animals.score = 0.7
        measurement.harm_assessment.humans.score = 0.7
        measurement.harm_assessment.environment.score = 0.7
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        assert evaluation.bias_detection.contribution_bias
        assert any("all-or-nothing bias" in issue for issue in evaluation.bias_detection.detected_issues)
    
    def test_authenticity_bias_detection(self):
        """Test detection of authenticity bias (greenwashing)."""
        # High benefit score with low authenticity should flag bias
        measurement = self.create_test_measurement(
            benefit_overrides={"authenticity": 0}  # Zero authenticity (marketing action)
        )
        
        # The bias detection checks for:
        # - score > 0.5 and authenticity <= 30, OR
        # - authenticity == 0 and score > 0.2
        # The default benefit score is 0.3, which triggers the second condition
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        assert evaluation.bias_detection.authenticity_bias
        assert any("greenwashing bias" in issue for issue in evaluation.bias_detection.detected_issues)
    
    def test_directness_bias_detection(self):
        """Test detection of directness bias (overattribution)."""
        # Low directness with high score should flag bias
        measurement = self.create_test_measurement(
            harm_overrides={"directness": 15}  # Low directness with high score (0.6)
        )

        # Ensure the score is high enough to trigger bias detection
        # The bias detection checks if directness <= 20 and score > 0.5
        measurement.harm_assessment.animals.score = 0.6
        measurement.harm_assessment.humans.score = 0.6
        measurement.harm_assessment.environment.score = 0.6
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        assert evaluation.bias_detection.directness_bias
        assert any("overattribution bias" in issue for issue in evaluation.bias_detection.detected_issues)
    
    def test_internal_consistency_checks(self):
        """Test internal ScaleFactors consistency checks."""
        # High deliberateness with low authenticity suggests greenwashing
        measurement = self.create_test_measurement(
            harm_overrides={"deliberateness": 90, "authenticity": 20}
        )
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        assert not evaluation.consistency_checks.scale_factors_internal_consistency
        assert any("greenwashing" in issue for issue in evaluation.consistency_checks.issues)
    
    def test_scale_factors_quality_metrics(self):
        """Test ScaleFactors quality assessment."""
        # Create measurement with good ScaleFactors
        measurement = self.create_test_measurement()
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        # Should have reasonable completeness score
        assert evaluation.completeness_score > 0.5
        
        # Test with poor ScaleFactors
        poor_measurement = self.create_test_measurement(
            harm_overrides={
                "scale_of_impact": -1.0,  # Invalid
                "proximity_to_tipping_point": "test"  # Placeholder
            }
        )
        
        poor_evaluation = self.framework.evaluate_measurement(poor_measurement)
        
        # Should have lower completeness score
        assert poor_evaluation.completeness_score < evaluation.completeness_score
    
    def test_damage_alignment_validation(self):
        """Test validation of damage field alignment with scores."""
        # High damage with low score should flag issue
        measurement = self.create_test_measurement(
            harm_overrides={"damage": 90}  # High damage but score is 0.6
        )
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        # Should detect underestimation
        assert any("High damage" in issue and "low score" in issue 
                  for issue in evaluation.consistency_checks.issues)
    
    def test_scale_impact_validation(self):
        """Test validation of scale_of_impact field."""
        # Very large scale with moderate score should flag issue
        measurement = self.create_test_measurement(
            harm_overrides={"scale_of_impact": 2000000.0}  # 2 million but score is 0.6
        )
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        # Should detect potential underestimation
        assert any("Very large scale" in issue 
                  for issue in evaluation.consistency_checks.issues)
    
    def test_overall_quality_with_scale_factors(self):
        """Test overall quality calculation includes ScaleFactors quality."""
        # Create measurement with ScaleFactors issues
        measurement = self.create_test_measurement(
            harm_overrides={"directness": 90},  # High directness
            benefit_overrides={"authenticity": 10}  # Low authenticity
        )
        
        # Force low scores to trigger alignment issues
        measurement.harm_assessment.animals.score = 0.2
        measurement.harm_assessment.humans.score = 0.2
        measurement.harm_assessment.environment.score = 0.2
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        # Should have poor quality due to ScaleFactors issues
        assert evaluation.overall_quality in ["poor", "fair"]
        assert evaluation.overall_quality_score < 0.7
    
    def test_comprehensive_bias_detection(self):
        """Test comprehensive bias detection across all ScaleFactors."""
        # Create measurement with multiple bias indicators
        measurement = self.create_test_measurement(
            harm_overrides={
                "contribution": 10.0,  # Low contribution but high score
                "directness": 20,      # Low directness but high score
                "authenticity": 15     # Low authenticity
            }
        )
        
        evaluation = self.framework.evaluate_measurement(measurement)
        
        # Should detect multiple bias types
        assert evaluation.bias_detection.contribution_bias
        assert evaluation.bias_detection.directness_bias
        assert len(evaluation.bias_detection.detected_issues) >= 2
