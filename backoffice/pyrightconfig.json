{"pythonVersion": "3.12", "reportMissingImports": true, "reportMissingTypeStubs": false, "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "diagnosticMode": "workspace", "include": ["./src/eko/**/*.py", "./src/eko_*/**/*.py", "./tests/**/*.py", "./src/cli/**/*.py", "./*.py"], "ignore": ["**/__pycache__", "**/.mypy_cache"], "extraPaths": [".", "./src"], "venvPath": ".", "venv": ".venv", "stubPath": "./src/typings", "reportUndefinedVariable": "warning", "reportOptionalMemberAccess": "warning"}