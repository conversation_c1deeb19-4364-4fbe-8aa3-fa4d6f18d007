import faulthandler
import sys
import traceback

from eko import console


def exception_hook(exc_type, exc_value, exc_traceback):
    print("Exception type:", exc_type)
    print("Exception value:", exc_value)
    traceback.print_exception(exc_type, exc_value, exc_traceback)
    console.print_exception(show_locals=False)

# Set the exception hook
sys.excepthook = exception_hook


import logging
import os
import signal

import click
import sentry_sdk
from dotenv import load_dotenv
from loguru import logger

from cli.analysis_v2 import ana
from cli.cms_command import cms
from cli.crawl_command import crawl
from cli.entity_commands import entities
from cli.fix_command import fix
from cli.llm_evals import llm_evals_group
from cli.prediction_v2_command import prediction_v2
from cli.scrape_command import scrape
from cli.selective_highlighting import cherry
from cli.test_command import test
from cli.virtual_entity_command import virtual_entity

# Import feature manager for initialization
from eko.db.data.feature_manager import feature_manager
from eko.statements.extract import extract_statements_from_doc


# Import visualization libraries only when needed
def import_viz_libs():
    global plt, np, sns, TSNE
    return True


from eko.commands.chunk import chunk_command, map_entity_reports_command
from eko.commands.db_api import dbapi_analyse_entity_command, dbapi_single_url_command
from eko.commands.statements import extract_statements_for_training_command

# Scrape commands moved to cli/scrape_command.py
from eko.db import bo_connection_config, get_bo_conn
from eko.db._deprecated_xfer import sync_xfer_tables_command
from eko.db.queue import listen_for_requests
from eko.entities.queries import get_entity_by_short_id
from eko.nlp.classifier import BertClassifier
from eko.nlp.esg_trainer import train_model

# PDF parsing import moved to cli/scrape_command.py
# Scrape imports moved to cli/scrape_command.py

sentry_sdk.init(
    dsn="https://<EMAIL>/4508439654760528",
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for tracing.
    traces_sample_rate=1.0,
)


def setup_logging(debug_logging=False):
    """
    Configure logging based on the debug_logging parameter

    Args:
        debug_logging: Boolean indicating whether to enable debug logging
    """
    # Set log levels based on debug_logging parameter
    logging_level = logging.DEBUG if debug_logging else logging.INFO
    logging_3rd_party_level = logging.DEBUG if debug_logging else logging.WARN
    logger_level = "DEBUG" if debug_logging else "INFO"

    # Remove any pre-configured handlers for the root logger
    logging.getLogger("httpcore").setLevel(logging_3rd_party_level)
    logging.getLogger("instructor").setLevel(logging_3rd_party_level)
    logging.getLogger("PIL").setLevel(logging_3rd_party_level)
    logging.getLogger("urllib3").setLevel(logging_3rd_party_level)
    logging.getLogger("openai").setLevel(logging_3rd_party_level)
    logging.getLogger("asyncio").setLevel(logging_3rd_party_level)
    logging.getLogger("httpx").setLevel(logging_3rd_party_level)
    logging.getLogger("anthropic").setLevel(logging_3rd_party_level)
    logging.getLogger("psycopg").setLevel(logging_3rd_party_level)
    logging.basicConfig(level=logging.NOTSET)
    # Remove the default logger
    logger.remove()

    # Customize the log levels' colors
    logger.level("DEBUG", color="<blue>")
    logger.level("INFO", color="<green>")
    logger.level("SUCCESS", color="<bold><green>")
    logger.level("WARNING", color="<yellow>")
    logger.level("ERROR", color="<red>")
    logger.level("CRITICAL", color="<bold><red>")

    def custom_path_formatter(record):
        # Get the full file path
        file_path = record["file"].path

        # Split the file path and get the last three segments
        parts = file_path.split('/')
        last_three_segments = '/'.join(parts[-3:])

        # Return the formatted string, replacing the default file path
        record["file"].path = last_three_segments
        exception = record["exception"]

        # Return the formatted string, adding exception traceback if present
        exception_message = ""
        if exception:
            exception_message = f"\n{traceback.format_exc()}"

        return "{process: <8} <level>{level: <8}</level> | <cyan>{time:YYYY-MM-DD HH:mm:ss}</cyan> | {file.path}:{line} | <level>{message}</level>\n" + exception_message.replace(
            "<", "\\<").replace(">", "\\>").replace("{", "\\{").replace("}", "\\}")

    logger.add(
        sys.stderr,
        format=custom_path_formatter,
        colorize=True,
        backtrace=True,
        diagnose=True,
        level=logger_level
    )

    return debug_logging


load_dotenv()


@click.group()
@click.option('--debug', '-d', is_flag=True, help="Enable debug logging")
def main(debug):
    setup_logging(debug)
    if debug:
        logger.debug("Debug logging enabled via command line flag")


@main.group()
def gw():
    pass


# @main.command()
# @click.option('--days', default=14, help="The number of days worth of reports to process, defaults to 14.")
# def map_issue_reports(days: int):
#     map_issue_reports_command(days)


@main.command()
@click.option('--entity', default=None, help="The short id of the entity to map.")
def map_entity_reports(entity: str):
    entity_obj = get_entity_by_short_id(entity)
    map_entity_reports_command(entity_obj)


@main.command()
def sync_tables():
    sync_xfer_tables_command()


@main.command()
@click.option("--doc-id", help="The document id to extract statements from.")
def extract_statements(doc_id: int):
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            extract_statements_from_doc(doc_id,None)





# @gw.command()
# @click.option('--entity', help="The short id of the entity to analyse the document for")
# @click.option('--doc-id', help="The document to analyse")
# def single_doc(entity: str, doc_id):
#     entity_obj = get_entity_by_short_id(entity)
#     with get_bo_conn() as conn:
#         with conn.cursor() as cur:
#             doc = process_single_doc(cur, entity_obj, doc_id)
#             logger.info(f"Processed document {doc}")
#             return doc
#

# @gw.command()
# @click.option('--entity', help="The short id of the entity to analyse the document for")
# @click.option('--url', help="The URL to analyse")
# def single_url(entity: str, url):
#     return single_url_command(entity, url)
#

# def single_url_command(entity, url):
#     entity_obj = get_entity_by_short_id(entity)
#     with get_bo_conn() as conn:
#         with conn.cursor() as cur:
#             doc = process_single_doc_url(cur, entity_obj, url)
#             logger.info(f"Processed document {doc}")
#             return doc
#


from datetime import datetime


@main.command()
def listen():
    listen_for_requests(operations={
        'single_url': dbapi_single_url_command,
        "analyse_entity": dbapi_analyse_entity_command
    }
    )


@main.command()
def extract_statements_for_training():
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute(
                "SELECT short_id FROM kg_base_entities WHERE type = 'company' and canonical and lei is not null order by id  limit 1000")
            rows = cur.fetchall()
            for row in rows:
                entity_obj = get_entity_by_short_id(row[0])
                after = datetime.now().year - 10
                max_workers = 16
                logger.info(f"Extracting statements for {entity_obj.name}")
                extract_statements_for_training_command(entity_obj, max_workers)



@main.command()
@click.option('--max-reports', default=0, help="The maximum number of reports to process.")
@click.option('--strategy', default="all", help="Which strategy to use to select reports to process.")
@click.option('--max-workers', default=1, help="How many processes to use.")
def chunk(strategy, max_reports, max_workers):
    chunk_command(max_reports, max_workers, strategy)





@main.command()
def train_classifier():
    from eko import eko_var_path
    model_save_path = os.path.join(eko_var_path, 'bert/issue_classifier')
    os.makedirs(model_save_path, exist_ok=True)

    train_model(
        db_config=bo_connection_config,
        model_save_path=model_save_path,
        num_epochs=5,  # Adjust based on your needs
        batch_size=20,  # Adjust based on your memory constraints
        max_length=512,  # Adjust based on your text length
    )


@main.command()
def test_classifier():
    from eko import eko_var_path
    model_save_path = os.path.join(eko_var_path, 'bert/issue_classifier')
    os.makedirs(model_save_path, exist_ok=True)

    # Texts to classify
    new_texts = [sys.stdin.read()]

    # Predict labels
    predicted_labels = BertClassifier(model_save_path).predict_long_texts(new_texts)

    # Print results
    for text, labels in zip(new_texts, predicted_labels):
        print(f"Text: {text}")
        print(f"Predicted Labels: {labels}")
        print("-" * 50)


@main.command()
def error():
    division_by_zero = 1 / 0


@main.command()
def claude_self_test():
    """Fast command to test CLI startup time. Avoids importing and initializing all modules."""
    logger.info("Finished self test")
    # We'll import and initialize modules only when needed

def migrate_metrics_tables_if_needed():
    """
    Check if old tables have data and migrate it to the new tables.
    """
    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Check if old tables exist and have data
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'dash' AND table_name = 'pipeline_metrics'
                )
            """)
            has_old_pipeline_metrics = cursor.fetchone()[0]

            if has_old_pipeline_metrics:
                # Check if it has data
                cursor.execute("SELECT COUNT(*) FROM dash.pipeline_metrics")
                count = cursor.fetchone()[0]
                if count > 0:
                    logger.info(f"Found {count} records in old pipeline_metrics table, migrating to trk_pipeline_metrics...")
                    cursor.execute("""
                        INSERT INTO dash.trk_pipeline_metrics
                        (run_id, entity_id, entity_name, stage, count, red_count, green_count, total_processing_time_ms)
                        SELECT
                            run_id, entity_id, entity_name, stage,
                            count, red_count, green_count, total_processing_time_ms
                        FROM dash.pipeline_metrics
                        ON CONFLICT DO NOTHING
                    """)

            # Check if old clustering metrics exist and have data
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'dash' AND table_name = 'clustering_metrics'
                )
            """)
            has_old_clustering_metrics = cursor.fetchone()[0]

            if has_old_clustering_metrics:
                cursor.execute("SELECT COUNT(*) FROM dash.clustering_metrics")
                count = cursor.fetchone()[0]
                if count > 0:
                    logger.info(f"Found {count} records in old clustering_metrics table, migrating to trk_clustering_metrics...")
                    cursor.execute("""
                        INSERT INTO dash.trk_clustering_metrics
                        (run_id, entity_id, entity_name, effect_type, silhouette_score, num_clusters, num_valid_statements)
                        SELECT
                            run_id, entity_id, entity_name, effect_type,
                            silhouette_score, COUNT(DISTINCT cluster_id), SUM(cluster_size)
                        FROM dash.clustering_metrics
                        GROUP BY run_id, entity_id, entity_name, effect_type, silhouette_score
                        ON CONFLICT DO NOTHING
                    """)

            # Check if old effect flag metrics exist and have data
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'dash' AND table_name = 'effect_flag_metrics'
                )
            """)
            has_old_effect_flag_metrics = cursor.fetchone()[0]

            if has_old_effect_flag_metrics:
                cursor.execute("SELECT COUNT(*) FROM dash.effect_flag_metrics")
                count = cursor.fetchone()[0]
                if count > 0:
                    logger.info(f"Found {count} records in old effect_flag_metrics table, migrating to trk_effect_flag_metrics...")
                    cursor.execute("""
                        INSERT INTO dash.trk_effect_flag_metrics
                        (run_id, entity_id, entity_name, effect_type, flag_id, flag_title, num_statements, merged_from_count)
                        SELECT
                            run_id, entity_id, entity_name, effect_type,
                            flag_id, flag_title, statement_count,
                            CASE WHEN is_merged THEN 1 ELSE 0 END
                        FROM dash.effect_flag_metrics
                        ON CONFLICT DO NOTHING
                    """)

            # Check if old error metrics exist and have data
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'dash' AND table_name = 'pipeline_errors'
                )
            """)
            has_old_pipeline_errors = cursor.fetchone()[0]

            if has_old_pipeline_errors:
                cursor.execute("SELECT COUNT(*) FROM dash.pipeline_errors")
                count = cursor.fetchone()[0]
                if count > 0:
                    logger.info(f"Found {count} records in old pipeline_errors table, migrating to trk_pipeline_errors...")
                    cursor.execute("""
                        INSERT INTO dash.trk_pipeline_errors
                        (run_id, entity_id, entity_name, stage, error_message, error_type)
                        SELECT
                            run_id, entity_id, entity_name, stage,
                            error_message, error_type
                        FROM dash.pipeline_errors
                        ON CONFLICT DO NOTHING
                    """)

        conn.commit()
        logger.info("Data migration completed")


def ensure_traceability_tables_exist():
    """
    Make sure the traceability tracking tables exist in the dash schema.
    If they don't exist, this creates empty tables so the dashboard UI can still function.
    """
    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Check if dash schema exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.schemata
                    WHERE schema_name = 'dash'
                )
            """)
            schema_exists = cursor.fetchone()[0]

            if not schema_exists:
                logger.info("Creating dash schema...")
                cursor.execute("CREATE SCHEMA IF NOT EXISTS dash")

            # Create the main tracking tables if they don't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS dash.trk_statement_decisions (
                    id SERIAL PRIMARY KEY,
                    run_id INTEGER NOT NULL,
                    entity_id INTEGER,
                    entity_name TEXT,
                    statement_id INTEGER,
                    effect_type TEXT,
                    decision_type TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS dash.trk_effect_assignments (
                    id SERIAL PRIMARY KEY,
                    run_id INTEGER NOT NULL,
                    entity_id INTEGER,
                    entity_name TEXT,
                    statement_id INTEGER,
                    effect_id INTEGER,
                    effect_type TEXT,
                    cluster_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS dash.trk_flag_creation (
                    id SERIAL PRIMARY KEY,
                    run_id INTEGER NOT NULL,
                    entity_id INTEGER,
                    entity_name TEXT,
                    flag_id INTEGER,
                    flag_title TEXT,
                    effect_type TEXT,
                    cluster_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS dash.trk_anomalies (
                    id SERIAL PRIMARY KEY,
                    run_id INTEGER NOT NULL,
                    entity_id INTEGER,
                    entity_name TEXT,
                    type TEXT,
                    source_object_type TEXT,
                    source_object_id INTEGER,
                    severity TEXT,
                    metadata JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );

                -- Create the new trk_pipeline_metrics table if it doesn't exist
                CREATE TABLE IF NOT EXISTS dash.trk_pipeline_metrics (
                    id SERIAL PRIMARY KEY,
                    run_id BIGINT NOT NULL,
                    entity_id INTEGER,
                    entity_name TEXT,
                    stage TEXT NOT NULL,
                    status TEXT DEFAULT 'in_progress',
                    count INTEGER DEFAULT 0,
                    red_count INTEGER DEFAULT 0,
                    green_count INTEGER DEFAULT 0,
                    total_processing_time_ms INTEGER DEFAULT 0,
                    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    completed_at TIMESTAMP WITH TIME ZONE
                );

                -- Create the new trk_clustering_metrics table if it doesn't exist
                CREATE TABLE IF NOT EXISTS dash.trk_clustering_metrics (
                    id SERIAL PRIMARY KEY,
                    run_id BIGINT NOT NULL,
                    entity_id INTEGER,
                    entity_name TEXT,
                    effect_type TEXT NOT NULL,
                    clustering_method TEXT,
                    eps FLOAT,
                    min_samples INTEGER,
                    num_raw_statements INTEGER DEFAULT 0,
                    num_valid_statements INTEGER DEFAULT 0,
                    num_clusters INTEGER DEFAULT 0,
                    statements_per_cluster FLOAT DEFAULT 0,
                    silhouette_score FLOAT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );

                -- Create the new trk_effect_flag_metrics table if it doesn't exist
                CREATE TABLE IF NOT EXISTS dash.trk_effect_flag_metrics (
                    id SERIAL PRIMARY KEY,
                    run_id BIGINT NOT NULL,
                    entity_id INTEGER,
                    entity_name TEXT,
                    effect_type TEXT NOT NULL,
                    flag_id INTEGER NOT NULL,
                    impact INTEGER,
                    confidence INTEGER,
                    num_statements INTEGER DEFAULT 0,
                    num_effect_sources INTEGER DEFAULT 0,
                    merged_from_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );

                -- Create the new trk_pipeline_errors table if it doesn't exist
                CREATE TABLE IF NOT EXISTS dash.trk_pipeline_errors (
                    id SERIAL PRIMARY KEY,
                    run_id BIGINT NOT NULL,
                    entity_id INTEGER,
                    entity_name TEXT,
                    stage TEXT NOT NULL,
                    error_message TEXT NOT NULL,
                    error_type TEXT NOT NULL,
                    object_id INTEGER,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)

            logger.info("Traceability tracking tables verified")

@main.command()
@click.option('--port', default=8050, help='Port to run the dashboard on')
@click.option('--debug', is_flag=True, help='Run in debug mode')
def run_metrics(port, debug):
    """
    Launch the metrics dashboard for the Effect Analysis Pipeline.

    This dashboard provides metrics and process overview.
    """
    from eko.dash.callbacks import register_metrics_callbacks
    from eko.dash.metrics_app import create_metrics_app

    # Ensure the required tables exist (even if empty) so the UI works correctly
    ensure_traceability_tables_exist()

    # Migrate data from old to new tables if it exists
    migrate_metrics_tables_if_needed()

    # Create the metrics app
    logger.info("Creating metrics dashboard...")
    app = create_metrics_app()

    # Register callbacks for metrics dashboard
    logger.info("Registering callbacks for metrics dashboard...")
    register_metrics_callbacks(app)

    logger.info("Starting Effect Analysis Metrics Dashboard")

    # Run the dashboard
    app.run(debug=debug, port=port, host='0.0.0.0')


@main.command()
@click.option('--port', default=8050, help='Port to run the dashboard on')
@click.option('--debug', is_flag=True, help='Run in debug mode')
def run_graph(port, debug):
    """
    Launch the graph dashboard for the Effect Analysis Pipeline.

    This dashboard provides traceability graph visualization.
    """
    from eko.dash.callbacks import register_graph_callbacks
    from eko.dash.graph_app import create_graph_app

    # Ensure the required tables exist (even if empty) so the UI works correctly
    ensure_traceability_tables_exist()

    # Migrate data from old to new tables if it exists
    migrate_metrics_tables_if_needed()

    # Create the graph app
    logger.info("Creating graph dashboard...")
    app = create_graph_app()

    # Register callbacks for graph dashboard
    logger.info("Registering callbacks for graph dashboard...")
    register_graph_callbacks(app)

    logger.info("Starting Effect Analysis Traceability Graph")

    # Run the dashboard
    app.run(debug=debug, port=port, host='0.0.0.0')


@main.command()
@click.option('--port', default=8050, help='Port to run the dashboard on')
@click.option('--debug', is_flag=True, help='Run in debug mode')
def run_pipeline(port, debug):
    """
    Launch the pipeline dashboard for the Effect Analysis Pipeline.

    This dashboard provides flag traceability explorer showing the complete pipeline.
    """
    from eko.dash.callbacks import register_pipeline_callbacks
    from eko.dash.pipeline_app import create_pipeline_app

    # Ensure the required tables exist (even if empty) so the UI works correctly
    ensure_traceability_tables_exist()

    # Migrate data from old to new tables if it exists
    migrate_metrics_tables_if_needed()

    # Load run data by default unless explicitly skipped
    runs_info = []
    initial_run_options = []
    from cli.launch_traceability_dash import load_recent_runs
    runs_info = load_recent_runs()
    logger.info(f"Loaded data for {len(runs_info)} runs")

    # Create initial run options for the dropdown
    if runs_info:
        initial_run_options = [
            {"label": f"{run['name']} ({run['created_at']})", "value": run['id']}
            for run in runs_info
        ]
        logger.info(f"Created {len(initial_run_options)} initial run options for dropdown")

    # Create the pipeline app
    logger.info("Creating pipeline dashboard...")
    app = create_pipeline_app(initial_run_options=initial_run_options)

    # Register callbacks for pipeline dashboard
    logger.info("Registering callbacks for pipeline dashboard...")
    register_pipeline_callbacks(app)

    logger.info("Starting Effect Analysis Pipeline Explorer")

    # Run the dashboard
    app.run(debug=debug, port=port, host='0.0.0.0')






def signal_handler(sig, frame):
    """Handle keyboard interrupts (CTRL+C) gracefully."""
    logger.info("Received SIGINT (Ctrl+C). Shutting down gracefully...")

    # Stop any running HTML servers
    try:
        from eko.analysis_v2.effects.html_server import stop_server
        if stop_server():
            logger.info("Stopped HTTP server")
    except ImportError:
        pass

    # Flush log database connections
    from eko.log.log import flush_logs, set_stopped
    flush_logs()
    set_stopped(True)

    # Explicitly shutdown any running process pools
    for handler in logging.root.handlers:
        handler.flush()

    # Exit the program
    sys.exit(0)


def initialize_feature_manager():
    """
    Initialize the feature manager by populating the kg_features table.
    This is done once at startup to ensure all possible DEMISE features are registered.
    """
    try:
        logger.info("Initializing feature manager...")
        feature_manager.initialize()
        logger.info("Feature manager initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing feature manager: {e}")
        # Don't raise the exception - allow the application to continue even if initialization fails


if __name__ == '__main__':
    # Added directly to main so it's available at root level

    # Add subcommands
    main.add_command(test)
    main.add_command(ana)
    main.add_command(entities)
    main.add_command(cms)
    main.add_command(fix)
    main.add_command(prediction_v2)
    main.add_command(scrape)
    main.add_command(crawl)
    main.add_command(virtual_entity)
    main.add_command(cherry)
    main.add_command(llm_evals_group)

    # Register signal handler for clean shutdown on Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Additionally register SIGUSR1 to dump all threads without terminating
    def dump_all_threads(sig, frame):
        print(f"\n--- Dumping all threads (PID: {os.getpid()}) ---")
        faulthandler.dump_traceback(all_threads=True)
        

    signal.signal(signal.SIGUSR1, dump_all_threads)
    signal.signal(signal.SIGQUIT, dump_all_threads)


# Initialize feature manager for better deadlock prevention
    # This populates the kg_features table and maintains a cache
    initialize_feature_manager()

    # Manually call start_profiler and stop_profiler
    # to profile the code in between
    # sentry_sdk.profiler.start_profiler()

    try:
        main()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt detected. Exiting...")
        # KeyboardInterrupt will be caught by signal_handler
    except Exception as e:
        logger.exception("Error in main function")
        sentry_sdk.capture_exception(e)
    # Calls to stop_profiler are optional - if you don't stop the profiler, it will keep profiling
    # your application until the process exits or stop_profiler is called.
    # sentry_sdk.profiler.stop_profiler()
