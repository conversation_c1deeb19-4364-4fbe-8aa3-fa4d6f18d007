# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

# EkoIntelligence Python Backend

This is the Python backend for the EkoIntelligence ESG analysis platform. It processes corporate documents, extracts sustainability statements, and provides intelligent analysis using LLM technologies.

## Architecture Overview

The backoffice is a comprehensive ESG analysis engine that follows a **fail-fast philosophy** with explicit error propagation:

**Core Pipeline Flow:**
1. **Document Ingestion** → **Statement Extraction** → **LLM Analysis** → **Effect Flag Generation** → **Score Calculation** → **Data Sync**

**Key Architectural Patterns:**
- **Dual Database Design**: Analytics DB (core processing) → xfer_ tables → Customer DB (frontend data)
- **Pydantic-First**: All core models are Pydantic classes with strict typing
- **Fail-Fast Error Handling**: Exceptions propagate immediately, no silent failures
- **LLM Provider Abstraction**: Support for OpenAI, Anthropic, Google Gemini, Groq, etc.
- **Caching Strategy**: Multi-level caching with PostgreSQL and disk cache
- **Vector Embeddings**: DEMISE model for domain/impact classification
- **Static Checks** - Do not use getattr() or similar runtime access unless there is no choice, the code needs to be statically analysed.

## Common Commands

### Development & Testing
```bash
# From /backoffice/ directory
uv run python main.py claude-self-test                    # Self-test all systems
uv run python main.py --help                             # Show all CLI commands
uv run python api.py                                     # Start FastAPI server

# Testing
uv run python -m pytest tests/on_commit/                 # Unit tests (pre-commit)
uv run python -m pytest tests/integration/               # Integration tests  
uv run python -m pytest tests/llm_evals/                 # LLM evaluation tests
uvx ty check <filename>                                   # Type checking with pyrefly
```

### Core Analysis Commands
```bash
# Full analysis pipeline
uv run python main.py ana full --entity "Entity Name" --start-year 2020 --end-year 2024

# Effect flag generation and clustering
uv run python main.py ana create-effect-flags --run-id 123
uv run python main.py ana cluster-effect-flags --run-id 123

# Claims vs Evidence Analysis
uv run python main.py analyze-claims --entity "EntityShortId" --start-year 2019 --end-year 2025
uv run python main.py analyze-promises --entity "EntityShortId" --start-year 2019 --end-year 2025

# Selective Highlighting Detection
uv run python main.py ana selective-highlighting analyze --entity "Company Name"
uv run python main.py ana selective-highlighting analyze --no-flooding  # Cherry picking only
uv run python main.py ana selective-highlighting analyze --dry-run      # Test mode
```

### Score Calculation (Fail-Fast System)
```bash
# Bayesian scoring (preferred - advanced inference)
uv run python main.py score-sync bayesian --entity-id 123

# Standard scoring 
uv run python main.py score-sync standard --entity-id 123

# Note: Score calculation failures halt analysis immediately (fail-fast)
```

### Entity Management
```bash
uv run python main.py entities search "Company Name"
uv run python main.py entities create --name "New Company" --type COMPANY
uv run python main.py entities virtual create --name "Virtual Entity"
```

### Data Pipeline & Visualization
```bash
# Launch analytics dashboard
uv run python src/eko/dash/app.py

# Pipeline tracking and metrics
uv run python main.py ana track-pipeline --run-id 123

# Create visualizations
uv run python main.py ana create-effect-flags-viz --run-id 123
```

## Key Package Architecture

### `/src/eko/` - Core Framework
- **`analysis_v2/`**: Main analysis pipeline (effects, selective highlighting, impact measurement)
- **`db/data/`**: Data Access Objects (DAOs) - each tied to a Pydantic model
- **`llm/`**: LLM provider abstraction (OpenAI, Anthropic, Gemini, etc.)
- **`models/`**: Pydantic data models and vector embeddings (DEMISE, TREVR)
- **`nlp/`**: NLP processing utilities (spaCy, classification, cleaning)
- **`entities/`**: Entity management (companies, SEC data, GLEIF, Wikipedia)

### Critical Modules

**Effect Analysis (`analysis_v2/effects/`)**:
- **Impact Measurement**: `impact/service.py` - Core impact scoring with LLM review
- **Scale Factors**: `impact/models/scale_factors.py` - Boolean-based impact scaling (recently converted from numeric)
- **Effect Flags**: `effect_flags.py` - Generate flags from statement clusters with anomaly detection
- **DEMISE Integration**: `flag_demise.py` - Domain/impact vector calculation
- **Trace Collection**: Uses TraceCollector system for comprehensive pipeline tracking (migrated Jan 2025)

**Score Calculation (`models/xfer/xfer_score.py`)**:
- **BayesianScoreSync**: Advanced Bayesian inference scoring 
- **ScoreSync**: Standard risk assessment scoring
- **Fail-Fast**: Score failures immediately halt pipeline execution

**Selective Highlighting (`analysis_v2/selective_highlighting/`)**:
- **Cherry Picking**: Detect repeated positive statements while downplaying negatives
- **Flooding**: Identify small positive impacts used to distract from major negatives
- **Vector Processing**: Uses 1024-dimensional domain embeddings with cosine similarity

## Database Architecture

### Analytics Database (`get_bo_conn()`)
- **Purpose**: Core processing, ML models, analysis pipelines
- **Key Schemas**: `public` (main data), `dash` (metrics)
- **Key Tables**: `kg_entities`, `ana_statements`, `ana_effect_flags`, `ana_cherry`, `ana_flooding`
- **Trace Storage**: All `ana_*` tables include `trace_json JSONB` columns with GIN indexes
- **Access**: `./bin/run_in_db.sh "SQL QUERY"`

### Customer Database (`get_cus_conn()`)  
- **Purpose**: Frontend data, user management
- **Key Tables**: `xfer_*` (synced from analytics), `acc_*`, `cus_*`
- **Access**: `./bin/run_in_customer_db.sh "SQL QUERY"`

### Data Synchronization
- **Flow**: Analytics DB → `xfer_` tables → Customer DB
- **Sync Module**: `/src/eko/db/sync.py`
- **Pipeline**: Results flow automatically after analysis completion

## Development Patterns

### DAO Pattern
Each DAO is tied to a Pydantic model:
```python
# Example: StatementDAO
create() -> StatementModel with id set
retrieve(id) -> StatementModel 
find(criteria) -> List[StatementModel]
update(model) -> StatementModel
delete(id) -> int
```

### LLM Integration Pattern
```python
from eko.llm.main import call_llms_typed, LLMOptions
from eko.llm import LLMModel

# Type-safe LLM calls with Pydantic models
result = call_llms_typed(
    prompt="Analyze this statement...",
    response_model=AnalysisResponse,
    options=LLMOptions(model=LLMModel.GEMINI_FLASH)
)
```

### Error Handling (Fail-Fast)
```python
# Good: Let exceptions propagate
def process_entity(entity_id: int) -> EntityModel:
    entity = entity_dao.retrieve(entity_id)
    if not entity:
        raise ValueError(f"Entity {entity_id} not found")
    return entity

# Bad: Silent failures
def process_entity(entity_id: int) -> Optional[EntityModel]:
    try:
        return entity_dao.retrieve(entity_id)
    except Exception:
        return None  # Don't do this!
```

### Vector Processing Pattern  
```python
# DEMISE model usage
from eko.models.vector.demise.demise_model import DEMISEModel

demise = DEMISEModel.model_construct()
demise.domain.environment.climate = 0.8
demise.effect.impact.positive_change = 0.6
```

## Configuration & Settings

### File Storage (Always use `eko_var_path`)
```python
from eko import eko_var_path
import os

cache_path = os.path.join(eko_var_path, "cache/entity_cache")
embed_path = os.path.join(eko_var_path, "embed/")
```

### Settings Management
```python
from eko.settings import get_setting

# Get hyperparameters
threshold = get_setting("SELECTIVE_HIGHLIGHTING.cherry_picking.similarity_threshold")
```

### Logging (Use loguru)
```python
from loguru import logger

# Good: Use logger.exception for errors
try:
    result = risky_operation()
except Exception:
    logger.exception("Failed to process data")
    raise  # Always re-raise

# Bad: Don't use logger.error for exceptions
except Exception as e:
    logger.error(f"Error: {e}")  # Missing stack trace
```

## Testing Strategy

### Test Categories
- **Unit Tests**: `tests/on_commit/` (run pre-commit)
- **Integration Tests**: `tests/integration/` (full pipeline tests)  
- **LLM Evaluation**: `tests/llm_evals/` (model performance tests)

### Running Tests
```bash
# Pre-commit tests (required)
uv run python -m pytest tests/on_commit/ -v

# Integration tests  
uv run python -m pytest tests/integration/ -v

# LLM evaluations
uv run python -m pytest tests/llm_evals/ -v
uv run python llm_evals/run_evals.py  # Standalone LLM eval runner
```

## Code Style Guidelines

### Python Standards
- **File Size**: Max 300-400 lines per file
- **Imports**: No re-exports, import directly from modules  
- **Types**: Use `Optional[T]`, prefer non-Optional fields
- **Naming**: `snake_case` variables, `PascalCase` classes, `UPPER_SNAKE_CASE` constants
- **Documentation**: Docstrings with param/return types, describe purpose not implementation

### Pydantic Best Practices
```python
# Good: Non-optional with default_factory
class MyModel(BaseModel):
    items: List[str] = Field(default_factory=list)
    
# Bad: Optional List/Dict
class MyModel(BaseModel):
    items: Optional[List[str]] = None
```

### CLI Command Pattern
```python
# CLI commands use hyphenated names
@click.command("create-effect-flags")
@click.option("--run-id", type=int, required=True)
def create_effect_flags(run_id: int):
    """Generate effect flags from statements."""
    pass
```

## Recent Architecture Changes

### Trace Data Separation Refactor (July 2025)

**CRITICAL ARCHITECTURAL PRINCIPLE**: Trace data must be completely separated from business model classes.

**Problem Solved**: Previous implementation had `trace_data` fields in Pydantic business models, violating separation of
concerns and causing JSON serialization issues.

**Solution Implemented**:

1. **Removed all `trace_data` fields** from business models (EffectModel, EffectFlagModel, FloodingModel,
   CherryPickingModel)
2. **Updated function signatures** to accept `trace_collector` as separate optional parameters
3. **Modified DAO methods** to accept trace data as separate parameters instead of accessing model fields
4. **Database storage** via JSONB columns with proper indexing

**Key Files Updated**:

- `effect_flags.py` - Function signatures updated to accept trace_collector parameters
- `effect_flags_helpers.py` - merge_similar_effect_flags() and merge_flag_group() updated
- `xfer.py` - convert_and_persist_xfer_flags() updated to accept trace_data_map parameter
- All model files - trace_data fields removed from business classes

**Verification**: All 99 unit tests pass, type checking passes, pre-commit hooks successful.

### Trace Collection System Migration (January 2025)

**Background**: Migrated from old `TraceabilityTracker` system to modern `TraceCollector` approach.

**Critical Issues Resolved**:

- **Root Cause**: "Tracker must be provided from the top level" error in `effects/utils.py:97`
- **Solution**: Removed tracker parameter requirement from `filter_valid_statements()` function
- **Impact**: Eliminated critical pipeline failures, maintained all tracking functionality

**Key Changes**:

- **New System**: `eko.analysis_v2.trace_collector.TraceCollector`
- **Storage**: JSONB `trace_json` columns in all analysis tables with GIN indexes
- **Integration**: Direct embedding in analysis functions, no parameter passing required
- **Quality Control**: Built-in anomaly detection (positive statements in red flags, etc.)

**Migration Pattern Applied**:

```python
# Before (Deprecated)
from eko.analysis_v2.pipeline_tracker_extended import get_traceability_tracker
def process_analysis(conn, run_id, tracker=None):
    if tracker:
        tracker.record_stat(entity, stage, count=results)

# After (Current)
from eko.analysis_v2.trace_collector import create_trace_collector
def process_analysis(conn, run_id):
    trace_collector = create_trace_collector(str(run_id), "analysis_stage")
    # Trace data automatically handled in analysis logic
```

**Files Updated**: `selective_highlighting/detection_pgvector.py`, `selective_highlighting/merging.py`,
`heart/trust_and_reliability/claim_evidence.py`, `heart/trust_and_reliability/promise_analysis.py`,
`effects/effect_flags.py`, `effects/utils.py` (critical fix), `prediction_v2/cli.py`

**Verification**: System self-test passes, 99 unit tests successful, no tracker references in active code.

### ScaleFactors Boolean Conversion
- **Backend**: Converted from 0-100 numeric scales to boolean yes/no questions for better LLM accuracy
- **Location**: `analysis_v2/effects/impact/models/scale_factors.py`
- **Impact**: Internal scale factor display changed, but main impact scores remain percentages
- **Frontend Sync**: Frontend types updated to match boolean structure

### Impact Measurement Pipeline
- **Service**: `analysis_v2/effects/impact/service.py` - Main measurement coordinator
- **LLM Review**: Automated bias detection and accuracy checking
- **Calculator**: `analysis_v2/effects/impact/calculator.py` - Score computation
- **Validation**: `analysis_v2/effects/impact/validation.py` - Score limits and caps

## Development Environment

### Dependencies
- **Python**: 3.11+ (managed with `uv`)
- **Key Libraries**: FastAPI, Pydantic, spaCy, PyTorch, LiteLLM, Loguru
- **ML Models**: Transformers, sentence-transformers, spaCy English model
- **Databases**: PostgreSQL with pgvector extension

### Environment Setup
```bash
# Install dependencies
uv sync --dev

# Download ML models  
uv run python -m spacy download en_core_web_sm

# Install Playwright for testing
uv run playwright install

# Verify setup
uv run python main.py claude-self-test
```
