-- Agent monitoring tables for CrewAI dashboard
-- These tables track all agent activities, LLM calls, and tool usage for monitoring

-- Main event tracking table for all agent actions
CREATE TABLE IF NOT EXISTS agent_execution_events (
    id SERIAL PRIMARY KEY,
    session_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    event_type TEXT NOT NULL, -- 'task_start', 'task_complete', 'tool_call', 'llm_call', 'decision', 'error'
    event_timestamp TIMESTAMP DEFAULT NOW(),
    event_data JSONB, -- All detailed event info
    task_name TEXT,
    tool_name TEXT,
    run_id INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- LLM call tracking with cost and performance metrics
CREATE TABLE IF NOT EXISTS agent_llm_calls (
    id SERIAL PRIMARY KEY,
    session_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    model_name TEXT,
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    cost_usd DECIMAL(10,4),
    call_timestamp TIMESTAMP DEFAULT NOW(),
    request_data JSONB, -- Prompt, parameters
    response_data JSONB, -- Response content
    duration_ms INTEGER,
    run_id INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Tool usage tracking for monitoring agent tool interactions
CREATE TABLE IF NOT EXISTS agent_tool_usage (
    id SERIAL PRIMARY KEY,
    session_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    tool_name TEXT NOT NULL,
    tool_input JSONB,
    tool_output JSONB,
    success BOOLEAN,
    error_message TEXT,
    call_timestamp TIMESTAMP DEFAULT NOW(),
    duration_ms INTEGER,
    run_id INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_agent_execution_events_session_id ON agent_execution_events(session_id);
CREATE INDEX IF NOT EXISTS idx_agent_execution_events_timestamp ON agent_execution_events(event_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_agent_execution_events_event_type ON agent_execution_events(event_type);
CREATE INDEX IF NOT EXISTS idx_agent_execution_events_agent_name ON agent_execution_events(agent_name);

CREATE INDEX IF NOT EXISTS idx_agent_llm_calls_session_id ON agent_llm_calls(session_id);
CREATE INDEX IF NOT EXISTS idx_agent_llm_calls_timestamp ON agent_llm_calls(call_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_agent_llm_calls_agent_name ON agent_llm_calls(agent_name);

CREATE INDEX IF NOT EXISTS idx_agent_tool_usage_session_id ON agent_tool_usage(session_id);
CREATE INDEX IF NOT EXISTS idx_agent_tool_usage_timestamp ON agent_tool_usage(call_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_agent_tool_usage_tool_name ON agent_tool_usage(tool_name);
CREATE INDEX IF NOT EXISTS idx_agent_tool_usage_agent_name ON agent_tool_usage(agent_name);

-- JSONB GIN indexes for fast JSON queries
CREATE INDEX IF NOT EXISTS idx_agent_execution_events_data ON agent_execution_events USING GIN (event_data);
CREATE INDEX IF NOT EXISTS idx_agent_llm_calls_request_data ON agent_llm_calls USING GIN (request_data);
CREATE INDEX IF NOT EXISTS idx_agent_llm_calls_response_data ON agent_llm_calls USING GIN (response_data);
CREATE INDEX IF NOT EXISTS idx_agent_tool_usage_input ON agent_tool_usage USING GIN (tool_input);
CREATE INDEX IF NOT EXISTS idx_agent_tool_usage_output ON agent_tool_usage USING GIN (tool_output);