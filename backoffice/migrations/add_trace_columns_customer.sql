-- Migration: Add trace_json column to customer database tables
-- Purpose: Store trace data for customer UI access
-- Date: 2025-06-30

-- Add trace_json column to xfer_flags
ALTER TABLE xfer_flags
    ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_xfer_flags_trace ON xfer_flags USING GIN (trace_json);

-- Add comment for documentation
COMMENT ON COLUMN xfer_flags.trace_json IS 'Trace data from analytics pipeline for admin debugging and analysis';
