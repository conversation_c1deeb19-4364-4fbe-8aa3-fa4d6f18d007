-- Migration: Add trace_json columns to analytics tables
-- Purpose: Store comprehensive trace data for flag creation pipeline
-- Date: 2025-06-30

-- Add trace_json column to ana_effects
ALTER TABLE ana_effects ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- Add trace_json column to ana_effect_flags  
ALTER TABLE ana_effect_flags ADD COLUMN IF NOT EXISTS trace_json JSONB;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ana_effects_trace ON ana_effects USING GIN (trace_json);
CREATE INDEX IF NOT EXISTS idx_ana_effect_flags_trace ON ana_effect_flags USING GIN (trace_json);

-- Add comments for documentation
COMMENT ON COLUMN ana_effects.trace_json IS 'Comprehensive trace data for effect creation pipeline including source statements, processing steps, and quality metrics';
COMMENT ON COLUMN ana_effect_flags.trace_json IS 'Comprehensive trace data for flag creation pipeline including effect sources, LLM calls, and relationship data';