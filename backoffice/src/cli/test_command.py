from typing import Optional

import click
from loguru import logger
from tabulate import tabulate

from eko.db import get_bo_conn
from eko.db.data.statement import StatementData
from eko.entities.queries import get_entity_by_short_id


@click.group()
def test():
    """Test commands for various features."""
    pass


@test.command()
@click.option('--statement-id', required=False, type=int, help="The specific statement ID to fix (optional)")
@click.option('--entity', required=False, help="The short id of an entity to fix all statements for (optional)")
@click.option('--fix', is_flag=True, help="Actually update the database with corrected impact values")
def fix_impact_values(statement_id: Optional[int], entity: Optional[str], fix: bool):
    """Fix inconsistent impact values in the database."""
    with get_bo_conn() as conn:
        statement_ids = []

        # Get statements to process
        if statement_id:
            statement_ids = [statement_id]
        elif entity:
            entity_obj = get_entity_by_short_id(entity)
            if not entity_obj:
                logger.error(f"Entity with short ID '{entity}' not found")
                return

            # Get all statements for the entity
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    WITH canonical_entities AS (
                        SELECT DISTINCT id
                        FROM kg_base_entities can
                        WHERE canonical_id = %s
                        OR id = %s
                    )
                    SELECT DISTINCT s.id
                    FROM kg_statements s
                    JOIN canonical_entities ce ON
                        (s.subject_entities IS NOT NULL AND ce.id = ANY(s.subject_entities)) OR
                        (s.object_entities IS NOT NULL AND ce.id = ANY(s.object_entities)) OR
                        (s.company_id IS NOT NULL AND ce.id = s.company_id)
                    ORDER BY s.id
                """,
                    (entity_obj.id, entity_obj.id),
                )

                statement_ids = [row[0] for row in cursor.fetchall()]
        else:
            logger.error("You must specify either a statement ID or an entity")
            return

        if not statement_ids:
            logger.info("No statements found to process")
            return

        logger.info(f"Processing {len(statement_ids)} statements")

        # Process each statement
        fixed_count = 0
        for stmt_id in statement_ids:
            try:
                # Get the statement
                statement = StatementData.get_by_id(conn, stmt_id)
                if not statement or not statement.demise:
                    continue

                # Check impact value
                stored_impact = statement.metadata.impact_value
                calculated_impact = statement.demise.impact.get_impact_value()

                if abs(stored_impact - calculated_impact) >= 0.001:
                    if fix:
                        # Update the impact value in the database
                        with conn.cursor() as cursor:
                            cursor.execute("""
                                UPDATE kg_statements
                                SET impact_value = %s
                                WHERE id = %s
                            """, (calculated_impact, stmt_id))

                        logger.info(f"Fixed statement {stmt_id}: {stored_impact:.5f} -> {calculated_impact:.5f}")
                        fixed_count += 1
                    else:
                        # Just report the issue
                        logger.info(f"Would fix statement {stmt_id}: {stored_impact:.5f} -> {calculated_impact:.5f}")
                        fixed_count += 1
            except Exception as e:
                logger.error(f"Error processing statement {stmt_id}: {str(e)}")

        if fix:
            conn.commit()
            logger.info(f"Fixed {fixed_count} statements")
        else:
            logger.info(f"Would fix {fixed_count} statements. Run with --fix to actually update the database.")



@test.command(name="debug-flow")
@click.option("--mode", type=click.Choice(['basic', 'full', 'data-only']), default='full',
              help="Debug mode: basic (simple test data), full (complex mock data), data-only (check existing data)")
@click.option("--entity", default=None, help="Entity short ID (only needed for data-only mode)")
@click.option("--keep-alive", is_flag=True, help="Keep the web server running until Ctrl+C is pressed")
def debug_process_flow(mode, entity, keep_alive):
    """
    Comprehensive debugging for the process flow visualization.

    This command combines various debugging tools to help diagnose and fix
    issues with the process flow visualization. It can:

    1. Generate a simple test visualization with minimal test data
    2. Generate a complete test visualization with complex mock data
    3. Analyze existing data for a specific entity

    Use this command when the process flow visualization is not working correctly.
    """
    if mode == 'basic':
        # Use the simple debug tool
        from cli.debug_process_flow import debug_process_flow_data
        debug_process_flow_data(keep_alive=keep_alive)

    elif mode == 'full':
        # Use the comprehensive test data
        from cli.debug_process_flow_standalone import generate_test_report
        generate_test_report(keep_alive=keep_alive)

    elif mode == 'data-only':
        # Check the data for an existing entity
        if not entity:
            print("Error: --entity parameter is required for data-only mode")
            return

        from cli.check_viz_data import check_viz_data
        check_viz_data(entity)

        # Also show how to run the standalone test
        print("\nTo run the standalone process flow visualization test with working mock data, use:")
        print("python -m cli.test_command debug-flow --mode full")


@test.command(name="function-calling")
@click.option("--model", default="gpt-4o", help="Model name to test with")
@click.option("--no-cache", is_flag=True, help="Bypass the cache")
@click.option("--max-iterations", default=3, help="Maximum tool iteration count")
def test_function_calling(model: str, no_cache: bool, max_iterations: int):
    """
    Test the function calling (tools) implementation

    This command tests the call_llms_tools function with a sample
    function/tool definition to ensure it works correctly with different
    LLM providers. It will attempt to use the specified model to call
    a 'get_weather' function and will execute the full tool loop.
    """
    from typing import List, Dict
    from loguru import logger
    from eko.llm import LLMModel
    from eko.llm.main import call_llms_tools, ToolsError
    import random

    # Setup test messages
    messages: List[Dict] = [
        {"role": "system", "content": "You are a helpful assistant that can use tools when needed."},
        {"role": "user", "content": "What is the weather like in London right now? After that, also check the weather in New York."}
    ]

    # Function implementation for the weather tool
    def get_weather(location: str, unit: str = "celsius"):
        """
        Mock implementation of a weather lookup function
        """
        logger.info(f"Getting weather for {location} in {unit}")

        # Mock different weather conditions based on location
        weather_conditions = {
            "london": {
                "celsius": f"{random.randint(10, 20)}°C",
                "fahrenheit": f"{random.randint(50, 68)}°F",
                "condition": random.choice(["Cloudy", "Rainy", "Partly Cloudy"])
            },
            "new york": {
                "celsius": f"{random.randint(15, 28)}°C",
                "fahrenheit": f"{random.randint(60, 82)}°F",
                "condition": random.choice(["Sunny", "Clear", "Hot"])
            },
            "san francisco": {
                "celsius": f"{random.randint(12, 22)}°C",
                "fahrenheit": f"{random.randint(54, 72)}°F",
                "condition": random.choice(["Foggy", "Mild", "Windy"])
            }
        }

        # Default for any location not specifically defined
        default_weather = {
            "celsius": f"{random.randint(5, 30)}°C",
            "fahrenheit": f"{random.randint(40, 86)}°F",
            "condition": random.choice(["Clear", "Cloudy", "Rainy", "Snowy", "Windy"])
        }

        # Normalize the location name for lookup
        location_key = location.lower().strip()
        for key in weather_conditions:
            if key in location_key:
                location_key = key
                break

        # Get the weather for this location, or use default
        weather = weather_conditions.get(location_key, default_weather)

        # Return the mock weather data
        return {
            "location": location,
            "temperature": weather[unit.lower()] if unit.lower() in ["celsius", "fahrenheit"] else weather["celsius"],
            "condition": weather["condition"],
            "humidity": f"{random.randint(30, 90)}%",
            "wind": f"{random.randint(5, 30)} km/h"
        }

    # Define a simple weather tool for testing with implementation
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get the current weather in a given location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city and state, e.g. San Francisco, CA"
                        },
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "The temperature unit to use"
                        }
                    },
                    "required": ["location"]
                }
            },
            "implementation": get_weather
        }
    ]

    # Get the model from the name
    target_model = None
    for llm in LLMModel:
        if llm.value.name == model:
            target_model = llm
            break

    if not target_model:
        logger.error(f"Model {model} not found. Available models:")
        for llm in LLMModel:
            logger.info(f"- {llm.value.name}")
        return

    logger.info(f"Testing function calling with model {target_model.value.name}...")

    try:
        # Now the function will execute the full tool loop
        from eko.llm.main import LLMOptions
        options = LLMOptions(no_cache=no_cache)
        final_response = call_llms_tools(
            [target_model],
            messages,
            tools,
            options=options
        )

        # Print the final response
        logger.info("======== Final Response ========")
        if hasattr(final_response, "content"):
            logger.info(final_response.content)
        else:
            logger.info(str(final_response))

        logger.info("==============================")
        logger.info("Tool loop completed successfully!")

    except ToolsError as e:
        logger.error(f"Tool calling error: {e}")
    except Exception as e:
        logger.error(f"Error testing function calling: {e}")


if __name__ == "__main__":
    test()
