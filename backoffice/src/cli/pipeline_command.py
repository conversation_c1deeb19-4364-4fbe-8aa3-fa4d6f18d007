import click
from loguru import logger

from eko.db import get_dash_conn
from eko.analysis_v2.effects.pipeline_tracker import migrate_pipeline_data_to_dash_schema


@click.group()
def pipeline():
    """Pipeline-related commands."""
    pass


@pipeline.command()
def migrate_tracking_data():
    """
    Migrate pipeline tracking data from public schema to dash schema.
    
    This is a one-time operation needed after updating the pipeline tracker to use dash schema.
    """
    with get_dash_conn() as conn:
        logger.info("Starting migration of pipeline tracking data to dash schema...")
        success = migrate_pipeline_data_to_dash_schema(conn)
        
        if success:
            logger.success("Successfully migrated pipeline tracking data to dash schema")
        else:
            logger.error("Failed to migrate pipeline tracking data")
            
    return success