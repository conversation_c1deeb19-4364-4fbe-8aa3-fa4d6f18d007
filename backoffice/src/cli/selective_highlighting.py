"""
CLI commands for selective highlighting detection.
"""
import os

import click
from loguru import logger
from typing import Optional

from eko import eko_var_path
from eko.analysis_v2 import analyze_selective_highlighting

from eko.analysis_v2.selective_highlighting.main import analyze_selective_highlighting
from eko.analysis_v2.selective_highlighting.report import generate_report
from eko.db import get_bo_conn
from eko.db.data.run import RunData
from eko.db.data.xfer import XferData
from eko.typing import not_none


@click.group()
def cherry():
    """Cherry picking and selective highlighting detection commands."""
    pass


@cherry.command("analyse")
@click.option("--run-id", type=int,
              help="Name of the virtual entity to analyze")
@click.option("--entity-id", "-e", type=int, default=None,
              help="Specific virtual entity ID to analyze")
@click.option("--entity", type=str, default=None,
              help="Name of the virtual entity to analyze")
@click.option("--cherry-picking/--no-cherry-picking", default=True,
              help="Detect cherry picking")
@click.option("--flooding/--no-flooding", default=True,
              help="Detect flooding")
@click.option("--dry-run", is_flag=True, default=False,
              help="Don't save results to database")
@click.option("--report", is_flag=True, default=False,
              help="Generate human-readable report to stdout")
def analyze_selective_highlighting_command(
    run_id: int,
    entity_id: Optional[int] = None,
    entity: Optional[str] = None,
    cherry_picking: bool = True,
    flooding: bool = True,
    dry_run: bool = False,
    report: bool = False
):
    """
    Analyze selective highlighting (cherry picking and flooding) for virtual entities.

    Uses optimized clustering algorithms for improved performance.
    """
    # If entity name is provided, get the entity ID
    resolved_entity_id = entity_id

    if entity and not entity_id:
        from eko.entities.virtual_queries import get_virtual_entity_by_name
        virt_entity = get_virtual_entity_by_name(entity)
        if virt_entity:
            resolved_entity_id = virt_entity.id
            logger.info(f"Resolved entity name '{entity}' to ID {resolved_entity_id}")
        else:
            logger.error(f"Could not find virtual entity with name '{entity}'")
            return
    elif not entity and not entity_id:
        logger.error("Either --entity-id or --entity must be provided.")
        return

    entity_str = f"entity {resolved_entity_id}" if resolved_entity_id else "all entities"
    if entity:
        entity_str = f"entity '{entity}' (ID: {resolved_entity_id})"

    logger.info(f"Starting selective highlighting analysis for {entity_str}")
    logger.info(f"Cherry picking detection: {'enabled' if cherry_picking else 'disabled'}")
    logger.info(f"Flooding detection: {'enabled' if flooding else 'disabled'}")
    logger.info(f"Dry run: {'enabled' if dry_run else 'disabled'}")
    logger.info(f"Report generation: {'enabled' if report else 'disabled'}")

    with get_bo_conn() as conn:
        run_model=RunData.get_by_id(conn, run_id)
    # Run the analysis
    cherry_picking_results, flooding_results = analyze_selective_highlighting(
        run_model=not_none(run_model),
        virt_entity_id=resolved_entity_id,
        cherry_picking=cherry_picking,
        flooding=flooding,
        save_results=(not dry_run)
    )

    XferData.sync_cherry_to_xfer_v2(conn, run_id)

    cp_count = len(cherry_picking_results)
    flooding_count = len(flooding_results)

    logger.info(f"Analysis complete:")
    logger.info(f"  - Cherry picking instances: {cp_count}")
    logger.info(f"  - Flooding instances: {flooding_count}")

    if dry_run:
        logger.info("Dry run complete. No results saved.")

    # Generate report if requested
    if report:
        report_text = generate_report(cherry_picking_results, flooding_results)
        print(report_text)


def run_selective_highlighting(end_year, entity, expanded_entity, run, start_year):
    # Cherry Picking and Flooding analysis with report
    cherry_picking_results, flooding_results = analyze_selective_highlighting(
        run_model=run, virtual_entity=expanded_entity, cherry_picking=True, flooding=True, save_results=True
    )
    cp_count = len(cherry_picking_results)
    flooding_count = len(flooding_results)
    logger.info(f"Analysis complete:")
    logger.info(f"  - Cherry picking instances: {cp_count}")
    logger.info(f"  - Flooding instances: {flooding_count}")
    # Generate selective highlighting report
    report_text = generate_report(cherry_picking_results, flooding_results)
    # Save the report to a file
    report_dir = os.path.join(eko_var_path, "reports")
    os.makedirs(report_dir, exist_ok=True)
    report_path = os.path.join(report_dir,
                               f"selective_highlighting_report_{entity.replace(' ', '_').lower()}_{start_year}_{end_year}.txt")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(report_text)
    logger.info(f"Selective highlighting report saved to: {report_path}")
