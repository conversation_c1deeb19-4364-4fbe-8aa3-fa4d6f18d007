from datetime import datetime
from typing import Optional

import click
from loguru import logger

from eko.analysis_v2.effects.clustering import ClusteringMethod
from eko.analysis_v2.effects.flag_main import create_effect_flags as create_viz
from eko.entities.queries import get_entities_fuzzy


# Function to map string clustering method to enum
def _get_clustering_method_enum(clustering_method: str) -> ClusteringMethod:
    method_map = {
        "dbscan": ClusteringMethod.DBSCAN,
        "kmeans": ClusteringMethod.KMEANS,
        "spectral": ClusteringMethod.SPECTRAL,
        "meanshift": ClusteringMethod.MEANSHIFT,
        "xmeans": ClusteringMethod.XMEANS,
        "affinity": ClusteringMethod.AFFINITY
    }
    return method_map.get(clustering_method, ClusteringMethod.DBSCAN)


@click.command(name="create-flags")
@click.option('--entity', required=True, help="The name of the entity to analyze")
@click.option('--clustering-method', default="dbscan",
              type=click.Choice(["dbscan", "kmeans", "spectral", "meanshift", "xmeans", "affinity"]),
              help="Clustering method to use (default: dbscan)")
@click.option('--start-year', default=datetime.now().year - 5, help="Start year for statement analysis")
@click.option('--end-year', default=datetime.now().year + 1, help="End year for statement analysis")
@click.option('--run-id', default=None, type=int,
              help="Specify an existing run ID to use, or leave empty to create a new run")
@click.option('--debug', is_flag=True, help="Show additional debugging information")
@click.option('--eps', default=2.2, type=float,
              help="Epsilon parameter for DBSCAN and distance threshold for other methods (default 2.2)")
@click.option('--min-samples', default=2, type=int,
              help="Minimum samples parameter for DBSCAN and k estimation for other methods (default 2)")
@click.option('--max-workers', default=4, type=int,
              help="Maximum number of worker threads for parallel processing (default: 4)")
@click.option('--viz', is_flag=True, help="Generate visualization (web interface)")
@click.option('--keep-alive', is_flag=True, help="When using --viz, keep the web server running until Ctrl+C is pressed")
def create_flags(entity: str, start_year: int, end_year: int, run_id: Optional[int],
                 clustering_method: str, debug: bool, eps: float, min_samples: int,
                 max_workers: int, viz: bool, keep_alive: bool):
    """Create effect flags with optional detailed visualization and information."""
    # Get the entities from the entity name
    entities = list(get_entities_fuzzy(entity, False))
    logger.info(f"Found {len(entities)} entities for '{entity}'")
    if not entities:
        logger.error(f"No entities found for '{entity}'")
        return

    # Convert the clustering method string to enum
    clustering_method_enum = _get_clustering_method_enum(clustering_method)


    # Get the virtual entity for the entities
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return

    result = create_viz(
        virtual_entity=expanded_entity,
        start_year=start_year,
        end_year=end_year,
        run_id=run_id,
        max_workers=max_workers
    )

    if result:
        logger.info(f"Successfully created {result['num_effects']} effects and {result['num_flags']} flags")
        if 'server_url' in result:
            logger.info(f"Report available at: {result['server_url']}")
            if keep_alive:
                logger.info("Server will stay running until Ctrl+C is pressed")
                # Block the main thread until a keyboard interrupt
                try:
                    import time
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("Server stopped by user")
            else:
                logger.info("Press Ctrl+C to stop the server when done")
                logger.info("Use --keep-alive to keep the server running until explicitly stopped")
        else:
            logger.info(f"Report saved to {result['report_path']}")
    else:
        logger.error("Failed to create effect flags visualization")


# Import visualization libraries only when needed
def _import_viz_libs():
    # This is a placeholder - in the actual code, this would import necessary visualization libraries
    return True