from typing import Optional
from urllib.parse import urlparse

import click
from loguru import logger
from psycopg.rows import dict_row

from eko.commands.fix import fix_doc_hash, fix_xfer, fix_if_embeddings_command, fix_metadata_command, fix_issues_command
from eko.db import get_bo_conn
from eko.domains.domain_categorizer import get_domain_info
from eko.entities import CompanyEntityData
from eko.entities.co_entity import CompanyEntityProcessor
from eko.llm.main import get_embedding
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.scrape.eko_reports import add_authors_to_document


@click.group(name="fix")
def fix():
    """Commands to fix various issues in the database"""
    pass


@fix.command()
def doc_hash():
    """Fix document hashes"""
    fix_doc_hash()


@fix.command()
def authors():
    """Fix document authors"""
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT id, authors FROM kg_documents WHERE authors IS NOT NULL")
            for document_id, authors in cur.fetchall():
                add_authors_to_document(cur, document_id, authors)


@fix.command()
def doc_metadata():
    """Fix document metadata"""
    fix_metadata_command()


@fix.command()
def issue_mapping():
    """Fix issue mappings"""
    fix_issues_command()


@fix.command()
def doc_ratings():
    """Fix document ratings based on domain credibility"""
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            no_matches = set()
            cur.execute("SELECT id, public_url FROM kg_documents WHERE status != 'deleted'")
            count = 0
            for id, url in cur.fetchall():
                count = count + 1
                if url is None:
                    continue
                url_domain = urlparse(url).netloc
                longest_domain = None
                for domain in domain_credibility_map.keys():
                    if str(url_domain).endswith(f".{domain}") or domain == url_domain:
                        if longest_domain is None or len(domain) > len(longest_domain):
                            longest_domain = domain

                if longest_domain is not None:
                    cur.execute("UPDATE kg_documents SET credibility = %s WHERE id = %s",
                                (domain_credibility_map[longest_domain], id))
                    logger.info(f"{count}: Updating credibility for {url} to {domain_credibility_map[longest_domain]}")
                    conn.commit()
                elif url.startswith("https://s3.eu-west-2.amazonaws.com/eko-mvp-reports/"):
                    cur.execute("UPDATE kg_documents SET credibility = %s WHERE id = %s", (4, id))
                    logger.info(f"{count}: Updating credibility for {url} to 4")
                    conn.commit()
                else:
                    if url_domain not in no_matches:
                        logger.info(f"No matching domain for {url_domain}")
                        no_matches.add(url_domain)

            logger.info(f"No matches for: {no_matches}")
            for url_domain in no_matches:
                print(f'"{url_domain}"', ",")


@fix.command()
def origin_domains():
    """Fix origin domains for documents"""
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT id, public_url FROM kg_documents WHERE status != 'deleted' and origin_domain is null")
            count = 0
            for domain_id, url in cur.fetchall():
                count = count + 1
                if url is None:
                    continue
                domain = urlparse(url).netloc
                if domain is None or len(domain) == 0:
                    continue
                try:
                    domain_info = get_domain_info(domain)
                    if domain_info is None:
                        logger.warning(f"No domain info for {domain}")
                        continue
                    url_domain = domain_info.domain
                except Exception as e:
                    logger.error(f"Error getting domain info for {url}: {str(e)}")
                    logger.exception(e)
                    continue
                cur.execute("UPDATE kg_documents SET origin_domain = %s WHERE id = %s", (url_domain, domain_id))
                logger.info(f"Updating origin domain for {url} as {url_domain}")
                conn.commit()
            logger.info(f"Updated {count} documents")


@fix.command()
def lei():
    """Fix LEI for entities"""
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute(
                """
                    SELECT id, name, type, jurisdiction
                    FROM kg_base_entities
                    WHERE type IN ('company','organisation')
                    ORDER BY id
                    """
            )

            # 2. Create a reusable processor.
            processor = CompanyEntityProcessor(cur)

            # 3. Prepare the same 'previous_entity_ids' set if you still need it globally.
            previous_entity_ids = set()

            # 4. Iterate over the fetched rows and call the new OO method.
            for entity_id, entity_name, entity_type, jurisdiction in cur.fetchall():
                try:
                    # Build a CompanyEntityData object for each row
                    data = CompanyEntityData(entity_id=entity_id, name=entity_name, entity_type=entity_type,
                                             strict=False,
                                             jurisdiction=jurisdiction, previous_names=[],
                                             previous_entity_ids=previous_entity_ids,
                                             )

                    # Use the OO processor
                    success = processor.process_entity(data)
                    if success:
                        logger.info(f"Successfully processed {entity_name} with LEI!")
                    else:
                        logger.info(f"No LEI match found for {entity_name}")

                except Exception as e:
                    logger.error(f"Error looking up LEI for {entity_name}: {str(e)}")
                    logger.exception(e)
                    conn.rollback()
                    continue

                # Commit after each iteration to save updates
                conn.commit()
            cur.execute("""
            WITH prioritized AS (
                SELECT
                    id,
                    canonical_eko_id,
                    lei,
                    canonical,
                    legal_name,
                    lei_exact,
                    -- Assign a numeric priority: 1 if canonical, 2 if lei_exact, 3 otherwise
                    CASE
                        WHEN canonical THEN 1
                        WHEN lei_exact THEN 2
                        WHEN lei is not null THEN 3
                        ELSE 4
                        END AS priority,
                    -- Rank rows within each LEI group by priority (and a tie-breaker if needed)
                    ROW_NUMBER() OVER (
                        PARTITION BY canonical_eko_id
                        ORDER BY
                            CASE
                                WHEN canonical THEN 1
                                WHEN lei_exact THEN 2
                                WHEN lei is not null THEN 3
                                ELSE 4
                                END,
                            id  -- tie-breaker if needed
                        ) AS rn
                FROM kg_base_entities
                WHERE canonical_eko_id IS NOT NULL
            )
            UPDATE kg_base_entities
            SET canonical = TRUE
            WHERE id IN (
                SELECT id
                FROM prioritized
                WHERE rn = 1
            );

""")
            cur.execute("""

            WITH cte_canonical AS (
                SELECT id AS canon_id,
                       canonical_eko_id,
                       legal_name
                FROM kg_base_entities
                WHERE canonical = TRUE
                  AND legal_name IS NOT NULL
            )

            UPDATE kg_base_entities AS x
            SET canonical_id = c.canon_id
            FROM cte_canonical c
            WHERE x.canonical_eko_id = c.canonical_eko_id
              AND x.legal_name IS NOT NULL;




""")

            cur.execute("""

            WITH cte_canonical AS (
                SELECT id,
                       canonical_eko_id,
                       legal_name
                FROM kg_base_entities
                WHERE canonical = TRUE
                  AND legal_name IS NOT NULL
            )

            UPDATE kg_entity_relations_map AS x
            SET canonical = TRUE
            FROM cte_canonical c
            WHERE x.from_entity_id in(select id from cte_canonical)
              AND x.to_entity_id in(select id from cte_canonical)




""")
            conn.commit()


@fix.command()
def if_embeddings():
    """Fix if embeddings"""
    fix_if_embeddings_command()


@fix.command()
@click.option('--run-id', default=None, help="The run to take evidence from")
def xfer(run_id: int):
    """Fix xfer data"""
    fix_xfer(run_id)


@fix.command(name="claim-importance")
@click.option("--batch-size", default=50, help="Batch size for processing claims")
@click.option("--limit", default=None, type=int, help="Maximum number of claims to process")
@click.option("--run-id", default=None, type=int, help="Specific run ID to process (processes all runs if not specified)")
@click.option("--force", is_flag=True, help="Force update even if importance is already set")
def fix_claim_importance(batch_size: int, limit: int, run_id: int, force: bool):
    """
    Update ana_claims entries with importance scores using LLM scoring.

    This command will:
    1. Find claims with importance = 0 (or all claims if --force is used)
    2. Score their importance using the LLM scoring function
    3. Update the database with the new importance scores
    4. Sync the updated claims to the customer database
    """
    from eko.analysis_v2.heart.trust_and_reliability.claim_evidence import score_claim_importance
    from eko.db.data.claim import ClaimData
    from eko.db.data.xfer import XferData
    from eko.db import get_bo_conn
    from concurrent.futures import ThreadPoolExecutor, as_completed
    import time

    logger.info(f"Fixing claim importance scores with batch size {batch_size}")

    with get_bo_conn() as conn:
        # Build the query to find claims that need importance scoring
        where_conditions = []
        params = []

        if not force:
            where_conditions.append("importance = 0")

        if run_id is not None:
            where_conditions.append("run_id = %s")
            params.append(run_id)

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # Get claims that need importance scoring
        with conn.cursor() as cursor:
            query = f"""
                SELECT id, text, company, context, run_id
                FROM ana_claims
                WHERE {where_clause}
                ORDER BY id
            """

            if limit:
                query += f" LIMIT {limit}"

            cursor.execute(query, params)
            claims_to_process = cursor.fetchall()

        logger.info(f"Found {len(claims_to_process)} claims to process")

        if not claims_to_process:
            logger.info("No claims found that need importance scoring")
            return

        # Process claims in batches
        total_processed = 0
        total_updated = 0
        run_ids_to_sync = set()

        for i in range(0, len(claims_to_process), batch_size):
            batch = claims_to_process[i:i + batch_size]
            logger.info(f"Processing batch {i // batch_size + 1}/{(len(claims_to_process) + batch_size - 1) // batch_size}")

            # Process batch with ThreadPoolExecutor for parallel LLM calls
            with ThreadPoolExecutor(max_workers=8) as executor:
                # Submit scoring tasks
                future_to_claim = {}
                for claim_id, text, company, context, claim_run_id in batch:
                    future = executor.submit(
                        score_claim_importance,
                        text,
                        company,
                        context or ""
                    )
                    future_to_claim[future] = (claim_id, claim_run_id)

                # Collect results and update database
                batch_updates = []
                for future in as_completed(future_to_claim):
                    claim_id, claim_run_id = future_to_claim[future]
                    try:
                        importance_score = future.result()
                        batch_updates.append((importance_score, claim_id))
                        run_ids_to_sync.add(claim_run_id)
                        logger.debug(f"Claim {claim_id}: importance score {importance_score}")
                    except Exception as e:
                        logger.error(f"Error scoring claim {claim_id}: {e}")
                        # Use default score of 50 for failed scoring
                        batch_updates.append((50, claim_id))
                        run_ids_to_sync.add(claim_run_id)

            # Update database with batch results
            if batch_updates:
                with conn.cursor() as cursor:
                    cursor.executemany(
                        "UPDATE ana_claims SET importance = %s WHERE id = %s",
                        batch_updates
                    )
                    conn.commit()
                    total_updated += len(batch_updates)

            total_processed += len(batch)
            logger.info(f"Processed {total_processed}/{len(claims_to_process)} claims, updated {total_updated}")

            # Small delay to avoid overwhelming the LLM API
            time.sleep(1)

        logger.info(f"Completed processing {total_processed} claims, updated {total_updated} with importance scores")

        # Sync updated claims to customer database
        if run_ids_to_sync:
            logger.info(f"Syncing {len(run_ids_to_sync)} runs to customer database...")
            for sync_run_id in run_ids_to_sync:
                try:
                    synced_claim_ids = XferData.sync_claims_to_xfer_v2(conn, sync_run_id)
                    logger.info(f"Synced {len(synced_claim_ids)} claims for run {sync_run_id}")
                except Exception as e:
                    logger.error(f"Error syncing run {sync_run_id}: {e}")

            logger.info("Completed syncing claims to customer database")


@fix.command(name="statement-embeddings")
@click.option("--batch-size", default=100, help="Batch size for processing statements")
@click.option("--limit", default=None, type=int, help="Maximum number of statements to process")
def fix_statement_embeddings(batch_size: int, limit: int):
    """
    Update kg_statements entries with demise_embedding and text_embedding vectors.

    demise_embedding is a 1024-dimensional vector created from DEMISEModel.to_fixed_size_vector().
    text_embedding is a 1536-dimensional vector created from get_text_embedding().
    """
    logger.info(f"Fixing statement embeddings with batch size {batch_size}")

    with get_bo_conn() as conn:
        with conn.cursor(row_factory=dict_row) as cur:
            # First, get the count of statements that need to be updated
            cur.execute("""
                SELECT COUNT(*) as count
                FROM kg_statements
                WHERE (demise_embedding IS NULL OR text_embedding IS NULL)
                AND model_json IS NOT NULL
                AND model_json != '{}'::jsonb
            """)

            total_count = cur.fetchone()['count']

            if limit is not None and limit < total_count:
                total_count = limit

            logger.info(f"Found {total_count} statements that need embeddings")

            # Process statements in batches
            offset = 0
            successful = 0
            failed = 0

            while offset < total_count:
                batch_limit = min(batch_size, total_count - offset)

                # Get statements that need embeddings
                cur.execute("""
                    SELECT s.id, statement_text, model_json, kd.year
                    FROM kg_statements s JOIN public.kg_documents kd on s.doc_id = kd.id
                    WHERE (demise_embedding IS NULL OR text_embedding IS NULL)
                    AND model_json IS NOT NULL
                    AND model_json != '{}'::jsonb
                    ORDER BY id
                    LIMIT %s OFFSET %s
                """, (batch_limit, offset))

                statements = cur.fetchall()

                if not statements:
                    break

                logger.info(f"Processing batch of {len(statements)} statements (offset {offset})")

                for stmt in statements:
                    try:
                        stmt_id = stmt['id']
                        stmt_text = stmt['statement_text']
                        model_json = stmt['model_json']
                        year = stmt['year']

                        # Generate embeddings
                        try:
                            # DEMISE embedding from model_json
                            demise_model = DEMISEModel.from_sparse_kv(model_json)
                            # Use to_fixed_size_vector to ensure demise_embedding is 1024 dimensions
                            demise_vector = demise_model.to_fixed_size_vector(1024)
                            effect_vector = demise_model.get_fixed_size_effect_vector(1024)
                            domain_vector = demise_model.domain.to_fixed_size_vector(1024)
                            # Calculate impact_value from the DEMISE model
                            impact_value = demise_model.get_impact_value() if demise_model else 0.0

                            # Text embedding from statement_text
                            text_vector = get_embedding(stmt_text)

                            if demise_vector and text_vector:
                                # Update the statement with both embeddings
                                cur.execute("""
                                    UPDATE kg_statements
                                    SET demise_embedding = %s::vector, text_embedding = %s::vector, domain_embedding = %s::vector, effect_embedding = %s::vector, impact_value= %s, start_year= coalesce(start_year, %s), end_year= coalesce(end_year, %s)
                                    WHERE id = %s
                                """, (demise_vector, text_vector, effect_vector, domain_vector, impact_value, year, year, stmt_id))

                                conn.commit()
                                successful += 1

                                if successful % 10 == 0:
                                    logger.info(f"Successfully updated {successful} statements")
                        except Exception as e:
                            logger.error(f"Error generating embeddings for statement {stmt_id}: {str(e)}")
                            conn.rollback()
                            failed += 1

                    except Exception as e:
                        logger.error(f"Error processing statement: {str(e)}")
                        conn.rollback()
                        failed += 1

                offset += batch_limit

            logger.info(f"Embedding update complete. Total: {total_count}, Successful: {successful}, Failed: {failed}")


@fix.command(name="reextract-statements")
@click.option("--batch-size", default=10, help="Batch size of documents to process")
@click.option("--limit", default=None, type=int, help="Maximum number of documents to process")
@click.option("--doc-id", default=None, type=int, help="Specific document ID to process (overrides limit)")
@click.option("--max-workers", default=4, help="Maximum number of parallel workers")
@click.option("--search", default=None, help="Optional search string.")
@click.option("--cheap", default=False, help="Save money")
def reextract_statements(batch_size: int, limit: int, doc_id: int, max_workers: int, search:Optional[str], cheap:bool):
    """
    Re-extracts statements from documents in the database using parallel processing.
    """
    logger.info(f"Re-extracting statements with batch size {batch_size} and {max_workers} workers")

    def process_document(doc_id):
        try:
            logger.info(f"Re-extracting statements for document {doc_id}")

            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    # Delete existing statements for this document
                    cur.execute("""
                        DELETE FROM kg_statements
                        WHERE doc_id = %s
                    """, (doc_id,))
                    conn.commit()

            # Re-extract statements
            from eko.statements.extract import extract_statements_from_doc
            extract_statements_from_doc(doc_id, search, cheap, True)

            logger.info(f"Successfully re-extracted statements for document {doc_id}")
            return True, doc_id

        except Exception as e:
            logger.exception(f"Error re-extracting statements for document {doc_id}: {e}")
            return False, doc_id

    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            if doc_id:
                cur.execute("""
                    SELECT id, title FROM kg_documents
                    WHERE id = %s AND status != 'deleted'
                """, (doc_id,))

                documents = cur.fetchall()
                if not documents:
                    logger.error(f"Document with ID {doc_id} not found or is deleted")
                    return

                total_count = 1
            else:
                if search is not None:
                    cur.execute("""
                        SELECT COUNT(DISTINCT doc_id) FROM kg_document_pages
                        WHERE status != 'deleted'
                        AND text_search_vector @@ websearch_to_tsquery('english', %s)
                    """, (search,))
                else:
                    cur.execute("""
                        SELECT COUNT(*) FROM kg_documents
                        WHERE status != 'deleted'
                    """)

                total_count = cur.fetchone()[0]

                if limit is not None and limit < total_count:
                    total_count = limit

                if search is not None:
                    cur.execute("""
                        SELECT DISTINCT doc_id FROM kg_document_pages
                        WHERE status != 'deleted'
                        AND text_search_vector @@ websearch_to_tsquery('english', %s)
                        ORDER BY doc_id
                        LIMIT %s
                    """, (search, total_count))
                else:
                    cur.execute("""
                        SELECT id FROM kg_documents
                        WHERE status != 'deleted'
                        ORDER BY hash
                        LIMIT %s
                    """, (total_count,))

                documents = cur.fetchall()

            logger.info(f"Found {len(documents)} documents to process")

            successful = 0
            failed = 0

            # Process in chunks of batch_size using ThreadPoolExecutor
            from concurrent.futures import ThreadPoolExecutor, as_completed

            for i in range(0, len(documents), batch_size):
                batch = documents[i:i+batch_size]
                logger.info(f"Processing batch of {len(batch)} documents (batch {i//batch_size + 1}/{(len(documents)+batch_size-1)//batch_size})")

                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_doc = {executor.submit(process_document, doc[0]): doc[0] for doc in batch}

                    for future in as_completed(future_to_doc):
                        success, doc_id = future.result()
                        if success:
                            successful += 1
                        else:
                            failed += 1

            logger.info(f"Statement re-extraction complete. Total documents: {len(documents)}, Successful: {successful}, Failed: {failed}")



# Domain credibility map - global variable needed for the doc_ratings function
domain_credibility_map = {
    "gov.uk": 5,
    "parliament.uk": 5,
    "nic.in": 5,
    "europa.eu": 5,
    "senate.gov": 5,
    "house.gov": 5,
    "un.org": 5,
    "who.int": 5,
    "worldbank.org": 5,
    "imf.org": 5,
    "oecd.org": 5,
    "nature.com": 5,
    "science.org": 5,
    "nasa.gov": 5,
    "noaa.gov": 5,
    "epa.gov": 5,
    "nih.gov": 5,
    "cdc.gov": 5,
    "fda.gov": 5,
    "nsf.gov": 5,
    "ed.gov": 5,
    "nytimes.com": 4,
    "washingtonpost.com": 4,
    "wsj.com": 4,
    "economist.com": 4,
    "ft.com": 4,
    "reuters.com": 4,
    "ap.org": 4,
    "bloomberg.com": 4,
    "bbc.co.uk": 4,
    "bbc.com": 4,
    "theguardian.com": 4,
    "cnn.com": 4,
    "forbes.com": 4,
    "nationalgeographic.com": 4,
    "scientificamerican.com": 4,
    "time.com": 4,
    "theatlantic.com": 4,
    "newyorker.com": 4,
    "pbs.org": 4,
    "npr.org": 4,
    "usatoday.com": 4,
    "latimes.com": 4,
    "chicagotribune.com": 4,
    "bostonglobe.com": 4,
    "independent.co.uk": 4,
    "telegraph.co.uk": 4,
    "thetimes.co.uk": 4,
    "politico.com": 4,
    "abc.net.au": 4,
    "cbc.ca": 4,
    "spiegel.de": 4,
    "lemonde.fr": 4,
    "eluniversal.com.mx": 4,
    "globo.com": 4,
    "aljazeera.com": 4,
    "nikkei.com": 4,
    "timeslive.co.za": 4,
    "news.com.au": 4,
    "theconversation.com": 4,
    "vox.com": 4,
    "slate.com": 4,
    "salon.com": 4,
    "thedailybeast.com": 4,
    "huffpost.com": 4,
    "huffingtonpost.com": 4,
    "buzzfeednews.com": 4,
    "vice.com": 4,
    "motherjones.com": 4,
    "fivethirtyeight.com": 4,
    "propublica.org": 4,
    "theintercept.com": 4,
    "thenation.com": 4,
    "rollingstone.com": 4,
    "thehill.com": 4,
    "axios.com": 4,
    "insider.com": 4,
    "businessinsider.com": 4,
    "marketwatch.com": 4,
    "barrons.com": 4,
    "cnbc.com": 4,
    "fortune.com": 4,
    "fastcompany.com": 4,
    "inc.com": 4,
    "hbr.org": 4,
    "harvard.edu": 5,
    "stanford.edu": 5,
    "mit.edu": 5,
    "caltech.edu": 5,
    "ox.ac.uk": 5,
    "cam.ac.uk": 5,
    "columbia.edu": 5,
    "princeton.edu": 5,
    "yale.edu": 5,
    "chicago.edu": 5,
    "berkeley.edu": 5,
    "cornell.edu": 5,
    "brown.edu": 5,
    "dartmouth.edu": 5,
    "upenn.edu": 5,
    "lse.ac.uk": 5,
    "ucl.ac.uk": 5,
    "imperial.ac.uk": 5,
    "ethz.ch": 5,
    "utoronto.ca": 5,
    "mcgill.ca": 5,
    "ubc.ca": 5,
    "anu.edu.au": 5,
    "unsw.edu.au": 5,
    "unimelb.edu.au": 5,
    "tsinghua.edu.cn": 5,
    "u-tokyo.ac.jp": 5,
    "nus.edu.sg": 5,
    "duke.edu": 5,
    "ucla.edu": 5,
    "umich.edu": 5,
    "wisc.edu": 5,
    "umn.edu": 5,
    "umd.edu": 5,
    "psu.edu": 5,
    "washington.edu": 5,
    "purdue.edu": 5,
    "gatech.edu": 5,
    "utexas.edu": 5,
    "ufl.edu": 5,
    "osu.edu": 5,
    "msu.edu": 5,
    "ucsd.edu": 5,
    "uci.edu": 5,
    "ucdavis.edu": 5,
    "nyu.edu": 5,
    "northwestern.edu": 5,
    "vanderbilt.edu": 5,
    "jhu.edu": 5,
    "wustl.edu": 5,
    "rice.edu": 5,
    "emory.edu": 5,
    "cmu.edu": 5,
    "georgetown.edu": 5,
    "bc.edu": 5,
    "nd.edu": 5,
    "usc.edu": 5,
    "tufts.edu": 5,
    "bu.edu": 5,
    "rochester.edu": 5,
    "virginia.edu": 5,
    "unc.edu": 5,
    "illinois.edu": 5,
    "pitt.edu": 5,
    "utah.edu": 5,
    "arizona.edu": 5,
    "ucsb.edu": 5,
    "ucr.edu": 5,
    "uconn.edu": 5,
    "rutgers.edu": 5,
    "iastate.edu": 5,
    "hawaii.edu": 5,
    "byu.edu": 5,
    "colorado.edu": 5,
    "ku.edu": 5,
    "fsu.edu": 5,
    "uga.edu": 5,
    "uw.edu": 5,
    "wsu.edu": 5,
    "oregonstate.edu": 5,
    "unl.edu": 5,
    "uiowa.edu": 5,
    "ou.edu": 5,
    "uky.edu": 5,
    "clemson.edu": 5,
    "auburn.edu": 5,
    "vt.edu": 5,
    "ncsu.edu": 5,
    "uvm.edu": 5,
    "umass.edu": 5,
    "buffalo.edu": 5,
    "stonybrook.edu": 5,
    "asu.edu": 5,
    "tamu.edu": 5,
    "law.com": 4,
    "scotusblog.com": 4,
    "lawfareblog.com": 4,
    "justsecurity.org": 4,
    "eff.org": 4,
    "aclu.org": 4,
    "amnesty.org": 4,
    "hrw.org": 4,
    "ilo.org": 4,
    "wto.org": 4,
    "unicef.org": 4,
    "unesco.org": 4,
    "unhcr.org": 4,
    "unfpa.org": 4,
    "undp.org": 4,
    "gatesfoundation.org": 4,
    "fordfoundation.org": 4,
    "rockefellerfoundation.org": 4,
    "carnegie.org": 4,
    "macfound.org": 4,
    "rand.org": 4,
    "brookings.edu": 4,
    "heritage.org": 4,
    "cato.org": 4,
    "cfr.org": 4,
    "wilsoncenter.org": 4,
    "pewresearch.org": 4,
    "gallup.com": 4,
    "ipsos.com": 4,
    "yougov.com": 4,
    "realclearpolitics.com": 3,
    "politifact.com": 4,
    "factcheck.org": 4,
    "snopes.com": 4,
    "arstechnica.com": 4,
    "wired.com": 4,
    "techcrunch.com": 4,
    "theverge.com": 4,
    "engadget.com": 4,
    "cnet.com": 4,
    "zdnet.com": 4,
    "pcmag.com": 4,
    "slashdot.org": 3,
}
