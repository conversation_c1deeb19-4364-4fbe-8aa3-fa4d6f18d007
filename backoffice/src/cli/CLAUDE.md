# CLI Module Knowledge Base

This document provides information about the CLI module structure and organization.

## Structure

The CLI module is organized into separate command files by functionality:

- **../../main.py**: Main CLI entry point that registers all command groups
- **analysis_v2.py**: Analysis-related commands under the `ana` group
- **cluster_command.py**: Clustering commands under the `cluster` group
- **cms_command.py**: CMS-related commands under the `cms` group
- **crawl_command.py**: Web crawling commands under the `crawl` group
- **dashboard.py**: Dashboard commands under the `dash` group
- **embed_command.py**: Embedding commands under the `embed` group
- **fix_command.py**: Database fix commands under the `fix` group
- **scrape_command.py**: Web scraping commands under the `scrape` group
- **test_command.py**: Test commands under the `test` group

## Command Design Pattern

Commands in this module follow a consistent structure:

1. Each command type has its own command group defined with `@click.group()`
2. The group and its commands are in a dedicated file
3. Commands are registered in main.py using `main.add_command(group_name)`
4. Each command file defines its commands using `@group.command()`

Example structure:
```python
@click.group(name="command_group")
def command_group():
    """Group description"""
    pass

@command_group.command(name="specific_command")
@click.option('--option', help="Option description")
def specific_command(option):
    """Command description"""
    # Command implementation
```

## Command Hierarchy

- `main` - Root command group
  - `ana` - Analysis commands
  - `cluster` - Clustering commands
  - `cms` - CMS-related commands
  - `crawl` - Web crawling commands
  - `dash` - Dashboard commands
  - `embed` - Embedding commands
  - `fix` - Database fix commands
  - `scrape` - Web scraping commands
  - `test` - Test commands

## Naming Conventions

- Command group names use snake_case
- Command names use kebab-case (`command-name` rather than `command_name`)
- Options use kebab-case with `--` prefix

## Best Practices

1. **Modularity**: Keep related commands together in their own module
2. **Documentation**: All commands should have docstrings
3. **Options**: Use click.option for command parameters
4. **Naming**: Use consistent naming across commands
5. **Error Handling**: All commands should handle errors gracefully
6. **Logging**: Use loguru's logger for console output
7. **Code Organization**: Move implementation details to the eko module, CLI is just a wrapper

## Adding New Commands

To add a new command group:

1. Create a new file `<group_name>_command.py`
2. Define a command group with `@click.group(name="group_name")`
3. Add command implementations with `@group_name.command()`
4. Import and register the group in main.py with `main.add_command(group_name)`

To add a command to an existing group:

1. Open the appropriate command file
2. Add a new function with `@group_name.command(name="command-name")`
3. Document the command with a docstring
4. Implement the command logic