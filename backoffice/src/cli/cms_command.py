import click
from datetime import datetime
from loguru import logger
from typing import Optional

from eko.analysis_v2.effects.flag_main import create_effect_flags
from eko.cms.content.create_profile import run_cms_publish
from eko.db import get_bo_conn
from eko.db.data.run import RunScope, RunData
from eko.db.data.xfer import XferData
from eko.typing import not_none


@click.group(name="cms")
def cms():
    """Commands related to CMS operations"""
    pass


@cms.command(name="create-profile")
@click.option('--entity', required=True, help="The name of the entity or virtual entity to create a profile for")
@click.option('--max-workers', default=32, type=int, help="Maximum number of workers to use")
@click.option('--skip-flags', is_flag=True, help="Maximum number of workers to use")
@click.option('--run-id', default=None, help="Run ID to use for analysis data")
@click.option('--start-year', default=datetime.now().year - 5, help="Start year for profile data")
@click.option('--search', default=None, help="Web search style query (POSTGRES) for finding entities,")
def create_profile_command(entity: str, max_workers: int, skip_flags:bool, run_id:Optional[int], 
                         start_year:int, search:Optional[str]):
    """Create a company profile in the CMS based on analysis data"""
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics

    # Get the virtual entity and its base entities
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return
        
    # Get search parameters from virtual entity if not supplied
    if search is None and expanded_entity.entity_regex:
        search = expanded_entity.entity_regex
        
    # Use the base entities from the virtual entity
    entities = expanded_entity.base_entities
    logger.info(f"Using virtual entity '{entity}' with {len(entities)} base entities")
            
    logger.info(f"Found {len(entities)} entities for '{entity}'")
    if not entities:
        logger.error(f"No entities found for '{entity}'")
        return
    logger.info("Publishing to CMS for entities:")
    logger.info([e.name for e in entities])
    with get_bo_conn() as conn:
        if run_id is None:
            if skip_flags:
                run = RunData.get_latest_completed_run(conn, "cms")
            else:
                run = RunData.create(conn, "cms", [], RunScope.ENTITY, expanded_entity.short_id, None)
            if run is None:
                logger.error("No run ID found")
                return
            run_id = not_none(run.id)
        # Call the implementation
        logger.info(f"Creating profile for entity {entity} using run_id {run_id}")
        if not skip_flags:
            create_effect_flags(expanded_entity, start_year, datetime.now().year + 1, run_id)
            XferData.sync_virtual_entities(conn)
            XferData.sync_run(run_id)
            XferData.sync_score_to_xfer_v2(conn, run_id)

    run_cms_publish(run_id, expanded_entity, max_workers, start_year)
    with get_bo_conn() as conn:
        RunData.mark_completed(conn, run_id)
        logger.info(f"Profile creation complete for {entity}")
