import click
import json
from loguru import logger
from psycopg.rows import dict_row
from typing import Optional

from eko.db import get_bo_conn
from eko.db.data.xfer import XferData
from eko.entities.cluster import cluster_entities, get_clusters, TEMPLATE_DIR, create_clustering_visualization, \
    display_html_visualization
from eko.log.log import log_info, set_log_context

# Setup Jinja2 environment for templates
# We'll create a templates directory next to this file
if not TEMPLATE_DIR.exists():
    TEMPLATE_DIR.mkdir(parents=True)


# Add custom JSON encoder for serializing entities


# Create the cluster visualization HTML template

# Save the template to the templates directory


@click.group()
def entities():
    """Commands related to entity clustering."""
    pass


@entities.command()
@click.option('--output-file', '-o', help="Optional file to write detailed stats to")
@click.option('--search', '-s', help="Optional search string to filter entities before clustering")
@click.option('--visualize/--no-visualize', default=True, help="Generate a t-SNE visualization of clusters")
@click.option('--viz-output', help="Path to save the visualization image")
def cluster(output_file: Optional[str], search: Optional[str], visualize: bool, viz_output: Optional[str]):
    """
    Cluster entities based on the canonical_id, determining the canonical entity for each cluster.

    This command uses an LLM with tools to:
    1. Identify all clusters of entities in the database (entities with the same canonical_eko_id)
    2. For each cluster, determine which entity should be the canonical (master) entity
    3. Update the database with the canonical entity determinations

    The LLM has access to tools for:
    - Database fuzzy search
    - Companies House API
    - Global LEI Foundation (GLEIF) API
    - SEC API
    - Wikipedia and web search

    Options:
        --search, -s: Optional search string to filter entities by name before clustering.
                    Only entities whose name/legal_name/common_name contain this string
                    will be included in the clustering.
        --visualize/--no-visualize: Generate a t-SNE visualization of the clusters (default: True)
        --viz-output: Path to save the visualization image

    Returns statistics about the clustering process.
    """
    set_log_context("cluster_entities", "cli")
    log_info("cluster_entities", f"Starting entity clustering via CLI{' with search filter: ' + search if search else ''}")

    # Get the initial clusters for visualization first
    if visualize:
        logger.info("Fetching initial clusters for visualization...")
        clusters = get_clusters(search)
        if clusters:
            logger.info(f"Found {len(clusters)} clusters for visualization")
            viz_path = create_clustering_visualization(clusters, viz_output)
            if viz_path:
                logger.info(f"Cluster visualization created at: {viz_path}")
        else:
            logger.warning("No clusters found for visualization")
            viz_path = None

    # Run the clustering process with optional search filter
    stats = cluster_entities(search)

    # Log the stats summary
    logger.info("=== Entity Clustering Statistics ===")
    logger.info(f"Total clusters: {stats['total_clusters']}")
    logger.info(f"Processed clusters: {stats['processed_clusters']}")
    logger.info(f"Successful updates: {stats['successful_updates']}")
    logger.info(f"Failed updates: {stats['failed_updates']}")
    logger.info(f"Already canonical: {stats['already_canonical']}")
    logger.info(f"Search filter: {stats['search_string'] or 'None'}")
    logger.info(f"Started: {stats['started_at']}")
    logger.info(f"Completed: {stats['completed_at']}")

    # Write detailed stats to file if requested
    if output_file:
        try:
            with open(output_file, 'w') as f:
                json.dump(stats, f, indent=2)
            logger.info(f"Detailed statistics written to {output_file}")
        except Exception as e:
            logger.error(f"Error writing statistics to {output_file}: {e}")

    # Display the visualization with updated stats
    if visualize and 'viz_path' in locals() and viz_path:
        display_html_visualization(clusters, viz_path, stats)

    log_info("cluster_entities", "Entity clustering via CLI completed")

    return stats


@entities.command(name="replace-in-doc")
@click.option("--doc-id", required=True, type=int, help="Document ID where entity should be replaced")
@click.option("--old-entity-id", required=True, type=int, help="ID of the entity to be replaced")
@click.option("--new-entity-id", required=True, type=int, help="ID of the entity to replace with")
def replace_entity(doc_id: int, old_entity_id: int, new_entity_id: int):
    """
    Replace all occurrences of one entity with another in a document and its related data.

    This command updates entity references in kg_document_authors and kg_statements tables,
    replacing the old entity ID with the new entity ID for a specific document.
    """
    logger.info(f"Replacing entity {old_entity_id} with {new_entity_id} in document {doc_id}")

    with get_bo_conn() as conn:
        with conn.cursor(row_factory=dict_row) as cur:
            # First, verify that both entities exist
            cur.execute("""
                SELECT id, name, type FROM kg_base_entities
                WHERE id IN (%s, %s)
            """ % (old_entity_id, new_entity_id))

            entities = cur.fetchall()
            if len(entities) < 2:
                logger.error(f"One or both entities not found: {old_entity_id}, {new_entity_id}")
                return

            # Verify that the document exists
            cur.execute("""
                SELECT id, title FROM kg_documents
                WHERE id = %s AND status != 'deleted'
            """, (doc_id,))

            document = cur.fetchone()
            if not document:
                logger.error(f"Document with ID {doc_id} not found or is deleted")
                return

            # Get entity names for logging
            entity_names = {}
            for entity in entities:
                entity_names[entity['id']] = f"{entity['name']} ({entity['type']}, ID: {entity['id']})"

            logger.info(f"Replacing {entity_names.get(old_entity_id)} with {entity_names.get(new_entity_id)} in document: {document['title']}")

            # 1. Update kg_document_authors table
            cur.execute("""
                SELECT COUNT(*) as count FROM kg_document_authors
                WHERE doc_id = %s AND entity_id = %s
            """, (doc_id, old_entity_id))

            author_count = cur.fetchone()['count']

            if author_count > 0:
                # Check if the new entity is already an author
                cur.execute("""
                    SELECT COUNT(*) as count FROM kg_document_authors
                    WHERE doc_id = %s AND entity_id = %s
                """, (doc_id, new_entity_id))

                new_author_exists = cur.fetchone()['count'] > 0

                if new_author_exists:
                    # If new entity is already an author, just delete the old one
                    cur.execute("""
                        DELETE FROM kg_document_authors
                        WHERE doc_id = %s AND entity_id = %s
                    """, (doc_id, old_entity_id))
                    logger.info(f"Deleted old author {entity_names.get(old_entity_id)} (new author already exists)")
                else:
                    # Otherwise, update the old entity to the new one
                    cur.execute("""
                        UPDATE kg_document_authors
                        SET entity_id = %s
                        WHERE doc_id = %s AND entity_id = %s
                    """, (new_entity_id, doc_id, old_entity_id))
                    logger.info(f"Updated {author_count} author references")

            # 2. Update kg_statements table
            # First, get all statements for this document
            cur.execute("""
                SELECT id, subject_entities, object_entities, company_id
                FROM kg_statements
                WHERE doc_id = %s
            """, (doc_id,))

            statements = cur.fetchall()
            logger.info(f"Found {len(statements)} statements to check")

            updated_statements = 0

            for stmt in statements:
                changes = []

                # Check and update subject_entities
                if stmt['subject_entities'] and old_entity_id in stmt['subject_entities']:
                    new_subjects = [new_entity_id if eid == old_entity_id else eid for eid in stmt['subject_entities']]
                    changes.append(("subject_entities", new_subjects))

                # Check and update object_entities
                if stmt['object_entities'] and old_entity_id in stmt['object_entities']:
                    new_objects = [new_entity_id if eid == old_entity_id else eid for eid in stmt['object_entities']]
                    changes.append(("object_entities", new_objects))

                # Check and update company_id
                if stmt['company_id'] == old_entity_id:
                    changes.append(("company_id", new_entity_id))

                # If changes are needed, update the statement
                if changes:
                    update_fields = []
                    update_values = []

                    for field, value in changes:
                        update_fields.append(f"{field} = %s")
                        update_values.append(value)

                    # Add statement ID as the last parameter
                    update_values.append(stmt['id'])

                    update_query = f"""
                        UPDATE kg_statements
                        SET {', '.join(update_fields)}
                        WHERE id = %s
                    """

                    cur.execute(update_query, update_values)
                    updated_statements += 1

            conn.commit()
            logger.info(f"Successfully updated {updated_statements} statements")
            logger.info(f"Entity replacement complete for document {doc_id}")


@entities.command()
def sync_virtual_entities():
    """
    Sync all virtual entities to the xfer_entities table.

    This enables the customer application to access virtual entities.
    The command will fetch all current virtual entities and their base entities,
    then serialize them to the xfer_entities table.
    """
    logger.info("Syncing virtual entities to xfer_entities table...")

    with get_bo_conn() as conn:
        synced_entities = XferData.sync_virtual_entities(conn)

    if synced_entities:
        logger.info(f"Successfully synced {len(synced_entities)} virtual entities")
        logger.info(f"Synced entities: {', '.join(synced_entities)}")
    else:
        logger.warning("No virtual entities were synced")


@entities.command()
@click.option('--run-id', '-r', multiple=True, type=int, help="Specific run ID(s) to sync. If not provided, all completed runs will be synced.")
def sync_runs():
    """
    Sync analysis runs to the xfer_runs table.

    This enables the customer application to access run data in the new format.
    The command will fetch all completed runs (or specific runs if provided),
    then serialize them to the xfer_runs table with all relevant data bundled as JSON.

    Options:
        --run-id, -r: Specific run ID(s) to sync. Can be specified multiple times.
                     If not provided, all completed runs will be synced.
    """
    logger.info("Syncing runs to xfer_runs table...")

    run_ids = [int(run_id) for run_id in click.get_current_context().params.get('run_id', [])]

    with get_bo_conn() as conn:
        synced_run_ids = XferData.sync_runs_to_xfer_v2(conn, run_ids if run_ids else None)

    if synced_run_ids:
        logger.info(f"Successfully synced {len(synced_run_ids)} runs")
        logger.info(f"Synced run IDs: {', '.join(map(str, synced_run_ids))}")
    else:
        logger.warning("No runs were synced")


@entities.command()
@click.option('--run-id', '-r', type=int, required=True, help="Run ID to sync promises for.")
def sync_promises(run_id: int):
    """
    Sync promises to the xfer_promises table for a specific run.

    This enables the customer application to access promise data in the new format.
    The command will fetch all promises for the specified run,
    then serialize them to the xfer_promises table with all relevant data bundled as JSON.

    Options:
        --run-id, -r: Run ID to sync promises for (required).
    """
    logger.info(f"Syncing promises to xfer_promises table for run {run_id}...")

    with get_bo_conn() as conn:
        synced_promise_ids = XferData.sync_promises_to_xfer_v2(conn, run_id)

    if synced_promise_ids:
        logger.info(f"Successfully synced {len(synced_promise_ids)} promises")
        logger.info(f"Synced promise IDs: {', '.join(map(str, synced_promise_ids))}")
    else:
        logger.warning("No promises were synced")


@entities.command()
@click.option('--run-id', '-r', type=int, required=True, help="Run ID to sync claims for.")
def sync_claims(run_id: int):
    """
    Sync claims to the xfer_claims table for a specific run.

    This enables the customer application to access claim data in the new format.
    The command will fetch all claims for the specified run,
    then serialize them to the xfer_claims table with all relevant data bundled as JSON.

    Options:
        --run-id, -r: Run ID to sync claims for (required).
    """
    logger.info(f"Syncing claims to xfer_claims table for run {run_id}...")

    with get_bo_conn() as conn:
        synced_claim_ids = XferData.sync_claims_to_xfer_v2(conn, run_id)

    if synced_claim_ids:
        logger.info(f"Successfully synced {len(synced_claim_ids)} claims")
        logger.info(f"Synced claim IDs: {', '.join(map(str, synced_claim_ids))}")
    else:
        logger.warning("No claims were synced")


@entities.command()
@click.option('--run-id', '-r', type=int, required=True, help="Run ID to sync cherry picking data for.")
def sync_cherry(run_id: int):
    """
    Sync cherry picking data to the xfer_selective_v2 table for a specific run.

    This enables the customer application to access cherry picking data in the new format.
    The command will fetch all cherry picking analyses for the specified run,
    then serialize them to the xfer_selective_v2 table with all relevant data bundled as JSON.

    Options:
        --run-id, -r: Run ID to sync cherry picking data for (required).
    """
    logger.info(f"Syncing cherry picking data to xfer_selective_v2 table for run {run_id}...")

    with get_bo_conn() as conn:
        synced_cherry_ids = XferData.sync_cherry_to_xfer_v2(conn, run_id)

    if synced_cherry_ids:
        logger.info(f"Successfully synced {len(synced_cherry_ids)} cherry picking analyses")
        logger.info(f"Synced cherry picking analysis IDs: {', '.join(map(str, synced_cherry_ids))}")
    else:
        logger.warning("No cherry picking analyses were synced")


@entities.command()
@click.option('--run-id', '-r', type=int, required=True, help="Run ID to sync vague term analysis data for.")
def sync_vague(run_id: int):
    """
    Sync vague term analysis data to the _deprecated_xfer_gw_vague_v2 table for a specific run.

    This enables the customer application to access vague term analysis data in the new format.
    The command will fetch all vague term analyses for the specified run,
    then serialize them to the _deprecated_xfer_gw_vague_v2 table with all relevant data bundled as JSON.

    Options:
        --run-id, -r: Run ID to sync vague term analysis data for (required).
    """
    logger.info(f"Syncing vague term analysis data to _deprecated_xfer_gw_vague_v2 table for run {run_id}...")

    with get_bo_conn() as conn:
        synced_vague_ids = XferData.sync_vague_to_xfer_v2(conn, run_id)

    if synced_vague_ids:
        logger.info(f"Successfully synced {len(synced_vague_ids)} vague term analyses")
        logger.info(f"Synced vague term analysis IDs: {', '.join(map(str, synced_vague_ids))}")
    else:
        logger.warning("No vague term analyses were synced")


@entities.command()
@click.option('--run-id', '-r', type=int, required=True, help="Run ID to sync entity scores for.")
def sync_scores(run_id: int):
    """
    Sync entity scores to the xfer_score table for a specific run.

    This enables the customer application to access entity scores in the new format.
    The command will calculate scores from effect flags for the specified run,
    then serialize them to the xfer_score table with all relevant data bundled as JSON.

    Options:
        --run-id, -r: Run ID to sync entity scores for (required).
    """
    logger.info(f"Calculating and syncing entity scores to xfer_score table for run {run_id}...")

    with get_bo_conn() as conn:
        synced_entity_ids = XferData.sync_score_to_xfer_v2(conn, run_id)

    if synced_entity_ids:
        logger.info(f"Successfully calculated and synced {len(synced_entity_ids)} entity scores")
        logger.info(f"Synced entity IDs: {', '.join(synced_entity_ids)}")
    else:
        logger.warning("No entity scores were calculated or synced")
