import json
import time
import traceback
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import Optional

import click
from loguru import logger

from eko.agent.crewai.crawl.crawler import run_crew, run_persistent_crew
from eko.commands.scrape import (
    crawl_impact_reports_command, crawl_disclosure_reports_command, crawl_url_command
)
from eko.db import get_bo_conn
from eko.pdf.main import pdf_parsing_cheap_mode


def get_latest_session_id(company_name: str) -> Optional[str]:
    """
    Get the most recent session ID for a company.

    Args:
        company_name: The name of the company

    Returns:
        Optional[str]: The most recent session ID, or None if no sessions exist
    """
    try:
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT session_id FROM agent_sessions 
                    WHERE company_name = %s
                    ORDER BY updated_at DESC
                    LIMIT 1
                    """,
                    (company_name,)
                )
                result = cur.fetchone()
                if result:
                    return result[0]
    except Exception as e:
        logger.error(f"Error getting latest session ID: {e}")
    
    return None


@click.group(name="crawl")
def crawl():
    """Commands for crawling websites for reports and other data"""
    pass


@crawl.command(name="disclosure-reports")
@click.option('--max-workers', default=1, help="How many processes to use.")
@click.option('--entity', required=True, help="The short_id of the entity/entities to crawl for")
def crawl_disclosure_reports(entity: str, max_workers: int):
    """Crawl for disclosure reports for a specified entity or entities"""
    futures = []
    pdf_parsing_cheap_mode()
    with ProcessPoolExecutor(max_workers) as executor:
        entities = entity.split(",")
        for e in entities:
            futures.append(executor.submit(crawl_disclosure_reports_command, e, max_workers))
        for future in as_completed(futures):
            try:
                result = future.result()
            except Exception as e:
                logger.error(f"Error in post-processing task: {str(e)}")
                logger.exception("Full traceback:", e)
                traceback.print_exc()


@crawl.command(name="impact-reports")
@click.option('--max-workers', default=1, help="How many processes to use.")
def crawl_impact_reports(max_workers: int):
    """Crawl for impact reports"""
    pdf_parsing_cheap_mode()
    crawl_impact_reports_command(max_workers)


@crawl.command(name="url")
@click.argument('url')
@click.option('--max-workers', default=32, help="How many processes to use.")
@click.option('--common-crawl', default=False, help="Whether to use CommonCrawl.")
def crawl_url(url, max_workers: int, common_crawl: bool):
    """Crawl a URL for reports and articles"""
    pdf_parsing_cheap_mode()
    crawl_url_command(url, max_workers, common_crawl)



@crawl.command(name="crew")
@click.argument('company_name')
@click.option('--max-iterations', default=50, help="Maximum number of iterations for the entire crew execution")
@click.option('--max-rpm', default=20, help="Maximum requests per minute")
@click.option('--output', help="Output file for the report (JSON format)")
@click.option('--log-file', help="Log file path")
@click.option('--session-id', help="Optional session ID for resuming previous runs")
def crawl_crew(company_name: str, max_iterations: int, max_rpm: int, output: str, log_file: str, session_id: str):
    """Use a CrewAI-powered multi-agent system to research a company's ESG practices
    
    This command runs a team of specialized AI agents that work together to perform comprehensive 
    research on a company. You can configure how thorough the search is by adjusting:
    
    --max-iterations: Controls how many cycles the agents will perform (higher = more thorough, but slower)
    --max-rpm: Controls how many requests per minute are allowed (higher = faster, but may trigger rate limits)
    --session-id: Specify a previous session ID to continue from where it left off
    
    For extensive research, try values like --max-iterations=200 --max-rpm=30
    """
    logger.info(f"Starting CrewAI multi-agent research for {company_name}")
    
    # Configure log file if provided
    if log_file:
        logger.add(log_file, rotation="10 MB")
        logger.info(f"Logging to {log_file}")

    try:
        # Run the CrewAI agent with optional session ID
        if session_id:
            logger.info(f"Using session ID: {session_id} to continue previous run")
            
        logger.info(f"Running with max_iterations={max_iterations}, max_rpm={max_rpm}")
        for retry in range(20):
            try:
                results = run_crew(company_name, max_iterations, max_rpm, session_id)
                break
            except Exception as e:
                logger.error(f"Error running CrewAI agents: {e}")
                logger.exception("Full traceback:")
                traceback.print_exc()
                if retry < 2:
                    logger.info("Retrying in 5 seconds...")
                    time.sleep(5)
                else:
                    raise e


        # Output results
        if output:
            with open(output, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Results saved to {output}")
        else:
            # Print summary to console
            print(f"\n{'=' * 80}")
            print(f"CREWAI RESEARCH RESULTS FOR {company_name.upper()}")
            print(f"{'=' * 80}")
            print(f"Status: {results.get('status', 'unknown')}")
            print(f"Visited URLs: {results.get('visited_urls_count', 0)}")
            print(f"Downloaded files: {results.get('downloaded_files_count', 0)}")
            print(f"Insights found: {results.get('insights_count', 0)}")
            print("\nSUMMARY:")
            print(f"{'-' * 80}")
            print(results.get('summary', 'No summary available'))
            
            # Print insights by category
            if results.get('insights_by_category'):
                print("\nKEY INSIGHTS BY CATEGORY:")
                print(f"{'-' * 80}")
                for category, behavior_types in results.get('insights_by_category', {}).items():
                    print(f"\n{category.upper()}:")
                    for behavior_type, insights in behavior_types.items():
                        if insights:
                            print(f"  {behavior_type.capitalize()}:")
                            # Show up to 3 insights per type
                            for i, insight in enumerate(insights[:3]):
                                desc = insight.get('description', '') if isinstance(insight, dict) else insight.description
                                desc_preview = desc[:100] + "..." if len(desc) > 100 else desc
                                print(f"    - {desc_preview}")
            
            print(f"{'=' * 80}\n")

        logger.info(f"CrewAI research completed for {company_name}")
    except Exception as e:
        logger.error(f"Error running CrewAI agents: {e}")
        logger.exception("Full traceback:")
        traceback.print_exc()

        # Print error summary
        print(f"\n{'=' * 80}")
        print(f"CREWAI RESEARCH RESULTS FOR {company_name.upper()}")
        print(f"{'=' * 80}")
        print("Status: error")
        print(f"Error: {str(e)}")
        print(f"{'=' * 80}\n")


@crawl.command(name="persistent-crew")
@click.argument('company_name')
@click.option('--max-iterations', default=50, help="Maximum number of iterations for the entire crew execution")
@click.option('--max-rpm', default=20, help="Maximum requests per minute")
@click.option('--output', help="Output file for the report (JSON format)")
@click.option('--log-file', help="Log file path")
@click.option('--session-id', help="Optional session ID for resuming previous runs")
@click.option('--continue', 'continue_flag', is_flag=True, help="Continue from the most recent session for this company")
def crawl_persistent_crew(company_name: str, max_iterations: int, max_rpm: int, output: str, 
                         log_file: str, session_id: str, continue_flag: bool):
    """Use a persistent CrewAI-powered multi-agent system to research a company
    
    This command runs a team of specialized AI agents that work together to perform comprehensive 
    research on a company, with persistence capabilities to continue from where it left off.
    
    Each run is tracked and saved, allowing you to stop and resume the research at any time.
    The agent automatically skips already visited URLs and continues to explore new links.
    
    Options:
    --max-iterations: Controls how many cycles the agents will perform (higher = more thorough)
    --max-rpm: Controls how many requests per minute are allowed (rate limiting)
    --session-id: Specify a previous session ID to continue from where it left off
    --continue: Automatically continue the most recent session for this company if one exists
    
    Example to start a new run:
    $ python main.py crawl persistent-crew "Acme Corp" --max-iterations=100
    
    Example to continue a previous run:
    $ python main.py crawl persistent-crew "Acme Corp" --session-id=crawl_1744847172
    
    Example to continue the most recent run (or start a new one if none exists):
    $ python main.py crawl persistent-crew "Acme Corp" --continue
    """
    logger.info(f"Starting persistent CrewAI multi-agent research for {company_name}")
    
    # Configure log file if provided
    if log_file:
        logger.add(log_file, rotation="10 MB")
        logger.info(f"Logging to {log_file}")

    try:
        # Handle --continue flag by finding the most recent session for this company
        if continue_flag:
            latest_session_id = get_latest_session_id(company_name)
            if latest_session_id:
                session_id = latest_session_id
                logger.info(f"Continuing most recent session: {session_id}")
            else:
                logger.info(f"No existing sessions found for {company_name}, starting a new one")
                session_id = None
                
        # Run the persistent CrewAI agent with optional session ID
        if session_id:
            logger.info(f"Using session ID: {session_id} to continue previous run")
            session_id_for_display = session_id
        else:
            session_id = f"crawl_{int(time.time())}"
            session_id_for_display = session_id
            logger.info(f"Starting new crawler session with ID: {session_id}")
            
        logger.info(f"Running with max_iterations={max_iterations}, max_rpm={max_rpm}")
        for retry in range(3):
            try:
                results = run_persistent_crew(company_name, max_iterations, max_rpm, session_id)
                break
            except Exception as e:
                logger.error(f"Error running persistent CrewAI agents: {e}")
                logger.exception("Full traceback:")
                traceback.print_exc()
                if retry < 2:
                    logger.info("Retrying in 5 seconds...")
                    time.sleep(5)
                else:
                    raise e

        # Output results
        if output:
            with open(output, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Results saved to {output}")
        else:
            # Print summary to console
            print(f"\n{'=' * 80}")
            print(f"PERSISTENT CREWAI RESEARCH RESULTS FOR {company_name.upper()}")
            print(f"{'=' * 80}")
            print(f"Session ID: {session_id_for_display}")
            print(f"Status: {results.get('status', 'unknown')}")
            print(f"Visited URLs: {results.get('visited_urls_count', 0)}")
            print(f"Downloaded files: {results.get('downloaded_files_count', 0)}")
            print(f"Insights found: {results.get('insights_count', 0)}")
            print("\nSUMMARY:")
            print(f"{'-' * 80}")
            print(results.get('summary', 'No summary available'))
            
            # Print insights by category
            if results.get('insights_by_category'):
                print("\nKEY INSIGHTS BY CATEGORY:")
                print(f"{'-' * 80}")
                for category, behavior_types in results.get('insights_by_category', {}).items():
                    print(f"\n{category.upper()}:")
                    for behavior_type, insights in behavior_types.items():
                        if insights:
                            print(f"  {behavior_type.capitalize()}:")
                            # Show up to 3 insights per type
                            for i, insight in enumerate(insights[:3]):
                                desc = insight.get('description', '') if isinstance(insight, dict) else insight.description
                                desc_preview = desc[:100] + "..." if len(desc) > 100 else desc
                                print(f"    - {desc_preview}")
            
            print("\nTo continue this session in the future, use either:")
            print(f"python main.py crawl persistent-crew \"{company_name}\" --session-id={session_id_for_display}")
            print(f"python main.py crawl persistent-crew \"{company_name}\" --continue")
            print(f"{'=' * 80}\n")

        logger.info(f"Persistent CrewAI research completed for {company_name}")
    except Exception as e:
        logger.error(f"Error running persistent CrewAI agents: {e}")
        logger.exception("Full traceback:")
        traceback.print_exc()

        # Print error summary
        print(f"\n{'=' * 80}")
        print(f"PERSISTENT CREWAI RESEARCH RESULTS FOR {company_name.upper()}")
        print(f"{'=' * 80}")
        print("Status: error")
        print(f"Error: {str(e)}")
        print(f"{'=' * 80}\n")
