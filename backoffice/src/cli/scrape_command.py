from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>xecutor, ThreadPoolExecutor, as_completed
from time import sleep

import click
from loguru import logger

from eko.commands.scrape import (
    scrape_reports_command, scrape_articles_command, scrape_single_doc_command, scrape_single_page_command
)
from eko.db import get_bo_conn
from eko.entities.queries import get_entity_by_short_id
from eko.scrape.reports import process_pdf, process_webpage
from eko.scrape.slack import scrape_slack_command
from eko.statements.extract import extract_statements_from_doc


@click.group(name="scrape")
def scrape():
    """Commands for scraping reports, articles, and other data from the web"""
    pass

# 
# @scrape.command(name="reports")
# @click.option('--entity', required=True, help="The short_id of the entity/entities to get reports from Google for.")
# @click.option('--max-workers', default=1, help="How many processes to use.")
# @click.option('--exclude-own-domains', default=True, help="Exclude domains owned by the entity.")
# def scrape_reports(entity: str, max_workers: int, exclude_own_domains: bool):
#     """Scrape reports for a specified entity or entities"""
#     futures = []
#     with ProcessPoolExecutor(max_workers) as executor:
#         entities = entity.split(",")
#         for e in entities:
#             futures.append(
#                 executor.submit(scrape_reports_command, get_entity_by_short_id(e), max_workers, exclude_own_domains))
#         for future in as_completed(futures):
#             try:
#                 result = future.result()
#             except Exception as e:
#                 logger.error(f"Error in post-processing task: {str(e)}")
#                 logger.exception("Full traceback:", e)
# 

# @scrape.command(name="articles")
# @click.option('--entity', required=True, help="The short_id of the entity/entities to get articles from Google for.")
# @click.option('--max-workers', default=1, help="How many processes to use.")
# def scrape_articles(entity: str, max_workers: int):
#     """Scrape articles for a specified entity or entities"""
#     futures = []
#     with ProcessPoolExecutor(max_workers) as executor:
#         entities = entity.split(",")
#         for short_id in entities:
#             entity_obj = get_entity_by_short_id(short_id)
#             futures.append(executor.submit(scrape_articles_command, entity_obj, max_workers))
#         for future in as_completed(futures):
#             try:
#                 result = future.result()
#             except Exception as short_id:
#                 logger.error(f"Error in post-processing task: {str(short_id)}")
#                 logger.exception("Full traceback:", short_id)
# 


@scrape.command(name="single-report")
@click.argument('url')
def scrape_single_report(url):
    """Scrape a single report from a URL"""
    scrape_single_doc_command(url)


@scrape.command(name="single-page")
@click.argument('url')
def scrape_single_page(url):
    """Scrape a single webpage from a URL"""
    scrape_single_page_command(url)


@scrape.command(name="slack")
def scrape_slack():
    """Continuously scrape Slack for new messages"""
    scrape_slack_command(32, 7 * 24 * 60 * 60)
    while True:
        sleep(3600)
        crawl_halt()
        scrape_slack_command(32, 2 * 60 * 60)


@scrape.command(name="fix-missing-pages")
@click.option("--batch-size", default=10, help="Number of documents to process in each batch")
@click.option("--limit", default=None, type=int, help="Maximum number of documents to process")
@click.option("--doc-id", default=None, type=int, help="Specific document ID to process (overrides limit)")
@click.option("--max-workers", default=4, help="Maximum number of parallel workers")
@click.option("--entity", default=None, help="Entity to process")
@click.option("--search", default=None, help="Search to apply to statement processing")
def fix_missing_pages(batch_size: int, limit: int, doc_id: int, max_workers: int, entity: str, search: str):
    """
    Fix documents that have no pages by reprocessing them with force flag.

    This command finds documents where (select count(*) from kg_document_pages where doc_id = kg_documents.id and status = 'active') = 0
    and reprocesses them using the document's URL with the force flag set to true.
    """
    logger.info(f"Fixing documents with missing pages, batch size: {batch_size}")

    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            if doc_id:
                # Process a specific document
                cur.execute("""
                    SELECT id, url, file_type, title
                    FROM kg_documents
                    WHERE id = %s AND status != 'deleted'
                """, (doc_id,))

                documents = cur.fetchall()
                if not documents:
                    logger.error(f"Document with ID {doc_id} not found or is deleted")
                    return

                total_count = 1
            elif entity is not None:
                # Count documents with no pages
                cur.execute("""
                    SELECT COUNT(*)
                    FROM kg_documents
                    WHERE status != 'deleted'
                    AND (SELECT COUNT(*) FROM kg_document_pages WHERE doc_id = kg_documents.id AND status = 'active') = 0
                    AND cleaned_text ILIKE %s
                    AND url IS NOT NULL
                """, ('%' + entity + '%',))

                total_count = cur.fetchone()[0]

                if limit is not None and limit < total_count:
                    total_count = limit

                # Get documents with no pages
                cur.execute("""
                    SELECT id, url, file_type, title
                    FROM kg_documents
                    WHERE status != 'deleted'
                    AND (SELECT COUNT(*) FROM kg_document_pages WHERE doc_id = kg_documents.id AND status = 'active') = 0
                    AND url IS NOT NULL
                    AND cleaned_text ILIKE %s
                    ORDER BY id
                    LIMIT %s
                """, ('%' + entity + '%', total_count))

                documents = cur.fetchall()

            else:
                # Count documents with no pages
                cur.execute("""
                            SELECT COUNT(*)
                            FROM kg_documents
                            WHERE status != 'deleted'
                              AND (SELECT COUNT(*) FROM kg_document_pages WHERE doc_id = kg_documents.id AND status = 'active') = 0
                              AND url IS NOT NULL
                            """)

                total_count = cur.fetchone()[0]

                if limit is not None and limit < total_count:
                    total_count = limit

                # Get documents with no pages
                cur.execute("""
                            SELECT id, url, file_type, title
                            FROM kg_documents
                            WHERE status != 'deleted'
                              AND (SELECT COUNT(*) FROM kg_document_pages WHERE doc_id = kg_documents.id AND status = 'active') = 0
                              AND url IS NOT NULL
                            ORDER BY id
                            LIMIT %s
                            """, (total_count,))

                documents = cur.fetchall()
            logger.info(f"Found {len(documents)} documents with missing pages")

            successful = 0
            failed = 0

            # Process documents in batches
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i+batch_size]
                logger.info(f"Processing batch of {len(batch)} documents (batch {i//batch_size + 1}/{(len(documents)+batch_size-1)//batch_size})")

                # Map to track document IDs to their processing futures
                doc_futures = {}
                processed_doc_ids = []

                # Step 1: Reprocess the documents
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # Submit all document processing tasks
                    for doc_id, url, file_type, title in batch:
                        if url and url.startswith('http'):
                            logger.info(f"Reprocessing document {doc_id}: {title} ({url})")

                            if file_type == 'pdf' or url.lower().endswith('.pdf'):
                                future = executor.submit(process_pdf, url, [], None, None, 3, True)
                            else:
                                future = executor.submit(process_webpage, url, [], None, None, None, 3, 3, 3, True)
                            doc_futures[future] = doc_id
                        else:
                            logger.warning(f"Document {doc_id} has no valid URL, skipping")
                            failed += 1

                    # Process results as they complete
                    for future in as_completed(doc_futures.keys()):
                        doc_id = doc_futures[future]
                        try:
                            result = future.result()
                            if result:
                                successful += 1
                                processed_doc_ids.append(doc_id)
                                logger.info(f"Successfully reprocessed document {doc_id}, result: {result}")
                            else:
                                failed += 1
                                logger.warning(f"Failed to reprocess document {doc_id}")
                        except Exception as e:
                            logger.exception(f"Error reprocessing document {doc_id}: {e}")
                            failed += 1

                # Step 2: Extract statements for successfully processed documents
                if processed_doc_ids:
                    logger.info(f"Extracting statements for {len(processed_doc_ids)} documents")
                    with ThreadPoolExecutor(max_workers=max_workers) as executor:
                        # Submit all statement extraction tasks
                        statement_futures = {}
                        for doc_id in processed_doc_ids:
                            future = executor.submit(extract_statements_from_doc, doc_id, search)
                            statement_futures[future] = doc_id

                        # Process results as they complete
                        for future in as_completed(statement_futures.keys()):
                            doc_id = statement_futures[future]
                            try:
                                future.result()
                                logger.info(f"Successfully extracted statements for document {doc_id}")
                            except Exception as e:
                                logger.exception(f"Error extracting statements for document {doc_id}: {e}")

            logger.info(f"Document reprocessing complete. Total documents: {len(documents)}, Successful: {successful}, Failed: {failed}")
