from typing import Optional

import click
from loguru import logger

from eko.db import get_bo_conn
from eko.db.data.entity import EntityData
from eko.db.data.virtual_entity import VirtualEntityData
from eko.entities.virtual_queries import (
    create_virtual_entity,
    get_expanded_virtual_entity_by_name,
    get_base_entities_by_regex,
    list_all_virtual_entities,
    refresh_virtual_entity_mappings
)


@click.group(name="virtual-entity")
def virtual_entity():
    """Commands related to virtual entities"""
    pass


@virtual_entity.command(name="create")
@click.option('--name', required=True, help="Unique name for the virtual entity")
@click.option('--type', 'entity_type', required=True, help="Type of entity (company, person, etc.)")
@click.option('--title', help="Optional title for display purposes")
@click.option('--description', help="Optional description")
@click.option('--entity-regex', help="Optional regex to automatically map base entities")
@click.option('--search-phrase', help="Optional search phrase for web searches")
def create_virtual_entity_command(name: str, entity_type: str, title: Optional[str] = None,
                                 description: Optional[str] = None, entity_regex: Optional[str] = None,
                                 search_phrase: Optional[str] = None):
    """Create a new virtual entity"""
    try:
        virtual_entity = create_virtual_entity(
            name, entity_type, title, description, entity_regex, search_phrase
        )
        logger.info(f"Created virtual entity: {virtual_entity.name} (ID: {virtual_entity.id}, Short ID: {virtual_entity.short_id})")
        
        # If entity_regex was provided, show the mapped entities
        if entity_regex:
            with get_bo_conn() as conn:
                base_entities = VirtualEntityData.get_base_entities(conn, virtual_entity.id)
                logger.info(f"Mapped {len(base_entities)} base entities to virtual entity")
                for entity in base_entities:
                    logger.info(f"  - {entity.name} (ID: {entity.id}, Short ID: {entity.short_id})")
    except Exception as e:
        logger.error(f"Error creating virtual entity: {e}")


@virtual_entity.command(name="update")
@click.option('--name', required=True, help="Name of the virtual entity to update")
@click.option('--new-name', help="New name for the virtual entity")
@click.option('--type', 'entity_type', help="New type for the virtual entity")
@click.option('--title', help="New title for the virtual entity")
@click.option('--description', help="New description for the virtual entity")
@click.option('--entity-regex', help="New regex for automatic mapping")
@click.option('--search-phrase', help="New search phrase for web searches")
def update_virtual_entity_command(name: str, new_name: Optional[str] = None, entity_type: Optional[str] = None,
                                 title: Optional[str] = None, description: Optional[str] = None,
                                 entity_regex: Optional[str] = None, search_phrase: Optional[str] = None):
    """Update an existing virtual entity"""
    try:
        with get_bo_conn() as conn:
            virtual_entity = VirtualEntityData.get_by_name(conn, name)
            if not virtual_entity:
                logger.error(f"Virtual entity not found: {name}")
                return
            
            updated = VirtualEntityData.update(
                conn, virtual_entity.id, new_name, entity_type, title, description, entity_regex, search_phrase
            )
            
            if updated:
                logger.info(f"Updated virtual entity: {updated.name} (ID: {updated.id}, Short ID: {updated.short_id})")
                
                # If entity_regex was provided, show the mapped entities
                if entity_regex:
                    base_entities = VirtualEntityData.get_base_entities(conn, updated.id)
                    logger.info(f"Mapped {len(base_entities)} base entities to virtual entity")
                    for entity in base_entities:
                        logger.info(f"  - {entity.name} (ID: {entity.id}, Short ID: {entity.short_id})")
            else:
                logger.error(f"Failed to update virtual entity: {name}")
    except Exception as e:
        logger.error(f"Error updating virtual entity: {e}")


@virtual_entity.command(name="delete")
@click.option('--name', required=True, help="Name of the virtual entity to delete")
@click.option('--confirm', is_flag=True, help="Confirm deletion without prompting")
def delete_virtual_entity_command(name: str, confirm: bool):
    """Delete a virtual entity"""
    try:
        with get_bo_conn() as conn:
            virtual_entity = VirtualEntityData.get_by_name(conn, name)
            if not virtual_entity:
                logger.error(f"Virtual entity not found: {name}")
                return
            
            if not confirm:
                click.confirm(f"Are you sure you want to delete virtual entity '{name}'?", abort=True)
            
            deleted = VirtualEntityData.delete(conn, virtual_entity.id)
            
            if deleted:
                logger.info(f"Deleted virtual entity: {name}")
            else:
                logger.error(f"Failed to delete virtual entity: {name}")
    except Exception as e:
        logger.error(f"Error deleting virtual entity: {e}")


@virtual_entity.command(name="list")
def list_virtual_entities_command():
    """List all virtual entities"""
    try:
        virtual_entities = list_all_virtual_entities()
        
        if not virtual_entities:
            logger.info("No virtual entities found")
            return
        
        logger.info(f"Found {len(virtual_entities)} virtual entities:")
        for ve in virtual_entities:
            logger.info(f"  - {ve.name} (ID: {ve.id}, Short ID: {ve.short_id}, Type: {ve.type})")
            if ve.title:
                logger.info(f"    Title: {ve.title}")
            if ve.entity_regex:
                logger.info(f"    Entity Regex: {ve.entity_regex}")
            if ve.search_phrase:
                logger.info(f"    Search Phrase: {ve.search_phrase}")
    except Exception as e:
        logger.error(f"Error listing virtual entities: {e}")


@virtual_entity.command(name="show")
@click.option('--name', required=True, help="Name of the virtual entity to show")
def show_virtual_entity_command(name: str):
    """Show details of a virtual entity"""
    try:
        virtual_entity = get_expanded_virtual_entity_by_name(name)
        
        if not virtual_entity:
            logger.error(f"Virtual entity not found: {name}")
            return
        
        logger.info(f"Virtual Entity: {virtual_entity.name}")
        logger.info(f"ID: {virtual_entity.id}")
        logger.info(f"Short ID: {virtual_entity.short_id}")
        logger.info(f"EKO ID: {virtual_entity.eko_id}")
        logger.info(f"Type: {virtual_entity.type}")
        
        if virtual_entity.title:
            logger.info(f"Title: {virtual_entity.title}")
        
        if virtual_entity.description:
            logger.info(f"Description: {virtual_entity.description}")
        
        if virtual_entity.entity_regex:
            logger.info(f"Entity Regex: {virtual_entity.entity_regex}")
        
        if virtual_entity.search_phrase:
            logger.info(f"Search Phrase: {virtual_entity.search_phrase}")
        
        if virtual_entity.common_names:
            logger.info(f"Common Names: {virtual_entity.common_names}")
        
        if virtual_entity.leis:
            logger.info(f"LEIs: {virtual_entity.leis}")
        
        if virtual_entity.legal_names:
            logger.info(f"Legal Names: {', '.join(virtual_entity.legal_names)}")
        
        if virtual_entity.aka:
            logger.info(f"Also Known As: {', '.join(virtual_entity.aka)}")
        
        logger.info(f"Base Entities ({len(virtual_entity.base_entities)}):")
        for entity in virtual_entity.base_entities:
            logger.info(f"  - {entity.name} (ID: {entity.id}, Short ID: {entity.short_id})")
            if entity.legal_name:
                logger.info(f"    Legal Name: {entity.legal_name}")
            if entity.common_name:
                logger.info(f"    Common Name: {entity.common_name}")
    except Exception as e:
        logger.error(f"Error showing virtual entity: {e}")


@virtual_entity.command(name="list-base-entities")
@click.option('--regex', required=True, help="Regular expression to match base entities")
def list_base_entities_command(regex: str):
    """List base entities matching a regex"""
    try:
        entities = get_base_entities_by_regex(regex)
        
        if not entities:
            logger.info(f"No base entities found matching regex: {regex}")
            return
        
        logger.info(f"Found {len(entities)} base entities matching regex: {regex}")
        for entity in entities:
            logger.info(f"  - {entity.name} (ID: {entity.id}, Short ID: {entity.short_id})")
            if entity.legal_name:
                logger.info(f"    Legal Name: {entity.legal_name}")
            if entity.common_name:
                logger.info(f"    Common Name: {entity.common_name}")
    except Exception as e:
        logger.error(f"Error listing base entities: {e}")


@virtual_entity.command(name="add-base-entity")
@click.option('--virtual-entity', required=True, help="Name of the virtual entity")
@click.option('--base-entity', required=True, help="Short ID of the base entity to add")
def add_base_entity_command(virtual_entity: str, base_entity: str):
    """Add a base entity to a virtual entity"""
    try:
        with get_bo_conn() as conn:
            ve = VirtualEntityData.get_by_name(conn, virtual_entity)
            if not ve:
                logger.error(f"Virtual entity not found: {virtual_entity}")
                return
            
            be = EntityData.get_by_short_id(conn, base_entity)
            if not be:
                logger.error(f"Base entity not found: {base_entity}")
                return
            
            added = VirtualEntityData.add_base_entity(conn, ve.id, be.id)
            
            if added:
                logger.info(f"Added base entity '{be.name}' to virtual entity '{ve.name}'")
            else:
                logger.info(f"Base entity '{be.name}' is already in virtual entity '{ve.name}'")
    except Exception as e:
        logger.error(f"Error adding base entity: {e}")


@virtual_entity.command(name="remove-base-entity")
@click.option('--virtual-entity', required=True, help="Name of the virtual entity")
@click.option('--base-entity', required=True, help="Short ID of the base entity to remove")
def remove_base_entity_command(virtual_entity: str, base_entity: str):
    """Remove a base entity from a virtual entity"""
    try:
        with get_bo_conn() as conn:
            ve = VirtualEntityData.get_by_name(conn, virtual_entity)
            if not ve:
                logger.error(f"Virtual entity not found: {virtual_entity}")
                return
            
            be = EntityData.get_by_short_id(conn, base_entity)
            if not be:
                logger.error(f"Base entity not found: {base_entity}")
                return
            
            removed = VirtualEntityData.remove_base_entity(conn, ve.id, be.id)
            
            if removed:
                logger.info(f"Removed base entity '{be.name}' from virtual entity '{ve.name}'")
            else:
                logger.error(f"Base entity '{be.name}' is not in virtual entity '{ve.name}'")
    except Exception as e:
        logger.error(f"Error removing base entity: {e}")


@virtual_entity.command(name="refresh")
@click.option('--name', required=True, help="Name of the virtual entity to refresh")
def refresh_virtual_entity_command(name: str):
    """Refresh all base entity mappings for a virtual entity based on its entity_regex"""
    try:
        count = refresh_virtual_entity_mappings(name)
        logger.info(f"Refreshed virtual entity '{name}'. {count} base entities mapped.")
        
        # Show the mapped entities
        virtual_entity = get_expanded_virtual_entity_by_name(name)
        if virtual_entity and virtual_entity.base_entities:
            logger.info(f"Base entities mapped to '{name}':")
            for entity in virtual_entity.base_entities:
                logger.info(f"  - {entity.name} (ID: {entity.id}, Short ID: {entity.short_id})")
    except ValueError as e:
        logger.error(str(e))
    except Exception as e:
        logger.error(f"Error refreshing virtual entity: {e}")
