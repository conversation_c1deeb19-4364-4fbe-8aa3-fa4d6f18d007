import json
from concurrent.futures import Thread<PERSON>oolExecutor
from typing_extensions import deprecated

import click
from datetime import datetime
from loguru import logger
from typing import Optional

from cli.effect_command import create_flags
from cli.selective_highlighting import run_selective_highlighting
from eko import console
from eko.analysis_v2 import create_effect_flags
from eko.analysis_v2.heart.trust_and_reliability.claim_evidence import run_claims
from eko.analysis_v2.heart.trust_and_reliability.promise_analysis import run_promises
from eko.analysis_v2.heart.trust_and_reliability.promise_report import generate_promise_report
# Removed old get_traceability_tracker import - using new trace system
from eko.analysis_v2.vague.vague_terms import analyze_vague_terms_command
from eko.cms.content.create_profile import run_cms_publish
from eko.commands.chunk import map_entity_reports_command
from eko.commands.scrape import scrape_media_command, scrape_for_flags_command, scrape_articles_command, \
    scrape_for_promises_command, scrape_reports_command, scrape_media_sites_command
# from eko.commands.statements import extract_statements_command
from eko.db import get_bo_conn
from eko.db._deprecated_xfer import remove_old_runs
from eko.db.data.run import RunData, RunScope, AnalysisRunModel
from eko.db.data.virtual_entity import VirtualEntityData
from eko.db.data.xfer import XferData
from eko.domains.domain_queries import get_crawl_skip_domains, get_crawl_impact_start_domains, \
    get_crawl_journalism_start_domains
from eko.entities.cluster import cluster_entities
from eko.entities.queries import get_entity_by_short_id
from eko.entities.virtual_queries import get_virtual_entity_by_name, get_virtual_entity_by_id
from eko.log.log import set_log_context, log_info
from eko.models.virtual_entity import VirtualEntityExpandedModel
from eko.prediction_v2.cli import run_prediction
from eko.scrape.spider import crawl_pdfs
from eko.statements.extract import extract_statements_by_search
from eko.typing import not_none


@click.group()
def ana():
    """
    Analysis CLI for Eko.
    """
    pass
#
# def transfer_analysis(entity, run_id, status_callback):
#     status_callback("Transferring Data")
#
#     # Use the new XferData methods for v2 tables
#     with get_bo_conn() as conn:
#         # Sync runs
#         XferData.sync_runs_to_xfer_v2(conn, [run_id])
#
#         # Sync claims
#         XferData.sync_claims_to_xfer_v2(conn, run_id)
#
#         # Sync promises
#         XferData.sync_promises_to_xfer_v2(conn, run_id)
#
#         # Sync cherry picking data
#         XferData.sync_cherry_to_xfer_v2(conn, run_id)
#
#         # # Sync vague term analysis data
#         # XferData.sync_vague_to_xfer_v2(conn, run_id)
#
#         # Sync entity scores
#         XferData.sync_score_to_xfer_v2(conn, run_id)
#
#         # Sync virtual entities
#         XferData.sync_virtual_entities(conn)
#
#     if status_callback:
#         status_callback("Cleaning up")
#     remove_old_runs(run_id)
#

@ana.command()
@click.option('--entity', help="The name of the entity/entities to get articles from Google for.")
@click.option('--max-workers', default=4, help="How many processes to use.")
def scrape(entity: str,  max_workers: int):
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics

    set_log_context("full-workup", entity)
    logger.info(f"Entity: {entity}")
    # Get the virtual entity
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return

    web_search = not_none(expanded_entity.search_phrase)
    entity_search = not_none(expanded_entity.entity_regex)

    log_info("full-workup", "Scraping media websites using Google")
    scrape_media_sites_command(expanded_entity, max_workers)
    log_info("full-workup", "Scraping for reports using Google")
    scrape_reports_command(expanded_entity, web_search or entity_search, max_workers)
    log_info("full-workup", "Scraping for media using Google")
    scrape_media_command(expanded_entity, web_search or entity_search, max_workers)
    log_info("full-workup", "Scraping for articles using Google")
    scrape_articles_command(expanded_entity, web_search or entity_search, max_workers)
    log_info("full-workup", "Scraping for articles based on flags")
    end_year = datetime.now().year + 1
    scrape_for_flags_command(expanded_entity, web_search or entity_search, datetime.now().year, max_workers)
    extract_statements_by_search(expanded_entity, web_search or entity_search)


@ana.command()
@click.option('--entity', help="The name of the entity/entities to get articles from Google for.")
def extract_statements(entity: str):
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics

    set_log_context("full-workup", entity)
    logger.info(f"Entity: {entity}")
    # Get the virtual entity
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return

    web_search = not_none(expanded_entity.search_phrase)
    entity_search = not_none(expanded_entity.entity_regex)

    extract_statements_by_search(expanded_entity, web_search or entity_search)


@ana.command()
@click.option('--entity', help="The name of the entity/entities to get articles from Google for.")
@click.option('--web-search', help="A query used to validate text for relevance.")
@click.option('--entity-search', help="A query used to validate text for relevance.")
@click.option('--max-workers', default=4, help="How many processes to use.")
@click.option('--skip-crawl', default=False, help="Skip crawling for disclosures.")
@click.option('--skip-scrape', default=False, help="Skip scraping.")
@click.option('--skip-flags', default=False, help="Skip flag creation.")
@click.option('--skip-entity-clusters', flag_value=True, help="Skip entity clustering")
@click.option('--skip-cms', default=False, help="Skip CMS publish.")
@click.option('--rounds', default=2, help="Number of rounds")
def full(entity: str, web_search, entity_search, max_workers: int, skip_crawl: bool, skip_scrape: bool,
         skip_flags, skip_entity_clusters, skip_cms, rounds: int):
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics

    start_year = datetime.now().year - 10
    set_log_context("full-workup", entity)
    futures = []
    logger.info(f"Entity: {entity}")
    entities = []

    # Get the virtual entity
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return
    expanded_entity: VirtualEntityExpandedModel = not_none(expanded_entity)

    # Get search parameters from virtual entity if not supplied
    if web_search is None and expanded_entity.search_phrase:
        web_search = expanded_entity.search_phrase

    if entity_search is None and expanded_entity.entity_regex:
        entity_search = expanded_entity.entity_regex

    with get_bo_conn() as conn:
        if skip_flags:
            run:AnalysisRunModel = not_none(RunData.get_latest_completed_run(conn, "full"))
        else:
            run:AnalysisRunModel = not_none(RunData.create(conn, "full", [], RunScope.ENTITY, expanded_entity.short_id, None))
        if run is None:
            logger.error("No run ID found")
            return
        run_id = not_none(run.id)
        try:
            # futures.append(exec.submit(scrape_articles_command, entity_obj, max_workers))
            # futures.append(exec.submit(scrape_for_promises_command, entity_obj, datetime.now().year, max_workers))
            if not skip_scrape:
                log_info("full-workup", "Scraping media websites using Google")
                scrape_media_sites_command(expanded_entity, max_workers)
                log_info("full-workup", "Scraping for reports using Google")
                scrape_reports_command(expanded_entity, web_search or entity_search, max_workers)
                log_info("full-workup", "Scraping for media using Google")
                scrape_media_command(expanded_entity, web_search or entity_search, max_workers)
                log_info("full-workup", "Scraping for articles using Google")
                scrape_articles_command(expanded_entity, web_search or entity_search, max_workers)
                log_info("full-workup", "Scraping for articles based on flags")
                end_year = datetime.now().year + 1
                for r in range(rounds):
                    VirtualEntityData.refresh_entity_mappings(conn, expanded_entity.id)
                    expanded_entity=not_none(VirtualEntityData.get_expanded(conn, expanded_entity.id))
                    scrape_for_flags_command(expanded_entity, web_search or entity_search, datetime.now().year, max_workers)
                    # log_info("full-workup",
                    #          "Extracting first round of statements, so we can scrape reports for those promises")
                    # extract_statements_command(entity_obj)
                    # log_info("full-workup", "Scraping for articles based on promises")
                    # scrape_for_promises_command(entity_obj, datetime.now().year, max_workers)

                # if not skip_crawl:
                #     with get_bo_conn() as conn:
                #         log_info("full-workup", "Crawling for articles from the entity's owned domains")
                #
                #         crawl_pdfs(conn, ["https://" + domain for domain in entity_obj.owned_domains],
                #                    entity_obj.owned_domains, max_workers,
                #                    get_crawl_skip_domains(), True, research_types=["disclosure"], depth=4)
                    extract_statements_by_search(expanded_entity, web_search or entity_search)
                    if not skip_entity_clusters:
                        # Run the clustering process with the search filter
                        stats = cluster_entities(entity_search or entity or web_search)

                        # Log the stats summary
                        logger.info("=== Entity Clustering Statistics ===")
                        logger.info(f"Total clusters: {stats['total_clusters']}")
                        logger.info(f"Processed clusters: {stats['processed_clusters']}")
                        logger.info(f"Successful updates: {stats['successful_updates']}")
                        logger.info(f"Failed updates: {stats['failed_updates']}")
                        logger.info(f"Already canonical: {stats['already_canonical']}")
                        logger.info(f"Search filter: {stats['search_string'] or 'None'}")
                        logger.info(f"Started: {stats['started_at']}")
                        logger.info(f"Completed: {stats['completed_at']}")

                    # Use entities from the virtual entity
                    entities = expanded_entity.base_entities
                    logger.info(f"Using virtual entity '{entity}' with {len(entities)} base entities")

                    if not entities:
                        logger.error(f"No entities found for '{entity_search or entity}'")
                        return


                    # Call the implementation
                    logger.info(f"Creating profile for entity {entity} using run_id {run_id}")
                    logger.info("Processing Entities:")
                    logger.info([e.name for e in entities])
                    if not skip_flags:
                        do_analysis(conn, end_year, entities, entity, expanded_entity, max_workers, run, start_year)

                RunData.mark_completed(conn, run_id)
                XferData.sync_run(run_id)
                XferData.sync_model_sections_to_xfer_v2(conn)
        except Exception as e:
            logger.error(f"Error in analysis: {e}")
            console.print_exception(show_locals=False)
            with get_bo_conn() as conn:
                RunData.mark_failed(conn, run_id, str(e))


@ana.command()
@click.option('--entity', help="The name of the entity/entities to get articles from Google for.")
@click.option('--max-workers', default=4, help="How many processes to use.")
@click.option('--rounds', default=2, help="Number of rounds")
def iterate_on_flags(entity: str, max_workers: int, rounds: int):
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics

    start_year = datetime.now().year - 10
    set_log_context("full-workup", entity)
    # Get the virtual entity
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return

    # Get search parameters from virtual entity if not supplied
    web_search = expanded_entity.search_phrase
    entity_search = expanded_entity.entity_regex

    with get_bo_conn() as conn:
        run:AnalysisRunModel = RunData.create(conn, "ignore", [], RunScope.ENTITY, expanded_entity.short_id, None)
        if not run:
            logger.error("No run ID found")
            return
        run_id = not_none(run.id)
    try:
        for r in range(rounds):
            scrape_for_flags_command(expanded_entity, not_none(web_search or entity_search), datetime.now().year, max_workers)
            extract_statements_by_search(expanded_entity, not_none(web_search or entity_search))
            # Call the implementation
            logger.info(f"Creating profile for entity {entity} using run_id {run_id}")
            create_effect_flags(expanded_entity, start_year, datetime.now().year+1, run_id)

        RunData.mark_completed(conn, run_id)
    except Exception as e:
        console.print_exception(show_locals=False)
        with get_bo_conn() as conn:
            RunData.mark_failed(conn, run_id, str(e))



@ana.command()
@click.option('--entity', help="The short_id of the entity/entities to get articles from Google for.")
@click.option('--max-workers', default=32, help="How many processes to use.")
@click.option('--start-year', default=datetime.now().year - 10, help="The start year for the analysis.")
@click.option('--end-year', default=datetime.now().year + 1, help="The end year for the analysis.")
def analysis(start_year, end_year,entity, max_workers ):
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return
    entities = expanded_entity.base_entities
    logger.info(f"Using virtual entity '{entity}' with {len(entities)} base entities")
    if not entities:
        logger.error(f"No entities found for virtual entity '{entity}'")
        return
    with get_bo_conn() as conn:
        try:
            run = RunData.create(conn, "full", [], RunScope.ENTITY, expanded_entity.short_id, None)
            run_id = not_none(run.id)
            if run_id:
                do_analysis(conn, end_year, entities, entity, expanded_entity, max_workers, run, start_year)
                RunData.mark_completed(conn, run_id)
                logger.info(f"Analysis complete for {entity}")
                logger.info(f"Analysis run ID: {run_id}")
                RunData.mark_completed(conn, run_id)
                XferData.sync_run(run_id)
                XferData.sync_model_sections_to_xfer_v2(conn)
        except Exception as e:
            logger.error(f"Error in analysis: {e}")
            console.print_exception(show_locals=False)
            with get_bo_conn() as conn:
                if run_id:
                    RunData.mark_failed(conn, run_id, str(e))




def do_analysis(conn, end_year, entities, entity, expanded_entity:VirtualEntityExpandedModel, max_workers, run:AnalysisRunModel, start_year):
    run_id = not_none(run.id)
    if run_id:
        # Tracking now handled by new trace system throughout the pipeline

        XferData.sync_virtual_entities(conn)

        create_effect_flags(expanded_entity, start_year, end_year, run_id)

        run_claims(expanded_entity, start_year, end_year, run_id, True, True)
        run_promises(expanded_entity, start_year - 10, end_year - 2, run_id, True, True)
        run_selective_highlighting(end_year, entity, expanded_entity, run, start_year)


        run_prediction(end_year, 3, "prophet", False, False, run_id, start_year, expanded_entity)

        XferData.sync_score_to_xfer_v2(conn, run_id)
        XferData.sync_model_sections_to_xfer_v2(conn)

        run_cms_publish(run_id, expanded_entity, max_workers, start_year)

    # transfer_analysis(entity, run_id, None)

# Selective Highlighting only command
@ana.command()
@click.option("--entity", required=True, help="The name of the entity to analyze selective highlighting for")
@click.option("--start-year", default=datetime.now().year - 10, help="Start year for selective highlighting analysis")
@click.option("--end-year", default=datetime.now().year, help="End year for selective highlighting analysis")
@click.option("--run-id", default=None, type=int, help="Specify an existing run ID to use, or leave empty to create a new run")
def selective_highlighting(entity: str, start_year: int, end_year: int, run_id: Optional[int] = None):
    """Analyze selective highlighting for the entity."""
    # Import functionality from dedicated module (lazy loading)
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics

    # Get the virtual entity and its base entities
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return

    # Use the base entities from the virtual entity
    entities = expanded_entity.base_entities
    logger.info(f"Using virtual entity '{entity}' with {len(entities)} base entities")

    if not entities:
        logger.error(f"No entities found for virtual entity '{entity}'")
        return

    # Use the virtual entity directly
    logger.info(f"Analyzing selective highlighting for virtual entity '{entity}'")

    with get_bo_conn() as conn:
        if run_id is None:
            run = RunData.create(conn, "full", [], RunScope.ENTITY, expanded_entity.short_id, None)
            if run is None:
                logger.error("No run ID found")
                return
            run_id = not_none(run.id)
        else:
            run=RunData.get_by_id(conn, run_id)
            if run is None:
                logger.error(f"Run {run_id} not found")
                return
        # Call the implementation
        try:
            logger.info(f"Creating profile for entity {entity} using run_id {run_id}")
            run_selective_highlighting(end_year, entity, expanded_entity, run, start_year)
            RunData.mark_completed(conn, run_id)
            XferData.sync_run(run_id)
            XferData.sync_score_to_xfer_v2(conn, run_id)
            XferData.sync_virtual_entities(conn)
            XferData.sync_cherry_to_xfer_v2(conn, run_id)
            logger.info(f"Selective highlighting analysis complete for {entity}")
            logger.info(f"Analysis run ID: {run_id}")
        except Exception as e:
            logger.error(f"Error in analysis: {e}")
            console.print_exception(show_locals=False)
            with get_bo_conn() as conn:
                RunData.mark_failed(conn, run_id, str(e))

@ana.command()
@click.option('--entity', required=True, help="The name of the entity to analyze claims for")
@click.option('--start-year', default=datetime.now().year - 10, help="Start year for claim analysis")
@click.option('--end-year', default=datetime.now().year, help="End year for claim analysis")
@click.option('--run-id', default=None, type=int,
              help="Specify an existing run ID to use, or leave empty to create a new run")
@click.option('--debug', is_flag=True, help="Show additional debugging information")
@click.option('--report', is_flag=True, help="Generate a detailed report of the analysis results")
def claims(entity: str, start_year: int, end_year: int, run_id: Optional[int] = None, debug: bool = False, report: bool = False):
    """Analyze claims made by the entity, finding evidence to support or contradict them."""
    # Import functionality from dedicated module (lazy loading)
    from eko.analysis_v2.heart.trust_and_reliability.claim_evidence import run_claims
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics

    # Get the virtual entity and its base entities
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return

    # Use the base entities from the virtual entity
    entities = expanded_entity.base_entities
    logger.info(f"Using virtual entity '{entity}' with {len(entities)} base entities")

    if not entities:
        logger.error(f"No entities found for virtual entity '{entity}'")
        return

    # Use the virtual entity directly
    logger.info(f"Analyzing claims for virtual entity '{entity}'")

    result = run_claims(expanded_entity, start_year, end_year, run_id, debug, report)

    if result and result.get("success"):
        logger.info(
            f"Successfully analyzed {result['total_claims']} claims and created {result['successful_verdicts']} verdicts")
        logger.info(f"Analysis run ID: {result['run_id']}")

        if report:
            # If a report was generated, print it or show the path
            if "report" in result:
                print(result["report"])
            elif "report_path" in result:
                print(f"Report saved to: {result['report_path']}")
        else:
            # Print basic summary
            print("\n" + "=" * 80)
            print(f"Claim Analysis Summary for {result['entity']}")
            print("-" * 80)
            print(f"Entities analyzed: {result.get('entity_count', 1)}")
            print(f"Total claims analyzed: {result['total_claims']}")
            print(f"Successful verdicts: {result['successful_verdicts']}")
            print(f"Run ID: {result['run_id']}")
            print("=" * 80 + "\n")
    else:
        error_msg = result.get("error", "Unknown error") if result else "Failed to analyze claims"
        logger.error(error_msg)


@ana.command()
@click.option('--entity', required=True, help="The name of the entity to analyze promises for")
@click.option('--start-year', default=datetime.now().year - 15, help="Start year for promise analysis")
@click.option('--end-year', default=datetime.now().year - 2,
              help="End year for promise analysis (should be at least 2 years before current year to allow time for fulfillment)")
@click.option('--run-id', default=None, type=int,
              help="Specify an existing run ID to use, or leave empty to create a new run")
@click.option('--debug', is_flag=True, help="Show additional debugging information")
@click.option('--report', is_flag=True, help="Generate a detailed report of the analysis results")
def promises(entity: str, start_year: int, end_year: int, run_id: Optional[int] = None, debug: bool = False, report: bool = False):
    """Analyze promises made by the entity, determining if they were kept or broken."""
    # Import functionality from dedicated module (lazy loading)
    from eko.analysis_v2.heart.trust_and_reliability.promise_analysis import run_promises
    from eko.entities.virtual_queries import get_virtual_entity_for_analytics

    # Get the virtual entity and its base entities
    expanded_entity = get_virtual_entity_for_analytics(virtual_entity_name=entity)
    if not expanded_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return

    # Use the base entities from the virtual entity
    entities = expanded_entity.base_entities
    logger.info(f"Using virtual entity '{entity}' with {len(entities)} base entities")

    if not entities:
        logger.error(f"No entities found for virtual entity '{entity}'")
        return

    # Use the virtual entity directly
    logger.info(f"Analyzing promises for virtual entity '{entity}'")

    result = run_promises(expanded_entity, start_year, end_year, run_id, debug)

    if result and result.get("success"):
        logger.info(
            f"Successfully analyzed {result['total_promises']} promises and created {result['successful_verdicts']} verdicts")
        logger.info(f"Analysis run ID: {result['run_id']}")

        if report:
            # Generate detailed report
            report_text = generate_promise_report(result)
            print(report_text)
        else:
            # Print basic summary
            print("\n" + "=" * 80)
            print(f"Promise Analysis Summary for {result['entity']}")
            print("-" * 80)
            print(f"Entities analyzed: {result.get('entity_count', 1)}")
            print(f"Total promises analyzed: {result['total_promises']}")
            print(f"Successful verdicts: {result['successful_verdicts']}")
            print(f"Run ID: {result['run_id']}")
            print("=" * 80 + "\n")
    else:
        error_msg = result.get("error", "Unknown error") if result else "Failed to analyze promises"
        logger.error(error_msg)



@ana.command(name="resp-flag")
@click.option("--effect-flag-id", required=True, type=int, help="The ID of the effect flag to analyze")
def resp_flag(effect_flag_id):
    """Analyze and store responsibility matrix for a specific effect flag."""
    from eko.analysis_v2.responsibility import analyze_and_store_responsibility

    logger.info(f"Analyzing responsibility matrix for effect flag {effect_flag_id}")

    responsibility_id = analyze_and_store_responsibility(effect_flag_id)

    if responsibility_id:
        logger.info(f"Responsibility matrix stored with ID {responsibility_id}")
    else:
        logger.error(f"Failed to analyze responsibility matrix for effect flag {effect_flag_id}")


@ana.command(name="resp-all")
def resp_all():
    """Analyze and store responsibility matrices for all effect flags."""
    from eko.analysis_v2.responsibility import analyze_all_effect_flags

    logger.info("Analyzing responsibility matrices for all effect flags")

    results = analyze_all_effect_flags()

    success_count = sum(1 for _, responsibility_id in results if responsibility_id is not None)
    total_count = len(results)

    logger.info(f"Analysis completed. Processed {total_count} effect flags, {success_count} succeeded.")

    # Log details about failed analyses
    failed_flags = [(effect_flag_id, responsibility_id) for effect_flag_id, responsibility_id in results if
                    responsibility_id is None]
    if failed_flags:
        logger.warning(f"Failed to analyze {len(failed_flags)} effect flags:")
        for effect_flag_id, _ in failed_flags:
            logger.warning(f"  - Effect flag ID: {effect_flag_id}")


# Register the create_flags command from effect_command
ana.add_command(create_flags)





@ana.command()
@click.option('--entity', required=True, help="The name or ID of the virtual entity to analyze")
@click.option('--run-id', type=int, help="The ID of the analysis run to use")
def vague(entity: str, run_id: int = None):
    """
    Analyze vague terms used by an entity.

    This command analyzes the use of vague terms in an entity's ESG communications,
    identifying potential instances of greenwashing through vague language.

    Args:
        entity: Name or ID of the virtual entity to analyze
        run_id: ID of the analysis run to use (optional)
    """
    # Try to get the virtual entity by name first
    virt_entity = get_virtual_entity_by_name(entity)

    # If not found, try to get by ID
    if not virt_entity and entity.isdigit():
        virt_entity = get_virtual_entity_by_id(int(entity))

    if not virt_entity:
        logger.error(f"Virtual entity '{entity}' not found")
        return

    # If no run ID is provided, create a new run
    if not run_id:
        with get_bo_conn() as conn:
            run = RunData.create_full_run(
                conn=conn,
                models=["vague_terms"],
                scope="entity",
                target=virt_entity.short_id
            )
            run_id = run.id
            logger.info(f"Created new run with ID {run_id}")

    # Run the analysis
    result = analyze_vague_terms_command(virt_entity.id, run_id)

    if result["success"]:
        logger.info(f"Successfully analyzed {result['analyses_count']} vague terms for {result['entity']}")
        logger.info(f"Analysis run ID: {result['run_id']}")

        # Print summary
        print("\n" + "=" * 80)
        print(f"Vague Terms Analysis Summary for {result['entity']}")
        print("-" * 80)
        print(f"Vague terms analyzed: {result['analyses_count']}")
        print(f"Document chunks analyzed: {result['summary_ids_count']}")
        print(f"Run ID: {result['run_id']}")
        print("=" * 80 + "\n")
    else:
        error_msg = result.get("error", "Unknown error")
        logger.error(error_msg)


@ana.command(name="measure-impact")
@click.argument('event_description')
@click.option('--run-id', type=int, help="Optional run ID for tracking")
@click.option('--evaluate', is_flag=True, help="Run evaluation framework on the result")
@click.option('--verbose', is_flag=True, help="Show detailed results")
def measure_impact_command(event_description: str, run_id: Optional[int], evaluate: bool, verbose: bool):
    """
    Measure the impact of a described event across multiple dimensions.
    
    EVENT_DESCRIPTION: Description of the event to analyze for impact
    """
    from eko.analysis_v2.effects.impact import ImpactMeasurementService
    
    logger.info(f"Measuring impact for event: {event_description[:100]}...")
    
    with get_bo_conn() as conn:
        service = ImpactMeasurementService()
        
        try:
            # Measure the impact
            measurement, evaluation = service.measure_impact(event_description, run_id)
            print("MEASUREMENT")
            print(json.dumps(measurement.model_dump(), indent=2))
            print("EVALUATION")
            print(json.dumps(evaluation.model_dump(), indent=2))

            # Print basic results
            print("\n" + "=" * 80)
            print("IMPACT MEASUREMENT RESULTS")
            print("=" * 80)
            print(f"Event: {measurement.event_summary}")
            print(f"Assessment ID: {measurement.event_id}")
            print(f"Model used: {measurement.model_used}")
            print(f"Assessed at: {measurement.assessed_at}")
            print("-" * 80)
            
            # Show aggregate scores
            print(f"Total Harm Score: {measurement.harm_score:.2f}/3.0")
            print(f"Total Benefit Score: {measurement.benefit_score:.2f}/3.0")
            print(f"Net Impact Score: {measurement.net_impact_score:+.2f} ({'positive' if measurement.net_impact_score > 0 else 'negative'} impact)")
            print("-" * 80)
            
            if verbose:
                # Show detailed breakdown
                print("\nHARM ASSESSMENT:")
                _print_assessment(measurement.harm_assessment)
                
                print("\nBENEFIT ASSESSMENT:")
                _print_assessment(measurement.benefit_assessment)
                
                if measurement.key_uncertainties:
                    print("\nKEY UNCERTAINTIES:")
                    for i, uncertainty in enumerate(measurement.key_uncertainties, 1):
                        print(f"  {i}. {uncertainty}")
                
                if measurement.ethical_considerations:
                    print("\nETHICAL CONSIDERATIONS:")
                    for i, consideration in enumerate(measurement.ethical_considerations, 1):
                        print(f"  {i}. {consideration}")
            
            # Run evaluation if requested
            if evaluate:
                print("\n" + "=" * 80)
                print("EVALUATION RESULTS")
                print("=" * 80)
                print(f"Overall Quality: {evaluation.overall_quality.upper()}")
                print(f"Completeness Score: {evaluation.completeness_score:.2f}/1.0")
                
                # Show bias detection
                bias = evaluation.bias_detection
                if bias.detected_issues:
                    print("\nPOTENTIAL BIASES DETECTED:")
                    for issue in bias.detected_issues:
                        print(f"  ⚠️  {issue}")
                else:
                    print("\n✅ No obvious biases detected")
                
                # Show consistency issues
                consistency = evaluation.consistency_checks
                if consistency.issues:
                    print("\nCONSISTENCY ISSUES:")
                    for issue in consistency.issues:
                        print(f"  ⚠️  {issue}")
                else:
                    print("\n✅ No consistency issues detected")
                
                # Show confidence analysis
                confidence_analysis = evaluation.confidence_analysis
                confidence_dist = confidence_analysis.confidence_distribution
                print(f"\nCONFIDENCE DISTRIBUTION:")
                print(f"  High: {confidence_dist['high']}, Medium: {confidence_dist['medium']}, Low: {confidence_dist['low']}")
                print(f"  Average confidence: {confidence_analysis.avg_confidence:.2f}/1.0")
            
            print("=" * 80 + "\n")
            logger.info("Impact measurement completed successfully")
            
        except Exception as e:
            logger.error(f"Failed to measure impact: {str(e)}")
            raise


def _print_assessment(assessment):
    """Helper function to print assessment details."""
    dimensions = [
        ("Animals", assessment.animals),
        ("Humans", assessment.humans), 
        ("Environment", assessment.environment)
    ]
    
    for name, dimension in dimensions:
        print(f"\n  {name}:")
        print(f"    Score: {dimension.score:.2f}/1.0 (Confidence: {dimension.confidence})")
        print(f"    Reasoning: {dimension.reasoning}")
        if hasattr(dimension, 'temporal_breakdown'):
            temporal = dimension.temporal_breakdown
            print(f"    Immediate: {temporal.immediate}")
            print(f"    Medium-term: {temporal.medium_term}")
            print(f"    Long-term: {temporal.long_term}")


# Add the create_flags command to the ana group
ana.add_command(create_flags)
