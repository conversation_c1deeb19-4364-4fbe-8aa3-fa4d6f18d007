pyproject.toml
src/backoffice.egg-info/PKG-INFO
src/backoffice.egg-info/SOURCES.txt
src/backoffice.egg-info/dependency_links.txt
src/backoffice.egg-info/entry_points.txt
src/backoffice.egg-info/requires.txt
src/backoffice.egg-info/top_level.txt
src/cli/__init__.py
src/cli/analysis_v2.py
src/cli/check_viz_data.py
src/cli/cms_command.py
src/cli/crawl_command.py
src/cli/effect_command.py
src/cli/entity_commands.py
src/cli/fix_command.py
src/cli/launch_traceability_dash.py
src/cli/llm_evals.py
src/cli/pipeline_command.py
src/cli/prediction_v2_command.py
src/cli/scrape_command.py
src/cli/selective_highlighting.py
src/cli/test_command.py
src/cli/vague_terms_command.py
src/cli/virtual_entity_command.py
src/eko/__init__.py
src/eko/settings.py
src/eko/agent/crewai/monitoring.py
src/eko/agent/crewai/telemetry_collector.py
src/eko/agent/crewai/crawl/agents.py
src/eko/agent/crewai/crawl/crawler.py
src/eko/agent/crewai/crawl/memory.py
src/eko/agent/crewai/crawl/tasks.py
src/eko/agent/crewai/crawl/tools.py
src/eko/agent/crewai/crawl/tools_mem/__init__.py
src/eko/agent/crewai/crawl/tools_mem/all_tools.py
src/eko/agent/crewai/crawl/tools_mem/get_progress.py
src/eko/agent/crewai/crawl/tools_mem/track_file.py
src/eko/agent/crewai/crawl/tools_mem/track_insight.py
src/eko/agent/crewai/crawl/tools_mem/track_search_query.py
src/eko/agent/crewai/crawl/tools_mem/track_url.py
src/eko/agent/crewai/crawl/tools_new/__init__.py
src/eko/agent/crewai/crawl/tools_new/all_tools.py
src/eko/agent/crewai/crawl/tools_new/analyze_text_for_relevance.py
src/eko/agent/crewai/crawl/tools_new/base.py
src/eko/agent/crewai/crawl/tools_new/download_pdf.py
src/eko/agent/crewai/crawl/tools_new/extract_esg_insights.py
src/eko/agent/crewai/crawl/tools_new/extract_links.py
src/eko/agent/crewai/crawl/tools_new/fetch_webpage.py
src/eko/agent/crewai/crawl/tools_new/generate_search_queries.py
src/eko/agent/crewai/crawl/tools_new/get_web_domain_info.py
src/eko/agent/crewai/crawl/tools_new/list_discovered_documents.py
src/eko/agent/crewai/crawl/tools_new/news_search.py
src/eko/agent/crewai/crawl/tools_new/search_ch.py
src/eko/agent/crewai/crawl/tools_new/search_gleif.py
src/eko/agent/crewai/crawl/tools_new/search_sec.py
src/eko/agent/crewai/crawl/tools_new/search_web.py
src/eko/agent/crewai/crawl/tools_new/search_wikipedia.py
src/eko/agent/crewai/crawl/tools_new/summarize_content.py
src/eko/agent/crewai/crawl/tools_new/track_document.py
src/eko/agent/crewai/monitoring/__init__.py
src/eko/agent/crewai/monitoring/agent_monitoring.py
src/eko/agent/crewai/monitoring/event_logger.py
src/eko/agent/crewai/monitoring/llm_wrapper.py
src/eko/agent/crewai/observability/__init__.py
src/eko/agent/crewai/observability/agentops_setup.py
src/eko/analysis_v2/__init__.py
src/eko/analysis_v2/citations.py
src/eko/analysis_v2/trace_collector.py
src/eko/analysis_v2/effects/__init__.py
src/eko/analysis_v2/effects/clustering.py
src/eko/analysis_v2/effects/constants.py
src/eko/analysis_v2/effects/effect_flags.py
src/eko/analysis_v2/effects/effect_flags_helpers.py
src/eko/analysis_v2/effects/effect_models.py
src/eko/analysis_v2/effects/flag_demise.py
src/eko/analysis_v2/effects/flag_html_server.py
src/eko/analysis_v2/effects/flag_main.py
src/eko/analysis_v2/effects/flag_metrics.py
src/eko/analysis_v2/effects/flag_reporting.py
src/eko/analysis_v2/effects/flag_utils.py
src/eko/analysis_v2/effects/flag_visualization.py
src/eko/analysis_v2/effects/html_server.py
src/eko/analysis_v2/effects/measure_impact.py
src/eko/analysis_v2/effects/model_section_assignment.py
src/eko/analysis_v2/effects/utils.py
src/eko/analysis_v2/effects/impact/__init__.py
src/eko/analysis_v2/effects/impact/calculator.py
src/eko/analysis_v2/effects/impact/confidence.py
src/eko/analysis_v2/effects/impact/indicators.py
src/eko/analysis_v2/effects/impact/review.py
src/eko/analysis_v2/effects/impact/scale_factors.py
src/eko/analysis_v2/effects/impact/service.py
src/eko/analysis_v2/effects/impact/validation.py
src/eko/analysis_v2/effects/impact/models/__init__.py
src/eko/analysis_v2/effects/impact/models/ass_feedback.py
src/eko/analysis_v2/effects/impact/models/assessment.py
src/eko/analysis_v2/effects/impact/models/base.py
src/eko/analysis_v2/effects/impact/models/bias.py
src/eko/analysis_v2/effects/impact/models/calculation_details.py
src/eko/analysis_v2/effects/impact/models/evaluation.py
src/eko/analysis_v2/effects/impact/models/impact_checks.py
src/eko/analysis_v2/effects/impact/models/impact_measure.py
src/eko/analysis_v2/effects/impact/models/impact_response.py
src/eko/analysis_v2/effects/impact/models/measurement.py
src/eko/analysis_v2/effects/impact/models/review.py
src/eko/analysis_v2/effects/impact/models/scale_factors.py
src/eko/analysis_v2/effects/models/__init__.py
src/eko/analysis_v2/effects/models/effect_flag_response.py
src/eko/analysis_v2/heart/responsibility.py
src/eko/analysis_v2/heart/trust_and_reliability/claim_evidence.py
src/eko/analysis_v2/heart/trust_and_reliability/claim_report.py
src/eko/analysis_v2/heart/trust_and_reliability/claims_and_promises.py
src/eko/analysis_v2/heart/trust_and_reliability/merging.py
src/eko/analysis_v2/heart/trust_and_reliability/promise_analysis.py
src/eko/analysis_v2/heart/trust_and_reliability/promise_report.py
src/eko/analysis_v2/selective_highlighting/__init__.py
src/eko/analysis_v2/selective_highlighting/data.py
src/eko/analysis_v2/selective_highlighting/detection_pgvector.py
src/eko/analysis_v2/selective_highlighting/llm_analysis.py
src/eko/analysis_v2/selective_highlighting/main.py
src/eko/analysis_v2/selective_highlighting/merging.py
src/eko/analysis_v2/selective_highlighting/report.py
src/eko/analysis_v2/selective_highlighting/models/__init__.py
src/eko/analysis_v2/selective_highlighting/models/cherry_picking.py
src/eko/analysis_v2/selective_highlighting/models/flooding.py
src/eko/analysis_v2/vague/__init__.py
src/eko/analysis_v2/vague/vague.py
src/eko/analysis_v2/vague/vague_models.py
src/eko/analysis_v2/vague/vague_terms.py
src/eko/cache/__init__.py
src/eko/cache/pg_cache.py
src/eko/cms/__init__.py
src/eko/cms/api/__init__.py
src/eko/cms/api/reports.py
src/eko/cms/content/__init__.py
src/eko/cms/content/article.py
src/eko/cms/content/create_profile.py
src/eko/cms/util/__init__.py
src/eko/cms/util/markdown_to_lexical.py
src/eko/commands/__init__.py
src/eko/commands/chunk.py
src/eko/commands/db_api.py
src/eko/commands/fix.py
src/eko/commands/scrape.py
src/eko/commands/statements.py
src/eko/config/__init__.py
src/eko/config/feature_flags.py
src/eko/dash/__init__.py
src/eko/dash/app.py
src/eko/dash/callbacks.py
src/eko/dash/callbacks_clustering.py
src/eko/dash/callbacks_entities.py
src/eko/dash/callbacks_errors.py
src/eko/dash/callbacks_filters.py
src/eko/dash/callbacks_flags.py
src/eko/dash/callbacks_overview.py
src/eko/dash/callbacks_traceability_table.py
src/eko/dash/graph_app.py
src/eko/dash/layout.py
src/eko/dash/metrics_app.py
src/eko/dash/pipeline_app.py
src/eko/dash/components/__init__.py
src/eko/dash/components/clustering.py
src/eko/dash/components/entities.py
src/eko/dash/components/errors.py
src/eko/dash/components/flags.py
src/eko/dash/components/overview.py
src/eko/dash/components/traceability_table.py
src/eko/db/__init__.py
src/eko/db/_deprecated_xfer.py
src/eko/db/core.py
src/eko/db/customer.py
src/eko/db/pool.py
src/eko/db/queries.py
src/eko/db/queue.py
src/eko/db/refdata.py
src/eko/db/sync.py
src/eko/db/data/__init__.py
src/eko/db/data/cherry.py
src/eko/db/data/claim.py
src/eko/db/data/effect.py
src/eko/db/data/effect_flag.py
src/eko/db/data/entity.py
src/eko/db/data/feature_manager.py
src/eko/db/data/flag.py
src/eko/db/data/prediction_sync.py
src/eko/db/data/promise.py
src/eko/db/data/run.py
src/eko/db/data/statement.py
src/eko/db/data/statement_embeddings.py
src/eko/db/data/vague.py
src/eko/db/data/vague_term.py
src/eko/db/data/virtual_entity.py
src/eko/db/data/xfer.py
src/eko/domains/__init__.py
src/eko/domains/domain_categorizer.py
src/eko/domains/domain_queries.py
src/eko/entities/__init__.py
src/eko/entities/cluster.py
src/eko/entities/co_entity.py
src/eko/entities/co_entity_rel.py
src/eko/entities/co_entity_util.py
src/eko/entities/companies_house.py
src/eko/entities/gleif.py
src/eko/entities/queries.py
src/eko/entities/sec.py
src/eko/entities/virtual_queries.py
src/eko/entities/wikipedia.py
src/eko/entities/agent/__init__.py
src/eko/entities/agent/tools.py
src/eko/entities/agent/models/__init__.py
src/eko/entities/agent/models/agent.py
src/eko/entities/agent/models/content.py
src/eko/entities/agent/models/insights.py
src/eko/entities/agent/models/types.py
src/eko/llm/__init__.py
src/eko/llm/distil.py
src/eko/llm/llm_nlp.py
src/eko/llm/main.py
src/eko/llm/prompts.py
src/eko/llm/score.py
src/eko/llm/test.py
src/eko/llm/tools.py
src/eko/llm/dynamic_prompts/__init__.py
src/eko/llm/dynamic_prompts/claim_importance.py
src/eko/llm/dynamic_prompts/doc.py
src/eko/llm/providers/__init__.py
src/eko/llm/providers/anthropic_litellm.py
src/eko/llm/providers/base.py
src/eko/llm/providers/claude.py
src/eko/llm/providers/deepseek_litellm.py
src/eko/llm/providers/gemini.py
src/eko/llm/providers/gemini_litellm.py
src/eko/llm/providers/groq.py
src/eko/llm/providers/groq_litellm.py
src/eko/llm/providers/litellm_provider.py
src/eko/llm/providers/ollama_litellm.py
src/eko/llm/providers/openai.py
src/eko/llm/providers/openai_compatible.py
src/eko/llm/providers/openai_compatible_litellm.py
src/eko/llm/providers/openai_litellm.py
src/eko/llm/providers/openai_o1.py
src/eko/llm/providers/runpod.py
src/eko/llm/providers/runpod_litellm.py
src/eko/llm/providers/vertex_provider.py
src/eko/log/__init__.py
src/eko/log/log.py
src/eko/model/__init__.py
src/eko/models/__init__.py
src/eko/models/_doc.py
src/eko/models/citation_model.py
src/eko/models/common.py
src/eko/models/entity.py
src/eko/models/issue.py
src/eko/models/issue_flag.py
src/eko/models/quantified_entity.py
src/eko/models/report.py
src/eko/models/run.py
src/eko/models/simple_entity.py
src/eko/models/statement_metadata.py
src/eko/models/time.py
src/eko/models/vague.py
src/eko/models/virtual_entity.py
src/eko/models/test/__init__.py
src/eko/models/vector/__init__.py
src/eko/models/vector/base_vector_model.py
src/eko/models/vector/base_vector_utils.py
src/eko/models/vector/to_markdown.py
src/eko/models/vector/demise/__init__.py
src/eko/models/vector/demise/demise_model.py
src/eko/models/vector/demise/domain.py
src/eko/models/vector/demise/engagement.py
src/eko/models/vector/demise/entity.py
src/eko/models/vector/demise/entity_type.py
src/eko/models/vector/demise/ethics.py
src/eko/models/vector/demise/impact.py
src/eko/models/vector/demise/motivation.py
src/eko/models/vector/demise/statement.py
src/eko/models/vector/derived/__init__.py
src/eko/models/vector/derived/categories.py
src/eko/models/vector/derived/effect.py
src/eko/models/vector/derived/effect_type.py
src/eko/models/vector/derived/enums.py
src/eko/models/vector/derived/reliability.py
src/eko/models/vector/derived/trust.py
src/eko/models/xfer/__init__.py
src/eko/models/xfer/xfer_cherry.py
src/eko/models/xfer/xfer_claim.py
src/eko/models/xfer/xfer_effect_flag.py
src/eko/models/xfer/xfer_promise.py
src/eko/models/xfer/xfer_run.py
src/eko/models/xfer/xfer_score.py
src/eko/models/xfer/xfer_vague.py
src/eko/nlp/__init__.py
src/eko/nlp/chunk.py
src/eko/nlp/classifier.py
src/eko/nlp/clean.py
src/eko/nlp/company.py
src/eko/nlp/core.py
src/eko/nlp/country.py
src/eko/nlp/esg.py
src/eko/nlp/esg_trainer.py
src/eko/nlp/lang.py
src/eko/nlp/util.py
src/eko/pdf/__init__.py
src/eko/pdf/main.py
src/eko/prediction_v2/__init__.py
src/eko/prediction_v2/analysis.py
src/eko/prediction_v2/cli.py
src/eko/prediction_v2/clustering.py
src/eko/prediction_v2/dao.py
src/eko/prediction_v2/regression.py
src/eko/prediction_v2/utils.py
src/eko/prediction_v2/models/__init__.py
src/eko/prediction_v2/models/analysis.py
src/eko/prediction_v2/models/cluster.py
src/eko/prediction_v2/models/prediction.py
src/eko/prediction_v2/models/transfer.py
src/eko/prediction_v2/models/types.py
src/eko/predictive/__init__.py
src/eko/predictive/analysis.py
src/eko/predictive/cli.py
src/eko/predictive/dao.py
src/eko/predictive/models.py
src/eko/predictive/regression.py
src/eko/predictive/temporal_clustering.py
src/eko/predictive/utils.py
src/eko/score/score_flag.py
src/eko/scrape/__init__.py
src/eko/scrape/common_crawl_middleware.py
src/eko/scrape/doc_processor.py
src/eko/scrape/eko_reports.py
src/eko/scrape/pdf_processor.py
src/eko/scrape/reports.py
src/eko/scrape/slack.py
src/eko/scrape/spider.py
src/eko/scrape/urls.py
src/eko/scrape/webpage_processor.py
src/eko/stat/__init__.py
src/eko/stat/cluster.py
src/eko/statements/__init__.py
src/eko/statements/demise.py
src/eko/statements/extract.py
src/eko/statements/llm_comparator.py
src/eko/statements/metadata.py
src/eko/statements/prompts.py
src/eko/statements/reconciliation.py
src/eko/statements/split.py
src/eko/typing/__init__.py
src/eko/util/__init__.py
src/eko/util/hash.py
src/eko/util/obj.py
src/eko/util/profiler.py
src/eko/util/slack.py
src/eko/util/timeout.py
src/eko/web/__init__.py
src/eko/web/get.py
src/llm_evals/__init__.py
src/llm_evals/demise_eval.py
src/llm_evals/extract_fixtures.py
src/llm_evals/html_report.py
src/llm_evals/metadata_eval.py
src/llm_evals/run_evals.py
src/llm_evals/test_data.py
src/llm_evals/utils.py
tests/test_enhanced_impact_evaluation.py
tests/test_prediction_v2.py
tests/test_statement_domain_filtering.py