Metadata-Version: 2.4
Name: backoffice
Version: 0.0.1
Requires-Python: ~=3.11
Requires-Dist: psycopg-binary
Requires-Dist: requests
Requires-Dist: PyPDF2
Requires-Dist: nltk
Requires-Dist: openai
Requires-Dist: groq
Requires-Dist: diskcache
Requires-Dist: python-dotenv
Requires-Dist: pymupdf
Requires-Dist: pytesseract
Requires-Dist: pycryptodome
Requires-Dist: jsonpointer
Requires-Dist: fuzzywuzzy
Requires-Dist: boto3
Requires-Dist: ollama
Requires-Dist: rank-bm25
Requires-Dist: appdirs
Requires-Dist: retry
Requires-Dist: spacy
Requires-Dist: retrying
Requires-Dist: progress
Requires-Dist: anthropic
Requires-Dist: pymupdf4llm
Requires-Dist: pydantic
Requires-Dist: instructor[litellm]
Requires-Dist: click
Requires-Dist: openparse
Requires-Dist: loguru
Requires-Dist: psycopg_pool
Requires-Dist: fastapi
Requires-Dist: uvicorn
Requires-Dist: jinja2
Requires-Dist: scrapy
Requires-Dist: bs4
Requires-Dist: langdetect
Requires-Dist: goose3
Requires-Dist: newspaper4k[all]
Requires-Dist: lxml[html-clean]
Requires-Dist: html2text
Requires-Dist: datasets
Requires-Dist: joblib
Requires-Dist: sentry-sdk
Requires-Dist: tiktoken
Requires-Dist: warcio
Requires-Dist: playwright
Requires-Dist: sec-api
Requires-Dist: pycountry
Requires-Dist: textdistance
Requires-Dist: dill
Requires-Dist: markdown
Requires-Dist: gh-md-to-html
Requires-Dist: markdown2
Requires-Dist: psycopg[binary]
Requires-Dist: torchvision
Requires-Dist: setuptools<71.0.0
Requires-Dist: transformers==4.48.1
Requires-Dist: mplcursors
Requires-Dist: pytest
Requires-Dist: torchaudio
Requires-Dist: tensorboard
Requires-Dist: accelerate
Requires-Dist: torch
Requires-Dist: mistune>=2.0.0
Requires-Dist: seaborn
Requires-Dist: tabulate
Requires-Dist: pandas
Requires-Dist: pyright
Requires-Dist: numpy
Requires-Dist: matplotlib
Requires-Dist: scikit-learn
Requires-Dist: python-slugify
Requires-Dist: dash
Requires-Dist: dash-bootstrap-components
Requires-Dist: plotly
Requires-Dist: runpod
Requires-Dist: google-genai
Requires-Dist: pydantic-settings
Requires-Dist: python-levenshtein
Requires-Dist: langchain
Requires-Dist: spider-client
Requires-Dist: jsonpickle
Requires-Dist: crewai[agentops]
Requires-Dist: pip>=25.1
Requires-Dist: pgvector>=0.4.1
Requires-Dist: scrapingbee>=2.0.1
Requires-Dist: litellm
Requires-Dist: google-cloud-aiplatform>=1.93.0
Requires-Dist: google-generativeai>=0.8.5
Requires-Dist: deepeval>=2.9.3
Requires-Dist: rich>=13.9.4
Requires-Dist: pyrefly>=0.16.2
Requires-Dist: json-repair>=0.44.1
Requires-Dist: boto3-stubs==1.38.45
Requires-Dist: html-to-markdown>=1.4.0
Requires-Dist: google-adk>=1.5.0
Requires-Dist: opentelemetry-api>=1.34.1
Requires-Dist: opentelemetry-sdk>=1.34.1
Requires-Dist: opentelemetry-exporter-otlp>=1.34.1
Requires-Dist: openinference-instrumentation-crewai>=0.1.10
