"""
Cluster models for predictive analytics.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class DSO_ClusterModel(BaseModel):
    """Model representing a cluster based on Domain+Subject+Object."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    year: int
    domain_centroid: List[float] = Field(default_factory=list)
    subject_centroid: List[float] = Field(default_factory=list)
    object_centroid: List[float] = Field(default_factory=list)
    statement_ids: List[int] = Field(default_factory=list)
    size: int
    coherence: float
    created_at: Optional[datetime] = None

    @property
    def dso_centroid(self) -> List[float]:
        """
        Get the combined Domain+Subject+Object centroid.

        Returns:
            The combined vector representation
        """
        return self.domain_centroid + self.subject_centroid + self.object_centroid