"""
Prediction models for predictive analytics.
"""

from datetime import datetime
from typing import List, Optional, Dict

from pydantic import BaseModel, Field

from .types import ComponentType, RegressionModelType


class ComponentPredictionModel(BaseModel):
    """Model representing a predicted component trend."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    cluster_id: int
    component_type: ComponentType
    predicted_year: int
    final_historical_year: int
    predicted_vector: List[float] = Field(default_factory=list)
    historical_vector: List[float] = Field(default_factory=list)
    historical_vectors_by_year: Dict[int, List[float]] = Field(default_factory=dict, description="Dictionary mapping historical years to their corresponding vectors")
    confidence: float
    model_type: RegressionModelType
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None