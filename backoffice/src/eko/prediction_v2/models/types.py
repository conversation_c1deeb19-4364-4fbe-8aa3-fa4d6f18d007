"""
Type definitions for predictive analytics.
"""

from enum import Enum


class RegressionModelType(str, Enum):
    """Enum for regression model types."""
    VAR = "var"
    GAUSSIAN_PROCESS = "gaussian_process"
    PROPHET = "prophet"
    ENSEMBLE = "ensemble"


class ComponentType(str, Enum):
    """Enum for DEMISE component types."""
    MOTIVATION = "motivation"
    STATEMENT_TYPE = "statement_type"
    ENGAGEMENT = "engagement"
    IMPACT = "impact"