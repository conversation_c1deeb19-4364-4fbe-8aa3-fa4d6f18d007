"""
Analysis models for predictive analytics.
"""

from datetime import datetime
from typing import List, Optional, Dict

from pydantic import BaseModel, Field

from .types import ComponentType


class PredictiveComponentAnalysisModel(BaseModel):
    """Model representing a human-readable analysis of a predicted component trend."""
    id: Optional[int] = None
    run_id: int
    prediction_id: int
    virtual_entity_id: int
    cluster_id: int
    component_type: ComponentType
    year: int
    summary: str
    detailed_analysis: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None


class ComponentAnalysisResponse(BaseModel):
    """Response model for LLM-generated component analysis."""
    summary: str
    detailed_analysis: str
    potential_risks: List[str]
    potential_opportunities: List[str]
    confidence: float


class ClusterAnalysisModel(BaseModel):
    """Model representing a combined analysis of all components for a cluster."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    cluster_id: int
    year: int
    summary: str
    detailed_analysis: str
    motivation_summary: str
    statement_type_summary: Optional[str] = None
    engagement_summary: str
    impact_summary: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    historical_vectors_by_year: Dict[int, Dict[str, List[float]]] = Field(default_factory=dict, description="Dictionary mapping historical years to component vectors by type")
    created_at: Optional[datetime] = None


class EntityYearAnalysisModel(BaseModel):
    """Model representing a combined analysis of all clusters for an entity in a specific year."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    year: int
    summary: str
    detailed_analysis: str
    cluster_summaries: Dict[int, str] = Field(default_factory=dict)
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None


class ClusterAnalysisResponse(BaseModel):
    """Response model for LLM-generated cluster analysis."""
    summary: str
    detailed_analysis: str
    potential_risks: List[str]
    potential_opportunities: List[str]
    confidence: float


class EntityYearAnalysisResponse(BaseModel):
    """Response model for LLM-generated entity-year analysis."""
    summary: str
    detailed_analysis: str
    potential_risks: List[str]
    potential_opportunities: List[str]
    confidence: float