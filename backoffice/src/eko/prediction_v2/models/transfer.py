"""
Transfer models for predictive analytics.
"""

from datetime import datetime
from typing import List, Optional, Dict

from pydantic import BaseModel, Field


class XferPredictiveComponentAnalysisModel(BaseModel):
    """Transfer model for predictive component analysis."""
    id: Optional[int] = None
    run_id: int
    prediction_id: int
    virtual_entity_id: int
    cluster_id: int
    component_type: str
    year: int
    summary: str
    detailed_analysis: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None


class XferClusterAnalysisModel(BaseModel):
    """Transfer model for cluster analysis."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    cluster_id: int
    year: int
    summary: str
    detailed_analysis: str
    motivation_summary: str
    statement_type_summary: Optional[str] = None
    engagement_summary: str
    impact_summary: str
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    historical_vectors_by_year: Dict[str, Dict[str, List[float]]] = Field(default_factory=dict, description="Dictionary mapping historical years to component vectors by type")
    created_at: Optional[datetime] = None


class XferEntityYearAnalysisModel(BaseModel):
    """Transfer model for entity-year analysis."""
    id: Optional[int] = None
    run_id: int
    virtual_entity_id: int
    year: int
    summary: str
    detailed_analysis: str
    cluster_summaries: Dict[str, str] = Field(default_factory=dict)
    potential_risks: List[str] = Field(default_factory=list)
    potential_opportunities: List[str] = Field(default_factory=list)
    confidence: float
    core_domain_keys: List[str] = Field(default_factory=list, description="Core domain keys with top values (>0.7)")
    overall_impact: float = 0.0
    created_at: Optional[datetime] = None