"""
Flooding model for selective highlighting detection.
"""

from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any

from eko.models.citation_model import Citation
from eko.models.statement_metadata import StatementAndMetadata


class FloodingModel(BaseModel):
    """Model representing a flooding instance."""
    id: Optional[int] = None
    virt_entity_id: int
    positive_statements: List[StatementAndMetadata] = Field(default_factory=list)
    negative_statements: List[StatementAndMetadata] = Field(default_factory=list)
    domain_vector: List[float]
    average_positive_impact: float
    negative_impact: float
    positive_count: int
    created_at: Optional[datetime] = None
    run_id: Optional[int] = None
    # LLM-generated fields
    analysis: str = ""
    reason: str = ""
    label: str = ""
    # Citations from the analysis
    citations: List[Citation] = Field(default_factory=list)
    # Additional metrics from LLM analysis
    severity: int = 0  # Impact score from LLM analysis
    confidence: int = 0  # Confidence score from LLM analysis
    authenticity: int = 0  # Authenticity score from LLM analysis
    # Merging information
    merged_from_ids: List[int] = Field(default_factory=list)  # IDs of models that were merged to create this one
    is_merged: bool = False  # Whether this model is a result of merging
    merge_type: Optional[str] = None  # Type of merge: "domain", "text", or "domain+text"
    # Trace data for comprehensive pipeline tracking
    trace_data: Optional[Dict[str, Any]] = None

    @property
    def positive_statement_ids(self) -> List[int]:
        """Get IDs of positive statements for database persistence."""
        return [stmt.id for stmt in self.positive_statements if stmt.id is not None]

    @property
    def negative_statement_ids(self) -> List[int]:
        """Get IDs of negative statements for database persistence."""
        return [stmt.id for stmt in self.negative_statements if stmt.id is not None]