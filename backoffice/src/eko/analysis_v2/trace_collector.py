"""
Trace collection system for comprehensive pipeline tracking.

This module provides a centralized system for collecting trace data throughout
the effect and flag creation pipeline, replacing the distributed trk_* table approach.
Uses Pydantic models following project conventions.
"""

import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from loguru import logger
from pydantic import BaseModel, Field

from eko.models.vector.derived.effect import EffectModel
from eko.models.statement_metadata import StatementAndMetadata


class ProcessingStep(BaseModel):
    """Represents a single processing step in the pipeline."""
    step: str = Field(..., description="Name of the processing step")
    timestamp: datetime = Field(default_factory=datetime.now, description="When the step was executed")
    duration_ms: int = Field(..., description="Duration of the step in milliseconds")
    result: Dict[str, Any] = Field(default_factory=dict, description="Results from the processing step")
    llm_call: Optional[Dict[str, Any]] = Field(None, description="LLM call details if applicable")
    algorithm: Optional[str] = Field(None, description="Algorithm used in the step")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Parameters used in the step")
    error: Optional[str] = Field(None, description="Error message if step failed")


class SourceStatement(BaseModel):
    """Source statement data for tracing."""
    id: int = Field(..., description="Statement ID")
    text: str = Field(..., description="Statement text (truncated for storage)")
    classification: str = Field(..., description="Effect type classification")
    confidence: float = Field(..., description="Classification confidence score")
    demise_scores: Dict[str, Any] = Field(default_factory=dict, description="DEMISE model scores")


class SourceEffect(BaseModel):
    """Source effect data for tracing."""
    id: int = Field(..., description="Effect ID")
    title: str = Field(..., description="Effect title")
    cluster_id: str = Field(default="", description="Clustering identifier")
    merged_from: List[int] = Field(default_factory=list, description="IDs of effects that were merged")


class SourceData(BaseModel):
    """Source data used in processing."""
    statements: List[SourceStatement] = Field(default_factory=list, description="Source statements")
    effects: List[SourceEffect] = Field(default_factory=list, description="Source effects")  
    documents: List[str] = Field(default_factory=list, description="Document identifiers")


class QualityMetrics(BaseModel):
    """Quality metrics for the processing."""
    confidence_score: float = Field(..., description="Overall confidence score")
    data_completeness: float = Field(..., description="Data completeness percentage")
    processing_errors: List[str] = Field(default_factory=list, description="Processing errors encountered")
    anomalies_detected: List[str] = Field(default_factory=list, description="Anomalies detected during processing")


class TraceData(BaseModel):
    """Complete trace data structure."""
    version: str = Field(default="1.0", description="Trace data format version")
    created_at: datetime = Field(default_factory=datetime.now, description="When trace was created")
    pipeline_stage: str = Field(..., description="Pipeline stage this trace represents")
    source_data: SourceData = Field(default_factory=SourceData, description="Source data used")
    processing_steps: List[ProcessingStep] = Field(default_factory=list, description="Processing steps executed")
    relationships: Dict[str, List[str]] = Field(default_factory=dict, description="Object relationships")
    quality_metrics: Optional[QualityMetrics] = Field(None, description="Quality metrics")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class TraceCollector:
    """
    Centralized trace collection system for pipeline processing.
    
    This class replaces the distributed trk_* table approach with a unified
    trace collection system that builds comprehensive trace data structures.
    Follows fail-fast principles - raises exceptions on errors.
    """
    
    def __init__(self, run_id: str, pipeline_stage: str):
        if not run_id:
            raise ValueError("run_id is required")
        if not pipeline_stage:
            raise ValueError("pipeline_stage is required")
            
        self.run_id = run_id
        self.pipeline_stage = pipeline_stage
        self.trace_data = TraceData(pipeline_stage=pipeline_stage)
        self.trace_data.metadata["run_id"] = run_id
        self.trace_data.metadata["pipeline_version"] = "2.1.0"
        self._current_step_start: Optional[float] = None
        
    def start_step(self, step_name: str) -> None:
        """Start timing a processing step."""
        if not step_name:
            raise ValueError("step_name is required")
            
        self._current_step_start = time.time()
        logger.debug(f"Starting trace step: {step_name}")
        
    def end_step(
        self,
        step_name: str,
        result: Optional[Dict[str, Any]] = None,
        llm_call: Optional[Dict[str, Any]] = None,
        algorithm: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ) -> None:
        """End timing a processing step and record results."""
        if not step_name:
            raise ValueError("step_name is required")
            
        if self._current_step_start is None:
            logger.warning(f"end_step called for {step_name} without start_step")
            duration_ms = 0
        else:
            duration_ms = int((time.time() - self._current_step_start) * 1000)
            self._current_step_start = None
            
        step = ProcessingStep(
            step=step_name,
            timestamp=datetime.now(),
            duration_ms=duration_ms,
            result=result or {},
            llm_call=llm_call,
            algorithm=algorithm,
            parameters=parameters,
            error=error
        )
        
        self.trace_data.processing_steps.append(step)
        logger.debug(f"Completed trace step: {step_name} ({duration_ms}ms)")
        
    def add_source_statements(self, statements: List[StatementAndMetadata]) -> None:
        """Add source statements to trace data."""
        for stmt_meta in statements:
            stmt = stmt_meta.statement
            stmt_data = SourceStatement(
                id=stmt.id,
                text=stmt.text[:200] + "..." if len(stmt.text) > 200 else stmt.text,
                classification=getattr(stmt, 'effect_type', 'UNKNOWN'),
                confidence=getattr(stmt, 'confidence_score', 0.0),
                demise_scores=getattr(stmt, 'demise_centroid', {})
            )
            self.trace_data.source_data.statements.append(stmt_data)
            
    def add_source_effects(self, effects: List[EffectModel]) -> None:
        """Add source effects to trace data."""
        for effect in effects:
            effect_data = SourceEffect(
                id=effect.id if effect.id else 0,
                title=effect.title,
                cluster_id=getattr(effect, 'cluster_id', ''),
                merged_from=getattr(effect, 'merged_from_ids', [])
            )
            self.trace_data.source_data.effects.append(effect_data)
            
    def add_relationship(self, relationship_type: str, object_ids: List[str]) -> None:
        """Add object relationships to trace data."""
        if not relationship_type:
            raise ValueError("relationship_type is required")
        if not isinstance(object_ids, list):
            raise ValueError("object_ids must be a list")
            
        if relationship_type not in self.trace_data.relationships:
            self.trace_data.relationships[relationship_type] = []
        self.trace_data.relationships[relationship_type].extend(object_ids)
        
    def set_quality_metrics(
        self,
        confidence_score: float,
        data_completeness: float,
        processing_errors: Optional[List[str]] = None,
        anomalies_detected: Optional[List[str]] = None
    ) -> None:
        """Set quality metrics for the processing."""
        if not (0.0 <= confidence_score <= 1.0):
            raise ValueError("confidence_score must be between 0.0 and 1.0")
        if not (0.0 <= data_completeness <= 1.0):
            raise ValueError("data_completeness must be between 0.0 and 1.0")
            
        self.trace_data.quality_metrics = QualityMetrics(
            confidence_score=confidence_score,
            data_completeness=data_completeness,
            processing_errors=processing_errors or [],
            anomalies_detected=anomalies_detected or []
        )
        
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to trace data."""
        if not key:
            raise ValueError("key is required")
        self.trace_data.metadata[key] = value

    def detect_flag_anomalies(self, effect_flag) -> List[str]:
        """
        Detect anomalies in flag creation (e.g., positive statements in red flags).

        Args:
            effect_flag: EffectFlagModel to check for anomalies

        Returns:
            List of anomaly descriptions detected
        """
        anomalies = []
        if not effect_flag or not effect_flag.statement_ids:
            return anomalies

        flag_effect_type = effect_flag.effect_type.name.lower()

        # Check statements for impact/type mismatches
        for stmt in self.trace_data.source_data.statements:
            if stmt.id not in effect_flag.statement_ids:
                continue

            # Get impact score from DEMISE scores if available
            impact_score = stmt.demise_scores.get("impact_value", 0.0) if stmt.demise_scores else 0.0

            # Detect positive statements in RED flags
            if flag_effect_type == "red" and impact_score > 0:
                anomaly = f"Positive impact statement (score: {impact_score}) in RED flag: {stmt.text[:100]}..."
                anomalies.append(anomaly)
                logger.warning(f"Anomaly detected: {anomaly}")

            # Detect negative statements in GREEN flags
            elif flag_effect_type == "green" and impact_score < 0:
                anomaly = f"Negative impact statement (score: {impact_score}) in GREEN flag: {stmt.text[:100]}..."
                anomalies.append(anomaly)
                logger.warning(f"Anomaly detected: {anomaly}")

        # Add anomalies to quality metrics
        if anomalies and self.trace_data.quality_metrics:
            self.trace_data.quality_metrics.anomalies_detected.extend(anomalies)
        elif anomalies:
            self.set_quality_metrics(
                confidence_score=0.5,  # Lower confidence due to anomalies
                data_completeness=1.0,
                processing_errors=[],
                anomalies_detected=anomalies,
            )

        return anomalies
        
    def get_trace_json(self) -> Dict[str, Any]:
        """Get the complete trace data as JSON."""
        return self.trace_data.model_dump(mode='json')

    def get_trace_data(self) -> Dict[str, Any]:
        """Get the complete trace data as a dictionary (alias for get_trace_json)."""
        return self.get_trace_json()
        
    def inherit_from(self, parent_trace: Optional[Dict[str, Any]]) -> None:
        """Inherit trace data from a parent process."""
        if not parent_trace:
            return
            
        # Inherit parent relationships
        parent_relationships = parent_trace.get("relationships", {})
        for rel_type, rel_ids in parent_relationships.items():
            if rel_type not in self.trace_data.relationships:
                self.trace_data.relationships[rel_type] = []
            self.trace_data.relationships[rel_type].extend(rel_ids)
            
        # Inherit parent source data
        parent_source = parent_trace.get("source_data", {})
        if parent_source.get("statements"):
            for stmt_dict in parent_source["statements"]:
                stmt = SourceStatement(**stmt_dict)
                self.trace_data.source_data.statements.append(stmt)
        if parent_source.get("effects"):
            for effect_dict in parent_source["effects"]:
                effect = SourceEffect(**effect_dict)
                self.trace_data.source_data.effects.append(effect)
            
        # Add parent metadata
        parent_run_id = parent_trace.get("metadata", {}).get("run_id")
        if parent_run_id:
            self.add_metadata("parent_trace_id", parent_run_id)


def create_trace_collector(run_id: str, pipeline_stage: str) -> TraceCollector:
    """Factory function to create a trace collector."""
    return TraceCollector(run_id, pipeline_stage)


def create_llm_call_trace(
    model: str,
    prompt_type: str,
    input_tokens: int,
    output_tokens: int,
    cost: float,
    response_summary: Optional[str] = None
) -> Dict[str, Any]:
    """Create LLM call trace data."""
    if not model:
        raise ValueError("model is required")
    if not prompt_type:
        raise ValueError("prompt_type is required")
    if input_tokens < 0:
        raise ValueError("input_tokens must be non-negative")
    if output_tokens < 0:
        raise ValueError("output_tokens must be non-negative")
    if cost < 0:
        raise ValueError("cost must be non-negative")
        
    return {
        "model": model,
        "prompt_type": prompt_type,
        "tokens": {
            "input": input_tokens,
            "output": output_tokens
        },
        "cost": cost,
        "response_summary": response_summary
    }


def create_clustering_trace(
    algorithm: str,
    parameters: Dict[str, Any],
    cluster_count: int,
    silhouette_score: Optional[float] = None
) -> Dict[str, Any]:
    """Create clustering trace data."""
    if not algorithm:
        raise ValueError("algorithm is required")
    if not isinstance(parameters, dict):
        raise ValueError("parameters must be a dictionary")
    if cluster_count < 0:
        raise ValueError("cluster_count must be non-negative")
    if silhouette_score is not None and not (-1.0 <= silhouette_score <= 1.0):
        raise ValueError("silhouette_score must be between -1.0 and 1.0")
        
    return {
        "cluster_count": cluster_count,
        "silhouette_score": silhouette_score,
        "parameters": parameters
    }


def merge_trace_data(traces: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Merge multiple trace data structures into one.
    Follows fail-fast principles - raises exceptions on invalid input.
    """
    if not traces:
        raise ValueError("traces list cannot be empty")
        
    if len(traces) == 1:
        return traces[0]
        
    # Use the most recent trace as base
    merged = traces[-1].copy()
    
    # Merge processing steps from all traces
    all_steps = []
    for trace in traces:
        steps = trace.get("processing_steps", [])
        if not isinstance(steps, list):
            raise ValueError("processing_steps must be a list")
        all_steps.extend(steps)
    merged["processing_steps"] = sorted(all_steps, key=lambda x: x.get("timestamp", ""))
    
    # Merge relationships
    all_relationships = {}
    for trace in traces:
        relationships = trace.get("relationships", {})
        if not isinstance(relationships, dict):
            raise ValueError("relationships must be a dictionary")
        for rel_type, rel_ids in relationships.items():
            if not isinstance(rel_ids, list):
                raise ValueError(f"relationship {rel_type} must be a list")
            if rel_type not in all_relationships:
                all_relationships[rel_type] = []
            all_relationships[rel_type].extend(rel_ids)
    # Remove duplicates
    for rel_type in all_relationships:
        all_relationships[rel_type] = list(set(all_relationships[rel_type]))
    merged["relationships"] = all_relationships
    
    # Merge source data
    merged_source = merged.get("source_data", {})
    for trace in traces[:-1]:  # Skip last one as it's already in merged
        source_data = trace.get("source_data", {})
        for key in ["statements", "effects", "documents"]:
            if key in source_data and key in merged_source:
                # Avoid duplicates by ID
                existing_ids = {item.get("id") for item in merged_source[key] if isinstance(item, dict)}
                for item in source_data[key]:
                    if isinstance(item, dict) and item.get("id") not in existing_ids:
                        merged_source[key].append(item)
                    elif not isinstance(item, dict):
                        merged_source[key].append(item)
    
    if "metadata" not in merged:
        merged["metadata"] = {}
    merged["metadata"]["merged_from_traces"] = len(traces)
    return merged
