"""
Impact measurement service for evaluating described events across multiple dimensions.

This module now serves as a compatibility layer that imports from the modular
impact package structure.
"""

from typing import Optional

from eko.analysis_v2.effects.impact import ImpactMeasurementService


# For backward compatibility, keep the measure_event_impact function
def measure_event_impact(
    event_description: str,
    run_id: Optional[int] = None
):
    """
    Legacy function for measuring event impact.

    Args:
        event_description: Description of the event to analyze
        run_id: Optional run ID for tracking

    Returns:
        Measurement or None if failed
    """
    service = ImpactMeasurementService()
    return service.measure_impact(event_description, run_id)
