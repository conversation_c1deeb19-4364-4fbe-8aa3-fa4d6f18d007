"""
Main impact measurement service.

This module provides the primary interface for measuring impact of described events,
coordinating between LLM assessment, programmatic scoring, and review processes.
"""

import uuid
from datetime import datetime
from typing import Optional, Union, Tuple, Dict, Any
from loguru import logger

from eko.analysis_v2.effects.impact.models.measurement import EventImpactMeasurement
from eko.analysis_v2.effects.impact.models.impact_response import LLMImpactResponse
from eko.analysis_v2.effects.impact.models.impact_measure import ImpactMeasurementReview
from eko.cache.pg_cache import MultiLevelCache
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed, LLMOptions
from eko.analysis_v2.effects.impact.calculator import ImpactScoreCalculator
from eko.analysis_v2.effects.impact.review import ImpactReviewer

cache = MultiLevelCache("impact_measurement")




class ImpactMeasurementService:
    """Service for measuring impact of described events."""

    def __init__(self, model: LLMModel = LLMModel.GEMINI_PRO):
        self.model = model
        self.prompt_version = "1.9"
        self.calculator = ImpactScoreCalculator()
        self.reviewer = ImpactReviewer()

    @cache.memoize(expire=60)
    def measure_impact(
        self,
        event_description: str,
        run_id: Optional[int] = None
    ) -> Union[None, EventImpactMeasurement]:
        """
        Measure the impact of a described event.

        Args:
            event_description: Description of the event to analyze
            run_id: Optional run ID for tracking

        Returns:
            Measurement or None if failed
        """
        if not event_description or not event_description.strip():
            logger.error("Event description is empty")
            raise ValueError("Event description cannot be empty")

        event_id = str(uuid.uuid4())
        logger.info(f"Starting impact measurement for event ID: {event_id}")
        logger.info(f"Event description: {event_description[:200]}...")
        logger.debug(f"Run ID: {run_id}, Model: {self.model.value.name}")

        try:
            measurement = self._measure_with_llm_review(
                event_description, event_id, run_id
            )
            
            if measurement:
                logger.info(f"Impact measurement completed successfully for event {event_id}")
                logger.info(f"Net impact score: {measurement.net_impact_score:.2f}")
            else:
                logger.warning(f"Impact measurement failed for event {event_id}")
                
            return measurement
        except Exception:
            logger.exception(f"Error during impact measurement for event {event_id}")
            raise

    def _measure_with_llm_review(
        self,
        event_description: str,
        event_id: str,
        run_id: Optional[int]
    ) -> Union[None, EventImpactMeasurement]:
        """
        Measure impact with LLM review system that checks for bias and accuracy.
        
        The LLM reviewer now handles the evaluation role previously done programmatically.
        """
        max_iterations = 3
        measurement: EventImpactMeasurement
        comment_measurement: Optional[EventImpactMeasurement] = None
        review: Union[None, ImpactMeasurementReview]= None

        for iteration in range(1, max_iterations + 1):
            logger.info(f"Impact measurement iteration {iteration}/{max_iterations}")
            
            # Get initial LLM assessment
            assessment_result = self._get_llm_assessment(event_description, iteration, review)
            
            # Extract calculation details from the proper field
            calculation_details = assessment_result.calculation_details
            
            # Create measurement result (scores will be calculated automatically by the model)
            measurement = EventImpactMeasurement(
                id=None,
                event_id=event_id,
                run_id=run_id,
                event_summary=assessment_result.event_summary,
                event_description=event_description,
                harm_assessment=assessment_result.harm_assessment,
                benefit_assessment=assessment_result.benefit_assessment,
                key_uncertainties=assessment_result.key_uncertainties,
                ethical_considerations=assessment_result.ethical_considerations,
                assessed_at=self._get_iso_timestamp(),
                model_used=self.model.value.name,
                prompt_version=self.prompt_version,
                calculation_details=calculation_details,
                review_decision= None,
                reviewer_feedback= None,
            )
            
            logger.info(f"Created measurement with calculation details: {'yes' if calculation_details else 'no'}")
            
            # Get review from second LLM
            review = self.reviewer.review_measurement(event_description, measurement, iteration)
            
            if review.review_decision.decision == "accept":
                logger.info(f"LLM reviewer accepted measurement on iteration {iteration}")
                logger.debug(f"Review reasoning: {review.review_decision.reasoning}")
                
                # LLM reviewer now handles quality assessment as part of the review
                # If accepted, we trust the reviewer's judgment
                logger.info(
                    f"Reviewer confidence: {review.review_decision.confidence_in_review}, "
                    f"Bias detected: {review.review_decision.bias_detected}"
                )
                return measurement

            elif review.review_decision.decision == "comment":
                logger.info(f"LLM reviewer requested amendments on iteration {iteration}")
                logger.info(f"Feedback supplied: {review.review_decision.assessment_feedback}")
                logger.info(f"Review reasoning: {review.review_decision.reasoning}")
                

                logger.info(
                    f"Reviewer confidence: {review.review_decision.confidence_in_review}, "
                    f"Biases addressed: {review.review_decision.bias_types}"
                )
                measurement.reviewer_feedback = sorted(review.review_decision.assessment_feedback, key=lambda x: str(x))
                measurement.review_decision = review.review_decision
                # Store the review decision and feedback
                if iteration == max_iterations:
                    return measurement
                else:
                    comment_measurement = measurement
                continue

            elif review.review_decision.decision == "redo":
                logger.warning(f"LLM reviewer rejected measurement on iteration {iteration}")
                logger.warning(f"Rejection reason: {review.review_decision.reasoning}")
                logger.debug(f"Bias detected: {review.review_decision.bias_detected}, Types: {review.review_decision.bias_types}")
                logger.debug(f"Accuracy issues: {review.review_decision.accuracy_issues}")
                
                if iteration == max_iterations:
                    logger.error("Max iterations reached, giving up on measuring impact for this event")
                    return comment_measurement
                # Continue to next iteration
                continue
            
            else:
                raise ValueError(str(f"Unknown review decision: {review.review_decision.decision}, accepting measurement"))

        # Should not reach here
        logger.error("Exited review loop unexpectedly")
        return None

    def _get_llm_assessment(self, event_description: str, iteration: int, review: Optional[ImpactMeasurementReview] = None) -> LLMImpactResponse:
        """Get impact assessment from the primary LLM.
        :param review: 
        """
        prompt = self._get_assessment_prompt()
        previous_review = f"Previous feedback from reviewer\n\n: {review.for_prompt()}" if review else ""
        user_prompt = event_description + "\n" + previous_review
        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = call_llms_typed(
            llms=[self.model],
            messages=messages,
            max_tokens=16000,
            response_model=LLMImpactResponse,
            options=LLMOptions(
                temperature=0.0,
                thinking=True,
                thinking_budget=4000,
                cache_key=f"impact_assessment:v{self.prompt_version}:{user_prompt}{(':' + str(iteration)) if iteration > 0 else ''}",
            ),
        )
        
        if response is None:
            raise ValueError("LLM returned no response for impact assessment")
        
        # Calculate scores programmatically
        response_with_scores, calculation_details = self.calculator.calculate_scores(response)
        
        # Store calculation details in the proper field (now using CompleteCalculationDetails)
        response_with_scores.calculation_details = calculation_details.model_dump()
        
        return response_with_scores

    def _get_assessment_prompt(self) -> str:
        """Get the assessment prompt for the primary LLM."""
        return """You are a specialized impact assessor trained to evaluate actions and events across three critical dimensions: environmental health, human welfare, and animal welfare.

You must provide a complete structured assessment following the LLMImpactResponse model specifications. The model documentation contains comprehensive guidelines for:

- Core assessment principles (equal moral consideration, temporal dynamics, climate change priority)
- Assessment process requirements (event summary, boolean checks, dimensional analysis, uncertainties, ethical considerations)
- Scale factor guidelines (degree, directness, authenticity, deliberateness, reversibility, scope, probability)
- Domain-specific guidance (climate impacts, animal welfare, human impacts, environmental health)

Your response must be thorough, evidence-based, and consider all affected beings equally. Climate change impacts deserve special attention due to their existential nature.

Follow the complete assessment process:
1. Provide a clear, factual 1-2 sentence event summary
2. Answer all boolean impact checks considering direct and indirect impacts
3. Provide detailed reasoning, temporal breakdown, and scale factors for each dimension
4. List critical uncertainties that could change the assessment
5. Identify moral issues, value conflicts, or controversial aspects

Be morally impartial, temporally aware, evidence-based, and holistic in your approach."""

    def _get_iso_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        return datetime.utcnow().isoformat() + "Z"
