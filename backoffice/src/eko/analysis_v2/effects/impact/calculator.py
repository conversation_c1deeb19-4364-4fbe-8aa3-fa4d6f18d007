"""
Main impact score calculator.

This module orchestrates the calculation of impact scores based on
boolean indicators and scale factors, replacing LLM-based scoring.
"""

from typing import Dict, Any, <PERSON><PERSON>, Optional
from loguru import logger

from typing import TYPE_CHECKING

from .indicators import ImpactIndicatorWeights
from .models.assessment import HarmImpactAssessment, BenefitImpactAssessment, DimensionAssessment
from .models.assessment import AnimalHarmImpact, AnimalBenefitImpact, HumanHarmImpact, HumanBenefitImpact, EnvironmentHarmImpact, EnvironmentBenefitImpact
from .models.impact_response import LLMImpactResponse
from .models.calculation_details import BackendCalculationDetails, CompleteCalculationDetails
from .scale_factors import ScaleFactorCalculator
from .confidence import ConfidenceCalculator


class ImpactScoreCalculator:
    """
    Main calculator for determining impact scores programmatically.
    
    This class takes the boolean indicators and scale factors from the LLM
    and calculates scores based on predefined weights and adjustment logic.
    """
    
    def __init__(self):
        self.indicators = ImpactIndicatorWeights
        self.scale_adjuster = ScaleFactorCalculator
        self.confidence_calc = ConfidenceCalculator
    
    def _get_animal_harm_base_score(self, impact: AnimalHarmImpact) -> <PERSON><PERSON>[float, str]:
        """Get base score for animal harm based on triggered indicators."""
        if impact.species_went_extinct:
            return 0.9, "Species extinction (highest priority animal harm indicator)"
        elif impact.many_animals_died:
            return 0.6, "Many animals died (high severity indicator)"
        elif impact.animals_died:
            return 0.5, "Animals died (moderate severity indicator)"
        elif impact.species_population_declined:
            return 0.45, "Species population declined (moderate indicator)"
        elif impact.animals_injured:
            return 0.4, "Animals injured (lower severity indicator)"
        elif impact.habitat_lost_for_animals:
            return 0.4, "Animal habitat lost (ecosystem damage indicator)"
        elif impact.animals_displaced:
            return 0.35, "Animals displaced (displacement indicator)"
        else:
            return 0.0, "Default animal harm score (no specific indicators triggered)"
    
    def _get_human_harm_base_score(self, impact: HumanHarmImpact) -> Tuple[float, str]:
        """Get base score for human harm based on triggered indicators."""
        if impact.genocide:
            return 1.0, "Genocide (highest severity human harm indicator)"
        elif impact.many_humans_died:
            return 0.8, "Many humans died (extreme severity indicator)"
        elif impact.humans_died:
            return 0.7, "Human fatalities (high severity indicator)"
        elif impact.rape:
            return 0.6, "Sexual violence (serious human rights violation)"
        elif impact.torture:
            return 0.5, "Torture (severe human rights violation)"
        elif impact.humans_injured_or_ill:
            return 0.4, "Serious injuries or illness (moderate harm indicator)"
        elif impact.public_health_harmed:
            return 0.4, "Public health degradation (community impact)"
        elif impact.basic_needs_unmet:
            return 0.35, "Basic human needs affected"
        elif impact.security:
            return 0.35, "Basic human security affected"
        elif impact.human_rights_violated:
            return 0.3, "Gross human rights violations, right-to-assembly, free-speech etc."
        elif impact.discrimination:
            return 0.25, "Discrimination (moderate harm indicator)"
        elif impact.money_lost:
            return 0.2, "Financial loss (minor harm indicator)"
        elif impact.livelihoods_lost:
            return 0.2, "Livelihoods lost (minor harm indicator)"
        else:
            return 0.0, "No human harm score (no specific indicators triggered)"
    
    def _get_environment_harm_base_score(self, impact: EnvironmentHarmImpact) -> Tuple[float, str]:
        """Get base score for environment harm based on triggered indicators."""
        if impact.climate_risk_intensified:
            return 0.9, "Climate risk intensified (highest priority environmental indicator)"
        elif impact.biodiversity_loss:
            return 0.7, "Biodiversity loss (severe ecosystem impact)"
        elif impact.deforestation_occurred:
            return 0.65, "Deforestation (major habitat destruction)"
        elif impact.water_contaminated:
            return 0.6, "Water contamination (resource degradation)"
        elif impact.ghg_emissions_increased:
            return 0.55, "Increased GHG emissions (climate impact)"
        elif impact.pollution_increased:
            return 0.5, "Increased pollution (environmental degradation)"
        elif impact.habitat_destroyed:
            return 0.45, "Habitat destruction (ecosystem damage)"
        else:
            return 0.0, "No environmental harm score (no specific indicators triggered)"
    
    def _get_animal_benefit_base_score(self, impact: AnimalBenefitImpact) -> Tuple[float, str]:
        """Get base score for animal benefits based on triggered indicators."""
        if impact.species_reintroduced:
            return 0.9, "Species reintroduction (highest priority animal benefit)"
        elif impact.habitat_restored_for_animals:
            return 0.7, "Animal habitat restoration (major ecosystem benefit)"
        elif impact.many_animals_rescued:
            return 0.65, "Many animals rescued (high impact rescue operation)"
        elif impact.animals_rescued:
            return 0.6, "Animals rescued (significant welfare improvement)"
        elif impact.species_population_increased:
            return 0.55, "Species population increase (conservation success)"
        elif impact.animals_rehabilitated:
            return 0.5, "Animal rehabilitation (welfare and recovery)"
        else:
            return 0.0, "No animal benefit score (no specific indicators triggered)"
    
    def _get_human_benefit_base_score(self, impact: HumanBenefitImpact) -> Tuple[float, str]:
        """Get base score for human benefits based on triggered indicators."""
        if impact.many_humans_saved:
            return 0.9, "Many humans saved (highest priority human benefit)"
        elif impact.humans_saved:
            return 0.8, "Humans saved (life-saving intervention)"
        elif impact.health_outcomes_improved:
            return 0.5, "Health outcomes improved (significant health benefit)"
        elif impact.education_or_training_provided:
            return 0.4, "Education/training provided (capacity building)"
        elif impact.jobs_created:
            return 0.4, "Jobs created (economic opportunity)"
        elif impact.human_rights_protected:
            return 0.4, "Human rights protected (rights advancement)"
        elif impact.infrastructure_upgraded:
            return 0.3, "Infrastructure upgraded (community improvement)"
        elif impact.incomes_increased:
            return 0.25, "Incomes increased (economic benefit)"
        elif impact.livelihoods_restored:
            return 0.25, "Livelihoods restored (economic recovery)"
        elif impact.social_inclusion_enhanced:
            return 0.25, "Social inclusion enhanced (social benefit)"
        else:
            return 0.0, "No human benefit score (no specific indicators triggered)"
    
    def _get_environment_benefit_base_score(self, impact: EnvironmentBenefitImpact) -> Tuple[float, str]:
        """Get base score for environment benefits based on triggered indicators."""
        if impact.biodiversity_enhanced:
            return 0.9, "Biodiversity enhanced (ecosystem health)"
        elif impact.habitat_restored:
            return 0.8, "Habitat restoration (ecosystem recovery)"
        elif impact.forest_restored:
            return 0.7, "Forest restoration (ecosystem and climate benefit)"
        elif impact.ghg_emissions_reduced:
            return 0.7, "GHG emissions reduced (direct climate benefit)"
        elif impact.carbon_sequestered:
            return 0.7, "Carbon sequestration (long-term climate benefit)"
        if impact.renewable_energy_generated:
            return 0.6, "Renewable energy generated (major climate benefit)"
        elif impact.pollution_reduced:
            return 0.5, "Pollution reduction (environmental improvement)"
        elif impact.water_quality_improved:
            return 0.45, "Water quality improved (resource enhancement)"
        elif impact.ecosystem_services_enhanced:
            return 0.4, "Ecosystem services enhanced (natural capital)"
        else:
            return 0.0, "No environmental benefit score (no specific indicators triggered)"
    
    def calculate_scores(self, llm_response: LLMImpactResponse) -> Tuple[LLMImpactResponse, CompleteCalculationDetails]:
        """
        Calculate scores for all dimensions based on the LLM response.
        
        Args:
            llm_response: The response from the LLM containing indicators and scale factors
            
        Returns:
            Tuple of (Updated LLM response with calculated scores and confidence levels,
                     CompleteCalculationDetails containing all calculation details for each dimension)
        """
        logger.info("Calculating impact scores programmatically")
        
        # Calculate harm scores
        harm_scores = self._calculate_harm_scores(
            llm_response.harm_assessment,
            llm_response.event_summary
        )
        
        # Calculate benefit scores
        benefit_scores = self._calculate_benefit_scores(
            llm_response.benefit_assessment,
            llm_response.event_summary
        )
        
        # Store all calculation details
        calculation_details = CompleteCalculationDetails()
        
        # Update the assessments with calculated scores
        harm_dimensions = {
            'animals': llm_response.harm_assessment.animals,
            'humans': llm_response.harm_assessment.humans,
            'environment': llm_response.harm_assessment.environment
        }
        
        benefit_dimensions = {
            'animals': llm_response.benefit_assessment.animals,
            'humans': llm_response.benefit_assessment.humans,
            'environment': llm_response.benefit_assessment.environment
        }
        
        for dimension in ['animals', 'humans', 'environment']:
            # Update harm assessment
            harm_dim = harm_dimensions[dimension]
            harm_dim.assessment.score = harm_scores[dimension]['score']
            harm_dim.assessment.confidence = harm_scores[dimension]['confidence']
            
            # Store calculation details
            if 'calculation_details' in harm_scores[dimension]:
                calculation_details.harm[dimension] = harm_scores[dimension]['calculation_details']
            
            # Update benefit assessment
            benefit_dim = benefit_dimensions[dimension]
            benefit_dim.assessment.score = benefit_scores[dimension]['score']
            benefit_dim.assessment.confidence = benefit_scores[dimension]['confidence']
            
            # Store calculation details
            if 'calculation_details' in benefit_scores[dimension]:
                calculation_details.benefit[dimension] = benefit_scores[dimension]['calculation_details']
        
        logger.info(
            f"Calculated scores - Harm: animals={harm_scores['animals']['score']:.2f}, "
            f"humans={harm_scores['humans']['score']:.2f}, env={harm_scores['environment']['score']:.2f} | "
            f"Benefit: animals={benefit_scores['animals']['score']:.2f}, "
            f"humans={benefit_scores['humans']['score']:.2f}, env={benefit_scores['environment']['score']:.2f}"
        )
        
        logger.debug(f"Calculation details captured for frontend: {calculation_details}")
        
        return llm_response, calculation_details
    
    def _calculate_harm_scores(
        self,
        harm_assessment: HarmImpactAssessment,
        event_summary: str
    ) -> Dict[str, Dict[str, Any]]:
        """Calculate harm scores for all dimensions."""
        scores = {}
        
        # Calculate animal harm
        scores['animals'] = self._calculate_animal_harm(
            harm_assessment.animals,
            harm_assessment.animals.assessment,
            event_summary
        )
        
        # Calculate human harm
        scores['humans'] = self._calculate_human_harm(
            harm_assessment.humans,
            harm_assessment.humans.assessment,
            event_summary
        )
        
        # Calculate environment harm
        scores['environment'] = self._calculate_environment_harm(
            harm_assessment.environment,
            harm_assessment.environment.assessment,
            event_summary
        )
        
        return scores
    
    def _calculate_benefit_scores(
        self,
        benefit_assessment: "BenefitImpactAssessment",
        event_summary: str
    ) -> Dict[str, Dict[str, Any]]:
        """Calculate benefit scores for all dimensions."""
        scores = {}
        
        # Calculate animal benefits
        scores['animals'] = self._calculate_animal_benefit(
            benefit_assessment.animals,
            benefit_assessment.animals.assessment,
            event_summary
        )
        
        # Calculate human benefits
        scores['humans'] = self._calculate_human_benefit(
            benefit_assessment.humans,
            benefit_assessment.humans.assessment,
            event_summary
        )
        
        # Calculate environment benefits
        scores['environment'] = self._calculate_environment_benefit(
            benefit_assessment.environment,
            benefit_assessment.environment.assessment,
            event_summary
        )
        
        return scores
    
    def _calculate_animal_harm(
        self,
        animal_check: AnimalHarmImpact,
        assessment: DimensionAssessment,
        event_summary: str
    ) -> Dict[str, Any]:
        """Calculate animal harm score and confidence."""
        logger.info("Starting animal harm calculation")
        
        # Get triggered indicators for tracking
        triggered_indicators = []
        indicator_mappings = [
            ('species_went_extinct', animal_check.species_went_extinct),
            ('many_animals_died', animal_check.many_animals_died),
            ('animals_died', animal_check.animals_died),
            ('species_population_declined', animal_check.species_population_declined),
            ('animals_injured', animal_check.animals_injured),
            ('habitat_lost_for_animals', animal_check.habitat_lost_for_animals),
            ('animals_displaced', animal_check.animals_displaced),
        ]
        
        for indicator_name, indicator_value in indicator_mappings:
            if indicator_value:
                triggered_indicators.append(indicator_name)
        
        logger.info(f"Animal harm triggered indicators: {triggered_indicators}")
        
        # Get initial score from indicators
        initial_score, base_score_rationale = self._get_animal_harm_base_score(animal_check)
        
        # Apply scale factor adjustments 
        score, scale_factor_adjustments = self.scale_adjuster.adjust_harm(
            initial_score, assessment.scale_factors, 'animals'
        )
        
        logger.info(f"Animal harm score from scale factors: {score}")
        
        # Calculate confidence
        confidence, confidence_factors = self.confidence_calc.calculate_confidence(
            assessment.scale_factors,
            assessment.temporal_breakdown,
            assessment.reasoning,
            score,
            len(triggered_indicators) > 1,
            len(triggered_indicators)
        )
        
        # Store calculation details
        calculation_details = BackendCalculationDetails(
            triggered_indicators=triggered_indicators,
            base_score=initial_score,
            base_score_rationale=base_score_rationale,
            scale_factor_adjustments=scale_factor_adjustments,
            pre_validation_score=score,
            final_score=score,
            confidence_factors=confidence_factors,
            scale_factors=assessment.scale_factors.model_dump()
        )
        
        return {
            'score': score, 
            'confidence': confidence,
            'calculation_details': calculation_details
        }
    
    def _calculate_human_harm(
        self,
        human_check: HumanHarmImpact,
        assessment: DimensionAssessment,
        event_summary: str
    ) -> Dict[str, Any]:
        """Calculate human harm score and confidence."""
        logger.info("Starting human harm calculation")
        
        # Get triggered indicators for tracking
        triggered_indicators = []
        indicator_mappings = [
            ('genocide', human_check.genocide),
            ('many_humans_died', human_check.many_humans_died),
            ('humans_died', human_check.humans_died),
            ('human_rights_violated', human_check.human_rights_violated),
            ('people_displaced', human_check.people_displaced),
            ('humans_injured_or_ill', human_check.humans_injured_or_ill),
            ('public_health_harmed', human_check.public_health_harmed),
            ('livelihoods_lost', human_check.livelihoods_lost),
            ('discrimination', human_check.discrimination),
            ('money_lost', human_check.money_lost),
        ]
        
        for indicator_name, indicator_value in indicator_mappings:
            if indicator_value:
                triggered_indicators.append(indicator_name)
        
        logger.info(f"Human harm triggered indicators: {triggered_indicators}")
        
        # Get initial score from indicators
        initial_score, base_score_rationale = self._get_human_harm_base_score(human_check)
        
        # Apply scale factor adjustments
        score, scale_factor_adjustments = self.scale_adjuster.adjust_harm(
            initial_score, assessment.scale_factors, 'humans'
        )
        
        logger.info(f"Human harm score from scale factors: {score}")
        
        # Calculate confidence
        confidence, confidence_factors = self.confidence_calc.calculate_confidence(
            assessment.scale_factors,
            assessment.temporal_breakdown,
            assessment.reasoning,
            score,
            len(triggered_indicators) > 1,
            len(triggered_indicators)
        )
        
        # Store calculation details
        calculation_details = BackendCalculationDetails(
            triggered_indicators=triggered_indicators,
            base_score=initial_score,
            base_score_rationale=base_score_rationale,
            scale_factor_adjustments=scale_factor_adjustments,
            pre_validation_score=score,
            final_score=score,
            confidence_factors=confidence_factors,
            scale_factors=assessment.scale_factors.model_dump()
        )
        
        return {
            'score': score,
            'confidence': confidence,
            'calculation_details': calculation_details
        }
    
    def _calculate_environment_harm(
        self,
        ecosystem_check: EnvironmentHarmImpact,
        assessment: DimensionAssessment,
        event_summary: str
    ) -> Dict[str, Any]:
        """Calculate environment harm score and confidence."""
        logger.info("Starting environment harm calculation")
        
        # Get triggered indicators for tracking
        triggered_indicators = []
        indicator_mappings = [
            ('climate_risk_intensified', ecosystem_check.climate_risk_intensified),
            ('biodiversity_loss', ecosystem_check.biodiversity_loss),
            ('deforestation_occurred', ecosystem_check.deforestation_occurred),
            ('habitat_destroyed', ecosystem_check.habitat_destroyed),
            ('ghg_emissions_increased', ecosystem_check.ghg_emissions_increased),
            ('water_contaminated', ecosystem_check.water_contaminated),
            ('pollution_increased', ecosystem_check.pollution_increased),
        ]
        
        for indicator_name, indicator_value in indicator_mappings:
            if indicator_value:
                triggered_indicators.append(indicator_name)
        
        logger.info(f"Environment harm triggered indicators: {triggered_indicators}")
        
        # Get initial score from indicators
        initial_score, base_score_rationale = self._get_environment_harm_base_score(ecosystem_check)
        
        # Apply scale factor adjustments
        score, scale_factor_adjustments = self.scale_adjuster.adjust_harm(
            initial_score, assessment.scale_factors, 'environment'
        )
        
        logger.info(f"Environment harm score from scale factors: {score}")
        
        # Calculate confidence
        confidence, confidence_factors = self.confidence_calc.calculate_confidence(
            assessment.scale_factors,
            assessment.temporal_breakdown,
            assessment.reasoning,
            score,
            len(triggered_indicators) > 1,
            len(triggered_indicators)
        )
        
        # Store calculation details
        calculation_details = BackendCalculationDetails(
            triggered_indicators=triggered_indicators,
            base_score=initial_score,
            base_score_rationale=base_score_rationale,
            scale_factor_adjustments=scale_factor_adjustments,
            pre_validation_score=score,
            final_score=score,
            confidence_factors=confidence_factors,
            scale_factors=assessment.scale_factors.model_dump()
        )
        
        return {
            'score': score,
            'confidence': confidence,
            'calculation_details': calculation_details
        }
    
    def _calculate_animal_benefit(
        self,
        animal_check: AnimalBenefitImpact,
        assessment: DimensionAssessment,
        event_summary: str
    ) -> Dict[str, Any]:
        """Calculate animal benefit score and confidence."""
        logger.info("Starting animal benefit calculation")
        
        # Get triggered indicators for tracking
        triggered_indicators = []
        indicator_mappings = [
            ('many_animals_rescued', animal_check.many_animals_rescued),
            ('species_reintroduced', animal_check.species_reintroduced),
            ('animals_rescued', animal_check.animals_rescued),
            ('species_population_increased', animal_check.species_population_increased),
            ('habitat_restored_for_animals', animal_check.habitat_restored_for_animals),
            ('animals_rehabilitated', animal_check.animals_rehabilitated),
        ]
        
        for indicator_name, indicator_value in indicator_mappings:
            if indicator_value:
                triggered_indicators.append(indicator_name)
        
        logger.info(f"Animal benefit triggered indicators: {triggered_indicators}")
        
        # Get initial score from indicators
        initial_score, base_score_rationale = self._get_animal_benefit_base_score(animal_check)
        
        # Apply scale factor adjustments
        score, scale_factor_adjustments = self.scale_adjuster.adjust_benefit(
            initial_score, assessment.scale_factors, 'animals', False
        )
        
        logger.info(f"Animal benefit score from scale factors: {score}")
        
        # Calculate confidence
        confidence, confidence_factors = self.confidence_calc.calculate_confidence(
            assessment.scale_factors,
            assessment.temporal_breakdown,
            assessment.reasoning,
            score,
            len(triggered_indicators) > 1,
            len(triggered_indicators)
        )
        
        # Store calculation details
        calculation_details = BackendCalculationDetails(
            triggered_indicators=triggered_indicators,
            base_score=initial_score,
            base_score_rationale=base_score_rationale,
            scale_factor_adjustments=scale_factor_adjustments,
            pre_validation_score=score,
            final_score=score,
            confidence_factors=confidence_factors,
            scale_factors=assessment.scale_factors.model_dump()
        )
        
        return {
            'score': score,
            'confidence': confidence,
            'calculation_details': calculation_details
        }
    
    def _calculate_human_benefit(
        self,
        human_check: HumanBenefitImpact,
        assessment: DimensionAssessment,
        event_summary: str
    ) -> Dict[str, Any]:
        """Calculate human benefit score and confidence."""
        logger.info("Starting human benefit calculation")
        
        # Get triggered indicators for tracking
        triggered_indicators = []
        indicator_mappings = [
            ('many_humans_saved', human_check.many_humans_saved),
            ('humans_saved', human_check.humans_saved),
            ('human_rights_protected', human_check.human_rights_protected),
            ('health_outcomes_improved', human_check.health_outcomes_improved),
            ('jobs_created', human_check.jobs_created),
            ('livelihoods_restored', human_check.livelihoods_restored),
            ('infrastructure_upgraded', human_check.infrastructure_upgraded),
            ('incomes_increased', human_check.incomes_increased),
            ('education_or_training_provided', human_check.education_or_training_provided),
            ('social_inclusion_enhanced', human_check.social_inclusion_enhanced),
        ]
        
        for indicator_name, indicator_value in indicator_mappings:
            if indicator_value:
                triggered_indicators.append(indicator_name)
        
        logger.info(f"Human benefit triggered indicators: {triggered_indicators}")
        
        # Get initial score from indicators
        initial_score, base_score_rationale = self._get_human_benefit_base_score(human_check)
        
        # Apply scale factor adjustments
        score, scale_factor_adjustments = self.scale_adjuster.adjust_benefit(
            initial_score, assessment.scale_factors, 'humans', False
        )
        
        logger.info(f"Human benefit score from scale factors: {score}")
        
        # Calculate confidence
        confidence, confidence_factors = self.confidence_calc.calculate_confidence(
            assessment.scale_factors,
            assessment.temporal_breakdown,
            assessment.reasoning,
            score,
            len(triggered_indicators) > 1,
            len(triggered_indicators)
        )
        
        # Store calculation details
        calculation_details = BackendCalculationDetails(
            triggered_indicators=triggered_indicators,
            base_score=initial_score,
            base_score_rationale=base_score_rationale,
            scale_factor_adjustments=scale_factor_adjustments,
            pre_validation_score=score,
            final_score=score,
            confidence_factors=confidence_factors,
            scale_factors=assessment.scale_factors.model_dump()
        )
        
        return {
            'score': score,
            'confidence': confidence,
            'calculation_details': calculation_details
        }
    
    def _calculate_environment_benefit(
        self,
        ecosystem_check: EnvironmentBenefitImpact,
        assessment: DimensionAssessment,
        event_summary: str
    ) -> Dict[str, Any]:
        """Calculate environment benefit score and confidence."""
        logger.info("Starting environment benefit calculation")
        
        # Get triggered indicators for tracking
        triggered_indicators = []
        indicator_mappings = [
            ('renewable_energy_generated', ecosystem_check.renewable_energy_generated),
            ('ghg_emissions_reduced', ecosystem_check.ghg_emissions_reduced),
            ('carbon_sequestered', ecosystem_check.carbon_sequestered),
            ('forest_restored', ecosystem_check.forest_restored),
            ('biodiversity_enhanced', ecosystem_check.biodiversity_enhanced),
            ('habitat_restored', ecosystem_check.habitat_restored),
            ('ecosystem_services_enhanced', ecosystem_check.ecosystem_services_enhanced),
            ('water_quality_improved', ecosystem_check.water_quality_improved),
            ('pollution_reduced', ecosystem_check.pollution_reduced),
        ]
        
        for indicator_name, indicator_value in indicator_mappings:
            if indicator_value:
                triggered_indicators.append(indicator_name)
        
        logger.info(f"Environment benefit triggered indicators: {triggered_indicators}")
        
        # Check for climate benefits
        has_climate_benefit = (
            ecosystem_check.renewable_energy_generated or
            ecosystem_check.ghg_emissions_reduced
        )
        
        # Get initial score from indicators
        initial_score, base_score_rationale = self._get_environment_benefit_base_score(ecosystem_check)
        
        # Apply scale factor adjustments
        score, scale_factor_adjustments = self.scale_adjuster.adjust_benefit(
            initial_score, assessment.scale_factors, 'environment', has_climate_benefit
        )
        
        logger.info(f"Environment benefit score from scale factors: {score}")
        
        # Calculate confidence
        confidence, confidence_factors = self.confidence_calc.calculate_confidence(
            assessment.scale_factors,
            assessment.temporal_breakdown,
            assessment.reasoning,
            score,
            len(triggered_indicators) > 1,
            len(triggered_indicators)
        )
        
        # Store calculation details
        calculation_details = BackendCalculationDetails(
            triggered_indicators=triggered_indicators,
            base_score=initial_score,
            base_score_rationale=base_score_rationale,
            scale_factor_adjustments=scale_factor_adjustments,
            pre_validation_score=score,
            final_score=score,
            confidence_factors=confidence_factors,
            scale_factors=assessment.scale_factors.model_dump()
        )
        
        return {
            'score': score,
            'confidence': confidence,
            'calculation_details': calculation_details
        }
    
