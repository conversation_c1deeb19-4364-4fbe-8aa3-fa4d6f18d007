"""
Impact score validation for basic range and consistency checks.

This module provides basic validation to ensure scores remain within valid ranges
and consistency checks for impact assessments.
"""

from typing import Dict, Any, <PERSON><PERSON>
from loguru import logger


class ImpactScoreValidator:
    """
    Validates impact scores for basic range and consistency checks.
    
    Since boolean indicators now directly drive score calculation,
    this validator focuses on ensuring scores remain within valid ranges.
    """
    
    def validate_and_cap_scores(
        self,
        harm_scores: Dict[str, Dict[str, Any]],
        benefit_scores: Dict[str, Dict[str, Any]]
    ) -> Tuple[Dict[str, Dict[str, Any]], Dict[str, Dict[str, Any]], Dict[str, Any]]:
        """
        Validate scores for basic range compliance.
        
        Args:
            harm_scores: Calculated harm scores by dimension
            benefit_scores: Calculated benefit scores by dimension
            
        Returns:
            Tuple of (validated harm scores, validated benefit scores, validation details)
        """
        validation_details = {
            'harm': {},
            'benefit': {}
        }
        
        # Validate score ranges
        harm_scores, harm_validation = self._validate_score_ranges(harm_scores, 'harm')
        validation_details['harm'] = harm_validation
        
        benefit_scores, benefit_validation = self._validate_score_ranges(benefit_scores, 'benefit')
        validation_details['benefit'] = benefit_validation
        
        return harm_scores, benefit_scores, validation_details
    
    def _validate_score_ranges(
        self,
        scores: Dict[str, Dict[str, Any]], 
        score_type: str
    ) -> Tuple[Dict[str, Dict[str, Any]], Dict[str, Any]]:
        """Validate that scores are within the valid range of 0.0 to 1.0."""
        validation_details = {}
        
        for dimension in ['animals', 'humans', 'environment']:
            if dimension in scores:
                original_score = scores[dimension]['score']
                
                # Ensure score is within valid range
                if original_score < 0.0:
                    logger.warning(f"{score_type} {dimension} score was negative: {original_score}, capping to 0.0")
                    scores[dimension]['score'] = 0.0
                elif original_score > 1.0:
                    logger.warning(f"{score_type} {dimension} score exceeded 1.0: {original_score}, capping to 1.0")
                    scores[dimension]['score'] = 1.0
                
                validation_details[dimension] = {
                    'original_score': original_score,
                    'final_score': scores[dimension]['score'],
                    'was_capped': scores[dimension]['score'] != original_score
                }
        
        return scores, validation_details