"""
Impact indicator weights and severity mappings.

This module contains the weights assigned to each boolean indicator
for calculating impact scores. These weights are derived from the
original validation logic in check_harm_limits and check_benefit_limits.
"""

from dataclasses import dataclass
from typing import Dict


@dataclass
class IndicatorWeight:
    """Weight configuration for a single indicator."""
    weight: float
    description: str


class ImpactIndicatorWeights:
    """Centralized repository of impact indicator weights."""
    
    # Animal harm indicators
    ANIMAL_HARM_WEIGHTS: Dict[str, IndicatorWeight] = {
        "species_went_extinct": IndicatorWeight(0.9, "Species driven to extinction"),
        "many_animals_died": IndicatorWeight(0.6, "Many animals died"),
        "animals_died": IndicatorWeight(0.5, "Animals died"),
        "species_population_declined": IndicatorWeight(0.45, "Species population declined"),
        "animals_injured": IndicatorWeight(0.4, "Animals injured or maimed"),
        "habitat_lost_for_animals": IndicatorWeight(0.4, "Animal habitat permanently lost"),
        "animals_displaced": IndicatorWeight(0.35, "Animals forcibly displaced"),
    }
    
    # Human harm indicators
    HUMAN_HARM_WEIGHTS: Dict[str, IndicatorWeight] = {
        "genocide": IndicatorWeight(0.95, "Genocide occurred"),
        "many_humans_died": IndicatorWeight(0.8, "Many human fatalities"),
        "humans_died": IndicatorWeight(0.7, "Human fatalities"),
        "human_rights_violated": IndicatorWeight(0.55, "Human rights breached"),
        "people_displaced": IndicatorWeight(0.5, "Involuntary displacement"),
        "humans_injured_or_ill": IndicatorWeight(0.5, "Serious injuries or illnesses"),
        "public_health_harmed": IndicatorWeight(0.45, "Community health degraded"),
        "livelihoods_lost": IndicatorWeight(0.4, "Jobs/income lost"),
        "discrimination": IndicatorWeight(0.4, "Discrimination occurred"),
        "money_lost": IndicatorWeight(0.3, "Financial loss"),
    }
    
    # Environment harm indicators
    ENVIRONMENT_HARM_WEIGHTS: Dict[str, IndicatorWeight] = {
        "climate_risk_intensified": IndicatorWeight(0.85, "Climate risk worsened"),
        "biodiversity_loss": IndicatorWeight(0.7, "Biodiversity declined"),
        "deforestation_occurred": IndicatorWeight(0.65, "Forest cover lost"),
        "habitat_destroyed": IndicatorWeight(0.6, "Habitat destroyed"),
        "ghg_emissions_increased": IndicatorWeight(0.6, "GHG emissions rose"),
        "water_contaminated": IndicatorWeight(0.45, "Water quality fell"),
        "pollution_increased": IndicatorWeight(0.4, "Pollution increased"),
    }
    
    # Animal benefit indicators
    ANIMAL_BENEFIT_WEIGHTS: Dict[str, IndicatorWeight] = {
        "many_animals_rescued": IndicatorWeight(0.6, "Many animals rescued"),
        "species_reintroduced": IndicatorWeight(0.5, "Species re-established"),
        "animals_rescued": IndicatorWeight(0.4, "Animals rescued"),
        "species_population_increased": IndicatorWeight(0.4, "Population increased"),
        "habitat_restored_for_animals": IndicatorWeight(0.35, "Habitat restored"),
        "animals_rehabilitated": IndicatorWeight(0.3, "Animals rehabilitated"),
    }
    
    # Human benefit indicators
    HUMAN_BENEFIT_WEIGHTS: Dict[str, IndicatorWeight] = {
        "many_humans_saved": IndicatorWeight(0.7, "Many humans saved"),
        "humans_saved": IndicatorWeight(0.6, "Humans rescued"),
        "human_rights_protected": IndicatorWeight(0.4, "Rights protected"),
        "health_outcomes_improved": IndicatorWeight(0.35, "Health improved"),
        "jobs_created": IndicatorWeight(0.3, "Jobs created"),
        "livelihoods_restored": IndicatorWeight(0.3, "Livelihoods restored"),
        "infrastructure_upgraded": IndicatorWeight(0.25, "Infrastructure improved"),
        "incomes_increased": IndicatorWeight(0.2, "Incomes rose"),
        "education_or_training_provided": IndicatorWeight(0.2, "Education provided"),
        "social_inclusion_enhanced": IndicatorWeight(0.15, "Inclusion enhanced"),
    }
    
    # Environment benefit indicators
    ENVIRONMENT_BENEFIT_WEIGHTS: Dict[str, IndicatorWeight] = {
        "renewable_energy_generated": IndicatorWeight(0.7, "Clean energy supplied"),
        "ghg_emissions_reduced": IndicatorWeight(0.6, "GHG emissions lowered"),
        "carbon_sequestered": IndicatorWeight(0.5, "Carbon sequestered"),
        "forest_restored": IndicatorWeight(0.5, "Forest regenerated"),
        "biodiversity_enhanced": IndicatorWeight(0.45, "Biodiversity improved"),
        "habitat_restored": IndicatorWeight(0.4, "Habitat restored"),
        "ecosystem_services_enhanced": IndicatorWeight(0.35, "Ecosystem services improved"),
        "water_quality_improved": IndicatorWeight(0.3, "Water quality improved"),
        "pollution_reduced": IndicatorWeight(0.3, "Pollution reduced"),
    }
    
    
    # Special case caps from the prompts
    SPECIAL_CASE_CAPS = {
        "recycling": 0.2,
        "awareness_campaign": 0.2,
        "initiative_start": 0.1,
    }
    
   
