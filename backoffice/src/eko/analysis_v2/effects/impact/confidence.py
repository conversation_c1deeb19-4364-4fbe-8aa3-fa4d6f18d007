"""
Confidence level calculation for impact assessments.

This module determines confidence levels based on various factors including
scale factor completeness, internal consistency, and data quality.
"""

from typing import Literal, List, Tuple

from eko.analysis_v2.effects.impact.models.base import TemporalBreakdown
from eko.analysis_v2.effects.impact.models.scale_factors import ScaleFactors


class ConfidenceCalculator:
    """Calculates confidence levels for impact assessments."""
    
    @classmethod
    def calculate_confidence(
        cls,
        scale_factors: ScaleFactors,
        temporal_breakdown: TemporalBreakdown,
        reasoning: str,
        score: float,
        has_multiple_indicators: bool,
        indicator_count: int
    ) -> Tuple[Literal["high", "medium", "low"], List[str]]:
        """
        Calculate confidence level based on multiple factors.
        
        Args:
            scale_factors: The scale factors from the assessment
            temporal_breakdown: The temporal breakdown of impacts
            reasoning: The reasoning provided by the LLM
            score: The calculated impact score
            has_multiple_indicators: Whether multiple boolean indicators were triggered
            indicator_count: Number of indicators that were triggered
            
        Returns:
            Tuple of (confidence level, list of reasons affecting confidence)
        """
        confidence_factors = []
        confidence_score = 0.0
        
        # Check scale factors completeness and quality
        sf_quality = cls._assess_scale_factors_quality(scale_factors)
        confidence_score += sf_quality * 0.3
        if sf_quality < 0.7:
            confidence_factors.append("Incomplete or low-quality scale factors")
        
        # Check temporal breakdown completeness
        temporal_quality = cls._assess_temporal_quality(temporal_breakdown)
        confidence_score += temporal_quality * 0.2
        if temporal_quality < 0.7:
            confidence_factors.append("Incomplete temporal analysis")
        
        # Check reasoning quality
        reasoning_quality = cls._assess_reasoning_quality(reasoning)
        confidence_score += reasoning_quality * 0.2
        if reasoning_quality < 0.7:
            confidence_factors.append("Limited reasoning provided")
        
        # Check internal consistency
        consistency_score = cls._check_internal_consistency(scale_factors, score)
        confidence_score += consistency_score * 0.2
        if consistency_score < 0.7:
            confidence_factors.append("Internal inconsistencies detected")
        
        # Check indicator support
        if has_multiple_indicators:
            confidence_score += 0.1
        elif indicator_count == 0:
            confidence_factors.append("No specific indicators triggered")
        
        # Determine final confidence level
        if confidence_score >= 0.8:
            confidence = "high"
        elif confidence_score >= 0.5:
            confidence = "medium"
        else:
            confidence = "low"
        
        # Special cases that reduce confidence based on boolean flags
        # High impact with low contribution
        if score > 0.8 and (scale_factors.not_contributing or scale_factors.very_minor_contributor):
            confidence = "medium" if confidence == "high" else confidence
            confidence_factors.append("High impact with minimal contribution")
        
        # High score with low directness
        if score > 0.6 and (scale_factors.third_party_action or scale_factors.entity_had_minor_influence):
            confidence = "low" if confidence == "high" else confidence
            confidence_factors.append("High score with low directness")
        
        return confidence, confidence_factors
    
    @classmethod
    def _assess_scale_factors_quality(cls, scale_factors: ScaleFactors) -> float:
        """
        Assess the quality and completeness of scale factors.
        
        Returns:
            Quality score between 0.0 and 1.0
        """
        quality_score = 0.0
        checks = 0
        
        # Check that boolean fields have meaningful responses (not all False)
        boolean_field_groups = [
            # Reversibility questions
            [scale_factors.is_irreversible, scale_factors.takes_centuries_to_reverse,
             scale_factors.requires_significant_effort_to_reverse, scale_factors.reversible_within_years,
             scale_factors.reversible_within_months, scale_factors.reversible_within_weeks,
             scale_factors.fully_reversible_immediately],
            
            # Scale of impact questions  
            [scale_factors.affects_none, scale_factors.affects_single_individual, scale_factors.affects_multiple_individuals,
             scale_factors.affects_many_beings, scale_factors.affects_large_population,
             scale_factors.affects_country_ecosystem, scale_factors.affects_species_biome,
             scale_factors.affects_all_global],
            
            # Directness questions
            [scale_factors.third_party_action, scale_factors.entity_had_minor_influence,
             scale_factors.entity_influenced_outcome, scale_factors.entity_action_led_to_impact,
             scale_factors.entity_decision_caused_impact, scale_factors.direct_action_by_entity,
             scale_factors.entity_sole_direct_cause],
            
            # Contribution questions
            [scale_factors.not_contributing, scale_factors.very_minor_contributor,
             scale_factors.minor_contributor, scale_factors.significant_contributor,
             scale_factors.major_contributor, scale_factors.dominant_contributor,
             scale_factors.sole_contributor],
            

            # Degree questions
            [scale_factors.no_impact, scale_factors.minor_impact, scale_factors.moderate_impact,
             scale_factors.major_impact, scale_factors.severe_impact,
             scale_factors.extreme_impact, scale_factors.existential_impact]
        ]
        
        # Each group should have at least one True value
        for group in boolean_field_groups:
            checks += 1
            if any(group):
                quality_score += 1
        
        # Check duration is valid
        checks += 1
        if scale_factors.duration in ["short", "medium", "long"]:
            quality_score += 1
        
        # Check proximity_to_tipping_point is meaningful
        checks += 1
        if (scale_factors.proximity_to_tipping_point.strip() and
            scale_factors.proximity_to_tipping_point.lower() not in ["test", "placeholder", "n/a", "none", ""]):
            quality_score += 1
        
        # Check for internal consistency
        consistency_penalty = 0
        
        # High damage with high reversibility is inconsistent
        if (scale_factors.existential_impact or scale_factors.extreme_impact) and scale_factors.fully_reversible_immediately:
            consistency_penalty += 0.1
        
        # Low damage with low reversibility is inconsistent  
        if scale_factors.no_impact and scale_factors.is_irreversible:
            consistency_penalty += 0.1
        
        # Short duration with low reversibility is inconsistent
        if scale_factors.duration == "short" and (scale_factors.is_irreversible or scale_factors.takes_centuries_to_reverse):
            consistency_penalty += 0.1
        
        # Global scope with minimal contribution is inconsistent
        if scale_factors.affects_all_global  and (scale_factors.not_contributing or scale_factors.very_minor_contributor):
            consistency_penalty += 0.1
        
        final_score = (quality_score / checks) - consistency_penalty
        return max(0.0, min(1.0, final_score))
    
    @classmethod
    def _assess_temporal_quality(cls, temporal_breakdown: TemporalBreakdown) -> float:
        """
        Assess the quality of temporal breakdown.
        
        Returns:
            Quality score between 0.0 and 1.0
        """
        score = 0.0
        
        # Check each temporal period has meaningful content
        if temporal_breakdown.immediate.strip() and len(temporal_breakdown.immediate) > 10:
            score += 0.33
        
        if temporal_breakdown.medium_term.strip() and len(temporal_breakdown.medium_term) > 10:
            score += 0.33
        
        if temporal_breakdown.long_term.strip() and len(temporal_breakdown.long_term) > 10:
            score += 0.34
        
        return score
    
    @classmethod
    def _assess_reasoning_quality(cls, reasoning: str) -> float:
        """
        Assess the quality of reasoning provided.
        
        Returns:
            Quality score between 0.0 and 1.0
        """
        if not reasoning:
            return 0.0
        
        # Basic quality checks
        quality = 0.5  # Base score
        
        # Length check
        if len(reasoning) > 100:
            quality += 0.2
        if len(reasoning) > 200:
            quality += 0.1
        
        # Content checks
        if any(keyword in reasoning.lower() for keyword in ['because', 'due to', 'results in', 'causes']):
            quality += 0.1
        
        if any(keyword in reasoning.lower() for keyword in ['scale', 'impact', 'affected', 'consequence']):
            quality += 0.1
        
        return min(1.0, quality)
    
    @classmethod
    def _check_internal_consistency(cls, scale_factors: ScaleFactors, score: float) -> float:
        """
        Check internal consistency between scale factors and score.
        
        Returns:
            Consistency score between 0.0 and 1.0
        """
        consistency = 1.0
        
        # High directness with low scores suggests underestimation
        if (scale_factors.entity_sole_direct_cause or scale_factors.direct_action_by_entity) and score < 0.3:
            consistency -= 0.2
        
        # High contribution with low scores indicates potential underestimation
        if (scale_factors.sole_contributor or scale_factors.dominant_contributor) and score < 0.4:
            consistency -= 0.2
        
        # Low directness with high scores suggests overattribution
        if (scale_factors.third_party_action or scale_factors.entity_had_minor_influence) and score > 0.6:
            consistency -= 0.2
        
        # High damage with low scores suggests underestimation
        if (scale_factors.existential_impact or scale_factors.extreme_impact) and score < 0.6:
            consistency -= 0.2
        
        # Low damage with high scores suggests overestimation
        if scale_factors.no_impact and score > 0.4:
            consistency -= 0.2
        
        # Very large scale with low scores suggests underestimation
        if scale_factors.affects_all_global  and score < 0.5:
            consistency -= 0.2
        
        return max(0.0, consistency)
