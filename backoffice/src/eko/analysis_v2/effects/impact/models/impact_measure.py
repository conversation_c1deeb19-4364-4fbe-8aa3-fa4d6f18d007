from pydantic import BaseModel, Field

from .measurement import EventImpactMeasurement
from .review import ReviewDecision


class ImpactMeasurementReview(BaseModel):
    """Complete review of an impact measurement by a second LLM."""

    review_id: str = Field(..., description="Unique ID for this review")
    original_event_description: str = Field(..., description="Original event description being analyzed")
    measurement: EventImpactMeasurement = Field(..., description="The measurement being reviewed")
    review_decision: ReviewDecision = Field(..., description="The reviewer's decision")
    reviewer_model: str = Field(..., description="Model used for the review")
    reviewed_at: str = Field(..., description="ISO timestamp of review")
    review_iteration: int = Field(..., description="Which iteration of review this is (1, 2, 3, etc.)")

    def clean_dump(self) -> dict:
        """Returns a clean dictionary representation of the review used to be passed to prompts."""
        return {
            "review_decision": self.review_decision.clean_dump(),
            "review_iteration": self.review_iteration
        }

    def for_prompt(self) -> str:
        """Returns a clean representation of the review used to be passed to prompts."""
        return f"""
        <original-measurement>{self.measurement.for_prompt()}</original-measurement>
        <review_decision>{self.review_decision.for_prompt()}</review_decision>
        <review_iteration>{self.review_iteration}</review_iteration>
"""
