"""
Review models for impact assessment.
"""

from typing import List, Optional, Literal
from pydantic import BaseModel, Field

from .bias import BiasType
from .ass_feedback import AssessmentFeedback


class ReviewDecision(BaseModel):
    """
    Decision from LLM review of impact measurement.
    
    You are a senior expert reviewer specializing in evaluating the quality of impact assessment 
    components across environmental, human, and animal welfare dimensions. Your job is to critically 
    evaluate the assessment methodology and reasoning for:
    1. Bias detection - Detect all 6 key biases
    2. Assessment quality - Whether reasoning, temporal breakdowns, and scale factors are well-founded
    3. Methodological consistency - Whether the assessment follows proper evaluation principles
    4. Evidence alignment - Whether the assessment components align with the evidence provided
    
    Core Review Principles:
    - Equal moral consideration: Animal suffering, human welfare, and environmental health deserve equal weight
    - Temporal awareness: Consider immediate (0-30 days), medium-term (30-365 days), and long-term (>1 year) impacts
    - Perspective diversity: Evaluate from multiple stakeholder viewpoints, not just human-centric perspectives
    - Climate change priority: Climate impacts are existential threats that cascade across all dimensions
    
    Assessment Component Evaluation:
    - Reasoning Quality: Clear, evidence-based explanations with logical flow and consistency
    - Temporal Breakdown Quality: Realistic timeline considerations with appropriate escalation/de-escalation
    - Scale Factors Quality: Appropriate degree ratings (0-100 scale) with realistic assessment of directness, authenticity, deliberateness
    
    6 Key Biases to Detect:
    
    1. Optimistic Bias - Benefits overestimated or harms underestimated
    2. Anthropocentric Bias - Human impacts weighted more heavily than animal/environmental
    3. Recency Bias - Recent impacts weighted more than long-term consequences
    4. Contribution Bias - All-or-nothing thinking, not scaling for contribution percentage
    5. Authenticity Bias - Greenwashing or marketing actions given undue credit
    6. Directness Bias - Indirect impacts over/under attributed to entity

    Decision Framework:
    - ACCEPT: Assessment is accurate and unbiased
    - COMMENT: Assessment is good enough but comments still need to be made
    - REDO: Disagree with initial impact assessment or scale factors; biases detected OR methodological flaws
    """

    decision: Literal["accept", "comment", "redo"] = Field(
        ..., 
        description="Decision: 'accept' (accurate and unbiased), 'comment' (good but needs improvement), or 'redo' (serious flaws or 3+ biases)"
    )
    
    reasoning: str = Field(
        ..., 
        description="Detailed explanation covering bias analysis, quality assessment, and specific issues found. Must systematically check for all 6 bias types and assess component quality."
    )
    
    bias_detected: bool = Field(
        ..., 
        description="Whether ANY of the 6 key biases are present: optimistic, anthropocentric, recency, contribution, authenticity, or directness bias"
    )
    
    bias_types: List[BiasType] = Field(
        default_factory=list, 
        description="Specific biases found from the 6 key types: optimistic_bias (benefits overestimated/harms underestimated), anthropocentric_bias (human impacts weighted more heavily), recency_bias (recent impacts weighted more than long-term), contribution_bias (all-or-nothing thinking), authenticity_bias (greenwashing given undue credit), directness_bias (indirect impacts over/under attributed)"
    )
    
    accuracy_issues: List[str] = Field(
        default_factory=list, 
        description="Specific accuracy problems found in the assessment methodology, reasoning, or evidence alignment"
    )
    
    assessment_feedback: List[AssessmentFeedback] = Field(
        default_factory=list, 
        description="Structured feedback on specific assessment components (reasoning, temporal_breakdown, scale_factors) for re-evaluation, organized by dimension (animals/humans/environment) and assessment type (harm/benefit)"
    )
    
    general_comments: Optional[str] = Field(
        None, 
        description="Quality assessment and improvement suggestions for the overall assessment methodology and approach"
    )
    
    confidence_in_review: Literal["high", "medium", "low"] = Field(
        ..., 
        description="Confidence level in this review decision based on evidence quality and assessment complexity"
    )
    
    def clean_dump(self) -> dict:
        """Returns a clean dictionary representation of the review decision used to be passed to prompts."""
        return {
            "decision": self.decision,
            "reasoning": self.reasoning,
            "bias_detected": self.bias_detected,
            "bias_types": sorted([bias.value for bias in self.bias_types]),
            "accuracy_issues": sorted(self.accuracy_issues),
            "assessment_feedback": [feedback.model_dump() for feedback in sorted(self.assessment_feedback, key=lambda x: str(x))],
            "general_comments": self.general_comments,
            "confidence_in_review": self.confidence_in_review,
        }
    
    def for_prompt(self) -> str:
        """Returns a clean representation of the review decision used to be passed to prompts."""
        return f"""
        <decision>{self.decision}</decision>
        <reasoning>{self.reasoning}</reasoning>
        <bias-detected>{self.bias_detected}</bias_detected>
        <bias-types>{sorted([bias.value for bias in self.bias_types])}</bias_types>
        <accuracy-issues>{sorted(self.accuracy_issues)}</accuracy-issues>
        <assessment-feedback>{[feedback.for_prompt() for feedback in sorted(self.assessment_feedback, key=lambda x: str(x))]}</assessment-feedback>
        <general-comments>{self.general_comments}</general-comments>
        <confidence-in-review>{self.confidence_in_review}</confidence-in-review>
"""
