from pydantic import BaseModel, Field, field_validator


class AssessmentFeedback(BaseModel):
    """
    Structured feedback on specific assessment components that can be improved during re-evaluation.
    
    Provides targeted guidance for improving assessment quality across the three core dimensions
    (animals, humans, environment) and both assessment types (harm, benefit).
    """

    dimension: str = Field(
        ..., 
        description="Impact dimension being assessed: 'animals' (animal welfare impacts), 'humans' (human welfare impacts), or 'environment' (environmental impacts)"
    )
    
    assessment_type: str = Field(
        ..., 
        description="Type of impact assessment: 'harm' (negative impacts) or 'benefit' (positive impacts)"
    )
    
    component: str = Field(
        ..., 
        description="Assessment component needing improvement: 'reasoning' (explanation quality), 'temporal_breakdown' (timeline analysis), 'scale_factors' (directness/authenticity ratings), or 'general' (overall approach)"
    )
    
    issue: str = Field(
        ..., 
        description="Specific problem identified with this component that affects assessment quality or introduces bias"
    )
    
    suggestion: str = Field(
        ..., 
        description="Detailed recommendation for improvement that addresses the identified issue and enhances assessment accuracy"
    )

    @field_validator('dimension')
    @classmethod
    def validate_dimension(cls, v):
        if v not in ['animals', 'humans', 'environment']:
            raise ValueError('Dimension must be animals, humans, or environment')
        return v

    @field_validator('assessment_type')
    @classmethod
    def validate_assessment_type(cls, v):
        if v not in ['harm', 'benefit']:
            raise ValueError('Assessment type must be harm or benefit')
        return v

    @field_validator('component')
    @classmethod
    def validate_component(cls, v):
        if v not in ['reasoning', 'temporal_breakdown', 'scale_factors', 'general']:
            raise ValueError('Component must be reasoning, temporal_breakdown, scale_factors, or general')
        return v

    def for_prompt(self) -> str:
        """Returns a clean representation of the assessment feedback used to be passed to prompts."""
        return f"""
        <dimension>{self.dimension}</dimension>
        <assessment_type>{self.assessment_type}</assessment_type>
        <component>{self.component}</component>
        <issue>{self.issue}</issue>
        <suggestion>{self.suggestion}</suggestion>
"""
