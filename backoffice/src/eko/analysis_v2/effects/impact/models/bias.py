"""
Bias-related models for impact assessment.
"""

from enum import Enum


class BiasType(str, Enum):
    """Types of bias that can be detected in impact assessments."""
    
    OPTIMISTIC_BIAS = "optimistic_bias"
    ANTHROPOCENTRIC_BIAS = "anthropocentric_bias"
    RECENCY_BIAS = "recency_bias"
    CONTRIBUTION_BIAS = "contribution_bias"
    AUTHENTICITY_BIAS = "authenticity_bias"
    DIRECTNESS_BIAS = "directness_bias"