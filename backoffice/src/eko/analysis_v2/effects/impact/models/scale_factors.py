from typing import Literal

from pydantic import BaseModel, Field, model_validator


class ScaleFactors(BaseModel):
    """
    Scale factors applied to derive a correct impact score using false/true questions, do not supply values which are false, because that is the default.
    """

    proximity_to_tipping_point: str = Field(
        ..., description="Proximity to tipping points, an assessment, leave empty if not applicable"
    )

    # Reversibility questions (7 questions covering 0-100 scale)
    is_irreversible: bool = Field(False,
                                  description="Is this impact completely irreversible (death, extinction, permanent damage, rape)?")
    takes_centuries_to_reverse: bool = Field(False, description="Would this impact take centuries or more to reverse?")
    requires_significant_effort_to_reverse: bool = Field(False,
                                                         description="Would reversing this impact require significant effort or resources?")
    reversible_within_years: bool = Field(False,
                                          description="Is this impact reversible within years (but not immediately)?")
    reversible_within_months: bool = Field(False, description="Is this impact reversible within months?")
    reversible_within_weeks: bool = Field(False, description="Is this impact reversible within weeks?")
    fully_reversible_immediately: bool = Field(False,
                                               description="Is this impact fully reversible immediately with minimal effort?")

    def has_reversibility(self):
        return any([self.is_irreversible, self.takes_centuries_to_reverse, self.requires_significant_effort_to_reverse,
                    self.reversible_within_years, self.reversible_within_months, self.reversible_within_weeks,
                    self.fully_reversible_immediately])


    # Scale of impact questions (8 questions covering individual to global scale)
    affects_none: bool = Field(False, description="Does this impact affect no-one?")
    affects_single_individual: bool = Field(False,
                                            description="Does this impact affect single individuals (humans/animals) or single hectare?")
    affects_multiple_individuals: bool = Field(False,
                                               description="Does this impact affect multiple individuals or small areas?")
    affects_many_beings: bool = Field(False,
                                      description="Does this impact affect many humans/animals or many hectares?")
    affects_large_population: bool = Field(False,
                                           description="Does this impact affect large populations/cities or large forests?")
    affects_country_ecosystem: bool = Field(False,
                                            description="Does this impact affect entire countries or ecosystems?")
    affects_species_biome: bool = Field(False, description="Does this impact affect entire species or biomes?")
    affects_all_global: bool = Field(False,
                                     description="Does this impact affect all humans/animals or all ecosystems globally?")



    # Directness questions (7 questions covering third party to direct action)
    third_party_action: bool = Field(False,
                                     description="Was this impact caused by third party action with no entity involvement?")
    entity_had_minor_influence: bool = Field(False, description="Did the entity have minor influence on this impact?")
    entity_influenced_outcome: bool = Field(False,
                                            description="Did the entity influence the outcome that led to this impact?")
    entity_action_led_to_impact: bool = Field(False,
                                              description="Did the entity's action lead to this impact (indirect causation)?")
    entity_decision_caused_impact: bool = Field(False,
                                                description="Did the entity's decision directly cause this impact?")
    direct_action_by_entity: bool = Field(False, description="Was this a direct action by the entity?")
    entity_sole_direct_cause: bool = Field(False, description="Is the entity the sole and direct cause of this impact?")



    # Authenticity questions (7 questions covering greenwashing to genuine commitment)
    pure_marketing_greenwashing: bool = Field(False,
                                              description="Is this action pure marketing/greenwashing with no genuine intent?")
    mostly_regulatory_compliance: bool = Field(False,
                                               description="Is this action mostly driven by regulatory compliance?")
    primarily_business_driven: bool = Field(False,
                                            description="Is this action primarily business driven with some genuine elements?")
    balanced_genuine_business: bool = Field(False,
                                            description="Does this action balance genuine and business motives equally?")
    mostly_genuine_some_business: bool = Field(False,
                                               description="Is this action mostly genuine with some business benefits?")
    genuine_commitment: bool = Field(False, description="Does this action represent genuine commitment to the impact?")
    purely_altruistic: bool = Field(False,
                                    description="Is this action purely altruistic with no business considerations?")

    # Deliberateness questions (7 questions covering accidental to fully intentional)
    completely_accidental: bool = Field(False, description="Was this impact completely accidental and unforeseeable?")
    foreseeable_not_intended: bool = Field(False, description="Was this impact foreseeable but not intended?")
    knew_consequences_acted_anyway: bool = Field(False,
                                                 description="Did the entity act despite knowing likely consequences?")
    intended_action_predictable_outcome: bool = Field(False,
                                                      description="Was this an intended action with predictable outcomes?")
    planned_with_awareness: bool = Field(False,
                                         description="Was this action planned with clear awareness of consequences?")
    fully_intentional: bool = Field(False, description="Was this impact fully intentional?")
    deliberately_planned_for_impact: bool = Field(False,
                                                  description="Was this impact deliberately planned and the primary goal?")



    # Contribution questions (7 questions covering no contribution to sole contributor)
    not_contributing: bool = Field(False, description="Is the entity not contributing to this problem/solution?")
    very_minor_contributor: bool = Field(False, description="Is the entity a very minor contributor?")
    minor_contributor: bool = Field(False, description="Is the entity a minor contributor?")
    significant_contributor: bool = Field(False, description="Is the entity a significant contributor?")
    major_contributor: bool = Field(False, description="Is the entity a major contributor?")
    dominant_contributor: bool = Field(False, description="Is the entity a dominant contributor?")
    sole_contributor: bool = Field(False, description="Is the entity the sole contributor to this problem/solution?")

    # Duration (keeping as is since it's already categorical)
    duration: Literal["short", "medium", "long"] = Field(...,
                                                         description="Duration of impact: short (<1 year), medium (1-50 years), long ( >50 years)")

    # Degree questions (7 questions covering no harm to total destruction)
    no_impact: bool = Field(False, description="Is there no harm or benefit from this impact?")
    minor_impact: bool = Field(False, description="Is this minor harm (wage decrease) or benefit (wage increase?")
    moderate_impact: bool = Field(False, description="Is this moderate harm (psychological) or benefit (better quality of life)?")
    major_impact: bool = Field(False, description="Is this major harm (such as maiming, PTSD) or major benefit (addiction recovery)?")
    severe_impact: bool = Field(False, description="Does this cause severe damage (torture, rape, trauma) or benefit (rewilding)?")
    extreme_impact: bool = Field(False,
                                 description="Does this cause extreme damage (maiming) or extreme benefit (re-forestation)?")
    existential_impact: bool = Field(False,
                                     description="Does this cause total destruction, death, or ultimate benefit (end of global warming)?")

    # Probability questions (7 questions covering will not happen to already happened)
    will_not_happen: bool = Field(False, description="Will this impact definitely not happen?")
    very_unlikely: bool = Field(False, description="Is this impact very unlikely to occur (<10% chance)?")
    unlikely: bool = Field(False, description="Is this impact unlikely to occur (~30% chance)?")
    even_chance: bool = Field(False, description="Is there an even chance this impact will occur (~50%)?")
    highly_likely: bool = Field(False, description="Is this impact highly likely to occur (~70% chance)?")
    virtually_certain: bool = Field(False, description="Is this impact virtually certain to happen (~90% chance)?")
    has_already_happened: bool = Field(False, description="Has this impact already happened or is happening now?")

    def for_prompt(self) -> str:
        """Returns a clean representation of the scale factors used to be passed to prompts."""
        # Convert boolean answers to structured format for prompts
        reversibility_answers = [
            f"is_irreversible: {self.is_irreversible}",
            f"takes_centuries_to_reverse: {self.takes_centuries_to_reverse}",
            f"requires_significant_effort_to_reverse: {self.requires_significant_effort_to_reverse}",
            f"reversible_within_years: {self.reversible_within_years}",
            f"reversible_within_months: {self.reversible_within_months}",
            f"reversible_within_weeks: {self.reversible_within_weeks}",
            f"fully_reversible_immediately: {self.fully_reversible_immediately}"
        ]

        scale_answers = [
            f"affects_single_individual: {self.affects_single_individual}",
            f"affects_multiple_individuals: {self.affects_multiple_individuals}",
            f"affects_many_beings: {self.affects_many_beings}",
            f"affects_large_population: {self.affects_large_population}",
            f"affects_country_ecosystem: {self.affects_country_ecosystem}",
            f"affects_species_biome: {self.affects_species_biome}",
            f"affects_all_global: {self.affects_all_global}"
        ]

        directness_answers = [
            f"third_party_action: {self.third_party_action}",
            f"entity_had_minor_influence: {self.entity_had_minor_influence}",
            f"entity_influenced_outcome: {self.entity_influenced_outcome}",
            f"entity_action_led_to_impact: {self.entity_action_led_to_impact}",
            f"entity_decision_caused_impact: {self.entity_decision_caused_impact}",
            f"direct_action_by_entity: {self.direct_action_by_entity}",
            f"entity_sole_direct_cause: {self.entity_sole_direct_cause}"
        ]

        return f"""
        <proximity_to_tipping_point>{self.proximity_to_tipping_point}</proximity_to_tipping_point>
        <reversibility>{', '.join(reversibility_answers)}</reversibility>
        <scale_of_impact>{', '.join(scale_answers)}</scale_of_impact>
        <directness>{', '.join(directness_answers)}</directness>
        <duration>{self.duration}</duration>
        """
