"""
Measurement models for impact assessment.
"""

from typing import List, Dict, Optional, Any

from pydantic import BaseModel, Field, model_validator

from .assessment import HarmImpactAssessment, BenefitImpactAssessment
from .review import ReviewDecision
from .ass_feedback import AssessmentFeedback


class EventImpactMeasurement(BaseModel):
    """Complete impact measurement result for an event."""
    id: Optional[int] = Field(None, description="Database ID")
    run_id: Optional[int] = Field(None, description="Run ID for tracking")
    event_id: Optional[str] = Field(None, description="Unique event identifier")
    event_summary: str = Field(..., description="Brief summary of the event")
    event_description: str = Field(..., description="Full event description analyzed")
    harm_assessment: HarmImpactAssessment = Field(..., description="Comprehensive harm assessment")
    benefit_assessment: BenefitImpactAssessment = Field(..., description="Comprehensive benefit assessment")
    key_uncertainties: List[str] = Field(..., description="Key uncertainties in the assessment")
    ethical_considerations: List[str] = Field(..., description="Ethical considerations identified")
    assessed_at: str = Field(..., description="ISO timestamp of assessment")
    model_used: str = Field(..., description="LLM model used")
    prompt_version: str = Field(..., description="Version of the prompt used")
    harm_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Overall harm score (0-1)")
    benefit_score: float = Field(default=0.0, ge=0.0, le=1.0, description="Overall benefit score (0-1)")
    net_impact_score: float = Field(default=0.0, ge=-1.0, le=1.0, description="Net impact score (-1 to 1)")
    calculation_details: Optional[Dict[str, Any]] = Field(None, description="Detailed calculation breakdown")
    reviewer_feedback: Optional[List[AssessmentFeedback]] = Field(None, description="Assessment feedback from reviewer")
    review_decision: Optional[ReviewDecision] = Field(None, description="Full review decision")

    @model_validator(mode='after')
    def calculate_aggregate_scores(self) -> 'EventImpactMeasurement':
        """Calculate aggregate scores from dimension assessments."""
        # Calculate harm score (average of all harm dimensions)
        self.harm_score = (
            self.harm_assessment.animals.assessment.score +
            self.harm_assessment.humans.assessment.score +
            self.harm_assessment.environment.assessment.score
        ) / 3.0
        
        # Calculate benefit score (average of all benefit dimensions)
        self.benefit_score = (
            self.benefit_assessment.animals.assessment.score +
            self.benefit_assessment.humans.assessment.score +
            self.benefit_assessment.environment.assessment.score
        ) / 3.0
        
        # Calculate net impact score using the specific formula
        if self.harm_score > self.benefit_score:
            # When harm > benefit: avg(benefits) - max(harms)
            self.net_impact_score = (
                sum([
                    self.benefit_assessment.animals.assessment.score,
                    self.benefit_assessment.humans.assessment.score,
                    self.benefit_assessment.environment.assessment.score,
                ]) / 3.0
            ) - max(
                self.harm_assessment.animals.assessment.score,
                self.harm_assessment.humans.assessment.score,
                self.harm_assessment.environment.assessment.score,
            )
        else:
            # When benefit >= harm: max(benefits) - avg(harms)
            self.net_impact_score = max(
                self.benefit_assessment.animals.assessment.score,
                self.benefit_assessment.humans.assessment.score,
                self.benefit_assessment.environment.assessment.score
            ) - (
                sum([
                    self.harm_assessment.animals.assessment.score,
                    self.harm_assessment.humans.assessment.score,
                    self.harm_assessment.environment.assessment.score
                ]) / 3.0
            )
        
        return self
    
    def clean_dump(self) -> dict:
        """Returns a clean dictionary representation of the measurement used to be passed to prompts."""
        return {
            "event_summary": self.event_summary,
            "event_description": self.event_description,
            "harm_score": self.harm_score,
            "benefit_score": self.benefit_score,
            "net_impact_score": self.net_impact_score,
            "reviewer_feedback": self.reviewer_feedback,
            "review_decision": self.review_decision,
            "key_uncertainties": sorted(self.key_uncertainties),
            "ethical_considerations": self.ethical_considerations,
            "harm_assessment": self.harm_assessment.model_dump(),
            "benefit_assessment": self.benefit_assessment.model_dump()
            
            
        }

    def for_prompt(self) -> str:
        """Returns a clean  representation of the measurement used to be passed to prompts."""

        return f"""
        <summary>{self.event_summary}</summary>
        <description>{self.event_description}</description>
        <assessment>
            <harm_score>{self.harm_score}/1.0</harm_score>
            <benefit_score>{self.benefit_score}/1.0</benefit_score>
            <net_impact_score>{self.net_impact_score}/1.0</net_impact_score>
            <reviewer_feedback>{[feedback.for_prompt() for feedback in self.reviewer_feedback] if self.reviewer_feedback else ""}</reviewer_feedback>
            <review_decision>{self.review_decision.for_prompt() if self.review_decision else ""}</review_decision>
            <key_uncertainties>{sorted(self.key_uncertainties)}</key_uncertainties>
            <ethical_considerations>{sorted(self.ethical_considerations)}</ethical_considerations>
            <harm_assessment>{self.harm_assessment.for_prompt()}</harm_assessment>
            <benefit_assessment>{self.benefit_assessment.for_prompt()}</benefit_assessment>
        </assessment>
"""

    def for_review(self) -> str:
        """Returns a clean  representation of the measurement used to be passed to prompts."""

        return f"""
            <summary>{self.event_summary}</summary>
            <description>{self.event_description}</description>
            <assessment>
                <harm_assessment>{self.harm_assessment.for_prompt()}</harm_assessment>
                <benefit_assessment>{self.benefit_assessment.for_prompt()}</benefit_assessment>
            </assessment>
    """
    @property
    def to_dict(self) -> dict:
        """Convert to dictionary representation."""
        return self.model_dump()
