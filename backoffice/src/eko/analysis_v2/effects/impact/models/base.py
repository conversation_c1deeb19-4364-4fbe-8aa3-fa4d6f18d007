"""
Base models shared between measure_impact and the impact calculation modules.

This module contains the fundamental Pydantic models used throughout the impact
assessment system to avoid circular imports.
"""

from pydantic import BaseModel, Field


class TemporalBreakdown(BaseModel):
    """Temporal breakdown of impacts across different time periods."""
    
    immediate: str = Field(..., description="Impact description for 0-30 days")
    medium_term: str = Field(..., description="Impact description for 30-365 days")
    long_term: str = Field(..., description="Impact description for >1 year")


