"""
Enhanced calculation details models for step-by-step impact calculation tracking.

This module provides detailed tracking of the calculation process including:
- Base score selection from indicators
- Step-by-step scale factor adjustments
- Validation and capping logic
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class ScaleFactorAdjustment(BaseModel):
    """Individual scale factor adjustment step."""
    factor_name: str = Field(..., description="Name of the scale factor")
    description: str = Field(..., description="Description of what this factor measures")
    multiplier: float = Field(..., description="Multiplier applied (e.g., 0.8, 1.0, 1.2)")
    score_before: float = Field(..., description="Score before this adjustment")
    score_after: float = Field(..., description="Score after this adjustment")
    triggered_criteria: Optional[List[str]] = Field(None, description="Which boolean criteria triggered this adjustment")


class ValidationDetails(BaseModel):
    """Details about score validation and capping."""
    original_score: float = Field(..., description="Score before validation")
    max_allowed_score: float = Field(..., description="Maximum allowed score based on indicators")
    capped_score: float = Field(..., description="Final score after capping")
    was_capped: bool = Field(..., description="Whether the score was capped")
    applied_limits: List[tuple[str, float]] = Field(default_factory=list, description="Limits applied based on indicators")
    confidence_adjusted: bool = Field(False, description="Whether confidence was reduced due to capping")


class BackendCalculationDetails(BaseModel):
    """Complete calculation details from backend calculator."""
    triggered_indicators: List[str] = Field(default_factory=list, description="Array of indicator names that were triggered")
    base_score: Optional[float] = Field(None, description="Initial score before any adjustments")
    base_score_rationale: Optional[str] = Field(None, description="Which indicator or logic determined the base score")
    scale_factor_adjustments: List[ScaleFactorAdjustment] = Field(default_factory=list, description="Step-by-step scale factor adjustments")
    pre_validation_score: Optional[float] = Field(None, description="Score after all scale factors but before validation")
    final_score: float = Field(..., description="Score after all adjustments and validation")
    confidence_factors: List[str] = Field(default_factory=list, description="Factors affecting confidence")
    scale_factors: Dict[str, Any] = Field(default_factory=dict, description="Complete scale factors data")
    has_climate_harm: Optional[bool] = Field(None, description="Whether this includes climate-related harm")
    has_climate_benefit: Optional[bool] = Field(None, description="Whether this includes climate-related benefits")
    probability: Optional[float] = Field(None, description="Probability factor if applicable")


class CompleteCalculationDetails(BaseModel):
    """Complete calculation details structure matching frontend expectations."""
    harm: Dict[str, BackendCalculationDetails] = Field(default_factory=dict, description="Harm calculation details by dimension")
    benefit: Dict[str, BackendCalculationDetails] = Field(default_factory=dict, description="Benefit calculation details by dimension")
    validation: Dict[str, Dict[str, ValidationDetails]] = Field(default_factory=dict, description="Validation details by type and dimension")
    
    def __init__(self, **data):
        super().__init__(**data)
        # Ensure validation structure exists
        if 'validation' not in data:
            self.validation = {
                'harm': {},
                'benefit': {}
            }