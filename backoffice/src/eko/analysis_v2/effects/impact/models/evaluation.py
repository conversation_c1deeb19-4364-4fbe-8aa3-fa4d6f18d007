"""
Evaluation models for impact assessment.
"""

from typing import List, Dict, Optional
from pydantic import BaseModel, Field


class ConsistencyChecks(BaseModel):
    """Results of consistency validation checks."""
    
    score_alignment: bool = Field(..., description="Whether scores align with reasoning")
    temporal_consistency: bool = Field(..., description="Whether temporal breakdowns are consistent")
    dimensional_balance: bool = Field(..., description="Whether dimensions are balanced appropriately")
    scale_factors_alignment: bool = Field(..., description="Whether ScaleFactors align with scores and reasoning")
    scale_factors_internal_consistency: bool = Field(..., description="Whether ScaleFactors are internally consistent")
    issues: List[str] = Field(default_factory=list, description="List of consistency issues found")


class ConfidenceAnalysis(BaseModel):
    """Analysis of confidence levels across dimensions."""
    
    confidence_distribution: Dict[str, int] = Field(..., description="Count of high/medium/low confidence ratings")
    avg_confidence: float = Field(..., description="Average confidence as numeric value (0-1)")
    low_confidence_high_impact: List[str] = Field(default_factory=list, description="Dimensions with low confidence but high impact")


class BiasDetection(BaseModel):
    """Detection of potential biases in the assessment."""

    anthropocentric_bias: bool = Field(..., description="Whether anthropocentric bias is detected")
    optimism_bias: bool = Field(..., description="Whether optimism bias is detected")
    recency_bias: bool = Field(..., description="Whether recency bias is detected")
    contribution_bias: bool = Field(..., description="Whether contribution bias is detected (all-or-nothing thinking)")
    authenticity_bias: bool = Field(..., description="Whether authenticity bias is detected (greenwashing)")
    directness_bias: bool = Field(..., description="Whether directness bias is detected (over/under attribution)")
    detected_issues: List[str] = Field(default_factory=list, description="List of bias issues detected")


class EventImpactEvaluation(BaseModel):
    """Evaluation result for an impact measurement."""
    
    measurement_id: Optional[str] = Field(None, description="ID of the measurement being evaluated")
    consistency_checks: ConsistencyChecks = Field(..., description="Consistency validation results")
    confidence_analysis: ConfidenceAnalysis = Field(..., description="Analysis of confidence levels")
    bias_detection: BiasDetection = Field(..., description="Bias detection results")
    completeness_score: float = Field(..., ge=0.0, le=1.0, description="Completeness score (0.0-1.0): 1.0=completely comprehensive, 0.8=mostly complete, 0.6=reasonably complete, 0.4=some gaps, 0.2=significant gaps, 0.0=incomplete")
    overall_quality: str = Field(..., description="Overall quality rating (poor/fair/good/excellent)")
    overall_quality_score: float = Field(..., ge=0, le=1.0, description="Overall quality as numeric score (0.0-1.0): 1.0=excellent quality, 0.8=good quality, 0.6=fair quality, 0.4=below average, 0.2=poor quality, 0.0=very poor")