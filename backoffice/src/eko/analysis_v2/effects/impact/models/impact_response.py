from typing import List, Dict, Any, Optional

from pydantic import BaseModel, Field, field_validator

from .assessment import HarmImpactAssessment, BenefitImpactAssessment


class LLMImpactResponse(BaseModel):
    """
    Comprehensive Impact Assessment Response Structure.

    This model captures the complete impact assessment framework for evaluating actions and events
    across three critical dimensions: environmental health, human welfare, and animal welfare.

    ## Core Assessment Principles

    ### 1. Equal Moral Consideration
    - Animal suffering matters as much as human suffering
    - Environmental destruction affects all life
    - Consider perspectives of all affected beings, not just humans

    ### 2. Temporal Dynamics
    - **Immediate** (0-30 days): Direct, observable impacts
    - **Medium-term** (30 days-1 year): Unfolding consequences
    - **Long-term** (>1 year): Lasting changes, future generations

    ### 3. Climate Change Priority
    Climate change is an existential threat that:
    - Multiplies all other harms
    - Affects all three dimensions severely
    - Has irreversible tipping points
    - Demands the highest impact scores when significantly affected

    ## Assessment Process Requirements

    1. **Event Summary**: Clear, factual 1-2 sentence summary of what occurred
    2. **Boolean Impact Checks**: Yes/no questions considering both direct and indirect impacts
    3. **Detailed Assessment per Dimension**: For each dimension (animals, humans, environment)
    4. **Key Uncertainties**: Critical unknowns that could significantly change the assessment
    5. **Ethical Considerations**: Moral issues, value conflicts, or controversial aspects

    ## Domain-Specific Guidelines

    ### Climate Impacts
    - CO2 emissions >1 million tons/year = major climate harm
    - New fossil fuel infrastructure = severe long-term impact
    - Deforestation of carbon sinks = extreme scores
    - Renewable energy at scale = significant benefits

    ### Animal Welfare
    - Consider wild animals, not just domestic
    - Habitat loss affects countless individuals
    - Species extinction = maximum severity
    - Factory farming = systematic harm

    ### Human Impacts
    - Consider marginalized populations
    - Economic impacts matter but aren't everything
    - Rights violations = severe harm
    - Future generations count

    ### Environmental Health
    - Biodiversity loss = ecosystem collapse risk
    - Pollution accumulates over time
    - Natural capital provides essential services
    - Tipping points can be irreversible
    """

    event_summary: str = Field(
        ...,
        description="""Clear, factual 1-2 sentence summary of what occurred in the event.
        Must be evidence-based and free from subjective interpretation.
        Focus on what actually happened, not its implications.""",
        min_length=10,
        max_length=500
    )

    harm_assessment: HarmImpactAssessment = Field(
        ...,
        description="""Detailed harm assessment across animals, humans, and environment dimensions.
        
        Each dimension includes boolean indicators and assessment details:
        1. **Boolean indicators**: Yes/no questions for specific impact types
        2. **Reasoning**: What specifically happened? Who/what was affected and how? What evidence supports this impact?
        3. **Temporal breakdown**: Immediate (0-30 days), medium-term (30 days-1 year), long-term (>1 year)
        4. **Scale factors**: Boolean yes/no questions for degree, directness, authenticity, deliberateness, reversibility, scope, and probability
        
        Guidelines for answering boolean indicators:
        - Consider both direct and indirect impacts
        - Include impacts that are highly probable, not just certain
        - Think about cascading effects and secondary consequences
        - Answer based on the full lifecycle of the action/event"""
    )

    benefit_assessment: BenefitImpactAssessment = Field(
        ...,
        description="""Detailed benefit assessment across animals, humans, and environment dimensions.
        
        Each dimension includes boolean indicators and assessment details:
        1. **Boolean indicators**: Yes/no questions for specific benefit types
        2. **Reasoning**: What positive outcomes occurred? Who/what benefited and how?
        3. **Temporal breakdown**: Benefits over immediate, medium-term, and long-term periods
        4. **Scale factors**: Boolean yes/no questions for degree, directness, authenticity, deliberateness, reversibility, scope, and probability
        
        Special considerations for benefits:
        - Climate benefits (renewable energy, emissions reduction) deserve high scores due to existential importance
        - Animal welfare improvements should consider both domestic and wild animals
        - Human benefits should prioritize marginalized populations and future generations
        - Environmental restoration and biodiversity enhancement are critical for ecosystem services"""
    )

    key_uncertainties: List[str] = Field(
        ...,
        description="""Critical unknowns that could significantly change the assessment if resolved.
        
        Focus on:
        - Information gaps that would materially affect impact scoring
        - Unknown long-term consequences or cascading effects
        - Uncertain scale or scope of impacts
        - Missing data about affected populations or ecosystems
        - Unclear causal relationships between actions and outcomes
        
        Each uncertainty should be specific and actionable for further research.""",
        min_length=0,
        max_length=10
    )

    ethical_considerations: List[str] = Field(
        ...,
        description="""Moral issues, value conflicts, or controversial aspects identified in the assessment.
        
        Consider:
        - Rights violations (human, animal, environmental rights)
        - Distributive justice (who bears costs vs. who receives benefits)
        - Intergenerational equity (impacts on future generations)
        - Conflicts between different moral frameworks (utilitarian vs. rights-based)
        - Cultural or religious perspectives on the impacts
        - Procedural justice (were affected parties consulted?)
        - Environmental justice (disproportionate impacts on vulnerable communities)
        
        Each consideration should identify the specific ethical tension or conflict.""",
        min_length=0,
        max_length=10
    )

    # Internal calculation details - not part of LLM response but added programmatically
    calculation_details: Optional[Dict[str, Any]] = Field(
        default=None,
        exclude=True,
        description="Internal calculation details for UI display, not serialized in schema"
    )

    @field_validator('event_summary')
    @classmethod
    def validate_event_summary(cls, v: str) -> str:
        """Ensure event summary is factual and concise."""
        if not v.strip():
            raise ValueError("Event summary cannot be empty")
        sentences = v.count('. ') + v.count('! ') + v.count('? ')
        if sentences > 3:
            raise ValueError("Event summary should be 1-2 sentences, not more than 3")
        return v.strip()

    @field_validator('key_uncertainties')
    @classmethod
    def validate_uncertainties(cls, v: List[str]) -> List[str]:
        """Ensure uncertainties are meaningful and not empty."""
        for uncertainty in v:
            if not uncertainty.strip():
                raise ValueError("Uncertainties cannot be empty strings")
            if len(uncertainty.strip()) < 10:
                raise ValueError("Each uncertainty must be at least 10 characters long")
        return [u.strip() for u in v]

    @field_validator('ethical_considerations')
    @classmethod
    def validate_ethical_considerations(cls, v: List[str]) -> List[str]:
        """Ensure ethical considerations are meaningful and not empty."""
        for consideration in v:
            if not consideration.strip():
                raise ValueError("Ethical considerations cannot be empty strings")
            if len(consideration.strip()) < 10:
                raise ValueError("Each ethical consideration must be at least 10 characters long")
        return [c.strip() for c in v]
