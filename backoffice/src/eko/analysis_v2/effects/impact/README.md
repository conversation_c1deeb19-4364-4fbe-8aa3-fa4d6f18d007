# Impact Score Calculation Module

This module provides programmatic calculation of impact scores based on boolean indicators and scale factors, replacing the previous LLM-based scoring approach.

## Overview

The impact calculation system separates the qualitative assessment (done by LLM) from the quantitative scoring (done programmatically). The LLM provides:
- Boolean indicators (e.g., "animals_died", "humans_saved")
- Scale factors (e.g., scale of impact, contribution percentage)
- Reasoning and temporal breakdowns

The programmatic calculator then uses these inputs to compute consistent, auditable scores.

## Architecture

### Core Components

1. **ImpactScoreCalculator** (`calculator.py`)
   - Main orchestrator for score calculation
   - Processes harm and benefit scores for all dimensions
   - Applies special case caps (recycling, awareness campaigns, etc.)

2. **ImpactIndicatorWeights** (`indicators.py`)
   - Centralized repository of indicator weights
   - Maps boolean indicators to severity/benefit scores
   - Based on the original validation logic

3. **ScaleFactorsAdjuster** (`scale_factors.py`)
   - Applies scale factor adjustments to base scores
   - Handles contribution, directness, authenticity adjustments
   - Special handling for climate impacts

4. **ConfidenceCalculator** (`confidence.py`)
   - Determines confidence levels based on multiple factors
   - Considers scale factor quality, temporal completeness
   - Checks for internal consistency

5. **Unified Model Structure**
   - `DimensionAssessment` now uses `SkipJsonSchema` for score/confidence fields
   - Ensures LLM doesn't generate these fields, they are calculated programmatically

## Score Calculation Process

### 1. Base Score Determination

For each dimension (animals/humans/environment), the calculator:
- Checks which boolean indicators are triggered
- Takes the maximum weight among triggered indicators
- Uses default scores if no indicators are triggered

Example weights:
- Animals died: 0.5
- Many animals died: 0.6
- Species went extinct: 0.9

### 2. Scale Factor Adjustments

The base score is modified by scale factors:

```python
# Scale adjustments
if scale_of_impact > 100000:
    adjustment *= 1.2
elif scale_of_impact > 10000:
    adjustment *= 1.1

# Contribution adjustments
if contribution < 20:
    adjustment *= 0.7
elif contribution < 50:
    adjustment *= 0.85

# Authenticity (for benefits)
if authenticity < 30:
    adjustment *= 0.5
```

### 3. Special Cases

Certain event types have maximum score caps:
- Recycling actions: 0.2
- Awareness campaigns: 0.2
- Initiative starts: 0.1

### 4. Confidence Calculation

Confidence is determined by:
- Scale factor completeness and quality
- Temporal breakdown completeness
- Reasoning quality
- Internal consistency
- Number of triggered indicators

## Usage

```python
from eko.analysis_v2.effects.impact import ImpactScoreCalculator

# Initialize calculator
calculator = ImpactScoreCalculator()

# LLM provides the response with indicators and scale factors
llm_response = get_llm_assessment(event_description)

# Calculate scores programmatically
result = calculator.calculate_scores(llm_response)

# Access calculated scores
harm_score = result.harm_assessment.animals.score
confidence = result.harm_assessment.animals.confidence
```

## Integration with ImpactMeasurementService

The service now:
1. Calls LLM with `LLMImpactResponse` model (with score/confidence fields excluded from schema)
2. Applies the calculator to compute scores programmatically
3. Returns the response with calculated scores filled in

## Benefits

1. **Consistency**: Same indicators always produce same scores
2. **Auditability**: Score calculation logic is explicit and traceable
3. **Maintainability**: Weights and logic can be adjusted without retraining
4. **Separation of Concerns**: LLM focuses on understanding, calculator on scoring

## Testing

Tests should verify:
- Correct weight application for each indicator
- Proper scale factor adjustments
- Special case handling
- Confidence calculation logic
- Edge cases (no indicators, extreme scale factors)

## Future Enhancements

1. Make weights configurable via settings
2. Add learning/calibration based on human feedback
3. Support for custom weight profiles per domain
4. Enhanced confidence metrics