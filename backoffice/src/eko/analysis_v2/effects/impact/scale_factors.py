"""
Scale factor adjustment calculations.

This module applies scale factor adjustments to initial indicator-based scores.
"""

from typing import Tuple, List, TYPE_CHECKING
from loguru import logger

from eko.analysis_v2.effects.impact.models.scale_factors import ScaleFactors
from eko.analysis_v2.effects.impact.models.assessment import EnvironmentHarmImpact, EnvironmentBenefitImpact
from eko.analysis_v2.effects.impact.models.calculation_details import ScaleFactorAdjustment


class ScaleFactorCalculator:
    """Applies scale factor adjustments to initial impact scores calculated from indicators using boolean questions."""

    @classmethod
    def _adjust_for_scale_of_impact(cls, scale_factors: ScaleFactors, dimension: str, score: float, 
                                   adjustments: List[ScaleFactorAdjustment]) -> float:
        """Apply scale of impact adjustments based on boolean questions."""
        score_before = score
        multiplier = 1.0
        adjustment_desc = []
        triggered_criteria = []

        # Apply adjustments based on scale questions (from highest to lowest impact)
        if scale_factors.affects_all_global:
            multiplier *= 1.0
            adjustment_desc.append("affects all globally")
            triggered_criteria.append("affects_all_global")
        elif scale_factors.affects_species_biome:
            multiplier *= 1.0
            adjustment_desc.append("affects species/biome")
            triggered_criteria.append("affects_species_biome")
        elif scale_factors.affects_country_ecosystem:
            multiplier *= 0.95
            adjustment_desc.append("affects country/ecosystem")
            triggered_criteria.append("affects_country_ecosystem")
        elif scale_factors.affects_large_population:
            multiplier *= 0.9
            adjustment_desc.append("affects large population")
            triggered_criteria.append("affects_large_population")
        elif scale_factors.affects_many_beings:
            multiplier *= 0.8
            adjustment_desc.append("affects many beings")
            triggered_criteria.append("affects_many_beings")
        elif scale_factors.affects_multiple_individuals:
            multiplier *= 0.6
            adjustment_desc.append("affects multiple individuals")
            triggered_criteria.append("affects_multiple_individuals")
        elif scale_factors.affects_single_individual:
            multiplier *= 0.5
            adjustment_desc.append("affects single individual")
            triggered_criteria.append("affects_single_individual")
        else:
            multiplier *= 0.5
            adjustment_desc.append("no scale specified")

        score *= multiplier
        
        # Track this adjustment
        adjustments.append(ScaleFactorAdjustment(
            factor_name="Scale of Impact",
            description=f"Geographic/population scope: {', '.join(adjustment_desc)}",
            multiplier=multiplier,
            score_before=score_before,
            score_after=score,
            triggered_criteria=triggered_criteria
        ))
        
        logger.info(f"Scale of impact adjustment: {', '.join(adjustment_desc)} -> {multiplier}x -> {score:.2f}")
        return score

    @classmethod
    def _adjust_for_contribution(cls, scale_factors: ScaleFactors, score: float, 
                                adjustments: List[ScaleFactorAdjustment]) -> float:
        """Apply contribution adjustments using boolean questions."""
        score_before = score
        multiplier = 1.0
        adjustment_desc = []
        triggered_criteria = []

        # Apply adjustments based on contribution questions (from highest to lowest contribution)
        if scale_factors.sole_contributor:
            multiplier *= 1.0
            adjustment_desc.append("sole contributor")
            triggered_criteria.append("sole_contributor")
        elif scale_factors.dominant_contributor:
            multiplier *= 1.0
            adjustment_desc.append("dominant contributor")
            triggered_criteria.append("dominant_contributor")
        elif scale_factors.major_contributor:
            multiplier *= 0.9
            adjustment_desc.append("major contributor")
            triggered_criteria.append("major_contributor")
        elif scale_factors.significant_contributor:
            multiplier *= 0.8
            adjustment_desc.append("significant contributor")
            triggered_criteria.append("significant_contributor")
        elif scale_factors.minor_contributor:
            multiplier *= 0.5
            adjustment_desc.append("minor contributor")
            triggered_criteria.append("minor_contributor")
        elif scale_factors.very_minor_contributor:
            multiplier *= 0.3
            adjustment_desc.append("very minor contributor")
            triggered_criteria.append("very_minor_contributor")
        elif scale_factors.not_contributing:
            multiplier *= 0.1
            adjustment_desc.append("not contributing")
            triggered_criteria.append("not_contributing")
        else:
            multiplier *= 0.6
            adjustment_desc.append("contribution unclear")

        score *= multiplier
        
        # Track this adjustment
        adjustments.append(ScaleFactorAdjustment(
            factor_name="Contribution",
            description=f"Entity's role in causing impact: {', '.join(adjustment_desc)}",
            multiplier=multiplier,
            score_before=score_before,
            score_after=score,
            triggered_criteria=triggered_criteria
        ))
        
        logger.info(f"Contribution adjustment: {', '.join(adjustment_desc)} -> {multiplier}x multiplier -> {score:.2f}")
        return score

    @classmethod
    def _adjust_for_probability(cls, scale_factors: ScaleFactors, dimension: str, score: float,
                                impact_type: str, adjustments: List[ScaleFactorAdjustment]) -> float:
        """Apply probability adjustments using boolean questions."""
        score_before = score
        multiplier = 1.0
        adjustment_desc = []
        triggered_criteria = []

        # Apply adjustments based on probability questions (from highest to lowest certainty)
        if scale_factors.has_already_happened:
            multiplier *= 1.0
            adjustment_desc.append("already happened")
            triggered_criteria.append("has_already_happened")
        elif scale_factors.virtually_certain:
            multiplier *= 1.0
            adjustment_desc.append("virtually certain")
            triggered_criteria.append("virtually_certain")
        elif scale_factors.highly_likely:
            multiplier *= 0.75
            adjustment_desc.append("highly likely")
            triggered_criteria.append("highly_likely")
        elif scale_factors.even_chance:
            multiplier *= 0.5
            adjustment_desc.append("even chance")
            triggered_criteria.append("even_chance")
        elif scale_factors.unlikely:
            multiplier *= 0.3
            adjustment_desc.append("unlikely")
            triggered_criteria.append("unlikely")
        elif scale_factors.very_unlikely:
            multiplier *= 0.1
            adjustment_desc.append("very unlikely")
            triggered_criteria.append("very_unlikely")
        elif scale_factors.will_not_happen:
            multiplier *= 0.0
            adjustment_desc.append("will not happen")
            triggered_criteria.append("will_not_happen")
        else:
            multiplier *= 0.2
            adjustment_desc.append("unknown probability")

        score *= multiplier
        
        # Track this adjustment
        adjustments.append(ScaleFactorAdjustment(
            factor_name="Probability",
            description=f"Likelihood of impact occurring: {', '.join(adjustment_desc)}",
            multiplier=multiplier,
            score_before=score_before,
            score_after=score,
            triggered_criteria=triggered_criteria
        ))
        
        if multiplier < 1.0:
            logger.info(
                f"Probability adjustment for {impact_type} ({dimension}): {', '.join(adjustment_desc)} -> {multiplier}x multiplier")
            logger.info(
                f"Probability downgrade applied to {impact_type} ({dimension}): {score_before:.2f} -> {score:.2f}")
        else:
            logger.info(f"No probability adjustment for {impact_type} ({dimension}): {', '.join(adjustment_desc)}")

        return score

    @classmethod
    def _adjust_for_duration(cls, scale_factors: ScaleFactors, score: float, 
                            adjustments: List[ScaleFactorAdjustment]) -> float:
        """ Adjust the benefit score based on the duration of the impact
        """
        score_before = score
        multiplier = 1.0
        adjustment_desc = []
        triggered_criteria = [scale_factors.duration]
        
        if scale_factors.duration == "short":
            multiplier *= 0.8
            adjustment_desc.append("short impact")
        elif scale_factors.duration == "medium":
            multiplier *= 0.9
            adjustment_desc.append("medium impact")
        elif scale_factors.duration == "long":
            multiplier *= 1.0
            adjustment_desc.append("long duration impact")
        else:
            raise ValueError(f"Unknown duration: {scale_factors.duration}")
            
        score *= multiplier
        
        # Track this adjustment
        adjustments.append(ScaleFactorAdjustment(
            factor_name="Duration",
            description=f"How long the impact lasts: {', '.join(adjustment_desc)}",
            multiplier=multiplier,
            score_before=score_before,
            score_after=score,
            triggered_criteria=triggered_criteria
        ))
        
        logger.info(f"Duration adjustment: {', '.join(adjustment_desc)} -> {multiplier}x -> {score:.2f}")
        return score

    @classmethod
    def _adjust_for_authenticity(cls, scale_factors: ScaleFactors, score: float, 
                                adjustments: List[ScaleFactorAdjustment]) -> float:
        """Apply authenticity adjustments using boolean questions."""
        score_before = score
        multiplier = 1.0
        adjustment_desc = []
        triggered_criteria = []

        # Apply adjustments based on authenticity questions (from most authentic to least authentic)
        if scale_factors.purely_altruistic:
            multiplier *= 1.0
            adjustment_desc.append("purely altruistic")
            triggered_criteria.append("purely_altruistic")
        elif scale_factors.genuine_commitment:
            multiplier *= 0.9
            adjustment_desc.append("genuine commitment to the impact")
            triggered_criteria.append("genuine_commitment")
        elif scale_factors.mostly_genuine_some_business:
            multiplier *= 0.8
            adjustment_desc.append("mostly genuine with some business benefits")
            triggered_criteria.append("mostly_genuine_some_business")
        elif scale_factors.balanced_genuine_business:
            multiplier *= 0.7
            adjustment_desc.append("balanced genuine and business motives")
            triggered_criteria.append("balanced_genuine_business")
        elif scale_factors.primarily_business_driven:
            multiplier *= 0.7
            adjustment_desc.append("primarily business driven")
            triggered_criteria.append("primarily_business_driven")
        elif scale_factors.mostly_regulatory_compliance:
            multiplier *= 0.6
            adjustment_desc.append("mostly regulatory compliance")
            triggered_criteria.append("mostly_regulatory_compliance")
        elif scale_factors.pure_marketing_greenwashing:
            multiplier *= 0.5
            adjustment_desc.append("pure marketing/greenwashing")
            triggered_criteria.append("pure_marketing_greenwashing")
        else:
            multiplier *= 0.6
            adjustment_desc.append("unknown authenticity")

        score *= multiplier
        
        # Track this adjustment
        adjustments.append(ScaleFactorAdjustment(
            factor_name="Authenticity",
            description=f"Motivation behind the action: {', '.join(adjustment_desc)}",
            multiplier=multiplier,
            score_before=score_before,
            score_after=score,
            triggered_criteria=triggered_criteria
        ))
        
        logger.info(f"Authenticity adjustment: {', '.join(adjustment_desc)} -> {multiplier}x -> {score:.2f}")
        return score

    @classmethod
    def _adjust_for_deliberateness(cls, scale_factors: ScaleFactors, score: float, 
                                  adjustments: List[ScaleFactorAdjustment]) -> float:
        """Apply deliberateness adjustments using boolean questions."""
        score_before = score
        multiplier = 1.0
        adjustment_desc = []
        triggered_criteria = []

        # Apply adjustments based on deliberateness questions (from most deliberate to least deliberate)
        if scale_factors.fully_intentional:
            multiplier *= 1.0
            adjustment_desc.append("fully intentional")
            triggered_criteria.append("fully_intentional")
        elif scale_factors.planned_with_awareness:
            multiplier *= 1.0
            adjustment_desc.append("planned with clear awareness of consequences")
            triggered_criteria.append("planned_with_awareness")
        elif scale_factors.intended_action_predictable_outcome:
            multiplier *= 0.9
            adjustment_desc.append("intended action with predictable outcomes")
            triggered_criteria.append("intended_action_predictable_outcome")
        elif scale_factors.knew_consequences_acted_anyway:
            multiplier *= 0.8
            adjustment_desc.append("acted despite knowing likely consequences")
            triggered_criteria.append("knew_consequences_acted_anyway")
        elif scale_factors.foreseeable_not_intended:
            multiplier *= 0.7
            adjustment_desc.append("foreseeable but not intended")
            triggered_criteria.append("foreseeable_not_intended")
        elif scale_factors.completely_accidental:
            multiplier *= 0.6
            adjustment_desc.append("completely accidental and unforeseeable")
            triggered_criteria.append("completely_accidental")
        else:
            multiplier *= 0.6
            adjustment_desc.append("unknown deliberateness")

        score *= multiplier
        
        # Track this adjustment
        adjustments.append(ScaleFactorAdjustment(
            factor_name="Deliberateness",
            description=f"How intentional the action was: {', '.join(adjustment_desc)}",
            multiplier=multiplier,
            score_before=score_before,
            score_after=score,
            triggered_criteria=triggered_criteria
        ))
        
        logger.info(f"Deliberateness adjustment: {', '.join(adjustment_desc)} -> {multiplier}x -> {score:.2f}")
        return score

    @classmethod
    def adjust_harm(cls, initial_score: float, scale_factors: ScaleFactors, dimension: str) -> tuple[float, List[ScaleFactorAdjustment]]:
        """
        Apply scale factor adjustments to an initial harm score.
        
        Args:
            initial_score: The initial score from indicators (0-1 range)
            scale_factors: The scale factors from the assessment
            dimension: The dimension being assessed (animals/humans/environment)
            
        Returns:
            Tuple of (adjusted score, list of scale factor adjustments)
        """
        score = initial_score
        adjustments = []
        logger.info(f"Initial score for harm ({dimension}): {score}")

        # Apply common adjustments
        score = cls._adjust_for_scale_of_impact(scale_factors, dimension, score, adjustments)
        score = cls._adjust_for_contribution(scale_factors, score, adjustments)
        score = cls._adjust_for_directness(scale_factors, score, adjustments)
        score = cls._adjust_for_reversibility(scale_factors, score, adjustments)
        score = cls._adjust_for_probability(scale_factors, dimension, score, "harm", adjustments)
        score = cls._adjust_for_duration(scale_factors, score, adjustments)
        score = cls._adjust_for_deliberateness(scale_factors, score, adjustments)

        # Duration and reversibility combo (environment)
        if dimension == "environment":
            if scale_factors.duration == "short" and (
                    scale_factors.reversible_within_months or scale_factors.reversible_within_weeks or scale_factors.fully_reversible_immediately):
                score_before = score
                score *= 0.8
                adjustments.append(ScaleFactorAdjustment(
                    factor_name="Duration/Reversibility Combo",
                    description="Short duration and easily reversible environmental impact gets additional reduction",
                    multiplier=0.8,
                    score_before=score_before,
                    score_after=score,
                    triggered_criteria=["short_duration", "easily_reversible"]
                ))
        logger.info(
            f"Duration/reversibility combo adjustment (environment): {scale_factors.duration} duration, easily reversible -> {score:.2f}")

        return score, adjustments

    @classmethod
    def _adjust_for_reversibility(cls, scale_factors: ScaleFactors, score: float, 
                                 adjustments: List[ScaleFactorAdjustment]) -> float:
        """Apply reversibility adjustments using boolean questions."""
        score_before = score
        multiplier = 1.0
        adjustment_desc = []
        triggered_criteria = []

        # Apply adjustments based on reversibility questions (irreversible impacts get reduced scores for harm)
        if scale_factors.is_irreversible:
            multiplier *= 1.0  # Full impact for irreversible harm
            adjustment_desc.append("irreversible")
            triggered_criteria.append("is_irreversible")
        elif scale_factors.takes_centuries_to_reverse:
            multiplier *= 0.95
            adjustment_desc.append("takes centuries to reverse")
            triggered_criteria.append("takes_centuries_to_reverse")
        elif scale_factors.requires_significant_effort_to_reverse:
            multiplier *= 0.9
            adjustment_desc.append("requires significant effort to reverse")
            triggered_criteria.append("requires_significant_effort_to_reverse")
        elif scale_factors.reversible_within_years:
            multiplier *= 0.85
            adjustment_desc.append("reversible within years")
            triggered_criteria.append("reversible_within_years")
        elif scale_factors.reversible_within_months:
            multiplier *= 0.8
            adjustment_desc.append("reversible within months")
            triggered_criteria.append("reversible_within_months")
        elif scale_factors.reversible_within_weeks:
            multiplier *= 0.75
            adjustment_desc.append("reversible within weeks")
            triggered_criteria.append("reversible_within_weeks")
        elif scale_factors.fully_reversible_immediately:
            multiplier *= 0.7
            adjustment_desc.append("fully reversible immediately")
            triggered_criteria.append("fully_reversible_immediately")
        else:
            multiplier *= 0.4
            adjustment_desc.append("unknown reversibility")

        score *= multiplier
        
        # Track this adjustment
        adjustments.append(ScaleFactorAdjustment(
            factor_name="Reversibility",
            description=f"How easily the impact can be reversed: {', '.join(adjustment_desc)}",
            multiplier=multiplier,
            score_before=score_before,
            score_after=score,
            triggered_criteria=triggered_criteria
        ))
        
        logger.info(f"Reversibility adjustment: {', '.join(adjustment_desc)} -> {multiplier}x -> {score:.2f}")
        return score

    @classmethod
    def _adjust_for_directness(cls, scale_factors: ScaleFactors, score: float, 
                              adjustments: List[ScaleFactorAdjustment]) -> float:
        """Apply directness adjustments using boolean questions."""
        score_before = score
        multiplier = 1.0
        adjustment_desc = []
        triggered_criteria = []

        # Apply adjustments based on directness questions (from most direct to least direct)
        if scale_factors.entity_sole_direct_cause:
            multiplier *= 1.0
            adjustment_desc.append("entity sole direct cause")
            triggered_criteria.append("entity_sole_direct_cause")
        elif scale_factors.direct_action_by_entity:
            multiplier *= 1.0
            adjustment_desc.append("direct action by entity")
            triggered_criteria.append("direct_action_by_entity")
        elif scale_factors.entity_decision_caused_impact:
            multiplier *= 0.95
            adjustment_desc.append("entity decision caused impact")
            triggered_criteria.append("entity_decision_caused_impact")
        elif scale_factors.entity_action_led_to_impact:
            multiplier *= 0.9
            adjustment_desc.append("entity action led to impact")
            triggered_criteria.append("entity_action_led_to_impact")
        elif scale_factors.entity_influenced_outcome:
            multiplier *= 0.8
            adjustment_desc.append("entity influenced outcome")
            triggered_criteria.append("entity_influenced_outcome")
        elif scale_factors.entity_had_minor_influence:
            multiplier *= 0.6
            adjustment_desc.append("entity had minor influence")
            triggered_criteria.append("entity_had_minor_influence")
        elif scale_factors.third_party_action:
            multiplier *= 0.3
            adjustment_desc.append("third party action")
            triggered_criteria.append("third_party_action")
        else:
            multiplier *= 0.6
            adjustment_desc.append("unknown directness")

        score *= multiplier
        
        # Track this adjustment
        adjustments.append(ScaleFactorAdjustment(
            factor_name="Directness",
            description=f"How directly the entity caused the impact: {', '.join(adjustment_desc)}",
            multiplier=multiplier,
            score_before=score_before,
            score_after=score,
            triggered_criteria=triggered_criteria
        ))
        
        logger.info(f"Directness adjustment: {', '.join(adjustment_desc)} -> {multiplier}x -> {score:.2f}")
        return score

    @classmethod
    def adjust_benefit(
            cls,
            initial_score: float,
            scale_factors: ScaleFactors,
            dimension: str,
            has_climate_benefit: bool = False
    ) -> tuple[float, List[ScaleFactorAdjustment]]:
        """
        Apply scale factor adjustments to an initial benefit score.
        
        Args:
            initial_score: The initial score from indicators (0-1 range)
            scale_factors: The scale factors from the assessment
            dimension: The dimension being assessed (animals/humans/environment)
            has_climate_benefit: Whether this provides climate benefits
            
        Returns:
            Tuple of (adjusted score, list of scale factor adjustments)
        """
        score = initial_score
        adjustments = []
        logger.info(f"Initial score for benefit ({dimension}): {score}")

        # Apply common adjustments
        score = cls._adjust_for_scale_of_impact(scale_factors, dimension, score, adjustments)
        score = cls._adjust_for_contribution(scale_factors, score, adjustments)
        score = cls._adjust_for_directness(scale_factors, score, adjustments)
        score = cls._adjust_for_reversibility(scale_factors, score, adjustments)
        score = cls._adjust_for_probability(scale_factors, dimension, score, "benefit", adjustments)
        score = cls._adjust_for_duration(scale_factors, score, adjustments)
        score = cls._adjust_for_deliberateness(scale_factors, score, adjustments)
        score = cls._adjust_for_authenticity(scale_factors, score, adjustments)

        # Additional directness adjustments (animals only) - for indirect impacts
        if dimension == "animals" and (scale_factors.third_party_action or scale_factors.entity_had_minor_influence):
            score_before = score
            score *= 0.8
            adjustments.append(ScaleFactorAdjustment(
                factor_name="Indirect Impact (Animals)",
                description="Additional reduction for indirect animal benefits",
                multiplier=0.8,
                score_before=score_before,
                score_after=score,
                triggered_criteria=["indirect_animal_impact"]
            ))
        logger.info(f"Additional directness adjustment (animals only): indirect impact -> {score:.2f}")

        # Climate benefit boost (environment)
        if dimension == "environment" and has_climate_benefit:
            if "climate" in scale_factors.proximity_to_tipping_point.lower():
                score_before = score
                score *= 1.2
                adjustments.append(ScaleFactorAdjustment(
                    factor_name="Climate Benefit Boost",
                    description="Additional boost for climate-related environmental benefits near tipping points",
                    multiplier=1.2,
                    score_before=score_before,
                    score_after=score,
                    triggered_criteria=["climate_benefit", "near_tipping_point"]
                ))

        logger.info(f"Climate benefit boost (environment): {score:.2f}")

        return score, adjustments

    @classmethod
    def check_climate_indicators(
            cls,
            harm_check: EnvironmentHarmImpact,
            benefit_check: EnvironmentBenefitImpact
    ) -> Tuple[bool, bool]:
        """
        Check if there are climate-related harm or benefit indicators.
        
        Args:
            harm_check: The environment harm impact assessment
            benefit_check: The environment benefit impact assessment
            
        Returns:
            Tuple of (has_climate_harm, has_climate_benefit)
        """
        has_climate_harm = False
        has_climate_benefit = False

        # Check harm indicators
        if harm_check.climate_risk_intensified:
            has_climate_harm = True
        if harm_check.ghg_emissions_increased:
            has_climate_harm = True

        # Check benefit indicators
        if benefit_check.renewable_energy_generated:
            has_climate_benefit = True
        if benefit_check.ghg_emissions_reduced:
            has_climate_benefit = True

        return has_climate_harm, has_climate_benefit
