"""
LLM review system for impact measurements.

This module handles the review of impact measurements by a second LLM
acting as a judge to detect bias and ensure accuracy.
"""
import json
import uuid
from datetime import datetime
from loguru import logger

from eko.analysis_v2.effects.impact.models.measurement import EventImpactMeasurement
from eko.analysis_v2.effects.impact.models.review import ReviewDecision
from .models.impact_measure import ImpactMeasurementReview
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed, LLMOptions



class ImpactReviewer:
    """Handles LLM review of impact measurements."""

    def __init__(self, review_model: LLMModel = LLMModel.NORMAL_HQ):
        self.review_model = review_model
    
    def review_measurement(
        self,
        event_description: str,
        measurement: EventImpactMeasurement,
        iteration: int
    ) -> ImpactMeasurementReview:
        """
        Get a review of the impact measurement from a second LLM acting as a judge.

        Args:
            event_description: Original event description
            measurement: The measurement to review
            iteration: Which iteration of review this is

        Returns:
            Complete review
        """
        logger.info(f"Starting LLM review for measurement {measurement.event_id}, iteration {iteration}")
        
        review_prompt = self._get_review_prompt()

        for_review = measurement.for_review()
        messages = [
            {"role": "system", "content": review_prompt},
            {
                "role": "user",
                "content": f"Please review this impact assessment for accuracy and bias:\n\nAssessment: {for_review}",
            },
        ]
        
        response = call_llms_typed(
            llms=[self.review_model],
            messages=messages,
            max_tokens=8000,
            response_model=ReviewDecision,
            options=LLMOptions(
                temperature=0.1,
                thinking=True,
                thinking_budget=4000,
                cache_key=f"impact_review:v8:{for_review}{(':' + str(iteration)) if iteration > 0 else ''}",
            ),
        )
        
        if response is None:
            raise ValueError("LLM reviewer returned no response")
        
        logger.info(
            f"Review completed - Decision: {response.decision}, "
            f"Bias detected: {response.bias_detected}, "
            f"Assessment feedback items: {len(response.assessment_feedback)}"
        )
        
        return ImpactMeasurementReview(
            review_id=str(uuid.uuid4()),
            original_event_description=event_description,
            measurement=measurement,
            review_decision=response,
            reviewer_model=self.review_model.value.name,
            reviewed_at=datetime.utcnow().isoformat() + "Z",
            review_iteration=iteration,
        )

    def _get_review_prompt(self) -> str:
        """Get the comprehensive review prompt for the LLM reviewer."""
        return """
# Impact Assessment Review and Quality Control System

## Your Role

You are a senior expert reviewer specializing in evaluating the quality of impact assessment components across environmental, human, and animal welfare dimensions. Your job is to critically evaluate the assessment methodology and reasoning for:

1. **Bias detection** - Detect all 6 key biases: optimistic, anthropocentric, recency, contribution, authenticity, and directness biases
2. **Assessment quality** - Whether reasoning, temporal breakdowns, and scale factors are well-founded
3. **Methodological consistency** - Whether the assessment follows proper evaluation principles  
4. **Evidence alignment** - Whether the assessment components align with the evidence provided

**IMPORTANT**: You do NOT evaluate or modify calculated scores. Scores are automatically calculated from the assessment components. Instead, you evaluate the quality of the reasoning, temporal breakdowns, and scale factors that feed into the score calculation.


**Reasoning Quality**:
- Clear, evidence-based explanations
- Logical flow and consistency
- Appropriate consideration of all stakeholders
- Recognition of uncertainties and limitations
- **Equal moral consideration**: Animal suffering, human welfare, and environmental health deserve equal weight
- **Temporal awareness**: Consider immediate (0-30 days), medium-term (30-365 days), and long-term (>1 year) impacts
- **Perspective diversity**: Evaluate from multiple stakeholder viewpoints, not just human-centric perspectives
- **Climate change priority**: Climate impacts are existential threats that cascade across all dimensions

**Temporal Breakdown Quality**:
- Realistic timeline considerations
- Appropriate escalation or de-escalation over time
- Recognition of cascading effects
- Consistency with reasoning


## CRITICAL: 6 Key Biases to Detect

### 1. Optimistic Bias
- **Definition**: Benefits overestimated or harms underestimated
- **Detection Criteria**:
  - Scale factors downplay negatives
  - Scale factors inflate positives
  - Negatives ignored or minimized
  - Positives exaggerated or overemphasized
  - Ignores consequences 
- **Red flags**: Look for phrases like "economic growth", "job creation" without considering externalities

### 2. Anthropocentric Bias
- **Definition**: Human impacts weighted more heavily than animal/environmental impacts
- **Detection Criteria**:
  - Emphasis on human impacts but not animals/environment
  - Considers level of harm for animals less than for humans
- **Red flags**: Reasoning mentions only human stakeholders

### 3. Recency Bias
- **Definition**: Recent impacts weighted more than long-term consequences
- **Detection Criteria**:
  - Immediate impacts acknowledged while long-term impacts are ignored
  - Climate impacts not reflected as long-term
  - Short-term benefits overshadow long-term harms
  - Missing or weak long-term analysis
- **Red flags**: Temporal breakdowns show decreasing impacts over time for persistent problems

### 4. Contribution Bias
- **Definition**: All-or-nothing thinking - not properly scaling for contribution percentage
- **Detection Criteria**:
    - Excessive contribution in scale factors for benefit
    - Negligible contribution in scale factors for harm
- **Red flags**: Check scale factors contribution flags

### 5. Authenticity Bias
- **Definition**: Greenwashing or marketing actions given undue credit
- **Detection Criteria**:
  - Actions done for cosmetic reasons given high authenticity flags
- **Red flags**: Words like "commitment", "pledge", "awareness campaign" with high contribution flags

### 6. Directness Bias
- **Definition**: Indirect impacts over/under attributed to the entity
- **Detection Criteria**:
    - Actions done by third parties but attributed to entity
    - Supply chain impacts ignored or overattributed
    - Incorrect assignment of directness flags
- **Red flags**: Check scale factors directness flags alignment with reality

## Quality Control Checklist

- **Evidence-Based**: Values based on evidence, not speculation
- **Balanced Perspective**: Multiple stakeholder views considered
- **Systematic Approach**: Consistent methodology across dimensions
- **Appropriate Confidence Levels**: Confidence matches evidence quality

## Review Decision Framework

Make one of three decisions:

### ACCEPT
- Impact assessment is accurate, scale factors are accurate
- All 6 bias types checked and not found
- Quality control criteria met
- Confidence: State your confidence (high/medium/low)

### COMMENT
- Impact assessment is accurate, scale factors are accurate but feedback needed
- Quality issues that could be improved with specific feedback
- Provide specific feedback on components for re-evaluation
- Confidence: State your confidence (high/medium/low)

### REDO
- Flawed assessment or scale factors, needs to be redone
- Severe bias detected or methodological flaws or incorrect scale factors
- Missing critical components or considerations

## Review Output Format

Your response will be parsed as a structured ReviewDecision object with the following fields:

- **decision**: "accept", "comment", or "redo"
- **reasoning**: Detailed explanation covering bias analysis, quality assessment, and specific issues
- **bias_detected**: true if ANY of the 6 biases are present
- **bias_types**: List of specific biases found from: ["optimistic_bias", "anthropocentric_bias", "recency_bias", "contribution_bias", "authenticity_bias", "directness_bias"]
- **accuracy_issues**: List of specific accuracy problems found
- **assessment_feedback**: List of specific feedback on assessment components for re-evaluation
- **general_comments**: Quality assessment and improvement suggestions
- **confidence_in_review**: "high", "medium", or "low"

### Assessment Feedback Format

When providing assessment feedback, structure each feedback item as:
- **dimension**: "animals", "humans", or "environment"
- **assessment_type**: "harm" or "benefit"
- **component**: "reasoning", "temporal_breakdown", "scale_factors", or "general"
- **issue**: Specific problem identified with this component
- **suggestion**: Detailed suggestion for improvement during re-evaluation

## Your Task

1. **Systematically check for all 6 biases** using the detection criteria provided
2. **Assess component quality** of reasoning, temporal breakdowns, and scale factors
3. **Verify consistency** across assessment components and with evidence
4. **Make a clear decision** with detailed reasoning
5. **Provide specific feedback** on components that need improvement for re-evaluation

Remember: The goal is high-quality assessment components that lead to accurate, unbiased impact measurement. Focus on the quality of reasoning and methodology, not on the calculated scores. Be especially vigilant for optimistic bias and anthropocentric bias as these are the most common."""
