from typing import LiteralString

from psycopg import Cursor

from eko.models import Location, SimpleEntity, QuantifiedEntity, PointInTime, Time, IssueFlag
from eko.models.issue import Issue


def get_flag_internal(cur: Cursor, flag_id: int, query: LiteralString) -> IssueFlag:
    cur.execute(query, (flag_id,))

    rows = cur.fetchall()
    if len(rows) == 0:
        raise ValueError(f"Flag with id {flag_id} not found")

    # Unpack the row data into variables
    id, embedding, flag_type, impact, issue, cluster_ids, explanation, reason, flag_text, confidence, disclosure, year, ethical_group, issue_type, description, summary, embedding, status, created_at, impact, impact_weight, title, locations, entities, quantity_id, quantity_amount, quantity_delta, quantity_type, quantity_unit, time_period_id, time_period_from_year, time_period_from_day, time_period_from_month, time_period_to_day, time_period_to_month, time_period_to_year, time_period_precision = rows[0]

    # Return an IssueFlag instance with the retrieved data
    return IssueFlag(
        id=id,
        type=flag_type,
        impact=impact,
        issue=issue,
        cluster_number=None,
        cluster_ids=None,
        issue_object=Issue(
            issue=issue,
            description=description,
            summary=summary,
            status=status,
            impact=impact,
            impact_weight=impact_weight,
            type=issue_type
        ),
        explanation=explanation,
        reason=reason,
        text=flag_text,
        confidence=confidence,
        disclosure=disclosure,
        year=year,
        locations=[Location(**location) for location in locations] if locations else [],
        entities=[SimpleEntity(**entity) for entity in entities] if entities else [],
        quantity=QuantifiedEntity(
            amount=quantity_amount,
            delta=quantity_delta,
            type=quantity_type,
            unit=quantity_unit
        ) if quantity_id else None,
        time=Time(
            from_=PointInTime(
                year=time_period_from_year,
                month=time_period_from_month,
                day=time_period_from_day
            ),
            to=PointInTime(
                year=time_period_to_year,
                month=time_period_to_month,
                day=time_period_to_day
            ),
            precision=time_period_precision
        ) if time_period_id else None
    )


def get_issue_flag(cur: Cursor, flag_id: int) -> IssueFlag:
    return get_flag_internal(
        cur,
        flag_id,
        """
        SELECT flags.id, flags.embedding, flag_type, flags.impact,
                flags.issue, cluster_ids, explanation, reason, flag_text, confidence,
                disclosure, year, tax.ethical_group, tax.type, tax.description, tax.summary,
                tax.embedding, tax.status, tax.created_at, tax.impact, tax.impact_weight, tax.title,
                (select ARRAY_AGG(JSON_BUILD_OBJECT('name', name,'type',location_type)) from kg_locations loc where loc.id = ANY(locations) )as locations,
                (select ARRAY_AGG(JSON_BUILD_OBJECT('name', name,'type', type)) from kg_base_entities e where e.id = ANY(entities) )as entities,
                q.id as quantity_id, q.amount as quantity_amount, q.delta as quantity_delta, q.quantity_type, q.unit as quantity_unit,
                tp.id as time_period_id, tp.from_year as time_period_from_year, tp.from_day as time_period_from_day, tp.from_month as time_period_from_month,
                tp.to_day as time_period_to_day, tp.to_month as time_period_to_month, tp.to_year as time_period_to_year,
                tp.precision as time_period_precision
        FROM ana_issue_flags flags
        JOIN _deprecated_kg_issues tax ON flags.issue = tax.issue JOIN public._deprecated_kg_issue_section_map map ON tax.issue = map.issue
        JOIN kg_model_sections sections ON map.model_section = sections.section AND map.model=sections.model
        LEFT JOIN kg_quantities q ON flags.quantity_id = q.id
        LEFT JOIN kg_time_periods tp ON flags.time_period_id = tp.id
        WHERE flags.id = %s
        """,
    )


def get_final_flag(cur: Cursor, flag_id: int) -> IssueFlag:
    return get_flag_internal(cur, flag_id, """
        SELECT flags.id, null, flag_type, flags.impact,
                flags.issue, '{}'::text[], explanation, reason, flag_text, confidence,
                false, year, tax.ethical_group, tax.type, tax.description, tax.summary,
                tax.embedding, tax.status, tax.created_at, tax.impact, tax.impact_weight, tax.title,
                (select ARRAY_AGG(JSON_BUILD_OBJECT('name', name,'type',location_type)) from kg_locations loc where loc.id = ANY(locations) )as locations,
                (select ARRAY_AGG(JSON_BUILD_OBJECT('name', name,'type', type)) from kg_base_entities e where e.id = ANY(entities) )as entities,
                q.id as quantity_id, q.amount as quantity_amount, q.delta as quantity_delta, q.quantity_type, q.unit as quantity_unit,
                tp.id as time_period_id, tp.from_year as time_period_from_year, tp.from_day as time_period_from_day, tp.from_month as time_period_from_month,
                tp.to_day as time_period_to_day, tp.to_month as time_period_to_month, tp.to_year as time_period_to_year,
                tp.precision as time_period_precision
        FROM ana_flags flags
        JOIN _deprecated_kg_issues tax ON flags.issue = tax.issue JOIN public._deprecated_kg_issue_section_map map ON tax.issue = map.issue
        JOIN kg_model_sections sections ON map.model_section = sections.section AND map.model=sections.model
        LEFT JOIN kg_quantities q ON flags.quantity_id = q.id
        LEFT JOIN kg_time_periods tp ON flags.time_period_id = tp.id
        WHERE flags.id = %s
        """)
