"""
Agent models for the web agent.
"""

from datetime import datetime
from typing import Dict, List, Optional, Set

from pydantic import BaseModel, Field

from .content import Link, PageContent
from .insights import ESGInsight


class AgentMemory(BaseModel):
    """Memory of the agent's findings and state."""
    company_name: str
    visited_urls: Set[str] = Field(default_factory=set)
    queued_links: List[Link] = Field(default_factory=list)
    insights: List[ESGInsight] = Field(default_factory=list)
    downloaded_files: List[str] = Field(default_factory=list)
    search_queries_used: List[str] = Field(default_factory=list)
    notes: List[str] = Field(default_factory=list)
    start_time: datetime = Field(default_factory=datetime.now)
    last_updated: datetime = Field(default_factory=datetime.now)


class AgentAction(BaseModel):
    """An action taken by the agent."""
    action_type: str
    params: Dict = Field(default_factory=dict)
    reasoning: str
    timestamp: datetime = Field(default_factory=datetime.now)


class AgentThought(BaseModel):
    """A thought process of the agent."""
    thought: str
    timestamp: datetime = Field(default_factory=datetime.now)


class AgentState(BaseModel):
    """Current state of the agent."""
    memory: AgentMemory
    current_url: Optional[str] = None
    current_content: Optional[PageContent] = None
    last_action: Optional[AgentAction] = None
    thoughts: List[AgentThought] = Field(default_factory=list)
    status: str = "initialized"