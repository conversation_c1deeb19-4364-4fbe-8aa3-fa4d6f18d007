"""
Content models for the web agent.
"""

from datetime import datetime
from typing import Dict, Optional

from pydantic import BaseModel, Field

from .types import ContentType, RelevanceScore


class PageContent(BaseModel):
    """Content extracted from a webpage or document."""
    url: str
    title: Optional[str] = None
    content_type: ContentType
    text: str
    summary: Optional[str] = None
    original_html: Optional[str] = None
    extracted_at: datetime = Field(default_factory=datetime.now)
    metadata: Dict = Field(default_factory=dict)


class Link(BaseModel):
    """A link found on a webpage."""
    url: str
    text: Optional[str] = None
    source_url: str
    relevance_score: RelevanceScore = RelevanceScore.MEDIUM_RELEVANCE
    relevance_reason: Optional[str] = None
    visited: bool = False
    visit_priority: int = 0