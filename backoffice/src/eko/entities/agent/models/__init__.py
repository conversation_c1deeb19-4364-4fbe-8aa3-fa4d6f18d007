"""
Data models for the web agent.
"""

from .types import ContentType, RelevanceScore, ESGCategory, BehaviorType
from .content import PageContent, Link
from .insights import ESGInsight
from .agent import AgentMemory, AgentAction, AgentThought, AgentState

__all__ = [
    "ContentType",
    "RelevanceScore", 
    "ESGCategory",
    "BehaviorType",
    "PageContent",
    "Link",
    "ESGInsight",
    "AgentMemory",
    "AgentAction",
    "AgentThought",
    "AgentState"
]