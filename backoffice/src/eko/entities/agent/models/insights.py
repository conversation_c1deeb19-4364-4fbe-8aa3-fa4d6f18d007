"""
Insight models for the web agent.
"""

from datetime import datetime
from typing import Dict, Optional

from pydantic import BaseModel, Field

from .types import ESGCategory, BehaviorType


class ESGInsight(BaseModel):
    """An insight about a company's ESG behavior."""
    company_name: str
    category: ESGCategory
    behavior_type: BehaviorType
    description: str
    source_url: str
    source_title: Optional[str] = None
    extracted_at: datetime = Field(default_factory=datetime.now)
    confidence: float = 0.5  # 0.0 to 1.0
    metadata: Dict = Field(default_factory=dict)