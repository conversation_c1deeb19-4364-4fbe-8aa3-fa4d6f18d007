"""
Type definitions for the web agent.
"""

from enum import Enum


class ContentType(str, Enum):
    """Types of content the agent can process."""
    WEBPAGE = "webpage"
    PDF = "pdf"
    ARTICLE = "article"
    REPORT = "report"
    SOCIAL_MEDIA = "social_media"
    OTHER = "other"


class RelevanceScore(int, Enum):
    """Relevance score for content."""
    NOT_RELEVANT = 0
    LOW_RELEVANCE = 1
    MEDIUM_RELEVANCE = 2
    HIGH_RELEVANCE = 3
    VERY_HIGH_RELEVANCE = 4


class ESGCategory(str, Enum):
    """ESG categories for classifying information."""
    ENVIRONMENTAL = "environmental"
    SOCIAL = "social"
    GOVERNANCE = "governance"
    GENERAL = "general"
    OTHER = "other"


class BehaviorType(str, Enum):
    """Types of company behavior."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    MIXED = "mixed"
    UNKNOWN = "unknown"