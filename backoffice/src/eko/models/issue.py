from typing import Literal, Dict, List

from loguru import logger
from pydantic import BaseModel

from eko.db import get_bo_conn
from eko.db.core import tuple_to_model


class Issue(BaseModel):
    issue: str
    description: str
    status: str
    summary: str
    impact: int
    impact_weight: float
    type: Literal["problem", "solution", "topic"]


# Global variables to hold loaded issues
issue_list: List[Issue] = []
issue_dictionary: Dict[str, Issue] = {}
issue_synonym_list: List[str] = []
issue_synonym_map: Dict[str, str] = {}

def load_issues():
    """Lazy load issues from database only when needed"""
    global issue_list, issue_dictionary, issue_synonym_list, issue_synonym_map
    
    # Skip if already loaded
    if issue_list and issue_dictionary:
        return
        
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute(
                "SELECT DISTINCT ON(issue) issue, description, status, summary, impact, impact_weight, type "
                "FROM  _deprecated_kg_issues ")
            issue_list = [tuple_to_model(x, Issue) for x in cur.fetchall()]
            issue_dictionary = {issue.issue: issue for issue in issue_list}
            cur.execute("SELECT DISTINCT issue, synonym FROM  _deprecated_kg_issue_synonyms ")
            rows = cur.fetchall()
            issue_synonym_list = [row[1] for row in rows]
            issue_synonym_map = {row[1]: row[0] for row in rows}
            logger.info(f"Loaded {len(issue_list)} issues and {len(issue_synonym_list)} synonyms")
