"""
Telemetry collector for CrewAI that works with the built-in observability.

This module provides a lightweight way to capture CrewAI's native telemetry
and store it in our database for dashboard visualization.
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger

from eko.db import get_bo_conn


class CrewAITelemetryCollector:
    """
    Collector that integrates with CrewAI's native telemetry system.
    
    This leverages CrewAI's built-in observability instead of custom wrappers.
    """
    
    def __init__(self, session_id: str):
        """Initialize the telemetry collector."""
        self.session_id = session_id
        
        # Enable CrewAI telemetry (it's enabled by default unless explicitly disabled)
        # We just ensure it's not disabled
        if os.getenv('CREWAI_DISABLE_TELEMETRY') == 'true':
            logger.warning("CrewAI telemetry is disabled. Dashboard data will be limited.")
            
        if os.getenv('OTEL_SDK_DISABLED') == 'true':
            logger.warning("OpenTelemetry is disabled globally. Dashboard data will be limited.")
    
    def capture_crew_execution(self, crew_result: Any, company_name: str, start_time: datetime, end_time: datetime):
        """
        Capture high-level crew execution data.
        
        This is called after crew execution to store summary data.
        """
        try:
            duration_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Store crew execution summary
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name)
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            "CrewAI",
                            "crew_execution",
                            json.dumps({
                                "company_name": company_name,
                                "result_summary": str(crew_result)[:500],
                                "duration_ms": duration_ms,
                                "start_time": start_time.isoformat(),
                                "end_time": end_time.isoformat()
                            }),
                            "crew_execution"
                        )
                    )
                    conn.commit()
                    logger.debug(f"Captured crew execution for session {self.session_id}")
        except Exception as e:
            logger.error(f"Failed to capture crew execution: {e}")
    
    def capture_task_execution(self, task_name: str, agent_name: str, result: Any, duration_ms: Optional[int] = None):
        """
        Capture individual task execution data.
        
        This can be called from task callbacks.
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name)
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            "task_execution",
                            json.dumps({
                                "result_summary": str(result)[:500],
                                "duration_ms": duration_ms,
                                "timestamp": datetime.now().isoformat()
                            }),
                            task_name
                        )
                    )
                    conn.commit()
                    logger.debug(f"Captured task execution: {task_name} by {agent_name}")
        except Exception as e:
            logger.error(f"Failed to capture task execution: {e}")
    
    @staticmethod
    def setup_opentelemetry_export():
        """
        Set up OpenTelemetry to export to our database.
        
        This would be the ideal approach - configuring CrewAI's built-in
        telemetry to export to our database instead of creating wrappers.
        """
        # This would require setting up an OpenTelemetry exporter
        # that writes to our PostgreSQL database tables
        # For now, we'll use the simpler callback approach
        pass


def create_telemetry_collector(session_id: str) -> CrewAITelemetryCollector:
    """Create a telemetry collector for the given session."""
    return CrewAITelemetryCollector(session_id)