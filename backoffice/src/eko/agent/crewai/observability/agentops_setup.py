"""
AgentOps integration for CrewAI observability.

This module provides setup and configuration for AgentOps monitoring
of CrewAI agents, which is the recommended observability solution.
"""

import os
from typing import Optional

from loguru import logger

try:
    import agentops
    AGENTOPS_AVAILABLE = True
except ImportError:
    AGENTOPS_AVAILABLE = False
    logger.warning("AgentOps not installed. Run: pip install 'crewai[agentops]' for monitoring")


class AgentOpsManager:
    """
    Manager for AgentOps observability integration.
    
    This class handles the initialization and management of AgentOps
    for monitoring CrewAI agent activities.
    """
    
    def __init__(self):
        self.session_id = None
        self.initialized = False
    
    def initialize(self, api_key: Optional[str] = None, tags: Optional[list] = None) -> bool:
        """
        Initialize AgentOps monitoring.
        
        Args:
            api_key: Optional API key (will use env var if not provided)
            tags: Optional tags to add to the session
            
        Returns:
            bool: True if initialization was successful
        """
        if not AGENTOPS_AVAILABLE:
            logger.warning("AgentOps not available - monitoring disabled")
            return False
        
        try:
            # Use provided API key or environment variable
            if api_key:
                os.environ['AGENTOPS_API_KEY'] = api_key
            
            # Check if API key is available
            if not os.getenv('AGENTOPS_API_KEY'):
                logger.warning("AGENTOPS_API_KEY not found - monitoring disabled")
                return False
            
            # Initialize AgentOps
            session = agentops.init(tags=tags or [])
            
            if session:
                self.session_id = session.session_id
                self.initialized = True
                logger.info(f"AgentOps initialized successfully - Session ID: {self.session_id}")
                return True
            else:
                logger.error("Failed to initialize AgentOps session")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing AgentOps: {e}")
            return False
    
    def tag_session(self, tags: list):
        """
        Add tags to the current AgentOps session.
        
        Args:
            tags: List of tags to add
        """
        if not self.initialized:
            logger.warning("AgentOps not initialized - cannot add tags")
            return
        
        try:
            agentops.add_tags(tags)
            logger.debug(f"Added tags to session: {tags}")
        except Exception as e:
            logger.error(f"Error adding tags to AgentOps session: {e}")
    
    def end_session(self, end_state: str = "Success"):
        """
        End the current AgentOps session.
        
        Args:
            end_state: The end state of the session (Success, Fail, Indeterminate)
        """
        if not self.initialized:
            return
        
        try:
            agentops.end_session(end_state)
            logger.info(f"AgentOps session ended with state: {end_state}")
            self.initialized = False
            self.session_id = None
        except Exception as e:
            logger.error(f"Error ending AgentOps session: {e}")
    
    def record_action(self, action_type: str, params: dict = None):
        """
        Record a custom action in AgentOps.
        
        Args:
            action_type: Type of action being recorded
            params: Optional parameters for the action
        """
        if not self.initialized:
            return
        
        try:
            agentops.record_action(action_type, params or {})
            logger.debug(f"Recorded action in AgentOps: {action_type}")
        except Exception as e:
            logger.error(f"Error recording action in AgentOps: {e}")
    
    def get_session_url(self) -> Optional[str]:
        """
        Get the URL for viewing the current session in AgentOps dashboard.
        
        Returns:
            Optional[str]: URL to the session or None if not available
        """
        if not self.initialized or not self.session_id:
            return None
        
        return f"https://app.agentops.ai/drilldown?session_id={self.session_id}"
    
    def is_available(self) -> bool:
        """Check if AgentOps is available and initialized."""
        return AGENTOPS_AVAILABLE and self.initialized


# Global instance for easy access
agentops_manager = AgentOpsManager()


def setup_agentops(api_key: Optional[str] = None, tags: Optional[list] = None) -> AgentOpsManager:
    """
    Setup AgentOps observability for CrewAI.
    
    Args:
        api_key: Optional API key (will use env var if not provided)
        tags: Optional tags to add to the session
        
    Returns:
        AgentOpsManager instance
    """
    agentops_manager.initialize(api_key, tags)
    return agentops_manager