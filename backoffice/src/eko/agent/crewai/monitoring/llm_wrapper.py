"""
LLM wrapper for monitoring CrewAI LLM calls.

This module provides a wrapper around LLM calls to automatically log
metrics and performance data for the monitoring dashboard.
"""

import time
from typing import Any, Dict, Optional

from crewai import LLM
from loguru import logger

from .event_logger import Agent<PERSON>ventLogger, TimedContext


class MonitoredLLM:
    """
    Wrapper around CrewAI LLM that logs all calls for monitoring.
    
    This class wraps LLM calls to automatically capture metrics such as
    token usage, cost, and response times for dashboard visualization.
    """
    
    def __init__(self, llm: LLM, event_logger: AgentEventLogger, agent_name: str):
        """
        Initialize the monitored LLM wrapper.
        
        Args:
            llm: The CrewAI LLM instance to wrap
            event_logger: Event logger for capturing metrics
            agent_name: Name of the agent using this LLM
        """
        self.llm = llm
        self.event_logger = event_logger
        self.agent_name = agent_name
        
        # Copy attributes from the original LLM
        for attr in dir(llm):
            if not attr.startswith('_') and not callable(getattr(llm, attr)):
                setattr(self, attr, getattr(llm, attr))
    
    def call(self, messages: Any, **kwargs) -> Any:
        """
        Wrapper around LLM call with monitoring.
        
        Args:
            messages: Messages to send to the LLM
            **kwargs: Additional arguments
            
        Returns:
            LLM response
        """
        with TimedContext() as timer:
            try:
                # Prepare request data for logging
                request_data = {
                    "messages": str(messages)[:500],  # Truncate for storage
                    "model": getattr(self.llm, 'model', 'unknown'),
                    "temperature": getattr(self.llm, 'temperature', None),
                    "kwargs": {k: str(v)[:100] for k, v in kwargs.items()}
                }
                
                # Make the actual LLM call
                response = self.llm.call(messages, **kwargs)
                
                # Extract metrics if available
                prompt_tokens = None
                completion_tokens = None
                total_tokens = None
                cost_usd = None
                
                # Try to extract token information from response
                if hasattr(response, 'usage'):
                    usage = response.usage
                    prompt_tokens = getattr(usage, 'prompt_tokens', None)
                    completion_tokens = getattr(usage, 'completion_tokens', None)
                    total_tokens = getattr(usage, 'total_tokens', None)
                
                # Prepare response data for logging
                response_data = {
                    "content": str(response)[:500],  # Truncate for storage
                    "success": True
                }
                
                # Log the LLM call
                self.event_logger.log_llm_call(
                    agent_name=self.agent_name,
                    model_name=getattr(self.llm, 'model', 'unknown'),
                    request_data=request_data,
                    response_data=response_data,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens,
                    cost_usd=cost_usd,
                    duration_ms=timer.duration_ms
                )
                
                return response
                
            except Exception as e:
                # Log failed LLM call
                error_data = {
                    "content": f"Error: {str(e)}",
                    "success": False,
                    "error": str(e)
                }
                
                self.event_logger.log_llm_call(
                    agent_name=self.agent_name,
                    model_name=getattr(self.llm, 'model', 'unknown'),
                    request_data=request_data,
                    response_data=error_data,
                    duration_ms=timer.duration_ms
                )
                
                raise
    
    def __getattr__(self, name: str) -> Any:
        """Delegate attribute access to the wrapped LLM."""
        return getattr(self.llm, name)
    
    def __setattr__(self, name: str, value: Any) -> None:
        """Set attributes on the wrapper or delegate to the wrapped LLM."""
        if name in ['llm', 'event_logger', 'agent_name']:
            super().__setattr__(name, value)
        else:
            if hasattr(self, 'llm'):
                setattr(self.llm, name, value)
            else:
                super().__setattr__(name, value)


def wrap_llm_with_monitoring(llm: LLM, event_logger: AgentEventLogger, agent_name: str) -> MonitoredLLM:
    """
    Wrap an LLM with monitoring capabilities.
    
    Args:
        llm: The LLM to wrap
        event_logger: Event logger for capturing metrics
        agent_name: Name of the agent using this LLM
        
    Returns:
        MonitoredLLM instance
    """
    return MonitoredLLM(llm, event_logger, agent_name)