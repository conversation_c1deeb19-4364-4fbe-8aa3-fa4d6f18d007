"""
Event logging system for CrewAI agent monitoring.

This module provides a centralized logging system for tracking all agent activities,
LLM calls, and tool usage for the monitoring dashboard.
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional

from loguru import logger

from eko.db import get_bo_conn


class AgentEventLogger:
    """
    Central event logger for CrewAI agent monitoring.
    
    This class provides methods to log various types of agent events to the monitoring
    database tables for visualization in the dashboard.
    """
    
    def __init__(self, session_id: str, run_id: Optional[int] = None):
        """
        Initialize the event logger.
        
        Args:
            session_id: The session ID for the agent run
            run_id: Optional run ID for tracking across multiple sessions
        """
        self.session_id = session_id
        self.run_id = run_id
        
    def log_event(
        self, 
        agent_name: str, 
        event_type: str, 
        event_data: Dict[str, Any],
        task_name: Optional[str] = None,
        tool_name: Optional[str] = None
    ):
        """
        Log a general agent event.
        
        Args:
            agent_name: Name of the agent
            event_type: Type of event ('task_start', 'task_complete', 'tool_call', 'llm_call', 'decision', 'error')
            event_data: Additional event data
            task_name: Optional task name
            tool_name: Optional tool name
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name, tool_name, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            event_type,
                            json.dumps(event_data),
                            task_name,
                            tool_name,
                            self.run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged event: {agent_name} - {event_type}")
        except Exception as e:
            logger.error(f"Failed to log event: {e}")
    
    def log_llm_call(
        self,
        agent_name: str,
        model_name: str,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        prompt_tokens: Optional[int] = None,
        completion_tokens: Optional[int] = None,
        total_tokens: Optional[int] = None,
        cost_usd: Optional[float] = None,
        duration_ms: Optional[int] = None
    ):
        """
        Log an LLM call with detailed metrics.
        
        Args:
            agent_name: Name of the agent making the call
            model_name: Name of the LLM model
            request_data: Request data (prompt, parameters)
            response_data: Response data
            prompt_tokens: Number of prompt tokens
            completion_tokens: Number of completion tokens
            total_tokens: Total tokens used
            cost_usd: Cost in USD
            duration_ms: Duration in milliseconds
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_llm_calls 
                        (session_id, agent_name, model_name, prompt_tokens, completion_tokens, 
                         total_tokens, cost_usd, request_data, response_data, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            model_name,
                            prompt_tokens,
                            completion_tokens,
                            total_tokens,
                            cost_usd,
                            json.dumps(request_data),
                            json.dumps(response_data),
                            duration_ms,
                            self.run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged LLM call: {agent_name} - {model_name}")
        except Exception as e:
            logger.error(f"Failed to log LLM call: {e}")
    
    def log_tool_usage(
        self,
        agent_name: str,
        tool_name: str,
        tool_input: Dict[str, Any],
        tool_output: Dict[str, Any],
        success: bool,
        error_message: Optional[str] = None,
        duration_ms: Optional[int] = None
    ):
        """
        Log tool usage with input/output data.
        
        Args:
            agent_name: Name of the agent using the tool
            tool_name: Name of the tool
            tool_input: Tool input data
            tool_output: Tool output data
            success: Whether the tool call was successful
            error_message: Error message if failed
            duration_ms: Duration in milliseconds
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_tool_usage 
                        (session_id, agent_name, tool_name, tool_input, tool_output, 
                         success, error_message, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            tool_name,
                            json.dumps(tool_input),
                            json.dumps(tool_output),
                            success,
                            error_message,
                            duration_ms,
                            self.run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged tool usage: {agent_name} - {tool_name}")
        except Exception as e:
            logger.error(f"Failed to log tool usage: {e}")
    
    def log_task_start(self, agent_name: str, task_name: str, task_description: str):
        """Log the start of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type='task_start',
            event_data={'task_description': task_description},
            task_name=task_name
        )
    
    def log_task_complete(self, agent_name: str, task_name: str, result: Any):
        """Log the completion of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type='task_complete',
            event_data={'result': str(result)[:1000]},  # Truncate large results
            task_name=task_name
        )
    
    def log_error(self, agent_name: str, error_message: str, error_details: Dict[str, Any]):
        """Log an error event."""
        self.log_event(
            agent_name=agent_name,
            event_type='error',
            event_data={
                'error_message': error_message,
                'error_details': error_details
            }
        )
    
    def log_decision(self, agent_name: str, decision_type: str, decision_data: Dict[str, Any]):
        """Log a decision made by an agent."""
        self.log_event(
            agent_name=agent_name,
            event_type='decision',
            event_data={
                'decision_type': decision_type,
                'decision_data': decision_data
            }
        )


class TimedContext:
    """Context manager for timing operations."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.duration_ms = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.duration_ms = int((self.end_time - self.start_time) * 1000)


def create_event_logger(session_id: str, run_id: Optional[int] = None) -> AgentEventLogger:
    """
    Factory function to create an event logger.
    
    Args:
        session_id: The session ID for the agent run
        run_id: Optional run ID for tracking
        
    Returns:
        AgentEventLogger instance
    """
    return AgentEventLogger(session_id, run_id)