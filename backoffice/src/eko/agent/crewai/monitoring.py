"""
Monitoring service for CrewAI agents.

This module provides event logging and tracking for agent activities,
LLM calls, and tool usage to support the dashboard monitoring system.
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
from loguru import logger

from eko.db import get_bo_conn


class AgentMonitoringService:
    """
    Service for monitoring agent activities and logging events to the database.
    
    This service tracks:
    - Agent execution events (task start/complete, decisions, errors)
    - LLM calls with cost and performance metrics
    - Tool usage with success/failure tracking
    """
    
    def __init__(self, session_id: str):
        """
        Initialize the monitoring service for a specific session.
        
        Args:
            session_id: The unique session ID for this agent run
        """
        self.session_id = session_id
        
    def log_event(
        self, 
        agent_name: str,
        event_type: str,
        event_data: Dict[str, Any],
        task_name: Optional[str] = None,
        tool_name: Optional[str] = None,
        run_id: Optional[int] = None
    ):
        """
        Log an agent execution event.
        
        Args:
            agent_name: Name of the agent performing the action
            event_type: Type of event (task_start, task_complete, tool_call, llm_call, decision, error)
            event_data: Additional data about the event
            task_name: Name of the task if applicable
            tool_name: Name of the tool if applicable
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_execution_events 
                        (session_id, agent_name, event_type, event_data, task_name, tool_name, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            event_type,
                            json.dumps(event_data),
                            task_name,
                            tool_name,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged event: {event_type} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log event: {e}")
    
    def log_llm_call(
        self,
        agent_name: str,
        model_name: Optional[str] = None,
        prompt_tokens: Optional[int] = None,
        completion_tokens: Optional[int] = None,
        total_tokens: Optional[int] = None,
        cost_usd: Optional[float] = None,
        request_data: Optional[Dict[str, Any]] = None,
        response_data: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[int] = None,
        run_id: Optional[int] = None
    ):
        """
        Log an LLM call with cost and performance metrics.
        
        Args:
            agent_name: Name of the agent making the call
            model_name: Name of the LLM model used
            prompt_tokens: Number of tokens in the prompt
            completion_tokens: Number of tokens in the completion
            total_tokens: Total tokens used
            cost_usd: Cost in USD for this call
            request_data: Request data sent to the LLM
            response_data: Response data from the LLM
            duration_ms: Duration of the call in milliseconds
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_llm_calls 
                        (session_id, agent_name, model_name, prompt_tokens, completion_tokens, 
                         total_tokens, cost_usd, request_data, response_data, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            model_name,
                            prompt_tokens,
                            completion_tokens,
                            total_tokens,
                            cost_usd,
                            json.dumps(request_data) if request_data else None,
                            json.dumps(response_data) if response_data else None,
                            duration_ms,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged LLM call: {model_name} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log LLM call: {e}")
    
    def log_tool_usage(
        self,
        agent_name: str,
        tool_name: str,
        tool_input: Dict[str, Any],
        tool_output: Dict[str, Any],
        success: bool,
        error_message: Optional[str] = None,
        duration_ms: Optional[int] = None,
        run_id: Optional[int] = None
    ):
        """
        Log tool usage with success/failure tracking.
        
        Args:
            agent_name: Name of the agent using the tool
            tool_name: Name of the tool being used
            tool_input: Input data passed to the tool
            tool_output: Output data from the tool
            success: Whether the tool usage was successful
            error_message: Error message if the tool failed
            duration_ms: Duration of the tool usage in milliseconds
            run_id: Run ID if part of a larger analysis run
        """
        try:
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_tool_usage 
                        (session_id, agent_name, tool_name, tool_input, tool_output, 
                         success, error_message, duration_ms, run_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id,
                            agent_name,
                            tool_name,
                            json.dumps(tool_input),
                            json.dumps(tool_output),
                            success,
                            error_message,
                            duration_ms,
                            run_id
                        )
                    )
                    conn.commit()
                    logger.debug(f"Logged tool usage: {tool_name} for agent {agent_name}")
        except Exception as e:
            logger.error(f"Failed to log tool usage: {e}")
            
    def log_task_start(self, agent_name: str, task_name: str, task_description: str):
        """Log the start of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type="task_start",
            event_data={
                "task_description": task_description,
                "started_at": datetime.now().isoformat()
            },
            task_name=task_name
        )
    
    def log_task_complete(self, agent_name: str, task_name: str, result: Any, duration_ms: Optional[int] = None):
        """Log the completion of a task."""
        self.log_event(
            agent_name=agent_name,
            event_type="task_complete",
            event_data={
                "result": str(result)[:1000],  # Truncate long results
                "completed_at": datetime.now().isoformat(),
                "duration_ms": duration_ms
            },
            task_name=task_name
        )
    
    def log_decision(self, agent_name: str, decision: str, reasoning: str, context: Dict[str, Any]):
        """Log an agent decision."""
        self.log_event(
            agent_name=agent_name,
            event_type="decision",
            event_data={
                "decision": decision,
                "reasoning": reasoning,
                "context": context,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_error(self, agent_name: str, error: Exception, context: Dict[str, Any]):
        """Log an error that occurred during agent execution."""
        self.log_event(
            agent_name=agent_name,
            event_type="error",
            event_data={
                "error_type": type(error).__name__,
                "error_message": str(error),
                "context": context,
                "timestamp": datetime.now().isoformat()
            }
        )


class MonitoredTool:
    """
    Wrapper class for tools to add monitoring capabilities.
    
    This wraps existing tools and automatically logs their usage.
    """
    
    def __init__(self, tool, monitoring_service: AgentMonitoringService, agent_name: str):
        """
        Initialize the monitored tool wrapper.
        
        Args:
            tool: The original tool to wrap
            monitoring_service: The monitoring service to log to
            agent_name: Name of the agent using this tool
        """
        self.tool = tool
        self.monitoring_service = monitoring_service
        self.agent_name = agent_name
        
        # Copy tool attributes
        self.name = getattr(tool, 'name', 'unknown_tool')
        self.description = getattr(tool, 'description', '')
        
    def __call__(self, *args, **kwargs):
        """Execute the tool with monitoring."""
        start_time = time.time()
        
        try:
            # Log tool input
            tool_input = {
                "args": args,
                "kwargs": kwargs
            }
            
            # Execute the tool
            result = self.tool(*args, **kwargs)
            
            # Calculate duration
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log successful tool usage
            self.monitoring_service.log_tool_usage(
                agent_name=self.agent_name,
                tool_name=self.name,
                tool_input=tool_input,
                tool_output={"result": str(result)[:1000]},  # Truncate long outputs
                success=True,
                duration_ms=duration_ms
            )
            
            return result
            
        except Exception as e:
            # Calculate duration even for errors
            duration_ms = int((time.time() - start_time) * 1000)
            
            # Log failed tool usage
            self.monitoring_service.log_tool_usage(
                agent_name=self.agent_name,
                tool_name=self.name,
                tool_input={"args": args, "kwargs": kwargs},
                tool_output={"error": str(e)},
                success=False,
                error_message=str(e),
                duration_ms=duration_ms
            )
            
            # Re-raise the exception
            raise


def wrap_tools_with_monitoring(tools: List, monitoring_service: AgentMonitoringService, agent_name: str) -> List:
    """
    Wrap a list of tools with monitoring capabilities.
    
    Args:
        tools: List of tools to wrap
        monitoring_service: The monitoring service to use
        agent_name: Name of the agent using these tools
        
    Returns:
        List of wrapped tools with monitoring
    """
    return [MonitoredTool(tool, monitoring_service, agent_name) for tool in tools]