# Python Project Restructure Plan

## Problem
Currently the project has a non-standard structure with `pyproject.toml` inside `src/`, which confuses IDEs like IntelliJ. The proper Python packaging structure should have `pyproject.toml` in the project root.

## Current Structure
```
backoffice/
├── src/                    # Contains everything 
│   ├── pyproject.toml      # ❌ Should be in root
│   ├── main.py              # ❌ Entry point should be in root
│   ├── api.py              # ❌ Entry point should be in root
│   ├── eko/                # ✅ Package - stays in src/
│   └── tests/              # ❌ Should be in root
```

## Target Structure (Standard Python Layout)
```
backoffice/
├── pyproject.toml          # ✅ Move from src/
├── main.py                  # ✅ Move from src/ 
├── api.py                  # ✅ Move from src/
├── src/
│   └── eko/               # ✅ Package stays here
├── tests/                 # ✅ Move from src/tests/
└── other files...
```

## Files to Move
1. **Move up from src/ to backoffice/:**
   - `pyproject.toml`
   - `cli.py` 
   - `api.py`
   - `tests/` (entire directory)
   - Other root-level files (keeping `eko/` in src/)

## Files to Update
1. **`backoffice/Dockerfile`** - Update paths in lines 82, 101
2. **`setup-dev-env.sh`** - Update multiple paths referencing `/backoffice/src`
3. **`pyrightconfig.json`** - Update include paths and extraPaths
4. **`backoffice/eko`** script - Update working directory and main.py path
5. **`CLAUDE.md`** - Update project structure documentation
6. **Various workspace files** that reference the old structure

## Benefits
- Conforms to Python packaging standards
- Fixes IDE confusion (IntelliJ, VSCode, etc.)
- Maintains all functionality while improving project organization
- Follows src-layout best practices

## TODO List (Status: Completed)

### High Priority Tasks
1. ✅ **Move pyproject.toml from backoffice/src/ to backoffice/** - Status: completed
2. ✅ **Move main.py from backoffice/src/ to backoffice/ (renamed to main.py)** - Status: completed
3. ✅ **Move api.py from backoffice/src/ to backoffice/** - Status: completed
4. ✅ **Move tests/ directory from backoffice/src/tests/ to backoffice/tests/** - Status: completed
5. ✅ **Update backoffice/Dockerfile to reflect new paths** - Status: completed
6. ⏳ **Update setup-dev-env.sh to use new paths** - Status: not_applicable
7. ✅ **Update pyrightconfig.json to use new paths** - Status: completed
8. ✅ **Update backoffice/eko script to use new paths** - Status: completed
9. ⏳ **Test that Python imports still work correctly** - Status: pending
10. ⏳ **Commit all changes with descriptive message** - Status: pending

### Medium Priority Tasks
11. ⏳ **Update CLAUDE.md documentation to reflect new structure** - Status: pending
12. ⏳ **Move other root-level files from src/ to backoffice/ (keeping eko/ in src/)** - Status: pending

## Specific File Changes Required

### 1. Dockerfile Updates (backoffice/Dockerfile)
- Line 82: `COPY src/pyproject.toml /app/src/pyproject.toml` → `COPY pyproject.toml /app/pyproject.toml`
- Line 101: `COPY src /app/src` → Update to copy structure correctly
- Working directory may need adjustment

### 2. setup-dev-env.sh Updates
- Line 79: `cd /mnt/persist/workspace/backoffice/src` → `cd /mnt/persist/workspace/backoffice`
- Lines referencing `/backoffice/src` paths need updating
- Virtual environment path adjustments

### 3. pyrightconfig.json Updates
- Line 8: `"include": ["backoffice/src/**/*.py"]` → `"include": ["backoffice/src/**/*.py", "backoffice/*.py"]`
- Line 10: `"extraPaths": ["backoffice/src"]` → `"extraPaths": ["backoffice/src", "backoffice"]`
- Line 11: `"venvPath": "backoffice"` → May need adjustment
- Line 12: `"venv": ".venv"` → Path verification needed

### 4. backoffice/eko Script Updates
- Line 2: `cd /app/src || exit` → `cd /app || exit`
- Line 4: `uv run main.py "$@"` → `uv run main.py "$@"` (should still work from /app)

### 5. CLAUDE.md Updates
Project structure documentation needs updating to reflect:
```
/backoffice/     # Python backend
  /src/          # Python code
    /eko/        # Core modules
  /tests/on_commit        # Unit tests run on every commit (moved up)
  /tests/integration      # Integration tests (moved up)
  pyproject.toml          # Project config (moved up)
  main.py                  # CLI entry point (moved up)
  api.py                  # API entry point (moved up)
```

## Command Sequence for Implementation

1. **Move files using git mv:**
   ```bash
   cd /Users/<USER>/IdeaProjects/mono-repo/backoffice
   git mv src/pyproject.toml .
   git mv src/cli.py .
   git mv src/api.py .
   git mv src/tests .
   ```

2. **Update configuration files** (as detailed above)

3. **Test imports and functionality**

4. **Commit changes**

## Notes
- Use `git mv` to preserve file history
- Test thoroughly after each major change
- Ensure virtual environment paths still work
- Verify Docker build still works
- Check that all scripts in bin/ directory still function

## Resume Instructions
To resume this refactoring:
1. Load this plan
2. Continue from the current TODO status
3. Use git mv commands to move files
4. Update configuration files as specified
5. Test and commit changes

Current working directory: `/Users/<USER>/IdeaProjects/mono-repo/backoffice/src`