<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="All Playwright" type="JavaScriptTestRunnerPlaywright" editBeforeRun="true">
    <node-interpreter value="project" />
    <playwright-package value="$PROJECT_DIR$/apps/customer/node_modules/@playwright/test" />
    <working-dir value="$PROJECT_DIR$/apps/customer" />
    <envs>
      <env name="PW_PORT" value="3337" />
    </envs>
    <scope-kind value="DIRECTORY" />
    <test-directory value="$PROJECT_DIR$/apps/customer/tests" />
    <method v="2" />
  </configuration>
</component>
