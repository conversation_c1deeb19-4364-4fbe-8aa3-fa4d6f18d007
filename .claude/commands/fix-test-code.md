---
allowed-tools: Bash(npx:*)
description: Fix broken code for a good test.
---

Please run `cd apps/customer && npx playwright test --reporter=line tests/$ARGUMENTS 2>&1`

<guidelines>
# Best Practices for Writing Playwright Tests

Here are essential best practices to follow when writing Playwright tests:

## Selector Best Practices

**Use stable selectors** - Rely on data attributes, accessible roles, or stable IDs that won't break when the UI changes. ONLY use data attributes, accessible roles, or stable IDs, never CSS selectors like `div > div:nth-child(3) > span`. If you need to change the code being tested to introduce a `data-testid` attribute then please do so.

**Centralize selector management** - Create reusable selector utilities in the apps/customer/tests/helpers/tests directory to avoid repeating the same selectors across multiple tests. If you need to create new utils files in that directory then please do so.

**Use Playwright's locator strategy** - Use `page.locator()` instead of `page.$()` to benefit from auto-waiting and better error messages.

## Timing and Waiting Best Practices

**Wait for specific conditions** - Use condition-based waiting like `waitForSelector()`, `waitForLoadState()`, or `waitForResponse()` instead of arbitrary timeouts like `await page.waitForTimeout(5000)` to make tests faster and more reliable.

**Wait for elements properly** - Always wait for elements to be visible, enabled, or loaded before interacting with them.

**Account for asynchronous behavior** - Plan for network requests, animations, and dynamic content loading in your test flow.

## Test Structure Best Practices

**Write focused, single-purpose tests** - Create tests that verify one specific behavior or feature, making them easier to debug and understand when they fail.

**Ensure proper test isolation** - Make each test independent by not relying on state from previous tests and avoiding shared data between test cases.

**Clean up test data** - Remove test artifacts from databases and systems after each test run to prevent interference with subsequent tests.

## Authentication and State Best Practices

**Use efficient authentication strategies** - Leverage Playwright's authentication features and storage state instead of manually logging in for each test.

**Use secure credential management** - Store usernames and passwords in environment variables or secure storage rather than hard-coding them in test code.

## Error Handling Best Practices

**Investigate and address test failures** - Always investigate flaky tests and understand root causes before addressing failures.

**Provide comprehensive error context** - Add meaningful test descriptions and custom error messages that help with debugging when tests fail.

**Use appropriate assertions** - Use Playwright's specific matchers instead of generic assertions to get better error messages and more precise validation.
</guidelines>

<notes>
You have a playwright MCP tool, you can use that to manually check things, get screenshots, read console logs and network logs.
You have a Context7 tool for documentation and you can get other documentation straight from the web.
You have Supabase MCP tools for the customer/cms database - and run_in_db.sh for the analytics database.
</notes>

Please fix the code that is tested by apps/customer/tests/$ARGUMENTS



Please use the following workflow:

- Add console checks if needed in the test and/or in the tested code :

  page.on('console', msg => {
  console.log(`Browser console: \${msg.type()}: \${msg.text()}`);
  });
-
- Run playwright tests as follows:  `cd apps/customer && npx playwright test --reporter=line tests/$ARGUMENTS` .
- Check the console logs
- Check the code base for what the test tests, so you can understand it.
- If stuck get more logging with  `DEBUG=pw:* cd apps/customer && npx playwright test --reporter=line tests/$ARGUMENTS`
- Determine if the test is broken or the code is broken.
- If the test is broken **stop all actions** and **ask what the user** what to do next.
- It is much more likely the code is broken, not the test. That is why you are being asked to look at it.
- Timeouts are sometimes a sign of a broken test, but more often opening new pages and some other operations can take a time so consider increasing timeouts if needed.
- Search the web for any details you need on libraries used, especially ones that change frequently, or use your Context7 MCP tool if you like.
- Read the code that is being tested to understand what it is doing, versus the behaviour the test expects. Remember it is most likely the code is broken not the test.
- Do the actual work, making use of the tools you have, especially run_in_db.sh for analytics db and run_in_customer_db.sh for customer db.
- Use `tsc --noEmit`
- Run the test again: `cd apps/customer && npx playwright test --reporter=line tests/$ARGUMENTS` .
- Commit changes as you need, with a verbose comment including the name of the test you are working.
Plan this out and think harder and carefully step by step, build up a set of Todos and work your way through this methodically. 
