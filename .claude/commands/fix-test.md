Can you thoroughly review apps/customer/tests/$ARGUMENTS.spec.ts for discrepancies between it and the code base. I think it is out of sync with functionality. Please also look for any selectors which rely on css rather than data-testid attributes or similar. Please look out for duplicated or repititve code that should make use of utility functions in apps/customer/tests/helpers/tests.

Firstly look at a working test such as apps/customer/tests/editor-ai-features.spec.ts or apps/customer/tests/ai-edit-responses.spec.ts or apps/customer/tests/report-api.spec.ts and see how it works.

<guidelines>
# Bad Practices for Writing Playwright Tests

Here are common bad practices to avoid when writing Playwright tests:

## Selector Issues

**Using fragile selectors** - Relying on CSS selectors that break easily when the UI changes, like
`div > div:nth-child(3) > span`. ONLY use data attributes, accessible roles, or stable IDs, never css. If you need to
change the code being tested to introduce a `data-testid` attribute then please do so.

**Hard-coding selectors everywhere** - Repeating the same selectors across multiple tests makes maintenance difficult. We have the apps/customer/tests/helpers/tests directory for this, make use of those utils. If you need to create new utils files in that directory then please do so. 

**Not using <PERSON><PERSON>'s locator strategy** - Using `page.$()` instead of `page.locator()` misses out on auto-waiting and better error messages.

## Timing and Waiting

**Using arbitrary sleeps** - Adding `await page.waitForTimeout(5000)` instead of waiting for specific conditions. This makes tests slow and flaky.

**Not waiting for elements properly** - Forgetting to wait for elements to be visible, enabled, or loaded before interacting with them.

**Assuming synchronous behavior** - Not accounting for network requests, animations, or dynamic content loading.

## Test Structure

**Writing overly long tests** - Creating tests that do too many things at once, making them hard to debug and understand when they fail.

**Poor test isolation** - Tests depending on the state left by previous tests or sharing data between test cases.

**Not cleaning up test data** - Leaving test artifacts in databases or systems that can affect subsequent test runs.

## Authentication and State

**Re-authenticating in every test** - Logging in manually for each test instead of using Playwright's authentication features or storage state.

**Hard-coding credentials** - Putting usernames and passwords directly in test code instead of using environment variables or secure storage.

## Error Handling

**Ignoring test failures** - Not investigating flaky tests or suppressing failures without understanding root causes.

**Insufficient error context** - Not adding meaningful test descriptions or custom error messages that help with debugging.

**Not using proper assertions** - Using generic assertions instead of Playwright's specific matchers that provide better error messages.
</guidelines>

<notes>
You have a playwright MCP tool, you can use that to manually check things, get screenshots, read console logs and network logs.
You have a Context7 tool for documentation.
You have supabase tools for the database.
</notes>

Please fix the test apps/customer/tests/$1.spec.ts

Firstly look at a working test such as apps/customer/tests/editor-ai.spec.ts or apps/customer/tests/document-templates.spec.ts and see how it works.

Reminders:

Use testUtils for all applicable operations. For example, login:

let testUtils: TestUtils;

test.beforeEach(async ({ page }) => {
testUtils = new TestUtils(page);
await testUtils.login();
});

Or create a document:
await testUtils.createDocumentFromTemplate()

Or wait for the editor:
await testUtils.waitForEditor()

Please use the following workflow:

- Check the code base for what the test, tests so you can understand it.
- Add console checks:

  page.on('console', msg => {
  console.log(\`Browser console: \${msg.type()}: \${msg.text()}\`);
  });Wek

- Run playwright tests as follows:  \`cd apps/customer && npx playwright test --reporter=line tests/$1.spec.ts\` .
- If stuck get more logging with  \`DEBUG=pw:* cd apps/customer && npx playwright test --reporter=line tests/$1.spec.ts\`
- Determine if the test is broken or the code is broken.
- If the test is broken fix the test, if the code is broken fix the code.
- Timeouts are sometimes a sign of a broken test, but opening new pages and some other operations can take a time so consider increasing timeouts if needed.
- Search the web for any details you need on libraries, especially ones that change frequently, or use your Context7 MCP tool if you like.
- Do the actual work, making use of the tools you have, especially run_in_db.sh for analytics db and run_in_customer_db.sh for customer db.
- Use \`tsc --noEmit\`
- Commit changes as you need, with a verbose comment including the name of the test you are working. And then push them at the end.

You're doing a great job, keep at it, plan this out and ultrathink carefully step by and work your way through this methodically. Be your best self!
