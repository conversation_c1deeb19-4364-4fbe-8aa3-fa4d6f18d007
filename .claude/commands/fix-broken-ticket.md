Ticket EKO-$ARGUMENTS

Please can you continue to work on the ticket EKO-$ARGUMENTS in Linear. Ignore any comments that say it's complete it
isn't.

Find the open PR associated with EKO-$ARGUMENTS read it but remember the problem IS NOT SOLVED.

Next please move the ticket into 'In Progress'.

Reminders:

- Use tailwind plugins and tailwind not custom CSS, check what plugins exist already before adding new.

Please use the following workflow:

Check Linear for the latest details on the issue, including any attachments

- Plan the work before you start in detail with small incremental steps, thinking through step by step.
- Search the web for any details you need on libraries, especially ones that change frequently, or use your Context7
  MCP.
- Update the ticket with a comment containing your revised plan of action.
- Do the actual work, making use of the tools you have, especially Supabase CMS, Supabase Customer AND Linear.
- For web frequently use `tsc --noEmit`, Then run playright tests `npx playwright test --reporter=line` at the end.
- For python run the pytests in tests.on_commit and run `uvx ty check` on the changed files.
- For the customer app please add a playwright test if appropriate to reproduce the issue, this test should be in a new
  file called `apps/customer/tests/issues/issue-eko-ARGUMENTS.spec.ts`
- Commit changes as you need, with a verbose comment including the Linear issue ID
- Update Linear with a report on what you have done so far.

After you've finished please create a PR if there isn't already created. To merge feature/eko-$ARGUMENTS and push all
changes. Please then move the ticket into 'In Review' NEVER 'Done'.

You're doing a great job, keep at it, plan this out and ultrathink carefully step by and work your way through this
methodically.

Don't forget you are being asked to look at it because the work failed testing or was interrupted, it is not working no
matter what the PR and Linear ticket say.
