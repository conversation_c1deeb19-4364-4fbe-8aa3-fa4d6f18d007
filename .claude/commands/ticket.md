Ticket EKO-$ARGUMENT

Please can you work on the ticket $ARGUMENT in Linear.

Firstly please move the ticket into 'In Progress'.

Secondly create a new branch called feature/eko-$ARGUMENT and use that for the development.

Reminders:

- Use tailwind plugins and tailwind not custom CSS, check what plugins exist already before adding new.

---

Please use the following workflow:

- Check Linear for details on the issue, including any attachments
- Plan the work before you start in detail with small incremental steps,  thinking through step by step.
- Search the web for any details you need on libraries, esepcially ones that change frequently, or use your Context7 MCP.
- Update the ticket with a comment containing your plan of action.
- Do the actual work, making use of the tools you have, especially Supabase AND Linear.
- For web frequently use `tsc --noEmit`
  Then run playright tests `npx playwright test --reporter=line` at the end.

- For python run the pytests in tests.on_commit and run `uvx ty check` on the changed files.

- For the customer app please add a playwright test if appropriate to reproduce the issue, this test should be in a new file called `apps/customer/tests/issues/issue-eko-$ARGUMENT.spec.ts`
- Commit changes as you need, with a verbose comment including the Linear issue ID
  -Update Linear with a report on what you have done so far.
- If resolving this issue breaks an existing test because the functionality or design was altered then please fix all related tests to the issue.

---

After you've finished please create a PR to merge feature/eko-$ARGUMENT and push all changes. Please then move the ticket into 'In Review' not 'Done'.

---

Then please update any README.md files (these are technical documentation for humans and you should write as a technical document writer) to make sure they are inline with your changes.

If we are on a feature branch look at ALL changes made on the branch since it started.

If we are on `main` then look at recent commits that relate to this conversation.

DO NOT list changes INSTEAD make sure the document reflects the true system design and state based on the changes made.  

---

Now please create a task/todo list of these tasks.
