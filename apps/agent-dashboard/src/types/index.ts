export interface AgentSession {
  session_id: string;
  company_name: string;
  created_at: string;
  updated_at: string;
  memory_data: any;
  status?: string;
}

export interface AgentExecutionEvent {
  id: number;
  session_id: string;
  agent_name: string;
  event_type: 'task_start' | 'task_complete' | 'tool_call' | 'llm_call' | 'decision' | 'error';
  event_timestamp: string;
  event_data: any;
  task_name?: string;
  tool_name?: string;
  run_id?: number;
  created_at: string;
}

export interface AgentLLMCall {
  id: number;
  session_id: string;
  agent_name: string;
  model_name?: string;
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  cost_usd?: number;
  call_timestamp: string;
  request_data: any;
  response_data: any;
  duration_ms?: number;
  run_id?: number;
  created_at: string;
}

export interface AgentToolUsage {
  id: number;
  session_id: string;
  agent_name: string;
  tool_name: string;
  tool_input: any;
  tool_output: any;
  success: boolean;
  error_message?: string;
  call_timestamp: string;
  duration_ms?: number;
  run_id?: number;
  created_at: string;
}

export interface SessionSummary {
  session_id: string;
  company_name: string;
  status: string;
  started_at: string;
  last_activity: string;
  total_events: number;
  total_llm_calls: number;
  total_cost: number;
  agent_names: string[];
  is_active: boolean;
}