import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { SessionSummary } from '../types';

interface SessionCardProps {
  session: SessionSummary;
}

export default function SessionCard({ session }: SessionCardProps) {
  const getStatusColor = (status: string, isActive: boolean) => {
    if (isActive) return 'bg-green-100 text-green-800';
    switch (status) {
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'running': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Link href={`/session/${session.session_id}`}>
      <div className="block p-6 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {session.company_name}
            </h3>
            <p className="text-sm text-gray-500 font-mono">
              {session.session_id.slice(0, 8)}...
            </p>
          </div>
          <div className="flex items-center gap-2">
            {session.is_active && (
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
            )}
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(session.status, session.is_active)}`}>
              {session.is_active ? 'ACTIVE' : session.status.toUpperCase()}
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wide">Events</p>
            <p className="text-lg font-semibold text-gray-900">{session.total_events}</p>
          </div>
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wide">LLM Calls</p>
            <p className="text-lg font-semibold text-gray-900">{session.total_llm_calls}</p>
          </div>
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wide">Cost</p>
            <p className="text-lg font-semibold text-gray-900">
              ${Number(session.total_cost).toFixed(4)}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wide">Last Activity</p>
            <p className="text-sm text-gray-700">
              {formatDistanceToNow(new Date(session.last_activity), { addSuffix: true })}
            </p>
          </div>
        </div>

        {session.agent_names && session.agent_names.length > 0 && (
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wide mb-2">Active Agents</p>
            <div className="flex flex-wrap gap-1">
              {session.agent_names.map((agent, index) => (
                <span
                  key={index}
                  className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                >
                  {agent}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </Link>
  );
}