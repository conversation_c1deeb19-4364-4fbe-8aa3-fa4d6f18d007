'use client';

import { formatDistanceToNow, format } from 'date-fns';
import { AgentExecutionEvent, AgentLLMCall, AgentToolUsage } from '../types';

type TimelineEvent = {
  id: string;
  timestamp: string;
  type: 'event' | 'llm_call' | 'tool_usage';
  agent_name: string;
  title: string;
  description: string;
  data: any;
  success?: boolean;
  cost?: number;
};

interface EventTimelineProps {
  events: AgentExecutionEvent[];
  llmCalls: AgentLLMCall[];
  toolUsage: AgentToolUsage[];
}

export default function EventTimeline({ events, llmCalls, toolUsage }: EventTimelineProps) {
  const timelineEvents: TimelineEvent[] = [
    ...events.map(e => ({
      id: `event-${e.id}`,
      timestamp: e.event_timestamp,
      type: 'event' as const,
      agent_name: e.agent_name,
      title: `${e.event_type.replace('_', ' ').toUpperCase()}`,
      description: e.task_name || e.tool_name || 'Agent activity',
      data: e.event_data,
      success: e.event_type !== 'error'
    })),
    ...llmCalls.map(l => ({
      id: `llm-${l.id}`,
      timestamp: l.call_timestamp,
      type: 'llm_call' as const,
      agent_name: l.agent_name,
      title: `LLM Call - ${l.model_name}`,
      description: `${l.total_tokens} tokens`,
      data: { request: l.request_data, response: l.response_data },
      success: true,
      cost: l.cost_usd || 0
    })),
    ...toolUsage.map(t => ({
      id: `tool-${t.id}`,
      timestamp: t.call_timestamp,
      type: 'tool_usage' as const,
      agent_name: t.agent_name,
      title: `Tool: ${t.tool_name}`,
      description: t.success ? 'Success' : t.error_message || 'Failed',
      data: { input: t.tool_input, output: t.tool_output },
      success: t.success
    }))
  ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  const getEventIcon = (event: TimelineEvent) => {
    if (event.type === 'llm_call') return '🤖';
    if (event.type === 'tool_usage') return '🔧';
    if (event.title.includes('ERROR')) return '❌';
    if (event.title.includes('COMPLETE')) return '✅';
    if (event.title.includes('START')) return '🚀';
    return '📝';
  };

  const getEventColor = (event: TimelineEvent) => {
    if (!event.success) return 'border-red-200 bg-red-50';
    if (event.type === 'llm_call') return 'border-blue-200 bg-blue-50';
    if (event.type === 'tool_usage') return 'border-green-200 bg-green-50';
    return 'border-gray-200 bg-gray-50';
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">Event Timeline</h3>
      
      {timelineEvents.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No events recorded yet
        </div>
      ) : (
        <div className="space-y-3">
          {timelineEvents.map((event) => (
            <details key={event.id} className={`border rounded-lg p-4 ${getEventColor(event)}`}>
              <summary className="cursor-pointer flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-lg">{getEventIcon(event)}</span>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900">{event.title}</span>
                      <span className="text-xs px-2 py-1 bg-gray-200 text-gray-700 rounded">
                        {event.agent_name}
                      </span>
                      {event.cost && event.cost > 0 && (
                        <span className="text-xs px-2 py-1 bg-yellow-200 text-yellow-800 rounded">
                          ${event.cost.toFixed(4)}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">{event.description}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(event.timestamp), { addSuffix: true })}
                  </div>
                  <div className="text-xs text-gray-400">
                    {format(new Date(event.timestamp), 'HH:mm:ss')}
                  </div>
                </div>
              </summary>
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                <pre className="text-xs bg-white p-3 rounded border overflow-auto max-h-64">
                  {JSON.stringify(event.data, null, 2)}
                </pre>
              </div>
            </details>
          ))}
        </div>
      )}
    </div>
  );
}