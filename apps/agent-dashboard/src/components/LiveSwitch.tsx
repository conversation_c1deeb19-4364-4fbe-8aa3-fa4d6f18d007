'use client';

import { useState, useEffect } from 'react';

interface LiveSwitchProps {
  onToggle: (isLive: boolean) => void;
  defaultLive?: boolean;
}

export default function LiveSwitch({ onToggle, defaultLive = false }: LiveSwitchProps) {
  const [isLive, setIsLive] = useState(defaultLive);

  const handleToggle = () => {
    const newState = !isLive;
    setIsLive(newState);
    onToggle(newState);
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium">Live Updates</span>
      <button
        onClick={handleToggle}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
          isLive ? 'bg-green-600' : 'bg-gray-300'
        }`}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
            isLive ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
      {isLive && (
        <div className="flex items-center gap-1">
          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-xs text-green-600">LIVE</span>
        </div>
      )}
    </div>
  );
}