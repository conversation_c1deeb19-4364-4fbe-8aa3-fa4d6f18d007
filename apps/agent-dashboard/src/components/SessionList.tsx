'use client';

import { useState, useEffect } from 'react';
import { SessionSummary } from '../types';
import SessionCard from './SessionCard';
import LiveSwitch from './LiveSwitch';

interface SessionListProps {
  initialSessions: SessionSummary[];
}

export default function SessionList({ initialSessions }: SessionListProps) {
  const [sessions, setSessions] = useState<SessionSummary[]>(initialSessions);
  const [isLive, setIsLive] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const refreshSessions = async () => {
    try {
      const response = await fetch('/api/sessions');
      if (response.ok) {
        const data = await response.json();
        setSessions(data.sessions);
        setLastRefresh(new Date());
      }
    } catch (error) {
      console.error('Failed to refresh sessions:', error);
    }
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isLive) {
      interval = setInterval(refreshSessions, 3000); // Refresh every 3 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isLive]);

  const activeSessions = sessions.filter(s => s.is_active);
  const recentSessions = sessions.filter(s => !s.is_active);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">CrewAI Agent Dashboard</h1>
          <p className="text-sm text-gray-500">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        <LiveSwitch onToggle={setIsLive} defaultLive={false} />
      </div>

      {activeSessions.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
            Active Sessions ({activeSessions.length})
          </h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {activeSessions.map((session) => (
              <SessionCard key={session.session_id} session={session} />
            ))}
          </div>
        </div>
      )}

      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Recent Sessions ({recentSessions.length})
        </h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {recentSessions.map((session) => (
            <SessionCard key={session.session_id} session={session} />
          ))}
        </div>
      </div>

      {sessions.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No agent sessions found.</p>
          <p className="text-sm text-gray-400 mt-2">
            Sessions will appear here when agents start running.
          </p>
        </div>
      )}
    </div>
  );
}