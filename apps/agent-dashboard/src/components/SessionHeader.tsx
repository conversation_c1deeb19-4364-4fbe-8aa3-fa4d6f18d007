import { formatDistanceToNow } from 'date-fns';

interface SessionHeaderProps {
  session: any;
}

export default function SessionHeader({ session }: SessionHeaderProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'running': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {session.company_name}
          </h1>
          <p className="text-sm text-gray-500 font-mono mb-1">
            Session ID: {session.session_id}
          </p>
          <p className="text-sm text-gray-500">
            Started {formatDistanceToNow(new Date(session.created_at), { addSuffix: true })}
          </p>
        </div>
        <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(session.memory_data?.status || 'unknown')}`}>
          {(session.memory_data?.status || 'unknown').toUpperCase()}
        </span>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{session.total_events || 0}</div>
          <div className="text-sm text-gray-500">Total Events</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{session.total_llm_calls || 0}</div>
          <div className="text-sm text-gray-500">LLM Calls</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{session.total_tool_calls || 0}</div>
          <div className="text-sm text-gray-500">Tool Calls</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">
            ${Number(session.total_cost || 0).toFixed(4)}
          </div>
          <div className="text-sm text-gray-500">Total Cost</div>
        </div>
      </div>
    </div>
  );
}