'use client';

import { useState, useEffect } from 'react';
import { RotateCcw, Pause, Play } from 'lucide-react';

export default function LiveToggle() {
  const [isLive, setIsLive] = useState(true);
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // Trigger refresh
          window.location.reload();
          return 5;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isLive]);

  const handleToggleLive = () => {
    setIsLive(!isLive);
    if (!isLive) {
      setCountdown(5);
    }
  };

  const handleManualRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="flex items-center space-x-4">
      <button
        onClick={handleManualRefresh}
        className="flex items-center space-x-2 px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors"
      >
        <RotateCcw className="h-4 w-4" />
        <span>Refresh</span>
      </button>
      
      <div className="flex items-center space-x-3">
        <button
          onClick={handleToggleLive}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
            isLive 
              ? 'bg-green-600 hover:bg-green-700 text-white' 
              : 'bg-slate-700 hover:bg-slate-600 text-white'
          }`}
        >
          {isLive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          <span>{isLive ? 'Live' : 'Paused'}</span>
        </button>
        
        {isLive && (
          <div className="flex items-center space-x-2 text-slate-300">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm">Refresh in {countdown}s</span>
          </div>
        )}
      </div>
    </div>
  );
}