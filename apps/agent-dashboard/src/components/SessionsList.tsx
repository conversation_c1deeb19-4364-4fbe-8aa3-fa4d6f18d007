import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { Clock, Play, CheckCircle, AlertCircle, ExternalLink, Bot } from 'lucide-react';
import DatabaseConnection from '@/lib/database';
import { SessionSummary } from '@/types';

export default async function SessionsList() {
  const sessions = await getSessionSummaries();

  if (!sessions.length) {
    return (
      <div className="text-center py-12">
        <Bot className="h-16 w-16 text-slate-600 mx-auto mb-4" />
        <p className="text-slate-400 text-lg">No agent sessions found</p>
        <p className="text-slate-500 text-sm mt-2">Sessions will appear here when agents start running</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {sessions.map((session) => (
        <SessionCard key={session.session_id} session={session} />
      ))}
    </div>
  );
}

function SessionCard({ session }: { session: SessionSummary }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Play className="h-4 w-4 text-green-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-slate-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'completed':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'error':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };

  return (
    <Link href={`/session/${session.session_id}`}>
      <div className="bg-slate-700/50 backdrop-blur-sm rounded-xl border border-slate-600 p-6 hover:bg-slate-700/70 transition-all cursor-pointer group">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-white group-hover:text-blue-300 transition-colors">
                {session.company_name}
              </h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(session.status)}`}>
                {session.status}
              </span>
              {session.is_active && (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-400">Active</span>
                </div>
              )}
            </div>
            
            <p className="text-slate-400 text-sm mb-4">
              Session ID: <span className="font-mono">{session.session_id}</span>
            </p>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-white">{session.total_events}</p>
                <p className="text-xs text-slate-400">Events</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-white">{session.total_llm_calls}</p>
                <p className="text-xs text-slate-400">LLM Calls</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-white">${session.total_cost.toFixed(2)}</p>
                <p className="text-xs text-slate-400">Cost</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-white">{session.agent_names.length}</p>
                <p className="text-xs text-slate-400">Agents</p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">
                  Started {formatDistanceToNow(new Date(session.started_at), { addSuffix: true })}
                </p>
                <p className="text-sm text-slate-400">
                  Last activity {formatDistanceToNow(new Date(session.last_activity), { addSuffix: true })}
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                {session.agent_names.slice(0, 3).map((agent, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-slate-600 text-slate-300 rounded text-xs"
                  >
                    {agent}
                  </span>
                ))}
                {session.agent_names.length > 3 && (
                  <span className="px-2 py-1 bg-slate-600 text-slate-300 rounded text-xs">
                    +{session.agent_names.length - 3}
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="ml-4 flex items-center">
            {getStatusIcon(session.status)}
            <ExternalLink className="h-4 w-4 text-slate-400 ml-2 group-hover:text-blue-300 transition-colors" />
          </div>
        </div>
      </div>
    </Link>
  );
}

async function getSessionSummaries(): Promise<SessionSummary[]> {
  try {
    const result = await DatabaseConnection.query(`
      WITH session_stats AS (
        SELECT 
          s.session_id,
          s.company_name,
          s.created_at as started_at,
          s.updated_at as last_activity,
          COALESCE(e.event_count, 0) as total_events,
          COALESCE(l.llm_call_count, 0) as total_llm_calls,
          COALESCE(l.total_cost, 0) as total_cost,
          COALESCE(e.agent_names, ARRAY[]::text[]) as agent_names,
          CASE 
            WHEN s.updated_at > NOW() - INTERVAL '10 minutes' THEN true
            ELSE false
          END as is_active,
          COALESCE(s.memory_data->>'status', 'unknown') as status
        FROM agent_sessions s
        LEFT JOIN (
          SELECT 
            session_id,
            COUNT(*) as event_count,
            ARRAY_AGG(DISTINCT agent_name) as agent_names
          FROM agent_execution_events 
          GROUP BY session_id
        ) e ON s.session_id = e.session_id
        LEFT JOIN (
          SELECT 
            session_id,
            COUNT(*) as llm_call_count,
            COALESCE(SUM(cost_usd), 0) as total_cost
          FROM agent_llm_calls 
          GROUP BY session_id
        ) l ON s.session_id = l.session_id
        ORDER BY s.updated_at DESC
        LIMIT 20
      )
      SELECT * FROM session_stats
    `);

    return result.rows.map(row => ({
      session_id: row.session_id,
      company_name: row.company_name,
      status: row.status,
      started_at: row.started_at,
      last_activity: row.last_activity,
      total_events: parseInt(row.total_events),
      total_llm_calls: parseInt(row.total_llm_calls),
      total_cost: parseFloat(row.total_cost),
      agent_names: row.agent_names || [],
      is_active: row.is_active
    }));
  } catch (error) {
    console.error('Error fetching session summaries:', error);
    return [];
  }
}