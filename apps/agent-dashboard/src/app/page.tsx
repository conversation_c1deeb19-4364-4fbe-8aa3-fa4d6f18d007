import { Suspense } from 'react';
import Link from 'next/link';
import SessionsList from '@/components/SessionsList';
import LiveToggle from '@/components/LiveToggle';
import { Activity, Bot, DollarSign, Users, BarChart3, TrendingUp } from 'lucide-react';
import DatabaseConnection from '@/lib/database';

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">CrewAI Dashboard</h1>
            <p className="text-slate-300">Monitor agent activities, LLM calls, and performance in real-time</p>
          </div>
          <LiveToggle />
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Suspense fallback={<div className="animate-pulse bg-slate-800 h-32 rounded-xl"></div>}>
            <StatsCard
              title="Active Sessions"
              icon={<Activity className="h-6 w-6" />}
              color="bg-green-500"
            />
          </Suspense>
          <Suspense fallback={<div className="animate-pulse bg-slate-800 h-32 rounded-xl"></div>}>
            <StatsCard
              title="Total Agents"
              icon={<Bot className="h-6 w-6" />}
              color="bg-blue-500"
            />
          </Suspense>
          <Suspense fallback={<div className="animate-pulse bg-slate-800 h-32 rounded-xl"></div>}>
            <StatsCard
              title="LLM Calls Today"
              icon={<Users className="h-6 w-6" />}
              color="bg-purple-500"
            />
          </Suspense>
          <Suspense fallback={<div className="animate-pulse bg-slate-800 h-32 rounded-xl"></div>}>
            <StatsCard
              title="Cost Today"
              icon={<DollarSign className="h-6 w-6" />}
              color="bg-yellow-500"
            />
          </Suspense>
        </div>

        {/* Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Link href="/agents" className="group">
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6 hover:bg-slate-700/50 transition-all">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-blue-500/20 rounded-lg group-hover:bg-blue-500/30 transition-colors">
                  <BarChart3 className="h-6 w-6 text-blue-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white group-hover:text-blue-300 transition-colors">
                    Agent Performance
                  </h3>
                  <p className="text-slate-400 text-sm">View detailed agent metrics and efficiency scores</p>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/costs" className="group">
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6 hover:bg-slate-700/50 transition-all">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-green-500/20 rounded-lg group-hover:bg-green-500/30 transition-colors">
                  <TrendingUp className="h-6 w-6 text-green-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white group-hover:text-green-300 transition-colors">
                    LLM Cost Analysis
                  </h3>
                  <p className="text-slate-400 text-sm">Track costs and usage patterns across models</p>
                </div>
              </div>
            </div>
          </Link>
        </div>

        {/* Sessions List */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl border border-slate-700 p-6">
          <h2 className="text-2xl font-semibold text-white mb-6">Agent Sessions</h2>
          <Suspense fallback={<div className="animate-pulse bg-slate-700 h-64 rounded-xl"></div>}>
            <SessionsList />
          </Suspense>
        </div>
      </div>
    </div>
  );
}

async function StatsCard({ 
  title, 
  icon, 
  color 
}: { 
  title: string; 
  icon: React.ReactNode; 
  color: string; 
}) {
  let value = "0";
  
  try {
    switch (title) {
      case "Active Sessions":
        const activeSessions = await DatabaseConnection.query(`
          SELECT COUNT(*) as count FROM agent_sessions 
          WHERE updated_at > NOW() - INTERVAL '5 minutes'
        `);
        value = activeSessions.rows[0]?.count || "0";
        break;
        
      case "Total Agents":
        const totalAgents = await DatabaseConnection.query(`
          SELECT COUNT(DISTINCT agent_name) as count FROM agent_execution_events
        `);
        value = totalAgents.rows[0]?.count || "0";
        break;
        
      case "LLM Calls Today":
        const llmCalls = await DatabaseConnection.query(`
          SELECT COUNT(*) as count FROM agent_llm_calls 
          WHERE call_timestamp >= CURRENT_DATE
        `);
        value = llmCalls.rows[0]?.count || "0";
        break;
        
      case "Cost Today":
        const costToday = await DatabaseConnection.query(`
          SELECT COALESCE(SUM(cost_usd), 0) as cost FROM agent_llm_calls 
          WHERE call_timestamp >= CURRENT_DATE
        `);
        const cost = parseFloat(costToday.rows[0]?.cost || "0");
        value = `$${cost.toFixed(2)}`;
        break;
    }
  } catch (error) {
    console.error(`Error fetching ${title}:`, error);
    value = "—";
  }

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-slate-400 text-sm font-medium">{title}</p>
          <p className="text-2xl font-bold text-white mt-1">
            {value}
          </p>
        </div>
        <div className={`${color} p-3 rounded-lg text-white`}>
          {icon}
        </div>
      </div>
    </div>
  );
}