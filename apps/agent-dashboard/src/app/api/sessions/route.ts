import { NextResponse } from 'next/server';
import { AgentQueries } from '../../../lib/queries';

export async function GET() {
  try {
    const sessions = await AgentQueries.getActiveSessions();
    
    return NextResponse.json({
      sessions,
      timestamp: new Date().toISOString(),
      count: sessions.length
    });
  } catch (error) {
    console.error('Failed to fetch sessions:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch sessions',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}