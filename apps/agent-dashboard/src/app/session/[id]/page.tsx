import { notFound } from 'next/navigation';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { ArrowLeft, Clock, DollarSign, Activity, Bot, Zap, AlertTriangle } from 'lucide-react';
import DatabaseConnection from '@/lib/database';
import { AgentExecutionEvent, AgentLLMCall, AgentToolUsage } from '@/types';

interface SessionPageProps {
  params: {
    id: string;
  };
}

export default async function SessionPage({ params }: SessionPageProps) {
  const { id } = params;
  
  const [sessionInfo, events, llmCalls, toolUsage] = await Promise.all([
    getSessionInfo(id),
    getSessionEvents(id),
    getSessionLLMCalls(id),
    getSessionToolUsage(id)
  ]);

  if (!sessionInfo) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href="/" className="text-slate-400 hover:text-white transition-colors">
            <ArrowLeft className="h-6 w-6" />
          </Link>
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">{sessionInfo.company_name}</h1>
            <p className="text-slate-300">Session: <span className="font-mono">{id}</span></p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total Events"
            value={events.length.toString()}
            icon={<Activity className="h-6 w-6" />}
            color="bg-blue-500"
          />
          <StatsCard
            title="LLM Calls"
            value={llmCalls.length.toString()}
            icon={<Zap className="h-6 w-6" />}
            color="bg-purple-500"
          />
          <StatsCard
            title="Tool Usage"
            value={toolUsage.length.toString()}
            icon={<Bot className="h-6 w-6" />}
            color="bg-green-500"
          />
          <StatsCard
            title="Total Cost"
            value={`$${llmCalls.reduce((sum, call) => sum + (call.cost_usd || 0), 0).toFixed(2)}`}
            icon={<DollarSign className="h-6 w-6" />}
            color="bg-yellow-500"
          />
        </div>

        {/* Timeline */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl border border-slate-700 p-6">
          <h2 className="text-2xl font-semibold text-white mb-6">Event Timeline</h2>
          
          {events.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="h-16 w-16 text-slate-600 mx-auto mb-4" />
              <p className="text-slate-400 text-lg">No events recorded</p>
            </div>
          ) : (
            <EventTimeline events={events} llmCalls={llmCalls} toolUsage={toolUsage} />
          )}
        </div>
      </div>
    </div>
  );
}

function StatsCard({ 
  title, 
  value, 
  icon, 
  color 
}: { 
  title: string; 
  value: string; 
  icon: React.ReactNode; 
  color: string; 
}) {
  return (
    <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-slate-400 text-sm font-medium">{title}</p>
          <p className="text-2xl font-bold text-white mt-1">{value}</p>
        </div>
        <div className={`${color} p-3 rounded-lg text-white`}>
          {icon}
        </div>
      </div>
    </div>
  );
}

function EventTimeline({ 
  events, 
  llmCalls, 
  toolUsage 
}: { 
  events: AgentExecutionEvent[];
  llmCalls: AgentLLMCall[];
  toolUsage: AgentToolUsage[];
}) {
  // Combine all events and sort by timestamp
  const allEvents = [
    ...events.map(e => ({ ...e, type: 'event' as const })),
    ...llmCalls.map(l => ({ ...l, type: 'llm' as const, event_timestamp: l.call_timestamp })),
    ...toolUsage.map(t => ({ ...t, type: 'tool' as const, event_timestamp: t.call_timestamp }))
  ].sort((a, b) => new Date(b.event_timestamp).getTime() - new Date(a.event_timestamp).getTime());

  return (
    <div className="space-y-4 max-h-96 overflow-y-auto">
      {allEvents.map((event, index) => (
        <TimelineEvent key={`${event.type}-${event.id}-${index}`} event={event} />
      ))}
    </div>
  );
}

function TimelineEvent({ event }: { event: any }) {
  const getEventIcon = (type: string, eventType?: string) => {
    if (type === 'llm') return <Zap className="h-4 w-4 text-purple-400" />;
    if (type === 'tool') return <Bot className="h-4 w-4 text-green-400" />;
    if (eventType === 'error') return <AlertTriangle className="h-4 w-4 text-red-400" />;
    return <Activity className="h-4 w-4 text-blue-400" />;
  };

  const getEventTitle = (event: any) => {
    if (event.type === 'llm') {
      return `LLM Call - ${event.model_name || 'Unknown Model'}`;
    }
    if (event.type === 'tool') {
      return `Tool Usage - ${event.tool_name}`;
    }
    return `Agent Event - ${event.event_type}`;
  };

  const getEventDescription = (event: any) => {
    if (event.type === 'llm') {
      return `${event.total_tokens || 0} tokens, $${(event.cost_usd || 0).toFixed(4)} cost`;
    }
    if (event.type === 'tool') {
      return `${event.success ? 'Success' : 'Failed'}${event.duration_ms ? ` (${event.duration_ms}ms)` : ''}`;
    }
    return event.task_name || 'No description';
  };

  return (
    <div className="flex items-start space-x-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600">
      <div className="flex-shrink-0 mt-1">
        {getEventIcon(event.type, event.event_type)}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-white font-medium">{getEventTitle(event)}</h4>
          <span className="text-xs text-slate-400">
            {formatDistanceToNow(new Date(event.event_timestamp), { addSuffix: true })}
          </span>
        </div>
        
        <p className="text-slate-300 text-sm mb-2">{getEventDescription(event)}</p>
        
        <div className="flex items-center space-x-4 text-xs text-slate-400">
          <span>Agent: {event.agent_name}</span>
          {event.duration_ms && (
            <span>Duration: {event.duration_ms}ms</span>
          )}
        </div>
        
        {/* Expandable details */}
        <details className="mt-2">
          <summary className="text-xs text-slate-400 cursor-pointer hover:text-slate-300">
            View Details
          </summary>
          <pre className="mt-2 p-3 bg-slate-800 rounded text-xs text-slate-300 overflow-auto max-h-40">
            {JSON.stringify(event.type === 'event' ? event.event_data : 
                           event.type === 'llm' ? { request: event.request_data, response: event.response_data } :
                           { input: event.tool_input, output: event.tool_output }, null, 2)}
          </pre>
        </details>
      </div>
    </div>
  );
}

async function getSessionInfo(sessionId: string) {
  try {
    const result = await DatabaseConnection.query(
      'SELECT * FROM agent_sessions WHERE session_id = $1',
      [sessionId]
    );
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error fetching session info:', error);
    return null;
  }
}

async function getSessionEvents(sessionId: string): Promise<AgentExecutionEvent[]> {
  try {
    const result = await DatabaseConnection.query(
      'SELECT * FROM agent_execution_events WHERE session_id = $1 ORDER BY event_timestamp DESC LIMIT 100',
      [sessionId]
    );
    return result.rows;
  } catch (error) {
    console.error('Error fetching session events:', error);
    return [];
  }
}

async function getSessionLLMCalls(sessionId: string): Promise<AgentLLMCall[]> {
  try {
    const result = await DatabaseConnection.query(
      'SELECT * FROM agent_llm_calls WHERE session_id = $1 ORDER BY call_timestamp DESC LIMIT 50',
      [sessionId]
    );
    return result.rows;
  } catch (error) {
    console.error('Error fetching LLM calls:', error);
    return [];
  }
}

async function getSessionToolUsage(sessionId: string): Promise<AgentToolUsage[]> {
  try {
    const result = await DatabaseConnection.query(
      'SELECT * FROM agent_tool_usage WHERE session_id = $1 ORDER BY call_timestamp DESC LIMIT 50',
      [sessionId]
    );
    return result.rows;
  } catch (error) {
    console.error('Error fetching tool usage:', error);
    return [];
  }
}