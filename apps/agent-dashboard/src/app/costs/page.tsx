import Link from 'next/link';
import { <PERSON><PERSON><PERSON>t, DollarSign, TrendingUp, <PERSON><PERSON>hart3, Clock, Zap } from 'lucide-react';
import DatabaseConnection from '@/lib/database';

export default async function CostsPage() {
  const [costAnalysis, dailyCosts, modelBreakdown] = await Promise.all([
    getCostAnalysis(),
    getDailyCosts(),
    getModelBreakdown()
  ]);

  const totalCostToday = dailyCosts.find(d => isToday(new Date(d.date)))?.total_cost || 0;
  const totalCostYesterday = dailyCosts.find(d => isYesterday(new Date(d.date)))?.total_cost || 0;
  const costTrend = totalCostYesterday > 0 ? ((totalCostToday - totalCostYesterday) / totalCostYesterday) * 100 : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <Link href="/" className="text-slate-400 hover:text-white transition-colors">
            <ArrowLeft className="h-6 w-6" />
          </Link>
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">LLM Cost Analysis</h1>
            <p className="text-slate-300">Track and analyze LLM usage costs across all agents</p>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <SummaryCard
            title="Today's Cost"
            value={`$${totalCostToday.toFixed(2)}`}
            trend={costTrend}
            icon={<DollarSign className="h-6 w-6" />}
            color="bg-green-500"
          />
          <SummaryCard
            title="Total Calls"
            value={costAnalysis.reduce((sum, model) => sum + model.call_count, 0).toString()}
            icon={<Zap className="h-6 w-6" />}
            color="bg-purple-500"
          />
          <SummaryCard
            title="Total Tokens"
            value={formatNumber(costAnalysis.reduce((sum, model) => sum + model.total_tokens, 0))}
            icon={<BarChart3 className="h-6 w-6" />}
            color="bg-blue-500"
          />
          <SummaryCard
            title="Avg Call Cost"
            value={`$${(costAnalysis.reduce((sum, model) => sum + model.total_cost, 0) / 
              Math.max(costAnalysis.reduce((sum, model) => sum + model.call_count, 0), 1)).toFixed(4)}`}
            icon={<TrendingUp className="h-6 w-6" />}
            color="bg-yellow-500"
          />
        </div>

        {/* Model Breakdown */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Cost by Model */}
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl border border-slate-700 p-6">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center space-x-2">
              <DollarSign className="h-6 w-6" />
              <span>Cost by Model</span>
            </h2>
            
            {modelBreakdown.length === 0 ? (
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400">No cost data available</p>
              </div>
            ) : (
              <div className="space-y-4">
                {modelBreakdown.map((model) => (
                  <ModelCostCard key={model.model_name} model={model} />
                ))}
              </div>
            )}
          </div>

          {/* Daily Cost Trend */}
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl border border-slate-700 p-6">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center space-x-2">
              <TrendingUp className="h-6 w-6" />
              <span>Daily Cost Trend</span>
            </h2>
            
            {dailyCosts.length === 0 ? (
              <div className="text-center py-12">
                <Clock className="h-16 w-16 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400">No daily cost data available</p>
              </div>
            ) : (
              <div className="space-y-3">
                {dailyCosts.slice(0, 7).map((day) => (
                  <DailyCostBar key={day.date} day={day} maxCost={Math.max(...dailyCosts.map(d => d.total_cost))} />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Detailed Cost Analysis */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl border border-slate-700 p-6">
          <h2 className="text-2xl font-semibold text-white mb-6">Detailed Analysis</h2>
          
          {costAnalysis.length === 0 ? (
            <div className="text-center py-12">
              <DollarSign className="h-16 w-16 text-slate-600 mx-auto mb-4" />
              <p className="text-slate-400 text-lg">No LLM cost data available</p>
              <p className="text-slate-500 text-sm mt-2">Data will appear here once agents start making LLM calls</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-left">
                <thead>
                  <tr className="border-b border-slate-600">
                    <th className="pb-3 text-slate-300 font-medium">Model</th>
                    <th className="pb-3 text-slate-300 font-medium text-right">Calls</th>
                    <th className="pb-3 text-slate-300 font-medium text-right">Tokens</th>
                    <th className="pb-3 text-slate-300 font-medium text-right">Total Cost</th>
                    <th className="pb-3 text-slate-300 font-medium text-right">Avg Cost</th>
                    <th className="pb-3 text-slate-300 font-medium text-right">Avg Duration</th>
                  </tr>
                </thead>
                <tbody>
                  {costAnalysis.map((model) => (
                    <tr key={model.model_name} className="border-b border-slate-700">
                      <td className="py-4 text-white font-medium">{model.model_name || 'Unknown'}</td>
                      <td className="py-4 text-slate-300 text-right">{model.call_count.toLocaleString()}</td>
                      <td className="py-4 text-slate-300 text-right">{model.total_tokens.toLocaleString()}</td>
                      <td className="py-4 text-green-400 text-right font-medium">${model.total_cost.toFixed(4)}</td>
                      <td className="py-4 text-slate-300 text-right">${model.avg_cost_per_call.toFixed(4)}</td>
                      <td className="py-4 text-slate-300 text-right">
                        {model.avg_duration_ms ? `${model.avg_duration_ms.toFixed(0)}ms` : '—'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

interface SummaryCardProps {
  title: string;
  value: string;
  trend?: number;
  icon: React.ReactNode;
  color: string;
}

function SummaryCard({ title, value, trend, icon, color }: SummaryCardProps) {
  return (
    <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-slate-400 text-sm font-medium">{title}</p>
          <p className="text-2xl font-bold text-white mt-1">{value}</p>
          {trend !== undefined && (
            <p className={`text-sm mt-1 flex items-center space-x-1 ${
              trend >= 0 ? 'text-green-400' : 'text-red-400'
            }`}>
              <TrendingUp className={`h-3 w-3 ${trend < 0 ? 'rotate-180' : ''}`} />
              <span>{Math.abs(trend).toFixed(1)}% vs yesterday</span>
            </p>
          )}
        </div>
        <div className={`${color} p-3 rounded-lg text-white`}>
          {icon}
        </div>
      </div>
    </div>
  );
}

interface ModelCostCardProps {
  model: {
    model_name: string;
    call_count: number;
    total_cost: number;
    avg_cost_per_call: number;
  };
}

function ModelCostCard({ model }: ModelCostCardProps) {
  return (
    <div className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600">
      <div>
        <h4 className="text-white font-medium">{model.model_name || 'Unknown Model'}</h4>
        <p className="text-slate-400 text-sm">{model.call_count} calls</p>
      </div>
      <div className="text-right">
        <p className="text-green-400 font-bold">${model.total_cost.toFixed(2)}</p>
        <p className="text-slate-400 text-sm">${model.avg_cost_per_call.toFixed(4)} avg</p>
      </div>
    </div>
  );
}

interface DailyCostBarProps {
  day: {
    date: string;
    total_cost: number;
    call_count: number;
  };
  maxCost: number;
}

function DailyCostBar({ day, maxCost }: DailyCostBarProps) {
  const percentage = maxCost > 0 ? (day.total_cost / maxCost) * 100 : 0;
  const date = new Date(day.date);
  const isCurrentDay = isToday(date);

  return (
    <div className="flex items-center space-x-4">
      <div className="w-20 text-sm text-slate-400">
        {date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
        {isCurrentDay && <span className="text-green-400 ml-1">•</span>}
      </div>
      
      <div className="flex-1">
        <div className="w-full bg-slate-700 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all ${
              isCurrentDay ? 'bg-green-500' : 'bg-blue-500'
            }`}
            style={{ width: `${percentage}%` }}
          />
        </div>
      </div>
      
      <div className="w-20 text-right">
        <span className="text-white font-medium">${day.total_cost.toFixed(2)}</span>
      </div>
    </div>
  );
}

// Helper functions
function formatNumber(num: number): string {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toString();
}

function isToday(date: Date): boolean {
  const today = new Date();
  return date.toDateString() === today.toDateString();
}

function isYesterday(date: Date): boolean {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.toDateString() === yesterday.toDateString();
}

// Database query functions
async function getCostAnalysis() {
  try {
    const query = `
      SELECT 
        model_name,
        COUNT(*) as call_count,
        SUM(prompt_tokens) as total_prompt_tokens,
        SUM(completion_tokens) as total_completion_tokens,
        SUM(total_tokens) as total_tokens,
        SUM(cost_usd) as total_cost,
        AVG(cost_usd) as avg_cost_per_call,
        AVG(duration_ms) as avg_duration_ms
      FROM agent_llm_calls 
      WHERE cost_usd IS NOT NULL
      GROUP BY model_name
      ORDER BY total_cost DESC
    `;
    
    const result = await DatabaseConnection.query(query);
    return result.rows;
  } catch (error) {
    console.error('Error fetching cost analysis:', error);
    return [];
  }
}

async function getDailyCosts() {
  try {
    const query = `
      SELECT 
        DATE(call_timestamp) as date,
        COUNT(*) as call_count,
        SUM(cost_usd) as total_cost
      FROM agent_llm_calls 
      WHERE call_timestamp >= CURRENT_DATE - INTERVAL '30 days'
        AND cost_usd IS NOT NULL
      GROUP BY DATE(call_timestamp)
      ORDER BY date DESC
      LIMIT 30
    `;
    
    const result = await DatabaseConnection.query(query);
    return result.rows;
  } catch (error) {
    console.error('Error fetching daily costs:', error);
    return [];
  }
}

async function getModelBreakdown() {
  try {
    const query = `
      SELECT 
        model_name,
        COUNT(*) as call_count,
        SUM(cost_usd) as total_cost,
        AVG(cost_usd) as avg_cost_per_call
      FROM agent_llm_calls 
      WHERE cost_usd IS NOT NULL
      GROUP BY model_name
      ORDER BY total_cost DESC
      LIMIT 10
    `;
    
    const result = await DatabaseConnection.query(query);
    return result.rows;
  } catch (error) {
    console.error('Error fetching model breakdown:', error);
    return [];
  }
}