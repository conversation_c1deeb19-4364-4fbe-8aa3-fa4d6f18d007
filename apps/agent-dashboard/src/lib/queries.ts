import DatabaseConnection from './database';
import { 
  AgentSession, 
  AgentExecutionEvent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Agent<PERSON><PERSON>Usage, 
  SessionSummary 
} from '../types';

export class AgentQueries {
  
  static async getActiveSessions(): Promise<SessionSummary[]> {
    const query = `
      WITH session_stats AS (
        SELECT 
          s.session_id,
          s.company_name,
          s.created_at,
          s.updated_at,
          s.memory_data->>'status' as status,
          COUNT(DISTINCT e.id) as total_events,
          COUNT(DISTINCT l.id) as total_llm_calls,
          COALESCE(SUM(l.cost_usd), 0) as total_cost,
          ARRAY_AGG(DISTINCT e.agent_name) FILTER (WHERE e.agent_name IS NOT NULL) as agent_names,
          MAX(GREATEST(
            COALESCE(e.event_timestamp, s.updated_at),
            COALESCE(l.call_timestamp, s.updated_at),
            COALESCE(t.call_timestamp, s.updated_at)
          )) as last_activity
        FROM agent_sessions s
        LEFT JOIN agent_execution_events e ON s.session_id = e.session_id
        LEFT JOIN agent_llm_calls l ON s.session_id = l.session_id  
        LEFT JOIN agent_tool_usage t ON s.session_id = t.session_id
        GROUP BY s.session_id, s.company_name, s.created_at, s.updated_at, s.memory_data
      )
      SELECT 
        session_id,
        company_name,
        COALESCE(status, 'unknown') as status,
        created_at as started_at,
        last_activity,
        total_events,
        total_llm_calls,
        total_cost,
        agent_names,
        (last_activity > NOW() - INTERVAL '5 minutes') as is_active
      FROM session_stats
      ORDER BY last_activity DESC
      LIMIT 50
    `;
    
    const result = await DatabaseConnection.query(query);
    return result.rows;
  }

  static async getSessionEvents(sessionId: string, limit: number = 100): Promise<AgentExecutionEvent[]> {
    const query = `
      SELECT * FROM agent_execution_events 
      WHERE session_id = $1 
      ORDER BY event_timestamp DESC 
      LIMIT $2
    `;
    
    const result = await DatabaseConnection.query(query, [sessionId, limit]);
    return result.rows;
  }

  static async getSessionLLMCalls(sessionId: string, limit: number = 100): Promise<AgentLLMCall[]> {
    const query = `
      SELECT * FROM agent_llm_calls 
      WHERE session_id = $1 
      ORDER BY call_timestamp DESC 
      LIMIT $2
    `;
    
    const result = await DatabaseConnection.query(query, [sessionId, limit]);
    return result.rows;
  }

  static async getSessionToolUsage(sessionId: string, limit: number = 100): Promise<AgentToolUsage[]> {
    const query = `
      SELECT * FROM agent_tool_usage 
      WHERE session_id = $1 
      ORDER BY call_timestamp DESC 
      LIMIT $2
    `;
    
    const result = await DatabaseConnection.query(query, [sessionId, limit]);
    return result.rows;
  }

  static async getSessionDetails(sessionId: string) {
    const sessionQuery = `
      SELECT s.*, 
             COUNT(DISTINCT e.id) as total_events,
             COUNT(DISTINCT l.id) as total_llm_calls,
             COUNT(DISTINCT t.id) as total_tool_calls,
             COALESCE(SUM(l.cost_usd), 0) as total_cost
      FROM agent_sessions s
      LEFT JOIN agent_execution_events e ON s.session_id = e.session_id
      LEFT JOIN agent_llm_calls l ON s.session_id = l.session_id
      LEFT JOIN agent_tool_usage t ON s.session_id = t.session_id
      WHERE s.session_id = $1
      GROUP BY s.session_id, s.company_name, s.created_at, s.updated_at, s.memory_data
    `;
    
    const result = await DatabaseConnection.query(sessionQuery, [sessionId]);
    return result.rows[0];
  }

  static async getRecentActivity(minutes: number = 30): Promise<AgentExecutionEvent[]> {
    const query = `
      SELECT * FROM agent_execution_events 
      WHERE event_timestamp > NOW() - INTERVAL '${minutes} minutes'
      ORDER BY event_timestamp DESC 
      LIMIT 200
    `;
    
    const result = await DatabaseConnection.query(query);
    return result.rows;
  }

  static async getAgentPerformance(sessionId?: string) {
    const whereClause = sessionId ? 'WHERE session_id = $1' : '';
    const params = sessionId ? [sessionId] : [];
    
    const query = `
      WITH agent_stats AS (
        SELECT 
          agent_name,
          COUNT(DISTINCT session_id) as sessions_count,
          COUNT(*) as total_events,
          COUNT(CASE WHEN event_type = 'error' THEN 1 END) as error_count,
          COUNT(CASE WHEN event_type = 'task_complete' THEN 1 END) as completed_tasks,
          AVG(CASE WHEN event_type = 'task_complete' THEN 
            EXTRACT(EPOCH FROM (event_timestamp - LAG(event_timestamp) OVER (
              PARTITION BY session_id, agent_name ORDER BY event_timestamp
            ))) END) as avg_task_duration
        FROM agent_execution_events 
        ${whereClause}
        GROUP BY agent_name
      )
      SELECT 
        agent_name,
        sessions_count,
        total_events,
        error_count,
        completed_tasks,
        ROUND(avg_task_duration::numeric, 2) as avg_task_duration_seconds,
        CASE WHEN total_events > 0 THEN 
          ROUND((error_count::numeric / total_events) * 100, 2) 
        ELSE 0 END as error_rate_percent
      FROM agent_stats
      ORDER BY total_events DESC
    `;
    
    const result = await DatabaseConnection.query(query, params);
    return result.rows;
  }

  static async getCostAnalysis(sessionId?: string) {
    const whereClause = sessionId ? 'WHERE session_id = $1' : '';
    const params = sessionId ? [sessionId] : [];
    
    const query = `
      SELECT 
        model_name,
        COUNT(*) as call_count,
        SUM(prompt_tokens) as total_prompt_tokens,
        SUM(completion_tokens) as total_completion_tokens,
        SUM(total_tokens) as total_tokens,
        SUM(cost_usd) as total_cost,
        AVG(cost_usd) as avg_cost_per_call,
        AVG(duration_ms) as avg_duration_ms
      FROM agent_llm_calls 
      ${whereClause}
      GROUP BY model_name
      ORDER BY total_cost DESC
    `;
    
    const result = await DatabaseConnection.query(query, params);
    return result.rows;
  }
}