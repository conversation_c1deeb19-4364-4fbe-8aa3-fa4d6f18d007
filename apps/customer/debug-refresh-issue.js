const { chromium } = require('@playwright/test');

async function debugRefreshIssue() {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({ 
    baseURL: 'http://localhost:3000'
  });
  const page = await context.newPage();

  try {
    console.log('Reproducing the exact test flow that\'s failing...');
    
    // Login and setup (same as test)
    await page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', 'test1_pass');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/customer/, { timeout: 45000 });
    
    await page.goto('/customer/documents', { timeout: 60000 });
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    await page.click('[data-testid="new-document-button"]');
    await page.waitForSelector('[role="dialog"]', { timeout: 20000 });
    
    const templateButton = page.locator('[data-testid^="template-"]').filter({ hasText: 'EKO Report' });
    await templateButton.first().click();
    
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 });
    await page.waitForSelector('.ProseMirror', { timeout: 30000 });
    
    // Wait for components
    await page.waitForSelector('.report-section, .report-group, .report-summary', { timeout: 30000 });
    await page.waitForTimeout(3000);
    
    console.log('1. Creating custom component...');
    
    // Create component (same as test)
    await page.click('button[title*="Insert Report Section"]');
    await page.waitForSelector('[role="dialog"]', { timeout: 10000 });
    await page.fill('input[placeholder="component-id"]', 'custom-section');
    await page.fill('input[placeholder="Component Title"]', 'Custom Analysis');
    await page.fill('textarea[placeholder*="Additional instructions"]', 'Provide analysis on custom metrics');
    await page.click('[data-testid="create-component-button"]');
    await page.waitForSelector('[role="dialog"]', { state: 'hidden', timeout: 10000 });
    
    console.log('2. Waiting for component to appear...');
    await page.waitForTimeout(2000);
    const customComponent = page.locator('[data-id="custom-section"]').first();
    await customComponent.waitFor({ timeout: 10000 });
    console.log('✓ Custom component is visible');
    
    console.log('3. Opening configuration...');
    await customComponent.scrollIntoViewIfNeeded();
    
    // Find menu trigger
    let menuTrigger = customComponent.locator('[data-testid="report-section-menu-trigger"]');
    if (await menuTrigger.count() === 0) {
      menuTrigger = customComponent.locator('[data-testid*="menu-trigger"]');
    }
    if (await menuTrigger.count() === 0) {
      menuTrigger = customComponent.locator('button:has(svg)').last();
    }
    
    console.log(`Menu trigger count: ${await menuTrigger.count()}`);
    await menuTrigger.first().click();
    await page.waitForTimeout(1000);
    
    await page.click('[role="menuitem"]:has-text("Configure")');
    await page.waitForSelector('[role="dialog"]', { timeout: 5000 });
    console.log('✓ Configuration dialog opened');
    
    // Fill config (like test)
    await page.fill('textarea[placeholder*="Additional instructions"]', 'Updated prompt for better analysis');
    await page.click('[data-testid="create-component-button"]');
    await page.waitForSelector('[role="dialog"]', { state: 'hidden', timeout: 10000 });
    console.log('✓ Configuration saved');
    
    console.log('4. Performing lock action...');
    
    // Lock the component
    await customComponent.scrollIntoViewIfNeeded();
    
    // Find menu trigger again
    menuTrigger = customComponent.locator('[data-testid="report-section-menu-trigger"]');
    if (await menuTrigger.count() === 0) {
      menuTrigger = customComponent.locator('[data-testid*="menu-trigger"]');
    }
    if (await menuTrigger.count() === 0) {
      menuTrigger = customComponent.locator('button:has(svg)').last();
    }
    
    console.log(`Menu trigger count for lock: ${await menuTrigger.count()}`);
    await menuTrigger.first().click();
    await page.waitForTimeout(1000);
    
    await page.click('[role="menuitem"]:has-text("Lock")');
    await page.waitForTimeout(2000); // Wait for lock to take effect
    console.log('✓ Component locked');
    
    console.log('5. Now attempting refresh (this is where it fails)...');
    
    // Check component state after lock
    await customComponent.scrollIntoViewIfNeeded();
    
    console.log('Component after lock:');
    const afterLockHTML = await customComponent.innerHTML();
    console.log(afterLockHTML.substring(0, 200) + '...');
    
    // Try to find menu trigger after lock
    menuTrigger = customComponent.locator('[data-testid="report-section-menu-trigger"]');
    console.log(`Menu trigger count after lock: ${await menuTrigger.count()}`);
    
    if (await menuTrigger.count() === 0) {
      console.log('Trying alternative selectors...');
      
      const alternatives = [
        '[data-testid*="menu-trigger"]',
        'button[title*="menu"]',
        'button[aria-label*="menu"]',
        'button:has(svg)',
        'button'
      ];
      
      for (const selector of alternatives) {
        const alt = customComponent.locator(selector);
        const count = await alt.count();
        console.log(`  ${selector}: ${count} found`);
        
        if (count > 0) {
          for (let i = 0; i < Math.min(count, 3); i++) {
            const testId = await alt.nth(i).getAttribute('data-testid');
            const title = await alt.nth(i).getAttribute('title');
            const text = await alt.nth(i).textContent();
            console.log(`    Button ${i}: testId="${testId}" title="${title}" text="${text}"`);
          }
        }
      }
    }
    
    if (await menuTrigger.count() === 0) {
      menuTrigger = customComponent.locator('button:has(svg)').last();
    }
    
    console.log(`Final menu trigger count: ${await menuTrigger.count()}`);
    
    if (await menuTrigger.count() > 0) {
      console.log('Attempting to click menu trigger...');
      try {
        await menuTrigger.first().click();
        await page.waitForTimeout(1000);
        
        console.log('Menu opened, looking for Refresh option...');
        const refreshItem = page.locator('[role="menuitem"]:has-text("Refresh")');
        const refreshCount = await refreshItem.count();
        console.log(`Refresh menu item count: ${refreshCount}`);
        
        if (refreshCount > 0) {
          console.log('Clicking Refresh...');
          await refreshItem.click();
          console.log('✓ Refresh clicked successfully!');
        } else {
          console.log('❌ Refresh menu item not found');
          
          const allMenuItems = await page.locator('[role="menuitem"]').all();
          console.log('Available menu items:');
          for (let i = 0; i < allMenuItems.length; i++) {
            const text = await allMenuItems[i].textContent();
            console.log(`  ${i}: "${text}"`);
          }
        }
        
      } catch (error) {
        console.log('❌ Error clicking menu trigger:', error.message);
      }
    } else {
      console.log('❌ No menu trigger found after lock');
    }
    
    // Take screenshot
    await page.screenshot({ path: 'debug-refresh-issue.png', fullPage: true });
    console.log('Screenshot saved');
    
  } catch (error) {
    console.error('Error during debug:', error);
    await page.screenshot({ path: 'debug-refresh-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

debugRefreshIssue();