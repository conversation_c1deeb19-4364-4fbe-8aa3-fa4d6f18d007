const { chromium } = require('@playwright/test');

async function debugComponentCreation() {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({ 
    baseURL: 'http://localhost:3000'
  });
  const page = await context.newPage();

  try {
    console.log('Starting debug of component creation...');
    
    // Login first
    console.log('Logging in...');
    await page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', 'test1_pass');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/customer/, { timeout: 45000 });
    
    console.log('Navigating to documents...');
    await page.goto('/customer/documents', { timeout: 60000 });
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    // Click New Document
    console.log('Clicking New Document...');
    const newDocButton = page.locator('[data-testid="new-document-button"]');
    await newDocButton.waitFor({ timeout: 20000 });
    await newDocButton.click();
    
    // Wait for template dialog and select ESG Report
    console.log('Selecting template...');
    await page.waitForSelector('[role="dialog"]', { timeout: 20000 });
    const esgTemplate = page.locator('text=EKO Report').first();
    await esgTemplate.click();
    
    // Wait for document editor
    console.log('Waiting for document editor...');
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 });
    await page.waitForSelector('.ProseMirror', { timeout: 30000 });
    
    console.log('Document editor loaded. Looking for toolbar buttons...');
    
    // Check what toolbar buttons are available
    const allButtons = await page.locator('button').all();
    console.log('All buttons on page:');
    for (let i = 0; i < Math.min(allButtons.length, 20); i++) {
      const buttonText = await allButtons[i].textContent();
      const buttonTitle = await allButtons[i].getAttribute('title');
      console.log(`  Button ${i}: text="${buttonText}" title="${buttonTitle}"`);
    }
    
    // Look specifically for Insert Report Section button
    const insertSectionButton = page.locator('button[title*="Insert Report Section"]');
    const insertSectionCount = await insertSectionButton.count();
    console.log(`Insert Report Section button count: ${insertSectionCount}`);
    
    if (insertSectionCount > 0) {
      console.log('Found Insert Report Section button, clicking...');
      await insertSectionButton.click();
      
      // Check if dialog opens
      const dialogExists = await page.locator('[role="dialog"]').count();
      console.log(`Dialog count after clicking: ${dialogExists}`);
      
      if (dialogExists > 0) {
        console.log('Dialog opened. Looking for form fields...');
        
        // Check what input fields are available
        const inputs = await page.locator('input').all();
        console.log('Input fields in dialog:');
        for (let i = 0; i < inputs.length; i++) {
          const placeholder = await inputs[i].getAttribute('placeholder');
          const type = await inputs[i].getAttribute('type');
          console.log(`  Input ${i}: placeholder="${placeholder}" type="${type}"`);
        }
        
        // Check for textareas
        const textareas = await page.locator('textarea').all();
        console.log('Textarea fields in dialog:');
        for (let i = 0; i < textareas.length; i++) {
          const placeholder = await textareas[i].getAttribute('placeholder');
          console.log(`  Textarea ${i}: placeholder="${placeholder}"`);
        }
      }
    } else {
      console.log('Insert Report Section button not found');
      
      // Check for similar buttons
      const reportButtons = await page.locator('button[title*="Report"], button[title*="Section"], button[title*="Insert"]').all();
      console.log('Report/Section/Insert buttons found:');
      for (let i = 0; i < reportButtons.length; i++) {
        const title = await reportButtons[i].getAttribute('title');
        const text = await reportButtons[i].textContent();
        console.log(`  Button ${i}: title="${title}" text="${text}"`);
      }
    }
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'debug-component-creation.png', fullPage: true });
    console.log('Screenshot saved as debug-component-creation.png');
    
  } catch (error) {
    console.error('Error during debug:', error);
    await page.screenshot({ path: 'debug-component-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

debugComponentCreation();