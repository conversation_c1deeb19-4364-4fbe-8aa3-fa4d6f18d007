import { test, expect } from '@playwright/test';

test('Simple editor test with console logging', async ({ page }) => {
  // Capture console messages
  const consoleLogs: string[] = [];
  page.on('console', msg => {
    consoleLogs.push(`${msg.type()}: ${msg.text()}`);
    console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
  });

  // Capture page errors
  page.on('pageerror', error => {
    console.log(`Page error: ${error.message}`);
    console.log(`Stack: ${error.stack}`);
  });

  // Login first
  await page.goto('http://localhost:3000/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'test123password');
  await page.click('[data-testid="login-button"]');
  await page.waitForURL(/\/customer/);

  // Go to documents page
  await page.goto('http://localhost:3000/customer/documents');
  await page.waitForLoadState('networkidle');

  // Create a blank document (simpler than template)
  await page.click('text=New Document');
  
  // Wait for the dialog
  await expect(page.locator('[role="dialog"]')).toBeVisible();
  
  // Click on blank document instead of template
  await page.click('text=Blank Document');
  
  // Wait for document creation
  await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 30000 });

  // Wait a bit to see what happens
  await page.waitForTimeout(5000);

  // Take a screenshot
  await page.screenshot({ path: 'simple-editor-test.png' });

  // Check if editor loaded
  const editorError = await page.locator('text=Something went wrong').count();
  const proseMirrorEditor = await page.locator('.ProseMirror').count();

  console.log(`Editor error count: ${editorError}`);
  console.log(`ProseMirror editor count: ${proseMirrorEditor}`);
  console.log(`Console logs count: ${consoleLogs.length}`);

  // Print last few console logs
  console.log('Last 10 console logs:');
  consoleLogs.slice(-10).forEach(log => console.log(log));
});