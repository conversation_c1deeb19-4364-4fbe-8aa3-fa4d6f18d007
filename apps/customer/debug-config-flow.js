const { chromium } = require('@playwright/test');

async function debugConfigFlow() {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({ 
    baseURL: 'http://localhost:3000'
  });
  const page = await context.newPage();

  try {
    console.log('Starting component configuration debug...');
    
    // Login and create document (reusing working flow)
    console.log('Setting up document...');
    await page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', 'test1_pass');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/customer/, { timeout: 45000 });
    
    await page.goto('/customer/documents', { timeout: 60000 });
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    await page.click('[data-testid="new-document-button"]');
    await page.waitForSelector('[role="dialog"]', { timeout: 20000 });
    
    const templateButton = page.locator('[data-testid^="template-"]').filter({ hasText: 'EKO Report' });
    await templateButton.first().click();
    
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 });
    await page.waitForSelector('.ProseMirror', { timeout: 30000 });
    
    // Wait for components and create custom component
    await page.waitForSelector('.report-section, .report-group, .report-summary', { timeout: 30000 });
    await page.waitForTimeout(3000);
    
    console.log('Creating custom component...');
    await page.click('button[title*="Insert Report Section"]');
    await page.waitForSelector('[role="dialog"]', { timeout: 10000 });
    await page.fill('input[placeholder="component-id"]', 'debug-section');
    await page.fill('input[placeholder="Component Title"]', 'Debug Section');
    await page.fill('textarea[placeholder*="Additional instructions"]', 'Debug prompt');
    await page.click('[data-testid="create-component-button"]');
    await page.waitForSelector('[role="dialog"]', { state: 'hidden', timeout: 10000 });
    
    console.log('Component created, now testing configuration flow...');
    await page.waitForTimeout(2000);
    
    // Find the custom component
    const customComponent = page.locator('[data-id="debug-section"]').first();
    const componentExists = await customComponent.count();
    console.log(`Custom component count: ${componentExists}`);
    
    if (componentExists === 0) {
      console.log('ERROR: Custom component not found');
      return;
    }
    
    console.log('Custom component found, looking for menu trigger...');
    
    // Scroll component into view
    await customComponent.scrollIntoViewIfNeeded();
    
    // Look for menu trigger buttons
    const menuTrigger = customComponent.locator('[data-testid="report-section-menu-trigger"]');
    const triggerCount = await menuTrigger.count();
    console.log(`Menu trigger count in custom component: ${triggerCount}`);
    
    if (triggerCount === 0) {
      console.log('Menu trigger not found, checking all buttons in component...');
      const buttons = await customComponent.locator('button').all();
      for (let i = 0; i < buttons.length; i++) {
        const testId = await buttons[i].getAttribute('data-testid');
        const title = await buttons[i].getAttribute('title');
        const text = await buttons[i].textContent();
        console.log(`  Button ${i}: data-testid="${testId}" title="${title}" text="${text}"`);
      }
      
      // Try alternative selectors
      console.log('Trying alternative menu selectors...');
      const altTriggers = [
        '[data-testid*="menu-trigger"]',
        'button[title*="menu"]',
        'button[aria-label*="menu"]',
        'button:has(svg)',
        '[role="button"]:has(svg)'
      ];
      
      for (const selector of altTriggers) {
        const altTrigger = customComponent.locator(selector);
        const altCount = await altTrigger.count();
        console.log(`  Alternative selector "${selector}": ${altCount} found`);
      }
      return;
    }
    
    console.log('Menu trigger found, clicking...');
    await menuTrigger.click();
    
    // Wait for menu to appear
    await page.waitForTimeout(1000);
    
    // Look for Configure menu item
    const configureItem = page.locator('[role="menuitem"]:has-text("Configure")');
    const configureCount = await configureItem.count();
    console.log(`Configure menu item count: ${configureCount}`);
    
    if (configureCount === 0) {
      console.log('Configure item not found, checking all menu items...');
      const menuItems = await page.locator('[role="menuitem"]').all();
      for (let i = 0; i < menuItems.length; i++) {
        const text = await menuItems[i].textContent();
        console.log(`  Menu item ${i}: "${text}"`);
      }
      return;
    }
    
    console.log('Configure menu item found, clicking...');
    await configureItem.click();
    
    // Check if dialog opens
    await page.waitForTimeout(1000);
    const dialogCount = await page.locator('[role="dialog"]').count();
    console.log(`Configuration dialog count: ${dialogCount}`);
    
    if (dialogCount === 0) {
      console.log('ERROR: Configuration dialog did not open');
    } else {
      console.log('SUCCESS: Configuration dialog opened!');
    }
    
    // Take screenshot
    await page.screenshot({ path: 'debug-config-flow.png', fullPage: true });
    console.log('Screenshot saved as debug-config-flow.png');
    
  } catch (error) {
    console.error('Error during debug:', error);
    await page.screenshot({ path: 'debug-config-flow-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

debugConfigFlow();