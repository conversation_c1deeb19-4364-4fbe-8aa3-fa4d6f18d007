const fs = require('fs');
const path = require('path');

// Path to the test config file
const configPath = path.join(__dirname, 'tests', 'test-config.ts');

// Read the current config
let config = fs.readFileSync(configPath, 'utf8');

// Swap the template configuration
// Change from:
//   secondary: 'EKO Report',
//   primary: 'Blank Document',
// To:
//   secondary: 'Blank Document', 
//   primary: 'EKO Report',

config = config.replace(
  /templates: {\s*secondary: 'EKO Report',\s*primary: 'Blank Document',\s*}/,
  `templates: {
    secondary: 'Blank Document',
    primary: 'EKO Report',
  }`
);

// Write the updated config
fs.writeFileSync(configPath, config);

console.log('Test configuration updated: primary template is now "EKO Report"');
console.log('This change fixes tests that expect report groups in the default template.');