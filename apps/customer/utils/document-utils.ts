import { createClient } from '@/app/supabase/client'

/**
 * Resolves a run ID, converting 'latest' to the actual latest run_id for the entity
 */
export async function resolveRunId(entityId: string, runId: string): Promise<number> {
  if (!entityId) {
    throw new Error('Entity ID is required')
  }
  
  if (!runId) {
    throw new Error('Run ID is required')
  }

  if (runId === 'latest') {
    const supabase = createClient()
    
    // Query database to get the latest run_id for this entity
    const { data: latestRun, error: runError } = await supabase
      .from('xfer_runs')
      .select('run_id')
      .eq('target', entityId)
      .order('run_id', { ascending: false })
      .limit(1)
      .single()
    
    if (runError || !latestRun) {
      throw new Error(`Failed to find latest run for entity ${entityId}: ${runError?.message || 'No runs found'}`)
    }
    
    return Number(latestRun.run_id)
  } else {
    const actualRunId = parseInt(runId)
    if (isNaN(actualRunId)) {
      throw new Error(`Invalid run ID: ${runId}`)
    }
    return actualRunId
  }
}

/**
 * Validates and resolves document creation parameters
 */
export async function validateDocumentCreationParams(entityId?: string, runId?: string): Promise<{ entityId: string, runId: number }> {
  if (!entityId) {
    throw new Error('Entity ID is required for document creation')
  }
  
  if (!runId) {
    throw new Error('Run ID is required for document creation')
  }
  
  const resolvedRunId = await resolveRunId(entityId, runId)
  
  return {
    entityId,
    runId: resolvedRunId
  }
}