/**
 * Feature flag utility functions for pattern matching and evaluation
 */

export interface FeatureFlagConfig {
  userFlags: string[];
  orgFlags: string[];
  defaultFlags: string[];
}


/**
 * Default feature flags - define which features are enabled by default
 */
export const DEFAULT_FEATURE_FLAGS = [
  'dashboard.flags',
  'dashboard.greenwashing',
  // 'dashboard.prediction',
  'document.create',
  'document.view',
  'document.editor.history',
  'trace.react',
  // 'dashboard.impact-assessment.modal',
  // "document.editor.export.word",
  // "document.editor.export.markdown",
  // "document.editor.export.html",
  // "document.editor.ai.tools",
  // "document.editor.ai.chat",
  // "document.editor.ai.edit",
  // "document.editor.collab.comments",
  // "document.editor.collab.share",
  // "document.editor.dynamic.reports",
  // "document.editor.dynamic.reports.config",
];

/**
 * Checks if a flag pattern matches a given flag name
 * Supports:
 * - exact match: "my.feature" matches "my.feature"
 * - wildcard match: "my.*" matches "my.feature" and "my.other.feature"
 * - negation: "!my.feature" disables "my.feature"
 * - wildcard negation: "!my.*" disables all flags starting with "my."
 */
export function matchesFlag(pattern: string, flagName: string): boolean {
  const isNegation = pattern.startsWith('!');
  const cleanPattern = isNegation ? pattern.slice(1) : pattern;

  if (cleanPattern === '*') {
    return true
  }


  if (cleanPattern.endsWith('*')) {
    // Wildcard pattern - match prefix
    const prefix = cleanPattern.slice(0, -1);
    return flagName.startsWith(prefix);
  } else {
    // Exact match
    return cleanPattern === flagName;
  }
}

/**
 * Evaluates if a feature flag is enabled based on the priority system:
 * 1. Check user-level flags first
 * 2. Check organization-level flags
 * 3. Fall back to default flags
 */
export function hasFeature(flagName: string, config: FeatureFlagConfig): boolean {
  // Helper function to check if flag is enabled in a flag array
  const checkFlagArray = (flags: string[]): boolean | null => {
    for (const pattern of flags) {
      if (matchesFlag(pattern, flagName)) {
        return !pattern.startsWith('!'); // Return false if negated, true if enabled
      }
    }
    return null; // Not found
  };

  // 1. Check user flags first
  const userResult = checkFlagArray(config.userFlags);
  if (userResult !== null) {
    return userResult;
  }

  // 2. Check organization flags
  const orgResult = checkFlagArray(config.orgFlags);
  if (orgResult !== null) {
    return orgResult;
  }

  // 3. Check default flags
  const defaultResult = checkFlagArray(config.defaultFlags);
  if (defaultResult !== null) {
    return defaultResult;
  }

  // 4. If not found anywhere, default to false
  return false;
}

/**
 * Utility function to normalize feature flag arrays from database
 * Handles null/undefined values from database
 */
export function normalizeFlags(flags: string[] | null | undefined): string[] {
  return flags || [];
}
