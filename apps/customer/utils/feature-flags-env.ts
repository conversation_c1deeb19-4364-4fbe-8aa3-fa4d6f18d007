/**
 * Environment-based feature flag configuration
 * This allows overriding feature flags through environment variables for testing
 */

import { DEFAULT_FEATURE_FLAGS } from './feature-flags';

/**
 * Get feature flags from environment variable
 */
function getEnvFeatureFlags(): string[] {
  if (typeof process !== 'undefined' && process.env.NEXT_PUBLIC_TEST_FEATURE_FLAGS) {
    return process.env.NEXT_PUBLIC_TEST_FEATURE_FLAGS.split(',').map(flag => flag.trim());
  }
  return [];
}

/**
 * Get default feature flags with environment overrides
 * This is used to inject test-specific feature flags
 */
export function getDefaultFeatureFlagsWithEnv(): string[] {
  const envFlags = getEnvFeatureFlags();
  if (envFlags.length > 0) {
    // Merge environment flags with defaults, removing duplicates
    return [...new Set([...DEFAULT_FEATURE_FLAGS, ...envFlags])];
  }
  return DEFAULT_FEATURE_FLAGS;
}

/**
 * Check if we're in a test environment
 */
export function isTestEnvironment(): boolean {
  return (
    process.env.NODE_ENV === 'test' ||
    process.env.CI === 'true' ||
    (typeof window !== 'undefined' && window.location.hostname === 'localhost')
  );
}