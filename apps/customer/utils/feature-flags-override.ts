/**
 * Feature flag overrides for testing and development
 * This file allows overriding default feature flags without modifying the main configuration
 */

export const TEST_FEATURE_FLAGS = [
  'document.editor.ai.tools',
  'document.editor.ai.chat',
  'document.editor.ai.edit',
];

/**
 * Get feature flags including test overrides
 * This function should be used in test environments to ensure all features being tested are enabled
 */
export function getFeatureFlagsWithTestOverrides(defaultFlags: string[]): string[] {
  if (process.env.NODE_ENV === 'test' || process.env.CI) {
    return [...defaultFlags, ...TEST_FEATURE_FLAGS];
  }
  return defaultFlags;
}