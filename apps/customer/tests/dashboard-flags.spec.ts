import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestModel } from './test-config';

test.describe('Dashboard Flags Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to flags page with test entity
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}&model=${getTestModel()}`);
    
    // Wait for page content to load instead of networkidle (which is unreliable)
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });
  });

  test('should display flags page with filter options', async ({ page }) => {
    // Wait for flags page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for filter controls
    await expect(page.locator('[data-testid="flag-type-filter"]')).toBeVisible();
    await expect(page.locator('[data-testid="disclosure-filter"]')).toBeVisible();

    // Verify flag type filter options
    const flagTypeFilter = page.locator('[data-testid="flag-type-filter"]');
    await flagTypeFilter.click();
    
    await expect(page.locator('[data-testid="filter-option-all"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-option-red"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-option-green"]')).toBeVisible();
  });

  test('should filter flags by red type', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Test red flags filter
    await page.click('[data-testid="flag-type-filter"]');
    await page.click('[data-testid="filter-option-red"]');
    await page.waitForLoadState('networkidle');

    // Verify only red flags are displayed
    const redFlags = page.locator('[data-testid="flag-item"][data-flag-type="red"]');
    const greenFlags = page.locator('[data-testid="flag-item"][data-flag-type="green"]');
    
    await expect(redFlags.first()).toBeVisible({ timeout: 10000 });
    expect(await greenFlags.count()).toBe(0);
  });

  test('should filter flags showing all types', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Test all flags filter
    await page.click('[data-testid="flag-type-filter"]');
    await page.click('[data-testid="filter-option-all"]');
    await page.waitForLoadState('networkidle');

    // Verify both types are displayed
    const allFlags = page.locator('[data-testid="flag-item"]');
    await expect(allFlags.first()).toBeVisible({ timeout: 10000 });
  });

  test('should filter flags by disclosure settings', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Test disclosure filter toggle
    const disclosureToggle = page.locator('[data-testid="disclosure-filter"]');
    
    // Check initial state
    const initialState = await disclosureToggle.isChecked();
    
    // Toggle disclosure filter
    await disclosureToggle.click();
    await page.waitForLoadState('networkidle');

    // Verify filter state changed
    const newState = await disclosureToggle.isChecked();
    expect(newState).toBe(!initialState);

    // Verify flags list updated
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible();
  });

  test('should navigate to flag detail page when flag is clicked', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Navigate to Negative Actions tab to ensure flags are visible
    const negativeTab = page.locator('text=Negative Actions');
    await negativeTab.click();
    await page.waitForTimeout(1000);

    // Find a visible flag item
    const flagItem = page.locator('[data-testid="flag-item"]').first();
    await expect(flagItem).toBeVisible({ timeout: 10000 });

    // Click on flag to navigate to detail page
    await flagItem.click();
    
    // Wait for navigation to complete
    await page.waitForLoadState('networkidle');

    // Verify we're on the flag detail page
    await expect(page.locator('[data-testid="flag-detail-modal"]')).toBeVisible({ timeout: 10000 });

    // Check detail page content
    await expect(page.locator('[data-testid="modal-flag-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="modal-flag-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="modal-flag-analysis"]')).toBeVisible();

    // Navigate back to flags list
    await page.goBack();
    await page.waitForLoadState('networkidle');

    // Verify we're back on the flags list page
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible();
  });

  test('should display flag search and autocomplete functionality', async ({ page }) => {
    // Wait for page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for search input
    const searchInput = page.locator('[data-testid="flag-search-input"]');
    await expect(searchInput).toBeVisible();

    // Test search functionality
    await searchInput.fill('environmental');
    await page.waitForTimeout(1000); // Wait for debounce

    // Verify search results update
    const searchResults = page.locator('[data-testid="flags-list"] [data-testid="flag-item"]');
    
    // Wait for search to complete
    await page.waitForTimeout(2000);
    
    // Verify search functionality works
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible();

    // Clear search
    await searchInput.clear();
    await page.waitForTimeout(1000);

    // Verify all flags are shown again
    await expect(page.locator('[data-testid="flags-list"] [data-testid="flag-item"]').first()).toBeVisible();
  });

  test('should provide entity analysis reporting functionality', async ({ page }) => {
    // Wait for page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for analysis report button/link
    const analysisButton = page.locator('[data-testid="entity-analysis-report"]');
    await expect(analysisButton).toBeVisible();
    
    await analysisButton.click();

    // Wait for some response to the click
    await page.waitForTimeout(2000);
  });

  test('should display flag metadata correctly', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Navigate to Negative Actions tab to ensure flags are visible
    const negativeTab = page.locator('text=Negative Actions');
    await negativeTab.click();
    await page.waitForTimeout(1000);

    // Find a visible flag item
    const flagItem = page.locator('[data-testid="flag-item"]').first();
    await expect(flagItem).toBeVisible();

    // Check flag metadata elements
    await expect(flagItem.locator('[data-testid="flag-title"]')).toBeVisible();

    // Verify flag type indicator (red/green)
    const flagTypeIndicator = flagItem.locator('[data-testid="flag-type-indicator"]');
    await expect(flagTypeIndicator).toBeVisible();
  });

  test('should handle empty states gracefully', async ({ page }) => {
    // Navigate with entity that might have no flags
    await page.goto(`/customer/dashboard/flags?entity=invalid-entity&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');

    // Verify flags page is loaded
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible();
  });

  test('should maintain filter state when navigating back', async ({ page }) => {
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });

    // Apply red flags filter
    await page.click('[data-testid="flag-type-filter"]');
    await page.click('[data-testid="filter-option-red"]');
    await page.waitForLoadState('networkidle');

    // Navigate away and back
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    await page.goBack();
    await page.waitForLoadState('networkidle');

    // Verify filter state is maintained (in URL or localStorage)
    const currentURL = page.url();
    expect(currentURL).toContain('flags');
  });

  test('should display loading states appropriately', async ({ page }) => {
    // Navigate to flags page
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}&model=${getTestModel()}`);

    // Wait for page to load completely
    await page.waitForLoadState('networkidle');
    
    // Verify content is loaded
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });
  });
});