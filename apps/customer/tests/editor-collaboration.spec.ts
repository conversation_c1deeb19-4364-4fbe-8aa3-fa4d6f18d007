import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Basic Features', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display basic editor functionality', async ({ page }) => {
    // Create a document using test utils
    await testUtils.createDocumentFromTemplate()

    // Wait for editor to load
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Check for basic toolbar buttons using data-testid
    await expect(page.locator('[data-testid="save-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="print-toggle-container"]')).toBeVisible()
    await expect(page.locator('[data-testid="print-toggle-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="export-button"]')).toBeVisible()

    // Check for formatting buttons using data-testid
    await expect(page.locator('[data-testid="bold-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="italic-button"]')).toBeVisible()
  })

  test('should allow text editing and formatting', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('This is a test document for basic editing')

    // Verify text was entered
    await expect(editor).toContainText('This is a test document for basic editing')

    // Select text
    await page.keyboard.press('ControlOrMeta+a')

    // Test formatting buttons using data-testid
    await expect(page.locator('[data-testid="bold-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="italic-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="underline-button"]')).toBeVisible()

    // Click bold button to test functionality
    await page.click('[data-testid="bold-button"]')
  })

  test('should save document content', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Content to save')

    // Click save button using data-testid
    await page.click('[data-testid="save-button"]')

    // Wait a moment for save to complete
    await page.waitForTimeout(1000)

    // Verify content persists
    await expect(editor).toContainText('Content to save')
  })

  test('should display document metadata', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if entity and run information is displayed (more flexible selectors)
    // Look for any entity name or run information that might be displayed
    const entityInfo = page.locator('text=/John Lewis|Entity|Run|Latest/i')
    await expect(entityInfo.first()).toBeVisible()

    // Check if back button is available (shows document context/navigation)
    const backButton = page.locator('button:has-text("Back")')
    await expect(backButton).toBeVisible()
  })

  test('should have export functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if export button exists and is clickable using data-testid
    const exportButton = page.locator('[data-testid="export-button"]')
    await expect(exportButton).toBeVisible()
    await expect(exportButton).toBeEnabled()
  })

  test('should have print functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if clean toggle switch exists and is clickable using data-testid
    const cleanSwitch = page.locator('[data-testid="print-toggle-button"]')
    await expect(cleanSwitch).toBeVisible()
    await expect(cleanSwitch).toBeEnabled()
  })

  test('should navigate back to documents list', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if back button exists
    const backButton = page.locator('button:has-text("Back")')
    await expect(backButton).toBeVisible()

    // Click back button
    await backButton.click()

    // Should navigate back to documents page (with optional query parameters)
    await expect(page).toHaveURL(/\/customer\/documents(\?.*)?$/)
  })

  test('should handle basic text formatting options', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Testing formatting options')

    // Select text
    await page.keyboard.press('ControlOrMeta+a')

    // Test various formatting buttons exist and are clickable using data-testid
    await expect(page.locator('[data-testid="bold-button"]')).toBeEnabled()
    await expect(page.locator('[data-testid="italic-button"]')).toBeEnabled()
    await expect(page.locator('[data-testid="underline-button"]')).toBeEnabled()

    // Test alignment buttons
    await expect(page.locator('[data-testid="align-left-button"]')).toBeEnabled()
    await expect(page.locator('[data-testid="align-center-button"]')).toBeEnabled()
    await expect(page.locator('[data-testid="align-right-button"]')).toBeEnabled()
  })

  test('should handle list creation', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('List item 1')

    // Test list buttons exist and are clickable using data-testid
    await expect(page.locator('[data-testid="bullet-list-button"]')).toBeEnabled()
    await expect(page.locator('[data-testid="numbered-list-button"]')).toBeEnabled()

    // Click bullet list button
    await page.click('[data-testid="bullet-list-button"]')

    // Add more list items
    await page.keyboard.press('Enter')
    await page.keyboard.type('List item 2')
  })

  test('should handle table insertion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Click in editor
    await editor.click()

    // Test table insertion button exists and is clickable using data-testid
    const tableButton = page.locator('[data-testid="insert-table-button"]')
    await expect(tableButton).toBeVisible()
    await expect(tableButton).toBeEnabled()
  })

  test('should handle image insertion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Click in editor
    await editor.click()

    // Test image insertion button exists and is clickable using data-testid
    const imageButton = page.locator('[data-testid="insert-image-button"]')
    await expect(imageButton).toBeVisible()
    await expect(imageButton).toBeEnabled()
  })

  test('should handle link insertion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Link text')

    // Select text
    await page.keyboard.press('ControlOrMeta+a')

    // Test link insertion button exists and is clickable using data-testid
    const linkButton = page.locator('[data-testid="insert-link-button"]')
    await expect(linkButton).toBeVisible()
    await expect(linkButton).toBeEnabled()
  })

  test('should handle undo and redo functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Text to undo')

    // Verify text is there
    await expect(editor).toContainText('Text to undo')

    // Test undo/redo buttons exist using data-testid
    await expect(page.locator('[data-testid="undo-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="redo-button"]')).toBeVisible()
  })
})
