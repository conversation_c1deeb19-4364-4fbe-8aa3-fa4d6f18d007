import { Page } from '@playwright/test';
import { getAuthCredentials } from '../test-config';

/**
 * Login helper function for Playwright tests
 */
export async function login(page: Page, email?: string, password?: string) {
  const credentials = getAuthCredentials();
  const loginEmail = email || credentials.email;
  const loginPassword = password || credentials.password;

  // Retry navigation if server is still starting up
  let retries = 5;
  while (retries > 0) {
    try {
      await page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
      await page.waitForLoadState('networkidle', { timeout: 30000 });
      break;
    } catch (error) {
      retries--;
      if (retries === 0) throw error;
      console.log(`Login navigation failed, retrying... (${retries} attempts left)`);
      await page.waitForTimeout(2000);
    }
  }

  // Wait for form elements to be ready
  await page.waitForSelector('#email', { timeout: 10000 });
  await page.waitForSelector('#password', { timeout: 10000 });
  await page.waitForSelector('button[type="submit"]', { timeout: 10000 });

  await page.fill('#email', loginEmail);
  await page.fill('#password', loginPassword);
  
  // Click submit and wait for navigation 
  const navigationPromise = page.waitForURL(/\/customer/, { timeout: 45000 });
  await page.click('button[type="submit"]');
  
  try {
    await navigationPromise;
    console.log(`Login successful, redirected to: ${page.url()}`);
  } catch (error) {
    // Enhanced fallback check with more detailed logging
    const currentUrl = page.url();
    console.log(`Navigation timeout, checking current URL: ${currentUrl}`);
    
    // Check for error messages on the page
    const errorMessage = await page.locator('[role="alert"], .error-message, text=Could not authenticate').first().textContent().catch(() => null);
    if (errorMessage) {
      throw new Error(`Login failed with error: ${errorMessage}`);
    }
    
    // If we ended up on login page with error params, that's a failure
    if (currentUrl.includes('/login') && currentUrl.includes('message=')) {
      throw new Error(`Login failed - authentication rejected: ${currentUrl}`);
    }
    
    // If we're on any customer page, consider it successful
    if (currentUrl.includes('/customer')) {
      console.log(`Login successful (fallback detection), redirected to: ${currentUrl}`);
      return;
    }
    
    // Otherwise it's a failure
    throw new Error(`Login failed - expected customer URL but got: ${currentUrl}`);
  }
  
  // Additional wait for page to stabilize after redirect
  await page.waitForLoadState('networkidle', { timeout: 15000 }).catch(() => {
    console.log('Network idle timeout after login, but proceeding...');
  });

  // Wait for DOM to fully initialize after login
  await page.waitForFunction(() => {
    return document.readyState === 'complete'
  }, { timeout: 5000 }).catch(() => {
    console.log('DOM initialization check failed after login, continuing...')
  });
}
