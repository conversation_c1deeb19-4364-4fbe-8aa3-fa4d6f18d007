import { expect, Page } from '@playwright/test'
import { getAuthCredentials, getTestTemplate } from '../../test-config'

/**
 * Simplified test utilities that maintain functionality while reducing complexity
 */
export class TestUtils {
  constructor(private page: Page) {}

  /**
   * Simplified login that handles both authenticated and non-authenticated states
   */
  async login(email?: string, password?: string) {
    const credentials = getAuthCredentials()
    const loginEmail = email || credentials.email
    const loginPassword = password || credentials.password

    // Try to access customer area first to check if already authenticated
    await this.page.goto('/customer', { timeout: 30000 })
    await this.page.waitForLoadState('networkidle', { timeout: 10000 })
    
    // If we're already on customer page, we're authenticated
    if (this.page.url().includes('/customer')) {
      return
    }

    // Not authenticated, proceed with login
    await this.page.goto('/login?next=%2Fcustomer', { timeout: 30000 })
    
    // Wait for and fill login form
    await this.page.waitForSelector('#email', { timeout: 10000 })
    await this.page.fill('#email', loginEmail)
    await this.page.fill('#password', loginPassword)
    await this.page.click('button[type="submit"]')

    // Wait for redirect to customer area
    await this.page.waitForURL(/\/customer/, { timeout: 45000 })
    await this.page.waitForLoadState('networkidle', { timeout: 15000 })
  }

  /**
   * Simplified document creation from template
   */
  async createDocumentFromTemplate(templateName?: string) {
    const template = templateName || getTestTemplate('primary') || 'Blank Document'
    
    // Navigate and wait for page load
    await this.page.goto('/customer/documents', { timeout: 60000 })
    await this.page.waitForLoadState('networkidle', { timeout: 30000 })
    
    // Check if we got redirected to login page
    if (this.page.url().includes('/login')) {
      // Complete login process
      await this.page.waitForSelector('#email', { timeout: 10000 })
      const credentials = getAuthCredentials()
      await this.page.fill('#email', credentials.email)
      await this.page.fill('#password', credentials.password)
      await this.page.click('button[type="submit"]')

      // Wait for redirect back to documents
      await this.page.waitForURL(/\/customer/, { timeout: 45000 })
      await this.page.waitForLoadState('networkidle', { timeout: 15000 })

      // Navigate back to documents page
      await this.page.goto('/customer/documents', { timeout: 60000 })
      await this.page.waitForLoadState('networkidle', { timeout: 30000 })
    }

    // Click New Document button
    await this.page.waitForSelector('[data-testid="new-document-button"], button:has-text("New Document")', { timeout: 30000 })
    await this.page.click('[data-testid="new-document-button"], button:has-text("New Document")')
    
    // Wait for template dialog
    await this.page.waitForSelector('[role="dialog"]', { timeout: 30000 })
    await this.page.waitForTimeout(2000) // Allow templates to load
    
    // Select template
    const templateMap = {
      'EKO Report': 'ESG Report', // Map for backwards compatibility
      'ESG Report': 'ESG Report',
      'Blank Document': 'Blank Document'
    }
    const mappedTemplate = templateMap[template as keyof typeof templateMap] || template
    await this.page.click(`[data-testid^="template-"]:has-text("${mappedTemplate}"), text="${mappedTemplate}"`)
    
    // Wait for document editor
    await this.page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 })
    await this.waitForEditor()
    
    return this.getDocumentIdFromUrl()
  }

  /**
   * Get document ID from current URL
   */
  getDocumentIdFromUrl(): string {
    const match = this.page.url().match(/\/documents\/([a-f0-9-]+)/)
    if (!match) throw new Error('Could not extract document ID from URL')
    return match[1]
  }

  /**
   * Simplified editor wait
   */
  async waitForEditor(timeout = 30000) {
    await this.page.waitForSelector('.ProseMirror[contenteditable="true"]', { timeout })
    const editor = this.page.locator('.ProseMirror')
    await expect(editor).toBeVisible({ timeout: 10000 })
    return editor
  }

  /**
   * Type text in editor
   */
  async typeInEditor(text: string) {
    const editor = await this.waitForEditor()
    await editor.click()
    await this.page.keyboard.type(text)
    return editor
  }

  /**
   * Simplified component loading wait
   */
  async waitForComponentLoading(componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first()
    
    // Wait for component to be visible
    await expect(component).toBeVisible({ timeout: 30000 })
    
    // Wait for loading spinner to disappear
    await expect(component.locator('.animate-spin')).not.toBeVisible({ timeout: 60000 })
      .catch(() => {
        // Component may already be loaded
      })
  }

  /**
   * Simplified menu item click
   */
  async clickMenuItem(menuItemText: string) {
    const menuItem = this.page.locator(`[role="menuitem"]:has-text("${menuItemText}")`)
    await menuItem.waitFor({ timeout: 5000 })
    await menuItem.click({ force: true }) // Use force click to avoid interception issues
  }

  /**
   * Simplified component configuration
   */
  async openComponentConfig(componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first()
    await component.scrollIntoViewIfNeeded()
    
    // Use a single selector for menu trigger
    const menuTrigger = component.locator('[data-testid*="menu-trigger"], button[title*="menu"]').first()
    await menuTrigger.click()
    await this.page.waitForTimeout(500) // Allow menu to position
    
    await this.clickMenuItem('Configure')
    await expect(this.page.locator('[role="dialog"]')).toBeVisible()
  }

  /**
   * Fill component configuration
   */
  async fillComponentConfig(config: {
    id?: string;
    title?: string;
    prompt?: string;
    entity?: string;
    model?: string;
  }) {
    if (config.id) {
      await this.page.fill('[data-testid="component-id-input"], input#id', config.id)
    }
    if (config.title) {
      await this.page.fill('[data-testid="component-title-input"], input#title', config.title)
    }
    if (config.prompt) {
      await this.page.fill('textarea[placeholder*="Additional instructions"]', config.prompt)
    }
    // Add other fields as needed
  }

  /**
   * Confirm component configuration
   */
  async confirmComponentConfig() {
    const confirmButton = this.page.locator('[data-testid="create-component-button"], button:has-text("Create Component")')
    await confirmButton.click({ force: true })
    await expect(this.page.locator('[role="dialog"]')).not.toBeVisible({ timeout: 15000 })
  }

  /**
   * Add report component
   */
  async addReportComponent(type: 'section' | 'group' | 'summary', config?: any) {
    const buttonTitles = {
      section: 'Insert Report Section',
      group: 'Insert Report Group', 
      summary: 'Insert Report Summary'
    }
    
    await this.waitForEditor()
    
    // Click toolbar button
    const button = this.page.locator(`button[title="${buttonTitles[type]}"]`)
    await button.waitFor({ timeout: 10000 })
    await button.click()
    
    // Wait for dialog
    await expect(this.page.locator('[role="dialog"]')).toBeVisible({ timeout: 5000 })
    
    if (config) {
      await this.fillComponentConfig(config)
    }
    
    await this.confirmComponentConfig()
    await this.page.waitForTimeout(2000) // Allow component to be added
  }

  /**
   * Perform component action
   */
  async performComponentAction(action: 'refresh' | 'lock' | 'preserve' | 'delete', componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first()
    await component.scrollIntoViewIfNeeded()
    
    // Click menu trigger
    const menuTrigger = component.locator('[data-testid*="menu-trigger"], button[title*="menu"]').first()
    await menuTrigger.click()
    await this.page.waitForTimeout(500)
    
    // Click action
    await this.clickMenuItem(action.charAt(0).toUpperCase() + action.slice(1))
    
    // Wait for action to complete
    await this.page.waitForTimeout(action === 'lock' || action === 'preserve' ? 2000 : 500)
  }

  /**
   * Check component state
   */
  async checkComponentState(state: 'locked' | 'preserved' | 'loading', componentSelector = '.report-section') {
    const component = this.page.locator(componentSelector).first()
    await expect(component).toBeVisible({ timeout: 5000 })
    
    if (state === 'locked' || state === 'preserved') {
      // Look for blue icon indicating locked/preserved state
      const stateIcon = component.locator('svg.text-blue-600:not(.animate-spin)')
      await expect(stateIcon).toBeVisible({ timeout: 10000 })
    } else if (state === 'loading') {
      const spinner = component.locator('.animate-spin')
      await expect(spinner).toBeVisible({ timeout: 5000 })
    }
  }

  /**
   * Wait for auto-save
   */
  async waitForAutoSave() {
    // Wait for save button to show "Save" (not "Saving...")
    const saveButton = this.page.locator('[data-testid="save-button"]:has-text("Save")')
    await expect(saveButton).toBeVisible({ timeout: 10000 })
      .catch(() => {
        // Fallback to network idle
        return this.page.waitForLoadState('networkidle', { timeout: 5000 })
      })
  }

  /**
   * Check for errors
   */
  async checkForErrors() {
    const errorElement = this.page.locator('.error, [role="alert"]:not(.sr-only):visible')
    const errorCount = await errorElement.count()
    
    if (errorCount > 0) {
      const errorText = await errorElement.first().textContent()
      if (errorText?.toLowerCase().includes('error')) {
        throw new Error(`Error found on page: ${errorText}`)
      }
    }
  }

  /**
   * Count components
   */
  async countComponents(type: string): Promise<number> {
    return await this.page.locator(`.${type}`).count()
  }

  /**
   * Check if component exists
   */
  async checkComponentExists(componentId: string) {
    await expect(this.page.locator(`[data-id="${componentId}"]`)).toBeVisible()
  }

  /**
   * Simple dialog field fill helper
   */
  async fillDialogField(name: string, value: string) {
    await this.page.fill(`[name="${name}"], [placeholder*="${name}" i], #${name}`, value)
  }

  /**
   * Mock API response
   */
  async mockApiResponse(endpoint: string, response: any) {
    await this.page.route(`**${endpoint}**`, route =>
      route.fulfill({
        status: 200,
        body: JSON.stringify(response),
      })
    )
  }

  /**
   * Mock API error
   */
  async mockApiError(endpoint: string, status = 500) {
    await this.page.route(`**${endpoint}**`, route =>
      route.fulfill({
        status,
        body: JSON.stringify({ error: 'Test error' }),
      })
    )
  }
}
