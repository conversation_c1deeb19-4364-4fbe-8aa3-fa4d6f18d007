import { Page } from '@playwright/test';
import { getAuthCredentials } from '../test-config';

/**
 * Simplified login helper for Playwright tests
 */
export async function login(page: Page, email?: string, password?: string) {
  const credentials = getAuthCredentials();
  const loginEmail = email || credentials.email;
  const loginPassword = password || credentials.password;

  // Try to access customer area first to check if already authenticated
  await page.goto('/customer', { timeout: 30000 });
  await page.waitForLoadState('networkidle', { timeout: 10000 });
  
  // If we're already on customer page, we're authenticated
  if (page.url().includes('/customer')) {
    return;
  }

  // Not authenticated, proceed with login
  await page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
  
  // Wait for and fill login form
  await page.waitForSelector('#email', { timeout: 10000 });
  await page.fill('#email', loginEmail);
  await page.fill('#password', loginPassword);
  await page.click('button[type="submit"]');

  // Wait for redirect to customer area
  await page.waitForURL(/\/customer/, { timeout: 45000 });
  await page.waitForLoadState('networkidle', { timeout: 15000 });
}