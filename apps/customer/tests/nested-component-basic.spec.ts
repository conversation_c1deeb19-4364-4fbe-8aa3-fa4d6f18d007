import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Basic Nested Component Loading Tests', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should load report-section with basic content', async ({ page }) => {
    // Mock API response with simple content
    const mockResponse = {
      text: `
        <h2>Environmental Impact Analysis</h2>
        <p>This analysis shows significant environmental concerns.</p>
        <p>The data indicates important trends that require attention.</p>
      `,
      citations: [
        {
          doc_page_id: 123456,
          title: "Environmental Impact Report 2023",
          url: "https://example.com/report1",
          domain: "Environmental"
        }
      ]
    }

    // Mock the report API endpoint
    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      })
    })

    // Create document from template
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selector and auto-selection
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities') && !text.includes('Select entity')
    }, { timeout: 15000 })

    // Wait for report sections to load
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 10000 })
    
    // Wait for content to load
    await page.waitForTimeout(3000)

    // Verify basic content is rendered
    await expect(page.locator('text=Environmental Impact Analysis').first()).toBeVisible()
    await expect(page.locator('text=This analysis shows significant environmental concerns').first()).toBeVisible()
    await expect(page.locator('text=The data indicates important trends').first()).toBeVisible()
  })

  test('should handle nested report-group structure', async ({ page }) => {
    // Mock responses for multiple sections within a group
    page.route('**/api/report/entity/*/harm/model/*/section/*', route => {
      const url = route.request().url()
      const sectionId = url.split('/').pop()?.split('?')[0] || 'unknown'
      
      const response = {
        text: `<h3>Section ${sectionId}</h3><p>Content for section ${sectionId} with detailed analysis.</p>`,
        citations: [
          { doc_page_id: Math.floor(Math.random() * 1000000), title: `Citation for ${sectionId}`, url: `https://example.com/${sectionId}`, domain: "Environmental" }
        ]
      }

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(response)
      })
    })

    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selection
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for report groups and sections to load
    await expect(page.locator('.report-group').first()).toBeVisible({ timeout: 10000 })
    
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 10000 })

    // Wait for content loading to complete
    await page.waitForTimeout(5000)

    // Verify the report group structure is maintained
    const reportGroup = page.locator('.report-group').first()
    const sectionsInGroup = reportGroup.locator('.report-section')
    const sectionCount = await sectionsInGroup.count()
    
    // Should have at least one section in the group
    expect(sectionCount).toBeGreaterThan(0)
    
    // Verify sections have content
    await expect(sectionsInGroup.first()).toBeVisible()
  })

  test('should handle loading states and basic error recovery', async ({ page }) => {
    let callCount = 0
    
    // Mock API with initial failures then success
    page.route('**/api/report/**', route => {
      callCount++
      
      if (callCount <= 1) {
        // First call fails
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Server error' })
        })
      } else {
        // Subsequent calls succeed
        route.fulfill({
          status: 200,
          headers: { 'content-type': 'application/json' },
          body: JSON.stringify({
            text: `<h2>Recovered Content</h2><p>Content loaded successfully after retry.</p>`,
            citations: []
          })
        })
      }
    })

    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for entity selection
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    // Wait for initial loading attempts (which will fail)
    await page.waitForTimeout(3000)

    // Try to manually refresh a component to trigger retry
    const reportSection = page.locator('.report-section').first()
    await expect(reportSection).toBeVisible({ timeout: 10000 })
    
    const menuTrigger = reportSection.locator('[data-testid="report-section-menu-trigger"]')
    await expect(menuTrigger).toBeVisible()
    await menuTrigger.click()
    await page.click('text=Refresh')
    
    // Wait for retry to succeed
    await page.waitForTimeout(3000)
    
    // Verify content loaded successfully
    await expect(page.locator('text=Recovered Content').first()).toBeVisible()
    await expect(page.locator('text=Content loaded successfully after retry').first()).toBeVisible()
  })

  test('should handle basic citation loading', async ({ page }) => {
    // Mock response with citations
    const mockResponse = {
      text: `
        <h2>Analysis with Citations</h2>
        <p>Multiple studies indicate significant trends.</p>
        <p>Further research supports these findings.</p>
      `,
      citations: [
        { doc_page_id: 100001, title: "Primary Research Study", url: "https://example.com/study1", domain: "Environmental" },
        { doc_page_id: 100002, title: "Trend Analysis Report", url: "https://example.com/trend", domain: "Social" },
        { doc_page_id: 100003, title: "Supporting Evidence", url: "https://example.com/evidence", domain: "Governance" }
      ]
    }

    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse)
      })
    })

    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for loading
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    await page.waitForTimeout(3000)

    // Verify content is displayed
    await expect(page.locator('text=Analysis with Citations').first()).toBeVisible()
    await expect(page.locator('text=Multiple studies indicate significant trends').first()).toBeVisible()
    await expect(page.locator('text=Further research supports these findings').first()).toBeVisible()

    // Check if citations are present
    const citations = page.locator('.citation-wrapper')
    const citationCount = await citations.count()
    console.log(`Found ${citationCount} citations rendered`)
    
    // If citations are found, verify they are visible
    expect(citationCount).toBeGreaterThanOrEqual(0)
  })

  test('should maintain component structure after loading', async ({ page }) => {
    // Mock simple response
    page.route('**/api/report/**', route => {
      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({
          text: `<h3>Test Section</h3><p>Test content loaded successfully.</p>`,
          citations: []
        })
      })
    })

    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report')

    // Wait for loading
    await expect(page.locator('[data-testid="entity-select"]')).toBeVisible({ timeout: 10000 })
    await page.waitForFunction(() => {
      const trigger = document.querySelector('[data-testid="entity-select"]')
      if (!trigger) return false
      const text = trigger.textContent || ''
      return !text.includes('Loading entities')
    }, { timeout: 15000 })

    await page.waitForTimeout(3000)

    // Verify component structure is maintained
    const reportSummaries = page.locator('.report-summary')
    const reportGroups = page.locator('.report-group')
    
    // Try multiple selectors for report sections count
    let reportSections;
    try {
      reportSections = page.locator('.report-section')
      if (await reportSections.count() === 0) throw new Error('No .report-section found')
    } catch (error) {
      try {
        reportSections = page.locator('[data-type="reportSection"]')
        if (await reportSections.count() === 0) throw new Error('No [data-type="reportSection"] found')
        console.log('Using [data-type="reportSection"] selector for section count')
      } catch (altError) {
        reportSections = page.locator('report-section')
        console.log('Using report-section element selector for section count')
      }
    }

    const summaryCount = await reportSummaries.count()
    const groupCount = await reportGroups.count()
    const sectionCount = await reportSections.count()

    console.log(`Found ${summaryCount} summaries, ${groupCount} groups, ${sectionCount} sections`)

    // Verify we have the expected structure
    expect(summaryCount).toBeGreaterThan(0)
    expect(groupCount).toBeGreaterThan(0)
    expect(sectionCount).toBeGreaterThan(0)

    // Verify components are visible
    await expect(reportSummaries.first()).toBeVisible()
    await expect(reportGroups.first()).toBeVisible()
    await expect(reportSections.first()).toBeVisible()
  })
})
