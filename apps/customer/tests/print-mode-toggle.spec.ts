import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Clean Mode Toggle', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should toggle clean mode on and off', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)

    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()

    // Verify Clean toggle container exists
    const toggleContainer = page.locator('[data-testid="print-toggle-container"]')
    await expect(toggleContainer).toBeVisible()

    // Verify Clean toggle switch exists
    const cleanSwitch = page.locator('[data-testid="print-toggle-button"]')
    await expect(cleanSwitch).toBeVisible()

    // Verify label shows "Clean"
    await expect(toggleContainer.locator('label')).toHaveText('Clean')

    // Verify editor is not in print mode initially
    const editor = await testUtils.waitForEditor()
    await expect(editor).not.toHaveClass(/eko-print-mode/)

    // Verify switch is initially unchecked
    await expect(cleanSwitch).not.toBeChecked()

    // Click clean mode switch to enable
    await cleanSwitch.click()

    // Verify switch is now checked
    await expect(cleanSwitch).toBeChecked()

    // Verify editor has print mode class
    await expect(editor).toHaveClass(/eko-print-mode/)

    // Click again to exit clean mode
    await cleanSwitch.click()

    // Verify switch is unchecked again
    await expect(cleanSwitch).not.toBeChecked()

    // Verify editor no longer has print mode class
    await expect(editor).not.toHaveClass(/eko-print-mode/)
  })

  test('should maintain editability in clean mode', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)

    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()

    // Enter clean mode using the switch
    const cleanSwitch = page.locator('[data-testid="print-toggle-button"]')
    await cleanSwitch.click()

    // Verify switch is checked
    await expect(cleanSwitch).toBeChecked()

    // Verify editor is still editable
    const editor = await testUtils.waitForEditor()
    await expect(editor).toHaveAttribute('contenteditable', 'true')

    // Try typing in clean mode
    await editor.click()
    await page.keyboard.type('Test content in clean mode')

    // Verify content was added
    await expect(editor.locator('text=Test content in clean mode')).toBeVisible()
  })

  test('should apply print styling in clean mode', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)

    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()

    // Add some content with report components
    await testUtils.typeInEditor('# Test Document\n\nThis is test content.')

    // Enter clean mode using the switch
    const cleanSwitch = page.locator('[data-testid="print-toggle-button"]')
    await cleanSwitch.click()

    // Verify switch is checked
    await expect(cleanSwitch).toBeChecked()

    // Verify print mode styles are applied
    const editor = await testUtils.waitForEditor()

    // Check that editor has print mode class
    await expect(editor).toHaveClass(/eko-print-mode/)

    // Verify background is white (print style)
    const bgColor = await editor.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor
    })

    // Should be white or rgb(255, 255, 255)
    expect(bgColor).toMatch(/rgb\(255,\s*255,\s*255\)|white/)
  })

  test('should hide visual decorations in clean mode', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)

    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()

    // Insert a report section (which has visual decorations)
    await page.locator('button[title="Insert Report Section"]').click()

    // Fill in the dialog
    await page.fill('input[placeholder="Enter entity short ID"]', 'TEST')
    await page.fill('input[placeholder="Enter run ID"]', '1')
    await page.selectOption('select', 'environmental')
    await page.click('button:has-text("Insert")')

    // Wait for report section to load
    await testUtils.waitForComponentLoading('.report-section')

    // Enter clean mode using the switch
    const cleanSwitch = page.locator('[data-testid="print-toggle-button"]')
    await cleanSwitch.click()

    // Verify switch is checked
    await expect(cleanSwitch).toBeChecked()

    // Verify that control elements are hidden in clean mode
    const reportSection = page.locator('.report-section').first()
    const controlElements = reportSection.locator('.absolute, button')

    // In clean mode, these should be hidden
    for (let i = 0; i < await controlElements.count(); i++) {
      const element = controlElements.nth(i)
      const isVisible = await element.isVisible()
      expect(isVisible).toBe(false)
    }
  })

  test('should work with view mode combination', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to print page (view + print mode)
    await page.goto(`/customer/documents/${documentId}/print`)
    
    // Verify document loads in view mode with print styling
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Verify editor is not editable (view mode)
    const editor = await testUtils.waitForEditor()
    await expect(editor).toHaveAttribute('contenteditable', 'false')
    
    // Verify print styles are applied (this page should have print styling)
    const bgColor = await editor.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor
    })
    
    // Should be white or transparent for print
    expect(bgColor).toMatch(/rgb\(255,\s*255,\s*255\)|white|rgba\(0,\s*0,\s*0,\s*0\)|transparent/)
  })
})
