import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

//TODO: Remove the skip please
test.describe.skip('Editor Context Integration Tests', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test.describe('Component Registration and Status Management', () => {
    test('should register components and manage their status correctly', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Test component registration by inserting a report section using test utils
      await testUtils.addReportComponent('section', {
        id: 'test-section-1',
        title: 'Test Section'
      })
      
      // Wait for the component to be inserted and registered
      await expect(page.locator('.report-section')).toBeVisible()

      // Check that component has the correct attributes
      const component = page.locator('.report-section').first()
      
      // Verify component has correct data-id
      await expect(component).toHaveAttribute('data-id', 'test-section-1')
      
      // Verify component has the correct class
      await expect(component).toHaveClass(/report-section/)
    })

    test('debug report group insertion', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Try inserting content directly via editor command
      await page.evaluate(() => {
        const editor = (window as any).testEditor
        if (editor) {
          // Try inserting a report group node directly
          editor.chain().focus().insertContent({
            type: 'reportGroup',
            attrs: {
              id: 'direct-group-test',
              title: 'Direct Group Test'
            },
            content: [{
              type: 'heading',
              attrs: { level: 2 },
              content: [{
                type: 'text',
                text: 'Direct Group Test'
              }]
            }]
          }).run()
        }
      })

      await page.waitForTimeout(1000)

      // Check what was actually inserted
      const groupCount = await page.locator('.report-group').count()
      const sectionCount = await page.locator('.report-section').count()
      const directGroupElement = await page.locator('[data-id="direct-group-test"]').count()

      console.log('Direct insertion results:', {
        groupCount,
        sectionCount,
        directGroupElement
      })

      // Verify the element exists
      await expect(page.locator('[data-id="direct-group-test"]')).toBeVisible()
    })

    test.skip('should handle component dependencies correctly', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Insert a report group first using test utils
      await testUtils.addReportComponent('group', {
        id: 'test-group-1',
        title: 'Test Group'
      })

      // Wait a bit for the component to render
      await page.waitForTimeout(1000)

      // Debug: Check what elements are actually in the editor
      const editorContent = await page.locator('.ProseMirror').innerHTML()
      console.log('Editor content after adding group:', editorContent)

      // Try different selectors
      const reportGroupByClass = await page.locator('.report-group').count()
      const reportSectionByClass = await page.locator('.report-section').count()
      const reportGroupByTag = await page.locator('report-group').count()
      const reportGroupByDataId = await page.locator('[data-id="test-group-1"]').count()

      console.log('Elements found:', {
        groupByClass: reportGroupByClass,
        sectionByClass: reportSectionByClass,
        groupByTag: reportGroupByTag,
        groupByDataId: reportGroupByDataId
      })

      // Check the actual node type in the editor
      const nodeClasses = await page.locator('[data-id="test-group-1"]').getAttribute('class')
      console.log('Node classes for test-group-1:', nodeClasses)

      // Use a more flexible selector - look for the wrapper that contains our group
      await expect(page.locator('[data-id="test-group-1"]')).toBeVisible()

      // Insert a report section inside the group
      const group = page.locator('.report-group').first()
      await group.click()
      
      // Insert section inside group using test utils
      await testUtils.addReportComponent('section', {
        id: 'test-section-2',
        title: 'Test Section Inside Group'
      })
      
      // Wait for both components to be registered
      await expect(page.locator('.report-section')).toBeVisible()

      // Check that the group and section are properly nested
      const groupComponent = page.locator('.report-group').first()
      const sectionComponent = page.locator('.report-section').first()
      
      // Verify components have correct attributes
      await expect(groupComponent).toHaveAttribute('data-id', 'test-group-1')
      await expect(sectionComponent).toHaveAttribute('data-id', 'test-section-2')
    })

    test('should update group status when child components change', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Create a complex structure with nested groups using test utils
      await testUtils.addReportComponent('group', {
        id: 'test-parent-group',
        title: 'Test Parent Group'
      })

      // Wait for the group to render
      await page.waitForTimeout(1000)

      const parentGroup = page.locator('[data-id="test-parent-group"]').first()
      await expect(parentGroup).toBeVisible()

      // Add multiple sections to the group
      await parentGroup.click()
      await testUtils.addReportComponent('section', {
        id: 'test-section-3',
        title: 'Test Section 3'
      })
      await testUtils.addReportComponent('section', {
        id: 'test-section-4',
        title: 'Test Section 4'
      })

      // Wait for all components to be registered
      await expect(page.locator('.report-section')).toHaveCount(2)

      // Verify all components have correct IDs
      const sections = page.locator('.report-section')
      await expect(sections.nth(0)).toHaveAttribute('data-id', 'test-section-3')
      await expect(sections.nth(1)).toHaveAttribute('data-id', 'test-section-4')
      
      // Verify parent group is properly set up
      await expect(parentGroup).toHaveAttribute('data-id', 'test-parent-group')
    })
  })

  test.describe('Memory Management', () => {
    test('should clean up resources when components are removed', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Insert a component using test utils
      await testUtils.addReportComponent('section', {
        id: 'test-section-cleanup',
        title: 'Test Section for Cleanup'
      })

      // Wait for component to render
      await page.waitForTimeout(1000)

      // Use class selector which is more reliable
      await expect(page.locator('.report-section')).toBeVisible()

      // Check that memory tracking is working by examining console logs
      const consoleLogs: string[] = []
      page.on('console', msg => {
        if (msg.text().includes('MemoryManager') || msg.text().includes('cleanup')) {
          consoleLogs.push(msg.text())
        }
      })

      // Remove the component
      const component = page.locator('.report-section').first()
      await component.click()
      await page.keyboard.press('Delete')

      // Component should be removed
      await expect(page.locator('.report-section')).toHaveCount(0)

      // Give time for cleanup to occur
      await page.waitForTimeout(1000)

      // Check that cleanup logs were generated
      expect(consoleLogs.some(log => log.includes('cleanup'))).toBe(true)
    })

    test('should handle page navigation without memory leaks', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Insert multiple components using test utils
      await testUtils.addReportComponent('section', {
        id: 'test-section-memory',
        title: 'Test Section for Memory Test'
      })
      await testUtils.addReportComponent('group', {
        id: 'test-group-memory',
        title: 'Test Group for Memory Test'
      })

      // Wait for components to render
      await page.waitForTimeout(1000)

      await expect(page.locator('.report-section')).toBeVisible()
      // For now, expect report-section since that's what's being rendered
      // TODO: Fix the report-group rendering issue
      await expect(page.locator('[data-id="test-group-memory"]')).toBeVisible()

      // Track memory usage
      const memoryBefore = await page.evaluate(() => {
        return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      })

      // Navigate away and back
      await page.goto('/customer/dashboard')
      await page.waitForLoadState('networkidle')
      
      await page.goto(`/customer/documents/${documentId}`)
      await testUtils.waitForEditor()

      // Check memory after navigation
      const memoryAfter = await page.evaluate(() => {
        return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      })

      // Memory usage should not have grown significantly (allowing for normal variance)
      if (memoryBefore > 0 && memoryAfter > 0) {
        const memoryGrowth = (memoryAfter - memoryBefore) / memoryBefore
        expect(memoryGrowth).toBeLessThan(0.5) // Less than 50% growth
      }
    })
  })

  test.describe('Document Operations', () => {
    test('should save and load documents correctly with new context', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add some content
      const editor = await testUtils.getEditor()
      await editor.click()
      await page.keyboard.type('Test content for document operations')
      
      // Insert a component using test utils
      await testUtils.addReportComponent('section', {
        id: 'test-section-document-ops',
        title: 'Test Section for Document Operations'
      })
      await expect(page.locator('.report-section')).toBeVisible()

      // Wait for auto-save to occur
      await page.waitForTimeout(2000)

      // Check that save indicator shows document is saved
      await expect(page.locator('[data-testid="save-indicator"]')).toContainText(/saved|up to date/i)

      // Reload the page to test loading
      await page.reload()
      await testUtils.waitForEditor()

      // Content should be restored
      await expect(editor).toContainText('Test content for document operations')
      await expect(page.locator('.report-section')).toBeVisible()
    })

    test('should handle version creation and restoration', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add initial content
      const editor = await testUtils.getEditor()
      await editor.click()
      await page.keyboard.press('ControlOrMeta+a')
      await page.keyboard.type('Initial version content')
      await page.waitForTimeout(1000)

      // Create a manual version
      await page.click('[data-testid="version-menu-trigger"]')
      await page.click('[data-testid="create-version"]')
      
      // Fill in version details
      await page.fill('[data-testid="version-summary"]', 'Initial version')
      await page.click('[data-testid="create-version-confirm"]')

      // Wait for version to be created
      await expect(page.locator('[data-testid="version-created-toast"]')).toBeVisible()

      // Modify content
      await editor.click()
      await page.keyboard.press('ControlOrMeta+a')
      await page.keyboard.type('Modified content')
      await page.waitForTimeout(1000)

      // Restore previous version
      await page.click('[data-testid="version-menu-trigger"]')
      await page.click('[data-testid="version-history"]')
      
      // Select the first version and restore
      await page.click('[data-testid="version-item"]:first-child [data-testid="restore-version"]')
      await page.click('[data-testid="confirm-restore"]')

      // Content should be restored
      await expect(editor).toContainText('Initial version content')
    })
  })

  test.describe('Performance Optimizations', () => {
    test('should debounce group status updates efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Track update calls
      const updateCalls: number[] = []
      
      await page.evaluate(() => {
        const originalConsoleLog = console.log
        console.log = (...args: any[]) => {
          if (args[0] && args[0].includes && args[0].includes('Group status update')) {
            (window as any).updateCalls = (window as any).updateCalls || []
            ;(window as any).updateCalls.push(Date.now())
          }
          originalConsoleLog.apply(console, args)
        }
      })

      // Create a group with multiple children using test utils
      await testUtils.addReportComponent('group', {
        id: 'test-performance-group',
        title: 'Test Performance Group'
      })
      const group = page.locator('.report-group').first()
      await expect(group).toBeVisible()

      // Rapidly add multiple sections
      await group.click()
      for (let i = 0; i < 5; i++) {
        await testUtils.addReportComponent('section', {
          id: `test-perf-section-${i}`,
          title: `Test Performance Section ${i}`
        })
        await page.waitForTimeout(50) // Small delay between additions
      }

      // Wait for all debounced updates to complete
      await page.waitForTimeout(1000)

      // Check that updates were debounced (fewer updates than actions)
      const calls = await page.evaluate(() => (window as any).updateCalls || [])
      expect(calls.length).toBeLessThan(5) // Should be fewer than the number of components added
    })

    test('should prevent unnecessary re-renders', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add performance marks
      await page.evaluate(() => {
        (window as any).renderCount = 0
        
        // Mock React's render tracking
        const originalConsoleLog = console.log
        console.log = (...args: any[]) => {
          if (args[0] && args[0].includes && args[0].includes('Component render')) {
            (window as any).renderCount++
          }
          originalConsoleLog.apply(console, args)
        }
      })

      // Insert component using test utils
      await testUtils.addReportComponent('section', {
        id: 'test-section-render-perf',
        title: 'Test Section for Render Performance'
      })
      await expect(page.locator('.report-section')).toBeVisible()

      // Perform actions that should not cause unnecessary re-renders
      const editor = await testUtils.getEditor()
      await editor.click()
      await page.keyboard.type('Some text')
      
      // Wait for renders to stabilize
      await page.waitForTimeout(1000)

      const renderCount = await page.evaluate(() => (window as any).renderCount || 0)
      
      // Should have reasonable number of renders (exact number depends on implementation)
      expect(renderCount).toBeLessThan(20) // Arbitrary reasonable threshold
    })
  })

  test.describe('Error Handling and Recovery', () => {
    test('should handle component loading errors gracefully', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Mock API failure for report sections
      await page.route('**/api/report/entity/**', route => {
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Internal server error' })
        })
      })

      // Insert a report section using test utils
      await testUtils.addReportComponent('section', {
        id: 'test-section-error-handling',
        title: 'Test Section for Error Handling'
      })
      
      // Component should be visible but in error state
      const component = page.locator('.report-section').first()
      await expect(component).toBeVisible()
      
      // Verify component is created with correct ID
      await expect(component).toHaveAttribute('data-id', 'test-section-error-handling')
      
      // Component should be visible even with API errors
      await expect(component).toBeVisible()
    })

    test('should recover from transient errors', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      let callCount = 0
      
      // Mock API to fail first call, succeed on retry
      await page.route('**/api/report/entity/**', route => {
        callCount++
        if (callCount === 1) {
          route.fulfill({
            status: 500,
            body: JSON.stringify({ error: 'Temporary error' })
          })
        } else {
          route.fulfill({
            status: 200,
            body: JSON.stringify({
              text: '<p>Test report content</p>',
              citations: []
            })
          })
        }
      })

      // Insert a report section using test utils
      await testUtils.addReportComponent('section', {
        id: 'test-section-error-recovery',
        title: 'Test Section for Error Recovery'
      })
      const component = page.locator('.report-section').first()
      await expect(component).toBeVisible()

      // Verify component is created with correct ID
      await expect(component).toHaveAttribute('data-id', 'test-section-error-recovery')
      
      // Component should be visible
      await expect(component).toBeVisible()
    })
  })
})
