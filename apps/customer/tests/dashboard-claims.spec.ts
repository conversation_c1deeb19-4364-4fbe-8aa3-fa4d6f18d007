import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestModel } from './test-config';

// Helper function to wait for claims page to load properly
async function waitForClaimsPageToLoad(page: any) {
  const loadingElement = page.locator('[data-testid="loading-claims"]');
  const contentElement = page.locator('[data-testid="claims-page-content"]');
  
  // Wait for either loading to appear or content to be ready
  await Promise.race([
    loadingElement.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {}),
    contentElement.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {})
  ]);
  
  // If we see loading, wait for it to complete
  if (await loadingElement.isVisible()) {
    await loadingElement.waitFor({ state: 'hidden', timeout: 30000 });
  }
  
  // Now the content should be visible
  await expect(contentElement).toBeVisible({ timeout: 10000 });
}

test.describe('Dashboard Claims Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to claims page with test entity
    await page.goto(`/customer/dashboard/gw/claims?entity=${getTestEntity()}&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');
  });

  test('should display claims page with filtering capabilities', async ({ page }) => {
    // Wait for claims page to load
    await waitForClaimsPageToLoad(page);

    // Check for filter controls
    await expect(page.locator('[data-testid="claims-filters"]')).toBeVisible();

    // Verify ESG claims filter is available
    const esgFilter = page.locator('[data-testid="esg-claims-filter"]');
    if (await esgFilter.isVisible()) {
      await expect(esgFilter).toBeVisible();
    }

    // Check confidence filter (>50)
    const confidenceFilter = page.locator('[data-testid="confidence-filter"]');
    if (await confidenceFilter.isVisible()) {
      await expect(confidenceFilter).toBeVisible();
    }

    // Check importance filter (>=30)
    const importanceFilter = page.locator('[data-testid="importance-filter"]');
    if (await importanceFilter.isVisible()) {
      await expect(importanceFilter).toBeVisible();
    }
  });

  test('should filter claims by ESG criteria', async ({ page }) => {
    // Wait for claims to load
    await expect(page.locator('[data-testid="claims-list"]')).toBeVisible({ timeout: 30000 });

    // Apply ESG filter if available
    const esgFilter = page.locator('[data-testid="esg-claims-filter"]');
    
    if (await esgFilter.isVisible()) {
      await esgFilter.click();
      await page.waitForLoadState('networkidle');

      // Verify filtered results
      const claimItems = page.locator('[data-testid="claim-item"]');
      
      if (await claimItems.count() > 0) {
        // Check that displayed claims have ESG-related content
        const firstClaim = claimItems.first();
        const claimText = await firstClaim.textContent();
        
        // ESG terms that might appear in claims
        const esgTerms = ['environmental', 'social', 'governance', 'sustainability', 'carbon', 'emission', 'renewable', 'diversity'];
        const hasEsgTerm = esgTerms.some(term => 
          claimText?.toLowerCase().includes(term)
        );
        
        // Note: This is a soft check as ESG classification might be more complex
        console.log('ESG filter applied, claim content:', claimText?.substring(0, 100));
      }
    }
  });

  test('should filter claims by confidence threshold', async ({ page }) => {
    // Wait for claims to load
    await expect(page.locator('[data-testid="claims-list"]')).toBeVisible({ timeout: 30000 });

    // Check for confidence filter controls
    const confidenceSlider = page.locator('[data-testid="confidence-slider"]');
    const confidenceInput = page.locator('[data-testid="confidence-input"]');

    if (await confidenceSlider.isVisible()) {
      // Set confidence threshold to 50
      await confidenceSlider.fill('50');
      await page.waitForLoadState('networkidle');
    } else if (await confidenceInput.isVisible()) {
      await confidenceInput.fill('50');
      await page.waitForLoadState('networkidle');
    }

    // Verify claims have confidence >= 50
    const claimItems = page.locator('[data-testid="claim-item"]');
    
    if (await claimItems.count() > 0) {
      const confidenceValues = await claimItems.locator('[data-testid="claim-confidence"]').allTextContents();
      
      for (const confidenceText of confidenceValues) {
        const confidence = parseFloat(confidenceText.replace('%', ''));
        if (!isNaN(confidence)) {
          expect(confidence).toBeGreaterThanOrEqual(50);
        }
      }
    }
  });

  test('should filter claims by importance threshold', async ({ page }) => {
    // Wait for claims to load
    await expect(page.locator('[data-testid="claims-list"]')).toBeVisible({ timeout: 30000 });

    // Check for importance filter controls
    const importanceSlider = page.locator('[data-testid="importance-slider"]');
    const importanceInput = page.locator('[data-testid="importance-input"]');

    if (await importanceSlider.isVisible()) {
      // Set importance threshold to 30
      await importanceSlider.fill('30');
      await page.waitForLoadState('networkidle');
    } else if (await importanceInput.isVisible()) {
      await importanceInput.fill('30');
      await page.waitForLoadState('networkidle');
    }

    // Verify claims have importance >= 30
    const claimItems = page.locator('[data-testid="claim-item"]');
    
    if (await claimItems.count() > 0) {
      const importanceValues = await claimItems.locator('[data-testid="claim-importance"]').allTextContents();
      
      for (const importanceText of importanceValues) {
        const importance = parseFloat(importanceText);
        if (!isNaN(importance)) {
          expect(importance).toBeGreaterThanOrEqual(30);
        }
      }
    }
  });

  test('should sort claims by importance descending, then confidence descending', async ({ page }) => {
    // Wait for claims to load
    await expect(page.locator('[data-testid="claims-list"]')).toBeVisible({ timeout: 30000 });

    // Get all claim items
    const claimItems = page.locator('[data-testid="claim-item"]');
    const itemCount = await claimItems.count();

    if (itemCount > 1) {
      // Extract importance and confidence values
      const claimsData = [];
      
      for (let i = 0; i < Math.min(itemCount, 5); i++) { // Check first 5 for performance
        const item = claimItems.nth(i);
        
        const importanceText = await item.locator('[data-testid="claim-importance"]').textContent();
        const confidenceText = await item.locator('[data-testid="claim-confidence"]').textContent();
        
        const importance = parseFloat(importanceText || '0');
        const confidence = parseFloat((confidenceText || '0').replace('%', ''));
        
        claimsData.push({ importance, confidence, index: i });
      }

      // Verify sorting: importance desc, then confidence desc
      for (let i = 0; i < claimsData.length - 1; i++) {
        const current = claimsData[i];
        const next = claimsData[i + 1];

        if (current.importance === next.importance) {
          // Same importance, confidence should be descending
          expect(current.confidence).toBeGreaterThanOrEqual(next.confidence);
        } else {
          // Different importance, should be descending
          expect(current.importance).toBeGreaterThanOrEqual(next.importance);
        }
      }
    }
  });

  test('should display claims using V2 components correctly', async ({ page }) => {
    // Wait for claims to load
    await expect(page.locator('[data-testid="claims-list"]')).toBeVisible({ timeout: 30000 });

    const claimItems = page.locator('[data-testid="claim-item"]');
    const firstClaim = claimItems.first();
    await expect(firstClaim).toBeVisible();

    // Check V2 component structure
    await expect(firstClaim.locator('[data-testid="claim-title"]')).toBeVisible();
    await expect(firstClaim.locator('[data-testid="claim-content"]')).toBeVisible();
    await expect(firstClaim.locator('[data-testid="claim-metadata"]')).toBeVisible();

    // Check metadata includes confidence and importance
    await expect(firstClaim.locator('[data-testid="claim-confidence"]')).toBeVisible();
    await expect(firstClaim.locator('[data-testid="claim-importance"]')).toBeVisible();

    // Check date information
    await expect(firstClaim.locator('[data-testid="claim-date"]')).toBeVisible();

    // Verify confidence format (should be percentage or decimal)
    const confidenceText = await firstClaim.locator('[data-testid="claim-confidence"]').textContent();
    expect(confidenceText).toMatch(/\d+%|\d+\.\d+/);

    // Verify importance format (should be numeric)
    const importanceText = await firstClaim.locator('[data-testid="claim-importance"]').textContent();
    expect(importanceText).toMatch(/\d+(\.\d+)?/);
  });

  test('should handle loading states properly', async ({ page }) => {
    // Navigate to claims page
    await page.goto(`/customer/dashboard/gw/claims?entity=${getTestEntity()}&model=${getTestModel()}`);

    // Check for loading indicators
    const loadingIndicators = page.locator('[data-testid*="loading"], [class*="loading"], [class*="spinner"]');
    
    try {
      // Loading indicators should appear initially
      await expect(loadingIndicators.first()).toBeVisible({ timeout: 5000 });
      
      // Then disappear as content loads
      await expect(loadingIndicators.first()).not.toBeVisible({ timeout: 30000 });
      
      // Content should be visible
      await expect(page.locator('[data-testid="claims-page-content"]')).toBeVisible();
    } catch {
      // If loading is very fast, content should be immediately visible
      await waitForClaimsPageToLoad(page);
    }
  });

  test('should display claim details when clicked', async ({ page }) => {
    // Wait for claims to load
    await expect(page.locator('[data-testid="claims-list"]')).toBeVisible({ timeout: 30000 });

    const claimItems = page.locator('[data-testid="claim-item"]');
    
    if (await claimItems.count() > 0) {
      const firstClaim = claimItems.first();
      
      // Get the current URL before clicking
      const currentUrl = page.url();
      
      // Click on claim to view details and wait for URL change
      const navigationPromise = page.waitForURL('**/customer/dashboard/gw/claims/**', { timeout: 10000 });
      await firstClaim.click();
      await navigationPromise;
      
      // Verify we navigated to a claim detail page
      const newUrl = page.url();
      expect(newUrl).not.toBe(currentUrl);
      expect(newUrl).toMatch(/\/customer\/dashboard\/gw\/claims\/\d+/);
      
      // Verify claim detail page content is visible
      await expect(page.locator('main').first()).toBeVisible({ timeout: 10000 });
    }
  });

  test('should handle empty states gracefully', async ({ page }) => {
    // Wait for claims page to load
    await waitForClaimsPageToLoad(page);

    // Apply very restrictive filters
    const confidenceSlider = page.locator('[data-testid="confidence-slider"]');
    if (await confidenceSlider.isVisible()) {
      await confidenceSlider.fill('95'); // Very high confidence
      await page.waitForLoadState('networkidle');
    }

    const importanceSlider = page.locator('[data-testid="importance-slider"]');
    if (await importanceSlider.isVisible()) {
      await importanceSlider.fill('90'); // Very high importance
      await page.waitForLoadState('networkidle');
    }

    // Check for empty state message or no results indicator
    try {
      await expect(page.locator('[data-testid="no-claims-message"]')).toBeVisible({ timeout: 10000 });
    } catch {
      // If claims are still present, that's also valid
      const claimItems = page.locator('[data-testid="claim-item"]');
      if (await claimItems.count() === 0) {
        // Should show some indication of no results
        await expect(page.locator('[data-testid="empty-state"]')).toBeVisible();
      }
    }
  });

  test('should maintain filter state in URL parameters', async ({ page }) => {
    // Wait for claims page to load
    await expect(page.locator('[data-testid="claims-page-content"]')).toBeVisible({ timeout: 30000 });

    // Apply filters
    const esgFilter = page.locator('[data-testid="esg-claims-filter"]');
    if (await esgFilter.isVisible()) {
      await esgFilter.click();
      await page.waitForLoadState('networkidle');
    }

    // Check URL contains filter parameters
    const currentURL = page.url();
    expect(currentURL).toContain('claims');

    // Navigate away and back
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    await page.goBack();
    await page.waitForLoadState('networkidle');

    // Verify we're back on claims page
    await expect(page.locator('[data-testid="claims-page-content"]')).toBeVisible({ timeout: 30000 });
  });

  test('should show appropriate metadata for each claim', async ({ page }) => {
    // Wait for claims to load
    await expect(page.locator('[data-testid="claims-list"]')).toBeVisible({ timeout: 30000 });

    const claimItems = page.locator('[data-testid="claim-item"]');
    
    if (await claimItems.count() > 0) {
      const firstClaim = claimItems.first();

      // Check all required metadata elements
      await expect(firstClaim.locator('[data-testid="claim-title"]')).toBeVisible();
      await expect(firstClaim.locator('[data-testid="claim-confidence"]')).toBeVisible();
      await expect(firstClaim.locator('[data-testid="claim-importance"]')).toBeVisible();
      await expect(firstClaim.locator('[data-testid="claim-date"]')).toBeVisible();

      // Check claim type or category if available
      const claimType = firstClaim.locator('[data-testid="claim-type"]');
      if (await claimType.isVisible()) {
        await expect(claimType).toBeVisible();
      }

      // Check source information if available
      const claimSource = firstClaim.locator('[data-testid="claim-source"]');
      if (await claimSource.isVisible()) {
        await expect(claimSource).toBeVisible();
      }
    }
  });
});