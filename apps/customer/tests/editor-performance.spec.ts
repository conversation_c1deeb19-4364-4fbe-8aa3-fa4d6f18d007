import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Performance Tests', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Enable detailed console logging for performance tracking
    page.on('console', msg => {
      if (msg.text().includes('performance') || msg.text().includes('timing')) {
        console.log(`Performance log: ${msg.text()}`)
      }
    })

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test.describe('Rendering Performance', () => {
    test('should render document efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Mark start time
      const startTime = Date.now()

      // Verify editor is responsive and content can be added
      await page.locator('.ProseMirror').click()
      await page.keyboard.type('Performance test content')
      
      // Verify text was added successfully
      await expect(page.locator('.ProseMirror')).toContainText('Performance test content')

      const endTime = Date.now()
      const totalTime = endTime - startTime

      // Should complete within reasonable time (10 seconds for basic operations)
      expect(totalTime).toBeLessThan(10000)
      
      // Log performance metrics
      console.log(`Basic editor operations completed in ${totalTime}ms`)
    })

    test('should handle rapid keyboard input efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Focus the editor
      await page.locator('.ProseMirror').click()

      // Measure performance of rapid typing
      const startTime = await page.evaluate(() => performance.now())

      // Type rapidly
      const testText = 'This is a test of rapid typing performance'
      await page.keyboard.type(testText, { delay: 10 }) // 10ms between keystrokes

      const endTime = await page.evaluate(() => performance.now())
      const typingTime = endTime - startTime

      // Should handle typing efficiently (150% overhead is reasonable)
      const expectedTime = testText.length * 10 * 2.5
      expect(typingTime).toBeLessThan(expectedTime)

      // Verify all text was captured
      await expect(page.locator('.ProseMirror')).toContainText(testText)

      console.log(`Typed ${testText.length} characters in ${typingTime}ms`)
    })

    test('should handle editor focus changes efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      const startTime = await page.evaluate(() => performance.now())

      // Test rapid focus changes
      for (let i = 0; i < 5; i++) {
        await page.locator('.ProseMirror').click()
        await page.keyboard.type('Focus test ' + i)
        await page.waitForTimeout(50)
      }

      // Verify content was added
      await expect(page.locator('.ProseMirror')).toContainText('Focus test 0')
      await expect(page.locator('.ProseMirror')).toContainText('Focus test 4')

      const endTime = await page.evaluate(() => performance.now())
      const totalTime = endTime - startTime

      // Should handle focus changes efficiently
      expect(totalTime).toBeLessThan(3000)

      console.log(`Focus changes completed in ${totalTime}ms`)
    })
  })

  test.describe.skip('Memory Performance', () => {
    test('should maintain stable memory usage during editor operations', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Get initial memory baseline
      const initialMemory = await page.evaluate(() => {
        return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      })

      // Perform multiple cycles of adding and removing content
      for (let cycle = 0; cycle < 3; cycle++) {
        // Add content
        await page.locator('.ProseMirror').click()
        await page.keyboard.type('Memory test content cycle ' + cycle)
        
        // Select all and delete
        await page.keyboard.press('Control+a')
        await page.keyboard.press('Delete')
        
        // Wait for cleanup
        await page.waitForTimeout(200)
      }

      // Final memory check
      const finalMemory = await page.evaluate(() => {
        return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0
      })

      // Verify memory usage is available and calculate growth
      expect(initialMemory).toBeGreaterThan(0)
      expect(finalMemory).toBeGreaterThan(0)
      
      const memoryGrowth = (finalMemory - initialMemory) / initialMemory
      
      // Memory should not grow significantly (allowing for some variance)
      expect(memoryGrowth).toBeLessThan(0.5) // Less than 50% growth
      
      console.log(`Memory usage: ${initialMemory} -> ${finalMemory} (${(memoryGrowth * 100).toFixed(1)}% change)`)
    })

    test('should handle navigation efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add some content
      await page.locator('.ProseMirror').click()
      await page.keyboard.type('Navigation test content')

      // Verify content exists
      await expect(page.locator('.ProseMirror')).toContainText('Navigation test content')

      const startTime = await page.evaluate(() => performance.now())

      // Navigate away
      await page.goto('/customer/dashboard')
      await page.waitForLoadState('networkidle')

      const endTime = await page.evaluate(() => performance.now())
      const navigationTime = endTime - startTime

      // Navigation should be efficient
      expect(navigationTime).toBeLessThan(5000)

      console.log(`Navigation completed in ${navigationTime}ms`)
    })
  })

  test.describe('Network Performance', () => {
    test('should handle API calls efficiently', async ({ page }) => {
      let apiCallCount = 0
      const apiCalls: string[] = []

      // Track API calls
      page.route('**/api/**', (route) => {
        apiCallCount++
        apiCalls.push(route.request().url())
        route.continue()
      })

      const startTime = Date.now()

      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Add some content to potentially trigger API calls
      await page.locator('.ProseMirror').click()
      await page.keyboard.type('API test content')

      // Wait for any background API calls to complete
      await page.waitForTimeout(2000)

      const endTime = Date.now()

      // Log API call metrics
      console.log(`Made ${apiCallCount} API calls during document creation and editing in ${endTime - startTime}ms`)
      
      // Verify reasonable performance (allow up to 50 seconds for document creation and API calls in test environment)
      expect(endTime - startTime).toBeLessThan(50000)
      
      // Log sample API calls
      console.log(`Sample API calls: ${apiCalls.slice(0, 3).join(', ')}`)
    })

    test('should handle slow network conditions gracefully', async ({ page }) => {
      // Simulate slow network for save operations
      await page.route('**/api/documents/**', async (route) => {
        if (route.request().method() === 'PATCH' || route.request().method() === 'PUT') {
          // Add delay to simulate slow network for saves
          await new Promise(resolve => setTimeout(resolve, 500))
        }
        route.continue()
      })

      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      const startTime = Date.now()

      // Add content under slow network conditions
      await page.locator('.ProseMirror').click()
      await page.keyboard.type('Slow network test content')

      // Verify content was added despite slow network
      await expect(page.locator('.ProseMirror')).toContainText('Slow network test content')

      const endTime = Date.now()
      
      // Should still be reasonably responsive
      expect(endTime - startTime).toBeLessThan(10000)
      
      console.log(`Content added under slow network in ${endTime - startTime}ms`)
    })
  })

  test.describe('Interaction Performance', () => {
    test('should respond to editor interactions quickly', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Test response time to editor interactions
      const startTime = await page.evaluate(() => performance.now())
      
      // Click and type several times
      for (let i = 0; i < 3; i++) {
        await page.locator('.ProseMirror').click()
        await page.keyboard.type(`Interaction test ${i} `)
        await page.waitForTimeout(50)
      }

      // Verify content was added
      await expect(page.locator('.ProseMirror')).toContainText('Interaction test 0')
      await expect(page.locator('.ProseMirror')).toContainText('Interaction test 2')
      
      const endTime = await page.evaluate(() => performance.now())
      const responseTime = endTime - startTime

      // Should respond within reasonable time
      expect(responseTime).toBeLessThan(2000)
      
      console.log(`Editor interactions completed in ${responseTime}ms`)
    })

    test('should handle sustained typing efficiently', async ({ page }) => {
      const documentId = await testUtils.createDocumentFromTemplate()
      await testUtils.waitForEditor()

      // Focus the editor
      await page.locator('.ProseMirror').click()

      const startTime = await page.evaluate(() => performance.now())

      // Type sustained content
      const testText = 'This is a sustained typing performance test with multiple sentences. It should handle continuous input efficiently without lag or dropped characters.'
      await page.keyboard.type(testText, { delay: 20 }) // 20ms between keystrokes

      const endTime = await page.evaluate(() => performance.now())
      const typingTime = endTime - startTime

      // Should handle typing efficiently (100% overhead is reasonable for sustained typing)
      const expectedTime = testText.length * 20 * 2
      expect(typingTime).toBeLessThan(expectedTime)

      // Verify all text was captured
      await expect(page.locator('.ProseMirror')).toContainText(testText)

      console.log(`Typed ${testText.length} characters in ${typingTime}ms`)
    })
  })
})
