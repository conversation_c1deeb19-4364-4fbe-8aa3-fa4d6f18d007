import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestModel } from './test-config';

test.describe('Dashboard Promises Page - EKO-172 Fixes', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();

    // Navigate to promises page with test entity
    await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');
  });

  test('should display promises chart with correct data-testid', async ({ page }) => {
    // Wait for page content to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Wait for promises data to be loaded by checking for the summary section
    await expect(page.locator('[data-testid="promises-summary"]')).toBeVisible({ timeout: 15000 });
    
    // Wait for promises list to indicate data has loaded
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 15000 });

    // Check that the chart is present with correct data-testid
    const chart = page.locator('[data-testid="promises-five-year-chart"]');
    await expect(chart).toBeVisible({ timeout: 15000 });

    // Check that chart has chart elements or shows "no data" message
    const chartElements = chart.locator('[data-testid="chart-element"]');
    const noDataMessage = chart.locator('text=No promise data available');
    
    // Either chart elements should be visible or no data message should be shown
    await expect(chartElements.first().or(noDataMessage)).toBeVisible({ timeout: 10000 });
  });

  test('should display promises page with basic filtering capabilities', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check that promises list is present
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 15000 });

    // Check that filter controls are present
    const statusFilter = page.locator('[data-testid="promise-status-filter"]');
    await expect(statusFilter).toBeVisible({ timeout: 10000 });

    // Check that sort controls are present
    const sortControl = page.locator('[data-testid="promises-sort"]');
    await expect(sortControl).toBeVisible({ timeout: 10000 });
  });

  test('should display promise items with correct data attributes', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    await expect(promiseItems.first()).toBeVisible({ timeout: 15000 });
    
    const firstPromise = promiseItems.first();

    // Check promise content elements (these should be present)
    await expect(firstPromise.locator('[data-testid="promise-title"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-content"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-status"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-date"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-confidence"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-category"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-theme"]')).toBeVisible();
  });

  test('should display timeline and assessment history components', async ({ page }) => {
    // Wait for promises page to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for promise timeline component
    const promiseTimeline = page.locator('[data-testid="promise-timeline"]');
    await expect(promiseTimeline).toBeVisible({ timeout: 15000 });

    // Check for assessment history
    const assessmentHistory = page.locator('[data-testid="promise-assessment-history"]');
    await expect(assessmentHistory).toBeVisible({ timeout: 15000 });
  });

  test('should handle empty states gracefully', async ({ page }) => {
    // Try with invalid entity to test empty state
    await page.goto(`/customer/dashboard/gw/promises?entity=invalid-entity&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');

    // Wait for content to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });
    
    // Check for empty state message or list
    await expect(page.locator('[data-testid="no-promises-message"], [data-testid="empty-state"], [data-testid="promises-list"]')).toBeVisible({ timeout: 15000 });
  });

  test('should display promise metadata correctly', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    await expect(promiseItems.first()).toBeVisible();
    
    const firstPromise = promiseItems.first();

    // Check for source information
    const promiseSource = firstPromise.locator('[data-testid="promise-source"]');
    await expect(promiseSource).toBeVisible();

    // Check for document references
    const promiseReferences = firstPromise.locator('[data-testid="promise-references"]');
    await expect(promiseReferences).toBeVisible();

    // Check for external links or citations
    const promiseCitations = firstPromise.locator('[data-testid="promise-citations"]');
    await expect(promiseCitations).toBeVisible();

    // Check for evidence
    const promiseEvidence = firstPromise.locator('[data-testid="promise-evidence"]');
    await expect(promiseEvidence).toBeVisible();

    // Check for target date
    const promiseTargetDate = firstPromise.locator('[data-testid="promise-target-date"]');
    await expect(promiseTargetDate).toBeVisible();
  });
});
