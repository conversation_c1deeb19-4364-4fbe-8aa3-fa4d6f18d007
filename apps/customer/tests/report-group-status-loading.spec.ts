import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report Group Status Loading', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should properly update report group status when all children are loaded', async ({ page }) => {
    // Set longer timeout for this test since it can take several minutes
    test.setTimeout(300000) // 5 minutes

    // Track API calls to understand loading order
    const apiCalls: string[] = []
    const apiResponses: Map<string, any> = new Map()

    // Mock API responses for report sections and summaries
    page.route('**/api/report/**', route => {
      const url = route.request().url()
      apiCalls.push(url)

      console.log(`API call intercepted: ${url}`)

      // Mock response based on endpoint type
      let mockResponse
      if (url.includes('/section/')) {
        mockResponse = {
          text: '<h2>Test Section Content</h2><p>This is test content for a report section with some analysis.</p>',
          citations: [
            { doc_page_id: 123456, title: 'Test Citation', url: 'https://example.com', domain: 'Test' },
          ],
        }
      } else if (url.includes('/summarize')) {
        mockResponse = {
          text: '<h2>Summary</h2><p>This is a summary of the report content.</p>',
          citations: [
            { doc_page_id: 789012, title: 'Summary Citation', url: 'https://example.com/summary', domain: 'Test' },
          ],
        }
      } else {
        mockResponse = {
          text: '<p>Default test content</p>',
          citations: [],
        }
      }

      apiResponses.set(url, mockResponse)

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse),
      })
    })

    // Create new document using test utils
    await testUtils.createDocumentFromTemplate('EKO Report')

    console.log('Document editor loaded, waiting for components to register...')

    // Wait for report components to be visible - be very patient
    await expect(page.locator('.report-group').first()).toBeVisible({ timeout: 120000 })
    await expect(page.locator('.report-section').first()).toBeVisible({ timeout: 120000 })

    console.log('Report components are visible, checking initial states...')

    // Get all report groups
    const reportGroups = page.locator('.report-group')
    const groupCount = await reportGroups.count()
    console.log(`Found ${groupCount} report groups`)

    // Initially, groups should be in loading state (yellow border or loading icon)
    for (let i = 0; i < groupCount; i++) {
      const group = reportGroups.nth(i)
      const groupHeader = group.locator('.absolute.-top-3').first()

      // Check for loading state indicators
      const hasLoadingIcon = await groupHeader.locator('svg.animate-spin').isVisible()
      const hasYellowBorder = await group.evaluate(el =>
        el.className.includes('border-yellow') || el.className.includes('opacity-80'),
      )

      if (hasLoadingIcon || hasYellowBorder) {
        console.log(`Group ${i} is in loading state`)
      }
    }

    // Entity is already selected (GVNZgYj9x7 - Inflexion) when document is created
    console.log('Entity already selected (Inflexion), waiting for sections to load...')

    // Wait longer for sections to start loading (API calls should begin)
    await page.waitForTimeout(10000)

    // Wait for all sections to finish loading
    // We'll wait for the loading indicators to disappear
    const reportSections = page.locator('.report-section')
    const sectionCount = await reportSections.count()
    console.log(`Found ${sectionCount} report sections`)

    // Wait for all sections to finish loading (no more loading spinners) - be very patient
    for (let i = 0; i < sectionCount; i++) {
      const section = reportSections.nth(i)
      console.log(`Waiting for section ${i} to finish loading...`)

      // Wait for loading spinner to disappear (if it exists) - much longer timeout
      const loadingSpinner = section.locator('svg.animate-spin')
      if (await loadingSpinner.isVisible()) {
        await expect(loadingSpinner).not.toBeVisible({ timeout: 120000 }) // 2 minutes per section
      }

      console.log(`Section ${i} finished loading`)
    }

    console.log('All sections loaded, checking group states...')

    // Wait a bit more for status updates to propagate
    await page.waitForTimeout(5000)

    // Now check that all report groups have updated to loaded state
    for (let i = 0; i < groupCount; i++) {
      const group = reportGroups.nth(i)
      const groupHeader = group.locator('.absolute.-top-3').first()

      console.log(`Checking group ${i} final state...`)

      // Get current status
      const groupStatus = await group.getAttribute('data-status')
      console.log(`Group ${i} current status: ${groupStatus}`)

      // Check if group has child sections
      const childSections = await group.locator('.report-section').count()
      console.log(`Group ${i} has ${childSections} child sections`)
      
      // Also check for child groups
      const childGroups = await group.locator('.report-group').count()
      console.log(`Group ${i} has ${childGroups} child groups`)

      // Groups with child components (sections or groups) should eventually be loaded
      if (childSections > 0 || childGroups > 0) {
        // Get the group ID for better debugging
        const groupId = await group.getAttribute('data-id')
        console.log(`Group ${i} (${groupId}) should transition to loaded state...`)
        
        // For groups that contain other groups, they need their child groups to be loaded first
        if (childGroups > 0) {
          console.log(`Group ${i} contains ${childGroups} child groups, waiting for them to load first...`)
          // Wait a bit longer for nested groups
          await page.waitForTimeout(10000)
        }
        
        // Should have green border (loaded state) - longer timeout
        await expect(group).toHaveClass(/border-green-200/, { timeout: 120000 })

        // Should show success icon (checkmark) instead of loading spinner
        await expect(groupHeader.locator('svg.animate-spin')).not.toBeVisible()
        await expect(groupHeader.locator('span').filter({ hasText: '✓' })).toBeVisible({ timeout: 30000 })

        console.log(`Group ${i} (${groupId}) is in loaded state ✓`)
      } else {
        // Groups without any children should automatically be loaded
        console.log(`Group ${i} has no child components, checking if loaded...`)
        const hasGreenBorder = await group.evaluate(el => el.className.includes('border-green-200'))
        if (!hasGreenBorder) {
          console.log(`Warning: Empty group ${i} is not in loaded state`)
        }
      }
    }

    // Check that the executive summary can now load
    console.log('Checking executive summary...')
    const execSummary = page.locator('.report-summary').filter({ hasText: 'Executive Summary' })

    if (await execSummary.isVisible()) {
      console.log('Executive summary found, checking its state...')

      // Executive summary should not show "Waiting for dependencies" anymore - longer timeout
      await expect(execSummary.locator('text=Waiting for dependencies')).not.toBeVisible({ timeout: 30000 })

      // Should show loaded content or be in loading state (not waiting)
      const summaryContent = execSummary.locator('.prose')
      if (await summaryContent.isVisible()) {
        console.log('Executive summary has content ✓')
      } else {
        // If no content yet, should at least be loading (not waiting)
        const isLoading = await execSummary.locator('svg.animate-spin').isVisible()
        if (isLoading) {
          console.log('Executive summary is loading (not waiting) ✓')

          // Wait for it to finish loading - much longer timeout
          await expect(execSummary.locator('svg.animate-spin')).not.toBeVisible({ timeout: 120000 })
          await expect(summaryContent).toBeVisible({ timeout: 30000 })
          console.log('Executive summary finished loading ✓')
        }
      }
    }

    // Verify API calls were made in correct order
    console.log(`Total API calls made: ${apiCalls.length}`)
    console.log('API calls:', apiCalls)

    // Should have section calls before summary calls
    const sectionCalls = apiCalls.filter(url => url.includes('/section/'))
    const summaryCalls = apiCalls.filter(url => url.includes('/summarize'))

    console.log(`Section calls: ${sectionCalls.length}`)
    console.log(`Summary calls: ${summaryCalls.length}`)

    // Verify that sections loaded before summaries (if both exist)
    if (sectionCalls.length > 0 && summaryCalls.length > 0) {
      const firstSectionIndex = apiCalls.findIndex(url => url.includes('/section/'))
      const firstSummaryIndex = apiCalls.findIndex(url => url.includes('/summarize'))

      expect(firstSectionIndex).toBeLessThan(firstSummaryIndex)
      console.log('API call order is correct: sections before summaries ✓')
    }

    // Final verification: document should be in a stable state
    await page.waitForTimeout(10000) // Wait longer for everything to settle

    // No loading spinners should be visible anywhere
    const allLoadingSpinners = page.locator('svg.animate-spin')
    const spinnerCount = await allLoadingSpinners.count()
    expect(spinnerCount).toBe(0)

    console.log('Test completed successfully ✓')
  })

  test('should handle group status updates when child components are refreshed', async ({ page }) => {
    // Set longer timeout for this test too
    test.setTimeout(300000) // 5 minutes

    // This test verifies that group status updates correctly when child components change state

    // Mock API responses
    let refreshCount = 0
    page.route('**/api/report/**', route => {
      refreshCount++

      const mockResponse = {
        text: `<h2>Refreshed Content ${refreshCount}</h2><p>This content was refreshed at ${new Date().toISOString()}</p>`,
        citations: [],
      }

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse),
      })
    })

    // Create document using test utils - entity is already set
    await testUtils.createDocumentFromTemplate('EKO Report')
    await testUtils.waitForEditor()

    // Wait for initial loading to complete - much longer
    await page.waitForTimeout(30000)

    // Find a report group and section
    const reportGroup = page.locator('.report-group').first()
    const reportSection = reportGroup.locator('.report-section').first()

    await expect(reportGroup).toBeVisible()
    await expect(reportSection).toBeVisible()

    // Verify group is initially loaded - longer timeout
    await expect(reportGroup).toHaveClass(/border-green-200/, { timeout: 60000 })

    // Refresh a section to trigger state change
    const sectionMenuTrigger = reportSection.locator('[data-testid*="menu-trigger"]').first()

    if (await sectionMenuTrigger.isVisible()) {
      await sectionMenuTrigger.click()
      await page.click('text=Refresh')

      // Group should temporarily show loading state
      await expect(reportGroup).toHaveClass(/border-yellow-200|opacity-80/, { timeout: 30000 })

      // Wait for refresh to complete - longer
      await page.waitForTimeout(15000)

      // Group should return to loaded state
      await expect(reportGroup).toHaveClass(/border-green-200/, { timeout: 60000 })

      console.log('Group status correctly updated after section refresh ✓')
    }
  })
})
