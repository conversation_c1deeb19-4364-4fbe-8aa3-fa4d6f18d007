import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Report Section Loading Behavior', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should load content efficiently when document is created', async ({ page }) => {
    // Track API calls to report endpoints
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      // Mock successful response
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content</p>',
          citations: []
        })
      })
    })

    // Create a new document from template using TestUtils
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Wait for editor to be fully loaded
    await testUtils.waitForEditor()

    // Wait for initial loading to complete
    await page.waitForTimeout(3000)

    // Verify the document loaded successfully
    await expect(page.locator('.ProseMirror')).toBeVisible()

    // Count initial API calls
    const initialCallCount = apiCalls.length
    
    // Wait additional time to ensure no excessive calls are made
    await page.waitForTimeout(2000)
    
    // Should not have made significantly more calls
    expect(apiCalls.length).toBeLessThanOrEqual(initialCallCount + 2)
    
    console.log(`Made ${apiCalls.length} API calls during document load`)
  })

  test('should handle API calls during document loading', async ({ page }) => {
    // Track API calls and auto-save calls
    const apiCalls: string[] = []
    const saveCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content for entity</p>',
          citations: []
        })
      })
    })

    // Track document save calls
    page.route('**/api/documents/**', route => {
      saveCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({ success: true })
      })
    })

    // Create a new document using TestUtils
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Wait for initial load
    await page.waitForTimeout(2000)

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    
    // Verify API calls were made during loading
    expect(apiCalls.length).toBeGreaterThan(0)
    console.log(`Made ${apiCalls.length} API calls and ${saveCalls.length} save calls during document load`)
  })

  test('should load content with mocked API responses', async ({ page }) => {
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content for run</p>',
          citations: []
        })
      })
    })

    // Create a new document using TestUtils
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Wait for initial load
    await page.waitForTimeout(2000)

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // Verify that content loads with mocked responses
    expect(initialCallCount).toBeGreaterThan(0)
    
    // Verify document was created successfully
    await testUtils.waitForEditor()
    await expect(page.locator('.ProseMirror')).toBeVisible()
  })

  test('should not make excessive API calls during editor interactions', async ({ page }) => {
    const apiCalls: string[] = []
    
    page.route('**/api/report/**', route => {
      apiCalls.push(route.request().url())
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          text: '<p>Test report content</p>',
          citations: []
        })
      })
    })

    // Create a new document using TestUtils
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report')
    await testUtils.waitForEditor()

    // Wait for initial content to load
    await page.waitForTimeout(3000)
    const initialCallCount = apiCalls.length

    // Trigger editor interactions that shouldn't cause API calls
    await page.locator('.ProseMirror').click()
    await page.keyboard.type('Some additional text')
    await page.waitForTimeout(1000)

    // Should not have made significantly more API calls
    expect(apiCalls.length).toBeLessThanOrEqual(initialCallCount + 1)
    
    console.log(`API calls after interactions: ${apiCalls.length} (started with ${initialCallCount})`)
  })

  test('should handle document save operations efficiently', async ({ page }) => {
    const saveApiCalls: string[] = []
    
    page.route('**/api/documents/**', route => {
      if (route.request().method() === 'PATCH' || route.request().method() === 'PUT') {
        saveApiCalls.push(route.request().url())
      }
      route.fulfill({
        status: 200,
        body: JSON.stringify({ success: true })
      })
    })

    // Create a new document using TestUtils
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report')
    await testUtils.waitForEditor()

    // Add content that would trigger saves
    await page.locator('.ProseMirror').click()
    await page.keyboard.type('Content that should be saved')
    
    // Wait for auto-save to potentially trigger
    await page.waitForTimeout(2000)
    
    // Verify content was added
    await expect(page.locator('.ProseMirror')).toContainText('Content that should be saved')
    
    console.log(`Document save operations: ${saveApiCalls.length} calls`)
  })
})
