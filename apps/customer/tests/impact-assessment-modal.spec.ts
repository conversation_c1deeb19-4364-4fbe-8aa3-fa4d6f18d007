import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'
import { getTestEntity } from './test-config'

test.describe('Impact Assessment Modal Display', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('should display impact assessment in flag detail modal', async ({ page }) => {
    // Navigate to flags page
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}`);
    await page.waitForLoadState('networkidle');
    
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });
    
    // Click on the first flag to open detail modal
    const firstFlag = page.locator('[data-testid="flag-item"]').first();
    await firstFlag.click();
    
    // Wait for modal to open
    await expect(page.locator('[data-testid="flag-detail-modal"]')).toBeVisible({ timeout: 10000 });

    // Check for impact badge within the modal
    const modalImpactBadge = page.locator('[data-testid="flag-detail-modal"] [data-testid="impact-badge"]')
    await expect(modalImpactBadge).toBeVisible()
    
    // Check that the impact badge contains impact percentage
    const badgeText = await modalImpactBadge.textContent()
    expect(badgeText).toMatch(/\d+%/); // Should contain a percentage
  });

  test('should display collapsible impact assessment when available', async ({ page }) => {
    // Navigate to flags page
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}`);
    await page.waitForLoadState('networkidle');
    
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });
    
    // Find a flag with high impact (more likely to have impact assessment)
    const highImpactFlags = page.locator('[data-testid="flag-item"]').filter({
      hasText: /High Impact|Very High Impact/i
    });
    
    const flagCount = await highImpactFlags.count();
    if (flagCount === 0) {
      console.log('No high impact flags found, skipping test');
      test.skip();
      return;
    }
    
    // Click on the first high impact flag
    await highImpactFlags.first().click();
    
    // Wait for modal
    await expect(page.locator('[data-testid="flag-detail-modal"]')).toBeVisible({ timeout: 10000 });
    
    // Check if collapsible impact assessment exists
    const impactAssessment = page.locator('[data-testid="collapsible-impact-assessment"]');
    const assessmentCount = await impactAssessment.count();
    
    if (assessmentCount > 0) {
      // Impact assessment is available
      await expect(impactAssessment).toBeVisible();
      
      // Check that it's initially collapsed
      const content = page.locator('[data-testid="impact-assessment-content"]');
      await expect(content).not.toBeVisible();
      
      // Click to expand
      await page.locator('[data-testid="impact-assessment-toggle"]').click();
      
      // Now content should be visible
      await expect(content).toBeVisible();
      
      // Check for key sections
      await expect(content.locator('text=Net Impact Score')).toBeVisible();
      await expect(content.locator('text=Harm Assessment')).toBeVisible();
      await expect(content.locator('text=Benefit Assessment')).toBeVisible();
    } else {
      console.log('No impact assessment data available for this flag');
    }
  });

  test('should show dimension scores in expanded impact assessment', async ({ page }) => {
    // Navigate to flags page
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}`);
    await page.waitForLoadState('networkidle');
    
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });
    
    // Find and click a high impact flag
    const highImpactFlags = page.locator('[data-testid="flag-item"]').filter({
      hasText: /High Impact|Very High Impact/i
    });
    
    const flagCount = await highImpactFlags.count();
    if (flagCount === 0) {
      test.skip();
      return;
    }
    
    await highImpactFlags.first().click();
    await expect(page.locator('[data-testid="flag-detail-modal"]')).toBeVisible({ timeout: 10000 });
    
    // Check for impact assessment
    const impactAssessment = page.locator('[data-testid="collapsible-impact-assessment"]');
    if (await impactAssessment.count() === 0) {
      test.skip();
      return;
    }
    
    // Expand the assessment
    await page.locator('[data-testid="impact-assessment-toggle"]').click();
    const content = page.locator('[data-testid="impact-assessment-content"]');
    await expect(content).toBeVisible();
    
    // Check for dimension labels
    const dimensions = ['Animals', 'Humans', 'Environment'];
    for (const dimension of dimensions) {
      const dimensionElements = content.locator(`text=${dimension}`);
      const count = await dimensionElements.count();
      expect(count).toBeGreaterThanOrEqual(1); // Should appear in harm and/or benefit sections
    }
    
    // Check for percentage scores (should have multiple percentage values)
    const percentages = content.locator('text=/\\d+%/');
    const percentageCount = await percentages.count();
    expect(percentageCount).toBeGreaterThan(0);
    
    // Check for confidence badges
    const confidenceBadges = content.locator('text=/high confidence|medium confidence|low confidence/i');
    const confidenceCount = await confidenceBadges.count();
    expect(confidenceCount).toBeGreaterThan(0);
  });

  test('should show quality metrics when available', async ({ page }) => {
    // Navigate to flags page
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}`);
    await page.waitForLoadState('networkidle');
    
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });
    
    // Click on first flag
    await page.locator('[data-testid="flag-item"]').first().click();
    await expect(page.locator('[data-testid="flag-detail-modal"]')).toBeVisible({ timeout: 10000 });
    
    // Check for impact assessment
    const impactAssessment = page.locator('[data-testid="collapsible-impact-assessment"]');
    if (await impactAssessment.count() === 0) {
      test.skip();
      return;
    }
    
    // Expand assessment
    await page.locator('[data-testid="impact-assessment-toggle"]').click();
    const content = page.locator('[data-testid="impact-assessment-content"]');
    await expect(content).toBeVisible();
    
    // Check if quality section exists
    const qualitySection = content.locator('text=Assessment Quality').locator('..');
    if (await qualitySection.count() > 0) {
      await expect(qualitySection).toBeVisible();
      
      // Check for quality metrics
      await expect(qualitySection.locator('text=Overall Quality:')).toBeVisible();
      await expect(qualitySection.locator('text=/excellent|good|fair|poor/')).toBeVisible();
      
      // Check for completeness score
      const completenessText = qualitySection.locator('text=Completeness:');
      if (await completenessText.count() > 0) {
        await expect(completenessText).toBeVisible();
        // Should have a percentage after it
        const completenessParent = completenessText.locator('..');
        await expect(completenessParent).toContainText(/%/);
      }
    }
  });

  test('should properly style harm vs benefit sections', async ({ page }) => {
    // Navigate to flags page
    await page.goto(`/customer/dashboard/flags?entity=${getTestEntity()}`);
    await page.waitForLoadState('networkidle');
    
    // Wait for flags to load
    await expect(page.locator('[data-testid="flags-list"]')).toBeVisible({ timeout: 30000 });
    
    // Click on first flag
    await page.locator('[data-testid="flag-item"]').first().click();
    await expect(page.locator('[data-testid="flag-detail-modal"]')).toBeVisible({ timeout: 10000 });
    
    // Check for impact assessment
    const impactAssessment = page.locator('[data-testid="collapsible-impact-assessment"]');
    if (await impactAssessment.count() === 0) {
      test.skip();
      return;
    }
    
    // Expand assessment
    await page.locator('[data-testid="impact-assessment-toggle"]').click();
    const content = page.locator('[data-testid="impact-assessment-content"]');
    await expect(content).toBeVisible();
    
    // Check harm section styling
    const harmSection = content.locator('text=Harm Assessment').first();
    const harmClasses = await harmSection.getAttribute('class');
    expect(harmClasses).toMatch(/text-red-/); // Should have red text styling
    
    // Check benefit section styling
    const benefitSection = content.locator('text=Benefit Assessment').first();
    const benefitClasses = await benefitSection.getAttribute('class');
    expect(benefitClasses).toMatch(/text-green-/); // Should have green text styling
    
    // Check for colored borders
    const redBorders = content.locator('.border-red-200');
    const greenBorders = content.locator('.border-green-200');
    
    // Should have some red and green borders
    expect(await redBorders.count()).toBeGreaterThan(0);
    expect(await greenBorders.count()).toBeGreaterThan(0);
  });
});
