import { expect, test } from '@playwright/test'
import { ReportContentMachineManager } from '../components/editor/services/state/reportContentMachine'

test.describe('Report Content Machine - Duplicate Prevention', () => {
  let manager: ReportContentMachineManager

  test.beforeEach(() => {
    manager = new ReportContentMachineManager()
  })

  test.afterEach(() => {
    manager.cleanup()
  })

  test('prevents duplicate content insertion', async () => {
    const componentId = 'test-section-1'
    const testContent = 'This is test content that should only be inserted once.'
    
    // Create a machine for a report section
    const machine = manager.createMachine(componentId, {
      componentType: 'report-section',
      endpoint: '/api/test',
      headings: 'remove'
    })

    // Initialize the machine
    manager.send(componentId, {
      type: 'INITIALIZE',
      componentId,
      componentType: 'report-section',
      config: {}
    })

    // Simulate content loaded
    manager.send(componentId, {
      type: 'CONTENT_LOADED',
      content: testContent
    })

    // Check state after content loaded
    let state = manager.getContentState(componentId)
    expect(state?.hasContent).toBe(true)
    expect(state?.contentInserted).toBe(false)
    expect(state?.canInsert).toBe(true)

    // Mock editor and getPos
    const mockEditor = {
      state: { doc: { nodeAt: () => ({ nodeSize: 10 }) } },
      commands: { insertContentAt: () => {} }
    }
    const mockGetPos = () => 100

    // First insertion attempt - should succeed
    manager.send(componentId, {
      type: 'INSERT_CONTENT',
      editor: mockEditor,
      getPos: mockGetPos
    })

    // Wait for insertion to complete
    await new Promise(resolve => setTimeout(resolve, 100))

    // Check state after insertion
    state = manager.getContentState(componentId)
    expect(state?.contentInserted).toBe(true)
    expect(state?.canInsert).toBe(false) // Should not allow insertion anymore

    // Second insertion attempt - should be prevented
    manager.send(componentId, {
      type: 'INSERT_CONTENT',
      editor: mockEditor,
      getPos: mockGetPos
    })

    // State should remain the same
    state = manager.getContentState(componentId)
    expect(state?.contentInserted).toBe(true)
    expect(state?.context.insertionHistory).toHaveLength(1) // Only one insertion
  })

  test('allows refresh and re-insertion with new content', async () => {
    const componentId = 'test-section-2'
    const content1 = 'First version of content'
    const content2 = 'Second version of content after refresh'
    
    const machine = manager.createMachine(componentId, {
      componentType: 'report-section',
      endpoint: '/api/test'
    })

    manager.send(componentId, {
      type: 'INITIALIZE',
      componentId,
      componentType: 'report-section',
      config: {}
    })

    // Load and insert first content
    manager.send(componentId, {
      type: 'CONTENT_LOADED',
      content: content1
    })

    const mockEditor = {
      state: { doc: { nodeAt: () => ({ nodeSize: 10 }) } },
      commands: { insertContentAt: () => {} }
    }
    const mockGetPos = () => 100

    manager.send(componentId, {
      type: 'INSERT_CONTENT',
      editor: mockEditor,
      getPos: mockGetPos
    })

    await new Promise(resolve => setTimeout(resolve, 100))

    // Verify first insertion
    let state = manager.getContentState(componentId)
    expect(state?.contentInserted).toBe(true)
    expect(state?.context.insertionHistory).toHaveLength(1)

    // Refresh the content
    manager.send(componentId, { type: 'REFRESH' })

    // Load new content
    manager.send(componentId, {
      type: 'CONTENT_LOADED',
      content: content2
    })

    // Should be able to insert new content after refresh
    state = manager.getContentState(componentId)
    expect(state?.hasContent).toBe(true)
    expect(state?.contentInserted).toBe(false) // Reset after refresh
    expect(state?.canInsert).toBe(true)

    // Insert new content
    manager.send(componentId, {
      type: 'INSERT_CONTENT',
      editor: mockEditor,
      getPos: mockGetPos
    })

    await new Promise(resolve => setTimeout(resolve, 100))

    // Verify second insertion
    state = manager.getContentState(componentId)
    expect(state?.contentInserted).toBe(true)
    expect(state?.context.insertionHistory).toHaveLength(2) // Two different contents inserted
  })

  test('prevents insertion when locked or preserved', async () => {
    const componentId = 'test-section-3'
    
    const machine = manager.createMachine(componentId, {
      componentType: 'report-section',
      locked: true
    })

    manager.send(componentId, {
      type: 'INITIALIZE',
      componentId,
      componentType: 'report-section',
      config: {}
    })

    // Lock the component
    manager.send(componentId, { type: 'LOCK' })

    // Try to load content - should not work when locked
    manager.send(componentId, {
      type: 'CONTENT_LOADED',
      content: 'This should not be insertable'
    })

    const state = manager.getContentState(componentId)
    expect(state?.state).toBe('locked')
    expect(state?.canInsert).toBe(false)
  })

  test('summary waits for all dependencies before generating', async () => {
    const summaryId = 'test-summary-1'
    const dep1Id = 'dep-1'
    const dep2Id = 'dep-2'
    
    const machine = manager.createMachine(summaryId, {
      componentType: 'report-summary',
      dependencies: [dep1Id, dep2Id],
      prompt: 'Summarize the content'
    })

    manager.send(summaryId, {
      type: 'INITIALIZE',
      componentId: summaryId,
      componentType: 'report-summary',
      config: {
        dependencies: [dep1Id, dep2Id]
      }
    })

    // Check initial state
    let state = manager.getContentState(summaryId)
    expect(state?.state).toBe('waitingForDependencies')

    // Update with partial dependencies
    const partialDeps = new Map([
      [dep1Id, 'Content from dependency 1']
    ])
    
    manager.send(summaryId, {
      type: 'DEPENDENCIES_UPDATED',
      dependencies: partialDeps
    })

    // Try to summarize - should not work yet
    manager.send(summaryId, { type: 'SUMMARIZE' })
    
    state = manager.getContentState(summaryId)
    expect(state?.state).toBe('waitingForDependencies') // Still waiting

    // Update with all dependencies
    const allDeps = new Map([
      [dep1Id, 'Content from dependency 1'],
      [dep2Id, 'Content from dependency 2']
    ])
    
    manager.send(summaryId, {
      type: 'DEPENDENCIES_UPDATED',
      dependencies: allDeps
    })

    // Now summarize should work
    manager.send(summaryId, { type: 'SUMMARIZE' })
    
    // State should change to generating
    state = manager.getContentState(summaryId)
    expect(['generatingSummary', 'contentLoaded', 'error']).toContain(state?.state)
  })

  test('tracks insertion history to prevent duplicate hash insertion', async () => {
    const componentId = 'test-section-4'
    const duplicateContent = 'This exact content should only be inserted once'
    
    const machine = manager.createMachine(componentId, {
      componentType: 'report-section'
    })

    manager.send(componentId, {
      type: 'INITIALIZE',
      componentId,
      componentType: 'report-section',
      config: {}
    })

    const mockEditor = {
      state: { doc: { nodeAt: () => ({ nodeSize: 10 }) } },
      commands: { insertContentAt: () => {} }
    }
    const mockGetPos = () => 100

    // Load and insert content
    manager.send(componentId, {
      type: 'CONTENT_LOADED',
      content: duplicateContent
    })

    manager.send(componentId, {
      type: 'INSERT_CONTENT',
      editor: mockEditor,
      getPos: mockGetPos
    })

    await new Promise(resolve => setTimeout(resolve, 100))

    // Clear and reload the same content
    manager.send(componentId, { type: 'CLEAR_CONTENT' })
    
    manager.send(componentId, {
      type: 'CONTENT_LOADED',
      content: duplicateContent // Same content
    })

    // Should be able to insert again after CLEAR_CONTENT (new session)
    let state = manager.getContentState(componentId)
    expect(state?.hasContent).toBe(true)
    expect(state?.contentInserted).toBe(false)
    expect(state?.canInsert).toBe(true) // Should allow insertion in new session
  })
})
