import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';
import { getTestEntity, getTestModel } from './test-config';

test.describe('Dashboard Promises Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to promises page with test entity
    await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');
  });

  test('should display promises page with filtering capabilities', async ({ page }) => {
    // Wait for promises page to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for filter controls
    await expect(page.locator('[data-testid="promises-filters"]')).toBeVisible();

    // Check for promise status filter (broken vs kept)
    const statusFilter = page.locator('[data-testid="promise-status-filter"]');
    await expect(statusFilter).toBeVisible();
    await statusFilter.click();
    
    // Verify filter options
    await expect(page.locator('[data-testid="filter-option-all-promises"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-option-kept-promises"]')).toBeVisible();
    await expect(page.locator('[data-testid="filter-option-broken-promises"]')).toBeVisible();
  });

  test('should display broken vs kept promises visualization', async ({ page }) => {
    // Wait for promises data to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for promises summary visualization
    const promisesSummary = page.locator('[data-testid="promises-summary"]');
    await expect(promisesSummary).toBeVisible();
    
    // Check broken promises count
    await expect(page.locator('[data-testid="broken-promises-count"]')).toBeVisible();
    
    // Check kept promises count
    await expect(page.locator('[data-testid="kept-promises-count"]')).toBeVisible();

    // Verify counts are numeric
    const brokenCount = await page.locator('[data-testid="broken-promises-count"]').textContent();
    const keptCount = await page.locator('[data-testid="kept-promises-count"]').textContent();
    
    expect(brokenCount).toMatch(/^\d+$/);
    expect(keptCount).toMatch(/^\d+$/);

    // Check for visual chart/graph
    const promisesChart = page.locator('[data-testid="promises-five-year-chart"]');
    await expect(promisesChart).toBeVisible();
    
    // Check chart has summary statistics (these are always visible)
    const chartSummaryElements = promisesChart.locator('.text-2xl');
    await expect(chartSummaryElements.first()).toBeVisible();
  });

  test('should filter promises by status (broken/kept)', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    // Test broken promises filter
    const statusFilter = page.locator('[data-testid="promise-status-filter"]');
    await expect(statusFilter).toBeVisible();
    await statusFilter.click();
    await page.click('[data-testid="filter-option-broken-promises"]');
    await page.waitForLoadState('networkidle');

    // Verify only broken promises are displayed
    const promiseItems = page.locator('[data-testid="promise-item"]');
    await expect(promiseItems.first()).toBeVisible();
    
    const promiseStatuses = await promiseItems.locator('[data-testid="promise-status"]').allTextContents();
    
    for (const status of promiseStatuses) {
      expect(status.toLowerCase()).toMatch(/broken|failed|unfulfilled/);
    }

    // Test kept promises filter
    await statusFilter.click();
    await page.click('[data-testid="filter-option-kept-promises"]');
    await page.waitForLoadState('networkidle');

    // Verify only kept promises are displayed
    const newPromiseItems = page.locator('[data-testid="promise-item"]');
    await expect(newPromiseItems.first()).toBeVisible();
    
    const newPromiseStatuses = await newPromiseItems.locator('[data-testid="promise-status"]').allTextContents();
    
    for (const status of newPromiseStatuses) {
      expect(status.toLowerCase()).toMatch(/kept|fulfilled|achieved/);
    }
  });

  test('should display promise details correctly', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    await expect(promiseItems.first()).toBeVisible();
    
    const firstPromise = promiseItems.first();

    // Check promise content elements
    await expect(firstPromise.locator('[data-testid="promise-title"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-content"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-status"]')).toBeVisible();
    await expect(firstPromise.locator('[data-testid="promise-date"]')).toBeVisible();

    // Check promise metadata
    const promiseConfidence = firstPromise.locator('[data-testid="promise-confidence"]');
    await expect(promiseConfidence).toBeVisible();
    const confidenceText = await promiseConfidence.textContent();
    expect(confidenceText).toMatch(/\d+%|\d+\.\d+/);

    // Check promise target date or timeline
    const promiseTargetDate = firstPromise.locator('[data-testid="promise-target-date"]');
    await expect(promiseTargetDate).toBeVisible();

    // Check promise evidence or assessment
    const promiseEvidence = firstPromise.locator('[data-testid="promise-evidence"]');
    await expect(promiseEvidence).toBeVisible();
  });

  test('should show promise detail modal when clicked', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    await expect(promiseItems.first()).toBeVisible();
    
    const firstPromise = promiseItems.first();
    
    // Click on promise to view details
    await firstPromise.click();

    // Verify modal opens
    await expect(page.locator('[data-testid="promise-detail-modal"]')).toBeVisible({ timeout: 10000 });
    
    // Verify modal content
    await expect(page.locator('[data-testid="modal-promise-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="modal-promise-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="modal-promise-status"]')).toBeVisible();
    await expect(page.locator('[data-testid="modal-promise-assessment"]')).toBeVisible();

    // Test close functionality
    const closeButton = page.locator('[data-testid="modal-close-button"]');
    await expect(closeButton).toBeVisible();
    await closeButton.click();

    // Verify modal closes
    await expect(page.locator('[data-testid="promise-detail-modal"]')).not.toBeVisible();
  });

  test('should display promise timeline and assessment history', async ({ page }) => {
    // Wait for promises page to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Check for promise timeline component
    const promiseTimeline = page.locator('[data-testid="promise-timeline"]');
    await expect(promiseTimeline).toBeVisible();

    // Check timeline entries
    const timelineEntries = promiseTimeline.locator('[data-testid="timeline-entry"]');
    await expect(timelineEntries.first()).toBeVisible();
    const firstEntry = timelineEntries.first();
    await expect(firstEntry.locator('[data-testid="timeline-date"]')).toBeVisible();
    await expect(firstEntry.locator('[data-testid="timeline-event"]')).toBeVisible();

    // Check for assessment history
    const assessmentHistory = page.locator('[data-testid="promise-assessment-history"]');
    await expect(assessmentHistory).toBeVisible();

    // Check assessment entries
    const assessmentEntries = assessmentHistory.locator('[data-testid="assessment-entry"]');
    await expect(assessmentEntries.first()).toBeVisible();
    const firstAssessment = assessmentEntries.first();
    await expect(firstAssessment.locator('[data-testid="assessment-date"]')).toBeVisible();
    await expect(firstAssessment.locator('[data-testid="assessment-status"]')).toBeVisible();
  });

  test('should handle loading states appropriately', async ({ page }) => {
    // Navigate to promises page
    await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);

    // Wait for content to load
    await page.waitForLoadState('networkidle');
    
    // Content should be visible
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });
  });

  test('should sort promises by relevance or date', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    // Check for sort controls
    const sortControl = page.locator('[data-testid="promises-sort"]');
    await expect(sortControl).toBeVisible();
    await sortControl.click();

    // Test sorting by date
    const sortByDate = page.locator('[data-testid="sort-by-date"]');
    await expect(sortByDate).toBeVisible();
    await sortByDate.click();
    await page.waitForLoadState('networkidle');

    // Verify promises are sorted by date
    const promiseDates = await page.locator('[data-testid="promise-date"]').allTextContents();
    expect(promiseDates.length).toBeGreaterThan(0);

    // Test sorting by relevance
    await sortControl.click();
    const sortByRelevance = page.locator('[data-testid="sort-by-relevance"]');
    await expect(sortByRelevance).toBeVisible();
    await sortByRelevance.click();
    await page.waitForLoadState('networkidle');
    
    // Verify sorting changed
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible();
  });

  test('should display promise categories or types', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    await expect(promiseItems.first()).toBeVisible();
    
    const firstPromise = promiseItems.first();

    // Check for promise category/type
    const promiseCategory = firstPromise.locator('[data-testid="promise-category"]');
    await expect(promiseCategory).toBeVisible();
    
    const categoryText = await promiseCategory.textContent();
    expect(categoryText).toBeTruthy();

    // Check for promise theme (environmental, social, governance)
    const promiseTheme = firstPromise.locator('[data-testid="promise-theme"]');
    await expect(promiseTheme).toBeVisible();
  });

  test('should handle empty states gracefully', async ({ page }) => {
    // Try with very restrictive filters or invalid entity
    await page.goto(`/customer/dashboard/gw/promises?entity=invalid-entity&model=${getTestModel()}`);
    await page.waitForLoadState('networkidle');

    // Wait for content to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });
    
    // Check for empty state message or list
    await expect(page.locator('[data-testid="no-promises-message"], [data-testid="empty-state"], [data-testid="promises-list"]')).toBeVisible({ timeout: 15000 });
  });

  test('should maintain filter state when navigating', async ({ page }) => {
    // Wait for promises page to load
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });

    // Apply broken promises filter
    const statusFilter = page.locator('[data-testid="promise-status-filter"]');
    await expect(statusFilter).toBeVisible();
    await statusFilter.click();
    await page.click('[data-testid="filter-option-broken-promises"]');
    await page.waitForLoadState('networkidle');

    // Navigate away and back
    await page.goto('/customer/dashboard');
    await page.waitForLoadState('networkidle');
    
    await page.goBack();
    await page.waitForLoadState('networkidle');

    // Verify we're back on promises page
    await expect(page.locator('[data-testid="promises-page-content"]')).toBeVisible({ timeout: 30000 });
  });

  test('should display promise sources and references', async ({ page }) => {
    // Wait for promises to load
    await expect(page.locator('[data-testid="promises-list"]')).toBeVisible({ timeout: 30000 });

    const promiseItems = page.locator('[data-testid="promise-item"]');
    await expect(promiseItems.first()).toBeVisible();
    
    const firstPromise = promiseItems.first();

    // Check for source information
    const promiseSource = firstPromise.locator('[data-testid="promise-source"]');
    await expect(promiseSource).toBeVisible();

    // Check for document references
    const promiseReferences = firstPromise.locator('[data-testid="promise-references"]');
    await expect(promiseReferences).toBeVisible();

    // Check for external links or citations
    const promiseCitations = firstPromise.locator('[data-testid="promise-citations"]');
    await expect(promiseCitations).toBeVisible();
  });
});
