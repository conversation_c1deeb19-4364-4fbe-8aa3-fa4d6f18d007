import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';

test.describe('Report Status Indicators Sync', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('summary component should wait for dependencies before loading', async ({ page }) => {
    await testUtils.createDocumentFromTemplate();
    
    // Create the ecological impact structure as shown in the image
    await testUtils.addReportComponent('group', {
      id: 'ecological-impact',
      title: 'Ecological Impact'
    });
    
    // Add individual sections first
    await testUtils.addReportComponent('section', {
      id: 'ecological-eco_01_climate',
      title: 'Climate & Emissions'
    });
    
    await testUtils.addReportComponent('section', {
      id: 'ecological-eco_02_energy',
      title: 'Energy'
    });
    
    await testUtils.addReportComponent('section', {
      id: 'ecological-eco_03_water',
      title: 'Water'
    });
    
    // Add summary component that should depend on the sections
    await testUtils.addReportComponent('summary', {
      id: 'ecological-summary',
      title: 'Ecological Summary'
    });
    
    // Wait for components to be rendered
    await page.waitForTimeout(2000);
    
    // Verify all components exist
    const summaryComponent = page.locator('[data-id="ecological-summary"]');
    const climateSection = page.locator('[data-id="ecological-eco_01_climate"]');
    const energySection = page.locator('[data-id="ecological-eco_02_energy"]');
    const waterSection = page.locator('[data-id="ecological-eco_03_water"]');
    
    await expect(summaryComponent).toBeVisible();
    await expect(climateSection).toBeVisible();
    await expect(energySection).toBeVisible();
    await expect(waterSection).toBeVisible();
    
    // EXPECTED BEHAVIOR: Summary should show "Waiting for dependencies..." 
    // when its dependent sections are not yet loaded
    const summaryText = await summaryComponent.textContent();
    expect(summaryText).toContain('Waiting for dependencies...');
    
    // Summary status should be 'waiting' when dependencies are not ready
    const summaryStatus = await summaryComponent.getAttribute('data-status');
    expect(summaryStatus).toBe('waiting');
    
    // Individual sections should start in 'idle' state
    const climateStatus = await climateSection.getAttribute('data-status');
    const energyStatus = await energySection.getAttribute('data-status');
    const waterStatus = await waterSection.getAttribute('data-status');
    
    expect(climateStatus).toBe('idle');
    expect(energyStatus).toBe('idle');
    expect(waterStatus).toBe('idle');
  });

  test('progress indicators should accurately reflect component completion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate();
    
    // Create a group with multiple sections to test progress counting
    await testUtils.addReportComponent('group', {
      id: 'test-group',
      title: 'Test Group'
    });
    
    // Add 4 sections to have clear progress tracking
    for (let i = 1; i <= 4; i++) {
      await testUtils.addReportComponent('section', {
        id: `section-${i}`,
        title: `Section ${i}`
      });
    }
    
    await page.waitForTimeout(1000);
    
    // EXPECTED BEHAVIOR: Progress should show 0/4 initially
    // Look for progress indicators that show completion counts
    const progressText = await page.locator('text=/\\d+\/\\d+/').first().textContent();
    expect(progressText).toMatch(/0\/4|0 of 4/);
    
    // Simulate loading the first section
    await testUtils.performComponentAction('refresh', '[data-id="section-1"]');
    await testUtils.waitForComponentLoading('[data-id="section-1"]');
    
    // EXPECTED BEHAVIOR: After first section loads, progress should show 1/4
    const section1 = page.locator('[data-id="section-1"]');
    await expect(section1).toHaveAttribute('data-status', 'loaded');
    
    // Progress indicator should update to reflect the completed section
    const updatedProgressText = await page.locator('text=/\\d+\/\\d+/').first().textContent();
    expect(updatedProgressText).toMatch(/1\/4|1 of 4/);
    
    // Visual progress bar should show 25% completion
    const progressBar = page.locator('[role="progressbar"], [aria-valuenow]').first();
    if (await progressBar.count() > 0) {
      const progressValue = await progressBar.getAttribute('aria-valuenow');
      expect(parseInt(progressValue || '0')).toBe(25);
    }
  });

  test('dependency chain should load in correct order', async ({ page }) => {
    await testUtils.createDocumentFromTemplate();
    
    // Create a dependency chain: sections -> summary -> overview
    await testUtils.addReportComponent('section', {
      id: 'base-section',
      title: 'Base Section'
    });
    
    await testUtils.addReportComponent('summary', {
      id: 'mid-summary',
      title: 'Mid Summary'
    });
    
    await testUtils.addReportComponent('summary', {
      id: 'top-overview',
      title: 'Top Overview'
    });
    
    await page.waitForTimeout(1000);
    
    // EXPECTED BEHAVIOR: Dependencies should be respected in loading order
    // Top overview should wait for mid summary
    // Mid summary should wait for base section
    
    const baseSection = page.locator('[data-id="base-section"]');
    const midSummary = page.locator('[data-id="mid-summary"]');
    const topOverview = page.locator('[data-id="top-overview"]');
    
    // Initially, all should be idle or waiting
    const baseSectionStatus = await baseSection.getAttribute('data-status');
    const midSummaryStatus = await midSummary.getAttribute('data-status');
    const topOverviewStatus = await topOverview.getAttribute('data-status');
    
    expect(['idle', 'waiting']).toContain(baseSectionStatus);
    expect(['idle', 'waiting']).toContain(midSummaryStatus);
    expect(['idle', 'waiting']).toContain(topOverviewStatus);
    
    // Summary components should show waiting text when dependencies not ready
    const midSummaryText = await midSummary.textContent();
    const topOverviewText = await topOverview.textContent();
    
    if (baseSectionStatus !== 'loaded') {
      expect(midSummaryText).toContain('Waiting for dependencies...');
    }
    
    if (midSummaryStatus !== 'loaded') {
      expect(topOverviewText).toContain('Waiting for dependencies...');
    }
  });

  test('error states should propagate correctly through dependency chain', async ({ page }) => {
    await testUtils.createDocumentFromTemplate();
    
    // Create components with dependency relationship
    await testUtils.addReportComponent('section', {
      id: 'error-section',
      title: 'Error Section'
    });
    
    await testUtils.addReportComponent('summary', {
      id: 'dependent-summary',
      title: 'Dependent Summary'
    });
    
    await page.waitForTimeout(1000);
    
    // Mock an error for the section
    await testUtils.mockApiError('/api/report/section');
    
    // Trigger loading of the error section
    await testUtils.performComponentAction('refresh', '[data-id="error-section"]');
    
    // Wait for error state to propagate
    await page.waitForTimeout(2000);
    
    // EXPECTED BEHAVIOR: Section should be in error state
    const errorSection = page.locator('[data-id="error-section"]');
    const errorStatus = await errorSection.getAttribute('data-status');
    expect(errorStatus).toBe('error');
    
    // Error section should show appropriate visual indicator
    const hasErrorIcon = await errorSection.locator('svg.text-red-600, .text-red-600 svg').count() > 0;
    expect(hasErrorIcon).toBe(true);
    
    // EXPECTED BEHAVIOR: Dependent summary should remain waiting
    // because its dependency failed, not load with empty/error data
    const dependentSummary = page.locator('[data-id="dependent-summary"]');
    const summaryStatus = await dependentSummary.getAttribute('data-status');
    expect(summaryStatus).toBe('waiting');
    
    const summaryText = await dependentSummary.textContent();
    expect(summaryText).toContain('Waiting for dependencies...');
  });

  test('visual indicators must match data status attributes', async ({ page }) => {
    await testUtils.createDocumentFromTemplate();
    
    // Add a component to test visual-data synchronization
    await testUtils.addReportComponent('section', {
      id: 'test-section',
      title: 'Test Section'
    });
    
    await page.waitForTimeout(1000);
    
    const testSection = page.locator('[data-id="test-section"]');
    
    // EXPECTED BEHAVIOR: Visual indicators must accurately reflect data-status
    const currentStatus = await testSection.getAttribute('data-status');
    
    // Check that visual indicators match the status
    const hasSpinner = await testSection.locator('.animate-spin, svg.animate-spin').count() > 0;
    const hasCheckmark = await testSection.locator('svg.text-green-600, .text-green-600 svg').count() > 0;
    const hasErrorIcon = await testSection.locator('svg.text-red-600, .text-red-600 svg').count() > 0;
    const hasWaitingIcon = await testSection.locator('svg.text-yellow-600, .text-yellow-600 svg').count() > 0;
    
    // Verify visual indicators match status
    if (currentStatus === 'loading') {
      expect(hasSpinner).toBe(true);
      expect(hasCheckmark).toBe(false);
      expect(hasErrorIcon).toBe(false);
    } else if (currentStatus === 'loaded') {
      expect(hasSpinner).toBe(false);
      expect(hasCheckmark).toBe(true);
      expect(hasErrorIcon).toBe(false);
    } else if (currentStatus === 'error') {
      expect(hasSpinner).toBe(false);
      expect(hasCheckmark).toBe(false);
      expect(hasErrorIcon).toBe(true);
    } else if (currentStatus === 'waiting') {
      expect(hasSpinner).toBe(false);
      expect(hasCheckmark).toBe(false);
      expect(hasErrorIcon).toBe(false);
      // Should show waiting indicator or "Waiting for dependencies..." text
      const hasWaitingText = (await testSection.textContent())?.includes('Waiting for dependencies...');
      expect(hasWaitingText || hasWaitingIcon).toBe(true);
    }
  });

  test('nested progress should roll up correctly', async ({ page }) => {
    await testUtils.createDocumentFromTemplate();
    
    // Create nested structure with clear hierarchy
    await testUtils.addReportComponent('group', {
      id: 'main-group',
      title: 'Main Group'
    });
    
    await testUtils.addReportComponent('group', {
      id: 'sub-group-1',
      title: 'Sub Group 1'
    });
    
    await testUtils.addReportComponent('section', {
      id: 'section-1a',
      title: 'Section 1A'
    });
    
    await testUtils.addReportComponent('section', {
      id: 'section-1b',
      title: 'Section 1B'
    });
    
    await page.waitForTimeout(1000);
    
    // EXPECTED BEHAVIOR: Progress should roll up through hierarchy
    // Sub-group should show 0/2 (0 of 2 sections complete)
    // Main group should show 0/1 (0 of 1 sub-group complete)
    
    // Check sub-group progress
    const subGroupProgress = await page.locator('text=/0\/2|0 of 2/').count();
    expect(subGroupProgress).toBeGreaterThan(0);
    
    // Check main group progress
    const mainGroupProgress = await page.locator('text=/0\/1|0 of 1/').count();
    expect(mainGroupProgress).toBeGreaterThan(0);
    
    // Simulate completing one section
    await testUtils.performComponentAction('refresh', '[data-id="section-1a"]');
    await testUtils.waitForComponentLoading('[data-id="section-1a"]');
    
    // EXPECTED BEHAVIOR: Sub-group should now show 1/2
    // But main group should still show 0/1 (sub-group not fully complete)
    const updatedSubGroupProgress = await page.locator('text=/1\/2|1 of 2/').count();
    expect(updatedSubGroupProgress).toBeGreaterThan(0);
    
    const unchangedMainGroupProgress = await page.locator('text=/0\/1|0 of 1/').count();
    expect(unchangedMainGroupProgress).toBeGreaterThan(0);
  });
});