import { test, expect } from '@playwright/test';
import { TestUtils } from './helpers/tests/test-utils';

test.describe('Account Billing Page', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page);
    await testUtils.login();
    
    // Navigate to billing page
    await page.goto('/customer/account/billing');
    // Wait for the billing page to load instead of networkidle
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });
  });

  test('should display billing page with contact information', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check for contact link or information
    const contactLink = page.locator('[data-testid="billing-contact-link"]');
    const contactInfo = page.locator('[data-testid="billing-contact-info"]');

    if (await contactLink.isVisible()) {
      await expect(contactLink).toBeVisible();
      
      // Verify contact link has proper attributes
      const href = await contactLink.getAttribute('href');
      expect(href).toBeTruthy();
      expect(href).toMatch(/\/customer\/account\/contact\/billing|mailto:|tel:|https?:/);
    }

    if (await contactInfo.isVisible()) {
      await expect(contactInfo).toBeVisible();
      
      // Check contact info contains relevant text
      const infoText = await contactInfo.textContent();
      expect(infoText?.toLowerCase()).toMatch(/billing|contact|support|inquiry/);
    }
  });

  test('should preserve query parameters correctly', async ({ page }) => {
    // Navigate with query parameters
    const testParams = '?plan=premium&source=upgrade';
    await page.goto(`/customer/account/billing${testParams}`);
    
    // Wait for billing page to load instead of networkidle
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check that URL still contains the query parameters
    const currentURL = page.url();
    expect(currentURL).toContain('plan=premium');
    expect(currentURL).toContain('source=upgrade');

    // If there are links on the page, they should preserve important parameters
    const internalLinks = page.locator('a[href*="/customer"]');
    if (await internalLinks.count() > 0) {
      const firstLink = internalLinks.first();
      const linkHref = await firstLink.getAttribute('href');
      
      // Internal navigation should maintain context where appropriate
      if (linkHref?.includes('billing') || linkHref?.includes('account')) {
        console.log('Internal billing link found:', linkHref);
      }
    }
  });

  test('should handle contact link interactions', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    const contactLink = page.locator('[data-testid="billing-contact-link"]');
    if (await contactLink.isVisible()) {
      const href = await contactLink.getAttribute('href');
      
     if (href?.includes('/customer/account/contact')) {
        // Internal contact page link
        await contactLink.click();
        
        // Should navigate to contact page
        try {
          await page.waitForURL('**/contact/**', { timeout: 10000 });
        } catch {
          console.log('Contact link navigation timed out or failed');
        }
      }
    }
  });

  test('should display billing inquiry form if available', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check for billing inquiry form
    const billingForm = page.locator('[data-testid="billing-inquiry-form"]');
    if (await billingForm.isVisible()) {
      await expect(billingForm).toBeVisible();

      // Check form fields
      await expect(billingForm.locator('[data-testid="inquiry-subject"]')).toBeVisible();
      await expect(billingForm.locator('[data-testid="inquiry-message"]')).toBeVisible();
      await expect(billingForm.locator('[data-testid="submit-inquiry"]')).toBeVisible();

      // Test form interaction
      await page.fill('[data-testid="inquiry-subject"]', 'Test Billing Inquiry');
      await page.fill('[data-testid="inquiry-message"]', 'This is a test message for billing support.');
      
      // Note: Don't actually submit to avoid spam
      const submitButton = page.locator('[data-testid="submit-inquiry"]');
      await expect(submitButton).toBeEnabled();
    }
  });

  test('should display current subscription information if available', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check for subscription details
    const subscriptionInfo = page.locator('[data-testid="subscription-info"]');
    if (await subscriptionInfo.isVisible()) {
      await expect(subscriptionInfo).toBeVisible();

      // Check subscription details
      const planName = subscriptionInfo.locator('[data-testid="current-plan"]');
      const billingCycle = subscriptionInfo.locator('[data-testid="billing-cycle"]');
      const nextBilling = subscriptionInfo.locator('[data-testid="next-billing-date"]');

      if (await planName.isVisible()) {
        await expect(planName).toBeVisible();
        const planText = await planName.textContent();
        expect(planText).toBeTruthy();
      }

      if (await billingCycle.isVisible()) {
        await expect(billingCycle).toBeVisible();
        const cycleText = await billingCycle.textContent();
        expect(cycleText?.toLowerCase()).toMatch(/monthly|annual|yearly/);
      }

      if (await nextBilling.isVisible()) {
        await expect(nextBilling).toBeVisible();
        const dateText = await nextBilling.textContent();
        expect(dateText).toMatch(/\d{4}|\w{3}|\d{1,2}/);
      }
    }
  });

  test('should display payment method information if available', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check for payment method section
    const paymentMethods = page.locator('[data-testid="payment-methods"]');
    if (await paymentMethods.isVisible()) {
      await expect(paymentMethods).toBeVisible();

      // Check for payment method cards
      const paymentCards = paymentMethods.locator('[data-testid="payment-method-card"]');
      if (await paymentCards.count() > 0) {
        const firstCard = paymentCards.first();
        
        // Check card details (should be masked for security)
        const cardNumber = firstCard.locator('[data-testid="card-number"]');
        const cardType = firstCard.locator('[data-testid="card-type"]');
        const expiryDate = firstCard.locator('[data-testid="card-expiry"]');

        if (await cardNumber.isVisible()) {
          const numberText = await cardNumber.textContent();
          expect(numberText).toMatch(/\*{4}|\*{8,12}\d{4}/); // Should be masked
        }

        if (await cardType.isVisible()) {
          const typeText = await cardType.textContent();
          expect(typeText?.toLowerCase()).toMatch(/visa|mastercard|amex|discover/);
        }

        if (await expiryDate.isVisible()) {
          const expiryText = await expiryDate.textContent();
          expect(expiryText).toMatch(/\d{2}\/\d{2,4}/); // MM/YY or MM/YYYY format
        }
      }

      // Check for add payment method button
      const addPaymentButton = page.locator('[data-testid="add-payment-method"]');
      if (await addPaymentButton.isVisible()) {
        await expect(addPaymentButton).toBeVisible();
      }
    }
  });

  test('should display billing history if available', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check for billing history section
    const billingHistory = page.locator('[data-testid="billing-history"]');
    if (await billingHistory.isVisible()) {
      await expect(billingHistory).toBeVisible();

      // Check for invoice entries
      const invoiceEntries = billingHistory.locator('[data-testid="invoice-entry"]');
      if (await invoiceEntries.count() > 0) {
        const firstInvoice = invoiceEntries.first();

        // Check invoice details
        await expect(firstInvoice.locator('[data-testid="invoice-date"]')).toBeVisible();
        await expect(firstInvoice.locator('[data-testid="invoice-amount"]')).toBeVisible();
        await expect(firstInvoice.locator('[data-testid="invoice-status"]')).toBeVisible();

        // Check for download link
        const downloadLink = firstInvoice.locator('[data-testid="download-invoice"]');
        if (await downloadLink.isVisible()) {
          await expect(downloadLink).toBeVisible();
          
          // Verify download link has proper attributes
          const href = await downloadLink.getAttribute('href');
          expect(href).toBeTruthy();
        }
      }
    }
  });

  test('should handle upgrade/downgrade options if available', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check for plan management section
    const planManagement = page.locator('[data-testid="plan-management"]');
    if (await planManagement.isVisible()) {
      await expect(planManagement).toBeVisible();

      // Check for upgrade button
      const upgradeButton = page.locator('[data-testid="upgrade-plan"]');
      if (await upgradeButton.isVisible()) {
        await expect(upgradeButton).toBeVisible();
        
        // Click should navigate to upgrade flow
        await upgradeButton.click();
        
        try {
          await page.waitForURL('**/upgrade**', { timeout: 10000 });
        } catch {
          // Might open modal or external link
          const upgradeModal = page.locator('[data-testid="upgrade-modal"]');
          if (await upgradeModal.isVisible()) {
            await expect(upgradeModal).toBeVisible();
            
            // Close modal
            const closeButton = upgradeModal.locator('[data-testid="close-modal"]');
            if (await closeButton.isVisible()) {
              await closeButton.click();
            }
          }
        }
      }

      // Check for downgrade options
      const downgradeButton = page.locator('[data-testid="downgrade-plan"]');
      if (await downgradeButton.isVisible()) {
        await expect(downgradeButton).toBeVisible();
      }
    }
  });

  test('should handle loading states appropriately', async ({ page }) => {
    // Navigate to billing page
    await page.goto('/customer/account/billing');

    // Check for loading indicators
    const loadingIndicators = page.locator('[data-testid*="loading"], [class*="loading"], [class*="spinner"]');
    
    try {
      // Loading indicators should appear initially
      await expect(loadingIndicators.first()).toBeVisible({ timeout: 5000 });
      
      // Then disappear as content loads
      await expect(loadingIndicators.first()).not.toBeVisible({ timeout: 30000 });
      
      // Content should be visible
      await expect(page.locator('[data-testid="billing-page"]')).toBeVisible();
    } catch {
      // If loading is very fast, content should be immediately visible
      await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });
    }
  });

  test('should handle navigation back to account settings', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check for back to account link
    const backToAccountLink = page.locator('[data-testid="back-to-account"], [data-testid="account-nav"]');
    if (await backToAccountLink.isVisible()) {
      await backToAccountLink.click();
      
      // Should navigate back to account page
      await page.waitForURL('**/account**', { timeout: 10000 });
      await expect(page.locator('[data-testid="account-page"]')).toBeVisible();
    } else {
      // Manual navigation test
      await page.goto('/customer/account');
      await expect(page.locator('[data-testid="account-page"]')).toBeVisible({ timeout: 30000 });
    }
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Wait for billing page to load
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible({ timeout: 30000 });

    // Check for error messages or states
    const errorMessage = page.locator('[data-testid="billing-error"], [data-testid="error-message"]');
    const noDataMessage = page.locator('[data-testid="no-billing-data"], [data-testid="empty-state"]');

    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toBeVisible();
      const errorText = await errorMessage.textContent();
      expect(errorText?.toLowerCase()).toMatch(/error|unavailable|failed/);
    }

    if (await noDataMessage.isVisible()) {
      await expect(noDataMessage).toBeVisible();
      const messageText = await noDataMessage.textContent();
      expect(messageText?.toLowerCase()).toMatch(/no.*billing|not.*available|contact/);
    }

    // Page should still be functional even with errors
    await expect(page.locator('[data-testid="billing-page"]')).toBeVisible();
  });
});
