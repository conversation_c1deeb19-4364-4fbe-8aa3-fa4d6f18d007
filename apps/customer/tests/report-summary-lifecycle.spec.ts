import { test, expect } from '@playwright/test'
import { reportContentMachine, ReportContentMachineManager } from '../components/editor/services/state/reportContentMachine'
import { createActor } from 'xstate'

test.describe('Report Summary Lifecycle Management', () => {
  let manager: ReportContentMachineManager

  test.beforeEach(() => {
    manager = new ReportContentMachineManager()
  })

  test.afterEach(() => {
    manager.cleanup()
  })

  test('should handle stopped actors gracefully when sending DEPENDENCIES_UPDATED', async () => {
    const summaryId = 'test-summary-lifecycle-1'
    const dep1Id = 'dep-1'
    const dep2Id = 'dep-2'
    
    // Create a summary machine
    const machine = manager.createMachine(summaryId, {
      componentType: 'report-summary',
      dependencies: [dep1Id, dep2Id],
      prompt: 'Test summary'
    })

    // Initialize the machine
    manager.send(summaryId, {
      type: 'INITIALIZE',
      componentId: summaryId,
      componentType: 'report-summary',
      config: {
        dependencies: [dep1Id, dep2Id]
      }
    })

    // Check initial state
    let state = manager.getContentState(summaryId)
    expect(state?.state).toBe('waitingForDependencies')

    // Stop the actor to simulate the lifecycle issue
    machine.stop()

    // Trying to send DEPENDENCIES_UPDATED should not crash
    const dependencies = new Map([
      [dep1Id, 'Content from dependency 1'],
      [dep2Id, 'Content from dependency 2']
    ])
    
    // This should handle the stopped actor gracefully
    manager.send(summaryId, {
      type: 'DEPENDENCIES_UPDATED',
      dependencies: dependencies
    })

    // The machine should still be stopped
    const snapshot = machine.getSnapshot()
    expect(snapshot.status).toBe('stopped')
  })

  test('should handle stopped actors gracefully when sending SUMMARIZE', async () => {
    const summaryId = 'test-summary-lifecycle-2'
    const dep1Id = 'dep-1'
    const dep2Id = 'dep-2'
    
    // Create a summary machine
    const machine = manager.createMachine(summaryId, {
      componentType: 'report-summary',
      dependencies: [dep1Id, dep2Id],
      prompt: 'Test summary'
    })

    // Initialize the machine
    manager.send(summaryId, {
      type: 'INITIALIZE',
      componentId: summaryId,
      componentType: 'report-summary',
      config: {
        dependencies: [dep1Id, dep2Id]
      }
    })

    // Set up dependencies
    const dependencies = new Map([
      [dep1Id, 'Content from dependency 1'],
      [dep2Id, 'Content from dependency 2']
    ])
    
    manager.send(summaryId, {
      type: 'DEPENDENCIES_UPDATED',
      dependencies: dependencies
    })

    // Stop the actor to simulate the lifecycle issue
    machine.stop()

    // Trying to send SUMMARIZE should not crash
    manager.send(summaryId, {
      type: 'SUMMARIZE'
    })

    // The machine should still be stopped
    const snapshot = machine.getSnapshot()
    expect(snapshot.status).toBe('stopped')
  })

  test('should recreate stopped actors when needed', async () => {
    const summaryId = 'test-summary-lifecycle-3'
    
    // Create a summary machine
    const machine1 = manager.createMachine(summaryId, {
      componentType: 'report-summary',
      dependencies: [],
      prompt: 'Test summary'
    })

    // Initialize the machine
    manager.send(summaryId, {
      type: 'INITIALIZE',
      componentId: summaryId,
      componentType: 'report-summary',
      config: {
        dependencies: []
      }
    })

    // Stop the actor
    machine1.stop()

    // Creating a new machine with the same ID should work
    const machine2 = manager.createMachine(summaryId, {
      componentType: 'report-summary',
      dependencies: [],
      prompt: 'Test summary'
    })

    // Should be a different instance
    expect(machine2).not.toBe(machine1)
    
    // New machine should be active
    const snapshot = machine2.getSnapshot()
    expect(snapshot.status).toBe('active')
  })

  test('should prevent multiple active machines for the same component', async () => {
    const summaryId = 'test-summary-lifecycle-4'
    
    // Create first machine
    const machine1 = manager.createMachine(summaryId, {
      componentType: 'report-summary',
      dependencies: [],
      prompt: 'Test summary'
    })

    // Try to create second machine with same ID
    const machine2 = manager.createMachine(summaryId, {
      componentType: 'report-summary',
      dependencies: [],
      prompt: 'Test summary'
    })

    // Should return the same instance
    expect(machine2).toBe(machine1)
    
    // Should still be active
    const snapshot = machine2.getSnapshot()
    expect(snapshot.status).toBe('active')
  })

  test('should handle actor state checks properly', async () => {
    const summaryId = 'test-summary-lifecycle-5'
    
    // Create a summary machine
    const machine = manager.createMachine(summaryId, {
      componentType: 'report-summary',
      dependencies: [],
      prompt: 'Test summary'
    })

    // Check initial state
    let state = manager.getContentState(summaryId)
    expect(state).toBeTruthy()
    expect(state?.state).toBe('uninitialized')

    // Initialize the machine
    manager.send(summaryId, {
      type: 'INITIALIZE',
      componentId: summaryId,
      componentType: 'report-summary',
      config: {
        dependencies: []
      }
    })

    // Check state after initialization
    state = manager.getContentState(summaryId)
    expect(state?.state).toBe('ready')

    // Stop the actor
    machine.stop()

    // getContentState should handle stopped actors gracefully
    state = manager.getContentState(summaryId)
    expect(state).toBeTruthy() // Should still return state even if stopped
  })
})