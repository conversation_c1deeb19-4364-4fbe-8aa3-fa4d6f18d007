  import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor View Modes', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
    
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });
  })

  test('should display editor in edit mode by default', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    // Wait for navigation to complete
    await page.waitForLoadState('networkidle')
    
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })
    
    // Editor should be editable by default
    // The waitForEditor() already returns the .ProseMirror element
    await expect(editor).toHaveAttribute('contenteditable', 'true')
    
    // Editor should accept text input
    await editor.click()
    await page.keyboard.type('Test editable content')
    
    // Content should appear
    await expect(editor.locator('text=Test editable content')).toBeVisible()
    
    // Main toolbar should be visible
    await expect(page.locator('[data-testid="editor-toolbar"]')).toBeVisible()
    
    // Collaboration toolbar should be visible
    await expect(page.locator('[data-testid="collaboration-toolbar"]')).toBeVisible()
    
    // Editor should be part of the main editor container
    await expect(page.locator('[data-testid="eko-document-editor"]')).toBeVisible()
  })

  test('should toggle print mode using print button', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    // Wait for navigation to complete
    await page.waitForLoadState('networkidle')
    
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })
    
    // Add some content first
    await editor.click()
    await page.keyboard.type('# Print Mode Test\n\nThis content will be styled for printing.')
    
    // Wait for auto-save
    await page.waitForTimeout(2000)
    
    // Find and click the clean mode toggle switch
    const cleanSwitch = page.locator('[data-testid="print-toggle-button"]')
    await expect(cleanSwitch).toBeVisible()
    await expect(cleanSwitch).not.toBeChecked()
    await cleanSwitch.click()

    // Switch should now be checked
    await expect(cleanSwitch).toBeChecked()
    
    // Editor container should have print mode class applied
    await expect(editor).toHaveClass(/eko-print-mode/)
    
    // Click to exit print mode
    await cleanSwitch.click()
    
    // Switch should be unchecked
    await expect(cleanSwitch).not.toBeChecked()
    
    // Print mode class should be removed
    await expect(editor).not.toHaveClass(/eko-print-mode/)
  })

  test('should work in view mode when configured', async ({ page }) => {
    // Test print toggle functionality within the regular editor
    const documentId = await testUtils.createDocumentFromTemplate()

    // Add some content first
    const editor = await testUtils.waitForEditor()
    await testUtils.typeInEditor('# Print Mode Test\n\nThis content should be visible in print mode.')

    // Wait for content to be added
    await page.waitForTimeout(1000)

    // Click the clean toggle switch to enter clean mode
    const cleanToggleSwitch = page.locator('[data-testid="print-toggle-button"]')
    await expect(cleanToggleSwitch).toBeVisible()
    await cleanToggleSwitch.click()

    // Wait for print mode to activate
    await page.waitForTimeout(1000)

    // In print mode, the editor should still be visible and editable
    const printEditor = await testUtils.waitForEditor()
    await expect(printEditor).toBeVisible({ timeout: 10000 })

    // Editor should still be editable in print mode (print mode ≠ view mode)
    // The printEditor is already the .ProseMirror element
    await expect(printEditor).toHaveAttribute('contenteditable', 'true')

    // Content should still be visible and readable
    await expect(printEditor).toContainText('Print Mode Test')

    // Print mode class should be applied to the editor container
    await expect(printEditor).toHaveClass(/eko-print-mode/)

    // Switch should be checked when in print mode
    await expect(cleanToggleSwitch).toBeChecked()
  })

  test('should display save button in editor toolbar', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })

    // Add content to trigger save functionality
    await testUtils.typeInEditor('Test save status')

    // Check that save button exists in editor toolbar (not collaboration toolbar)
    await expect(page.locator('[data-testid="save-button"]')).toBeVisible()
  })

  test('should handle keyboard shortcuts for formatting', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })
    
    // Test bold shortcut
    await testUtils.typeInEditor('Test text')
    await page.keyboard.press('ControlOrMeta+a')
    await page.keyboard.press('ControlOrMeta+b')
    
    // Text should be bold
    await expect(editor.locator('strong')).toBeVisible({ timeout: 5000 })
    
    // Test italic shortcut
    await page.keyboard.press('ControlOrMeta+a')
    await page.keyboard.press('ControlOrMeta+i')
    
    // Text should be italic (might be both bold and italic)
    await expect(editor.locator('em, strong em, em strong')).toBeVisible({ timeout: 5000 })
  })

  test('should render toolbar buttons correctly', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })

    // Check main formatting buttons exist using correct data-testid
    await expect(page.locator('[data-testid="bold-button"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="italic-button"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="underline-button"]')).toBeVisible({ timeout: 10000 })

    // Check table and image buttons exist using correct data-testid
    await expect(page.locator('[data-testid="insert-table-button"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="insert-image-button"]')).toBeVisible({ timeout: 10000 })

    // Check undo/redo buttons exist using data-testid
    await expect(page.locator('[data-testid="undo-button"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="redo-button"]')).toBeVisible({ timeout: 10000 })

    // Check save button exists in editor toolbar
    await expect(page.locator('[data-testid="save-button"]')).toBeVisible({ timeout: 10000 })

    // Check clean toggle switch exists
    await expect(page.locator('[data-testid="print-toggle-button"]')).toBeVisible({ timeout: 10000 })

    // Check export button exists
    await expect(page.locator('[data-testid="export-button"]')).toBeVisible({ timeout: 10000 })
  })

  test('should handle table insertion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })

    // Click at the beginning of the editor
    await editor.click()

    // Find and click table button in editor toolbar using correct data-testid
    const tableButton = page.locator('[data-testid="insert-table-button"]')
    await expect(tableButton).toBeVisible()
    await tableButton.click()

    // Table should be created
    await expect(editor.locator('table')).toBeVisible({ timeout: 5000 })

    // Should have either header cells or data cells
    const headerCells = editor.locator('th')
    const dataCells = editor.locator('td')

    // At least one type of cell should exist
    const headerCount = await headerCells.count()
    const dataCount = await dataCells.count()
    expect(headerCount + dataCount).toBeGreaterThan(0)
  })

  test('should handle mobile viewport responsively', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })
    
    // Toolbar should still be visible on mobile
    await expect(page.locator('[data-testid="editor-toolbar"]')).toBeVisible()
    
    // Editor should be touch-friendly and functional
    await testUtils.typeInEditor('Mobile editing test')
    await expect(editor.locator('text=Mobile editing test')).toBeVisible()
    
    // Toolbar buttons should still be clickable
    await expect(page.locator('[data-testid="bold-button"]')).toBeVisible({ timeout: 10000 })
  })

  test('should preserve content when navigating between pages', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    // Wait for navigation to complete
    await page.waitForLoadState('networkidle')
    
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })

    // Add content
    await testUtils.typeInEditor('# Content Preservation Test\n\nThis content should persist.')

    // Manually trigger save to ensure content is saved
    await page.click('[data-testid="save-button"]')

    // Wait for save to complete - look for save confirmation or wait longer
    await page.waitForTimeout(5000)
    await page.waitForLoadState('networkidle')

    // Navigate away and back
    await page.goto('/customer/dashboard')
    await page.waitForLoadState('networkidle')

    // Navigate back to the document
    await page.goto(`/customer/documents/${documentId}`)
    await page.waitForLoadState('networkidle')

    // Wait for editor to load
    const newEditor = await testUtils.waitForEditor()
    await expect(newEditor).toBeVisible({ timeout: 10000 })

    // Content should be preserved - wait longer and be more specific
    await expect(newEditor).toContainText('Content Preservation Test', { timeout: 15000 })
    await expect(newEditor).toContainText('This content should persist.', { timeout: 15000 })
  })

  test('should handle export functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 30000 })

    // Add some content to export
    await testUtils.typeInEditor('# Export Test\n\nThis document will be exported.')

    // Check that export button exists in editor toolbar
    const exportButton = page.locator('[data-testid="export-button"]')
    await expect(exportButton).toBeVisible()

    // Click export button to open dropdown
    await exportButton.click()

    // Check that export options are available
    await expect(page.locator('text=Export as PDF')).toBeVisible()
  })
})
