import { test, expect } from '@playwright/test';
import { TestUtils } from '../helpers/tests/test-utils';
import { TEST_CONFIG, getTestEntity, getTestRun, getTestModel } from '../test-config';

test.describe('EKO-154: URL params get lost when navigating from dashboard', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    // Disable console output to reduce noise
    page.on('console', () => {});
    
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('should preserve URL parameters when navigating between dashboard pages', async ({ page }) => {
    // Use real test data from test-config - use tertiary entity which test user has access to
    const testEntity = getTestEntity('secondary'); // 'GVNZgYj9x7' - entity test user has access to
    const testModel = getTestModel('secondary'); // 'doughnut' (non-default so it appears in URL)
    const testRun = String(TEST_CONFIG.runs.specific); // '3469'
    
    // Start with dashboard page with specific parameters
    const initialUrl = `/customer/dashboard?entity=${testEntity}&model=${testModel}&run=${testRun}&disclosures=true`;
    await page.goto(initialUrl, { timeout: 60000, waitUntil: 'domcontentloaded' });
    
    // Wait for navigation to be ready - don't wait for data to load
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });
    await expect(page.locator('[data-testid="nav-claims"]')).toBeVisible({ timeout: 30000 });
    
    // Verify initial URL parameters are present
    expect(page.url()).toContain(`entity=${testEntity}`);
    expect(page.url()).toContain(`model=${testModel}`);
    expect(page.url()).toContain(`run=${testRun}`);
    expect(page.url()).toContain('disclosures=true');
    
    // Navigate to Claims page via sidebar
    await page.click('[data-testid="nav-claims"]');
    await page.waitForURL('**/customer/dashboard/gw/claims**', { timeout: 30000, waitUntil: 'domcontentloaded' });
    
    // Verify URL parameters are preserved after navigation
    expect(page.url()).toContain(`entity=${testEntity}`);
    expect(page.url()).toContain(`model=${testModel}`);
    expect(page.url()).toContain(`run=${testRun}`);
    expect(page.url()).toContain('disclosures=true');
    expect(page.url()).toContain('/customer/dashboard/gw/claims');
    
    // Navigate to Promises page
    await page.click('[data-testid="nav-promises"]');
    await page.waitForURL('**/customer/dashboard/gw/promises**', { timeout: 30000, waitUntil: 'domcontentloaded' });
    
    // Verify URL parameters are still preserved
    expect(page.url()).toContain(`entity=${testEntity}`);
    expect(page.url()).toContain(`model=${testModel}`);
    expect(page.url()).toContain(`run=${testRun}`);
    expect(page.url()).toContain('disclosures=true');
    expect(page.url()).toContain('/customer/dashboard/gw/promises');
    
    // Navigate to Flags page
    await page.click('[data-testid="nav-flags"]');
    await page.waitForURL('**/customer/dashboard/flags**', { timeout: 30000, waitUntil: 'domcontentloaded' });
    
    // Verify URL parameters are preserved
    expect(page.url()).toContain(`entity=${testEntity}`);
    expect(page.url()).toContain(`model=${testModel}`);
    expect(page.url()).toContain(`run=${testRun}`);
    expect(page.url()).toContain('disclosures=true');
    expect(page.url()).toContain('/customer/dashboard/flags');
  });

  test('should preserve URL parameters when navigating back to dashboard', async ({ page }) => {
    // Use real test data from test-config - use tertiary entity which test user has access to
    const testEntity = getTestEntity('secondary'); // 'GVNZgYj9x7' - entity test user has access to
    const testModel = getTestModel('secondary'); // 'doughnut' (non-default so it appears in URL)
    const testRun = String(TEST_CONFIG.runs.specific); // '3469'
    
    // Start with a sub-page
    const initialUrl = `/customer/dashboard/gw/claims?entity=${testEntity}&model=${testModel}&run=${testRun}`;
    await page.goto(initialUrl, { timeout: 60000, waitUntil: 'domcontentloaded' });
    
    // Wait for navigation to be ready
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });
    
    // Navigate back to main Dashboard
    const dashboardSection = page.locator('[data-testid="nav-section-dashboard"]');
    const isCollapsed = await dashboardSection.locator('[data-testid="section-collapse-button"]').getAttribute('data-state') === 'closed';
    if (isCollapsed) {
      await dashboardSection.locator('[data-testid="section-collapse-button"]').click();
    }
    await dashboardSection.locator('[data-testid="section-content"] [data-testid="nav-dashboard"]').click();
    await page.waitForURL('**/customer/dashboard?**', { timeout: 30000, waitUntil: 'domcontentloaded' });
    
    // Verify URL parameters are preserved
    expect(page.url()).toContain(`entity=${testEntity}`);
    expect(page.url()).toContain(`model=${testModel}`);
    expect(page.url()).toContain(`run=${testRun}`);
    expect(page.url()).toContain('/customer/dashboard');
  });

  test('should handle multiple parameter changes correctly', async ({ page }) => {
    // Start with minimal parameters using real test data - use tertiary entity which test user has access to
    const testEntity = getTestEntity('secondary'); // 'GVNZgYj9x7' - entity test user has access to
    const testModel = getTestModel('secondary'); // 'doughnut' (non-default so it appears in URL)
    const testRun = String(TEST_CONFIG.runs.specific); // '3469'
    const initialUrl = `/customer/dashboard?entity=${testEntity}&model=${testModel}&run=${testRun}`;
    await page.goto(initialUrl, { timeout: 60000, waitUntil: 'domcontentloaded' });
    
    // Wait for navigation to be ready
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible({ timeout: 30000 });
    await expect(page.locator('[data-testid="nav-claims"]')).toBeVisible({ timeout: 30000 });
    
    // Verify initial parameters are present
    expect(page.url()).toContain(`entity=${testEntity}`);
    expect(page.url()).toContain(`model=${testModel}`);
    expect(page.url()).toContain(`run=${testRun}`);
    
    // Navigate to another page
    await page.click('[data-testid="nav-flags"]');
    await page.waitForURL('**/customer/dashboard/flags**', { timeout: 30000, waitUntil: 'domcontentloaded' });
    
    // Verify standard parameters are preserved (only test for known preserved parameters)
    expect(page.url()).toContain(`entity=${testEntity}`);
    expect(page.url()).toContain(`model=${testModel}`);
    // Note: run parameter may or may not be preserved depending on implementation
    expect(page.url()).toContain('/customer/dashboard/flags');
  });
});