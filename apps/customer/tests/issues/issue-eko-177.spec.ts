import { test, expect } from '@playwright/test';
import { TestUtils } from '../helpers/tests/test-utils';

test.describe('EKO-177: Customer Analysis Triggering Feature Flag', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
  });

  test('should hide entire Start Company Analysis section when feature flag is disabled (default)', async ({ page }) => {
    // Login as standard test user (who should not have the analysis.user.trigger flag enabled by default)
    await testUtils.login();

    // Navigate to the analysis companies page
    await page.goto('/customer/analysis/companies');
    await page.waitForLoadState('networkidle');

    // Wait for the page to load - look for an element that should always be present (Add Company section)
    await expect(page.locator('text=Add Company')).toBeVisible({ timeout: 30000 });

    // Verify the entire "Start Company Analysis" section is NOT visible (feature flag disabled by default)
    await expect(page.locator('text=Start Company Analysis')).not.toBeVisible();

    // Verify the EntitySelector is NOT visible (it's part of the hidden section)
    await expect(page.locator('[data-testid="entity-selector"]')).not.toBeVisible();

    // Verify the analysis trigger buttons are NOT visible (feature flag disabled by default)
    await expect(page.locator('button').filter({ hasText: 'Deep Analysis (Hours)' })).not.toBeVisible();
    await expect(page.locator('button').filter({ hasText: 'Quick Analysis (Minutes)' })).not.toBeVisible();

    // Verify the instructions are also not visible (since they explain the buttons)
    await expect(page.locator('text=A full analysis will take between 12-48 hours')).not.toBeVisible();
  });

  test.skip('should show entire Start Company Analysis section when feature flag is enabled', async ({ page }) => {
    // This test is skipped because we need to implement a mechanism to enable feature flags in tests
    // When the analysis.user.trigger flag is enabled, the following should be visible:
    // - Start Company Analysis card title
    // - EntitySelector component
    // - Deep Analysis (Hours) button
    // - Quick Analysis (Minutes) button
    // - Instructions text explaining the analysis types

    // TODO: Implement feature flag enabling mechanism for tests
    // This could be done by:
    // 1. Creating a test user with the flag enabled in the database
    // 2. Adding a test utility to modify user feature flags
    // 3. Using environment variables to override feature flags in test mode

    console.log('Test skipped: Feature flag enabling mechanism not yet implemented');
  });

  test('should maintain existing functionality when Start Company Analysis section is hidden', async ({ page }) => {
    // This test ensures that when the feature flag is disabled, the page still functions correctly

    await testUtils.login();
    await page.goto('/customer/analysis/companies');
    await page.waitForLoadState('networkidle');

    // Wait for the page to load - look for an element that should always be present (Add Company section)
    await expect(page.locator('text=Add Company')).toBeVisible({ timeout: 30000 });

    // Verify the Start Company Analysis section is NOT visible
    await expect(page.locator('text=Start Company Analysis')).not.toBeVisible();

    // Verify the Analysis Queue section is still visible (this should not be affected by the feature flag)
    await expect(page.locator('text=Analysis Queue')).toBeVisible();

    // Check for any console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Wait a bit to catch any console errors
    await page.waitForTimeout(2000);

    // Verify no console errors occurred
    expect(consoleErrors.filter(error =>
      !error.includes('favicon') &&
      !error.includes('404') &&
      !error.includes('net::ERR_')
    )).toHaveLength(0);
  });

  test('should show appropriate UI when Start Company Analysis section is hidden', async ({ page }) => {
    // Test that when the feature flag is disabled, the UI is still coherent

    await testUtils.login();
    await page.goto('/customer/analysis/companies');
    await page.waitForLoadState('networkidle');

    // Wait for the page to load - look for an element that should always be present (Add Company section)
    await expect(page.locator('text=Add Company')).toBeVisible({ timeout: 30000 });

    // Verify the Start Company Analysis section is NOT visible
    await expect(page.locator('text=Start Company Analysis')).not.toBeVisible();

    // Verify other sections are still visible and functional
    await expect(page.locator('text=Analysis Queue')).toBeVisible();

    // The entire Start Company Analysis card should be hidden
    await expect(page.locator('[data-testid="entity-selector"]')).not.toBeVisible();
  });
});
