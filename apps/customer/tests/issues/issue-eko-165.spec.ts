import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-165: Table of Contents Duplicate Headings', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should not show duplicate headings in TipTap table of contents', async ({ page }) => {
    // Create a new document with headings
    await testUtils.createDocumentFromTemplate('Blank Document')
    await testUtils.waitForEditor()

    // Add some content with headings
    const editor = await testUtils.getEditor()
    await editor.click()
    
    // Add multiple headings
    await page.keyboard.type('# Main Title')
    await page.keyboard.press('Enter')
    await page.keyboard.type('Some content here')
    await page.keyboard.press('Enter')
    await page.keyboard.press('Enter')
    
    await page.keyboard.type('## Section 1')
    await page.keyboard.press('Enter')
    await page.keyboard.type('Content for section 1')
    await page.keyboard.press('Enter')
    await page.keyboard.press('Enter')
    
    await page.keyboard.type('## Section 2')
    await page.keyboard.press('Enter')
    await page.keyboard.type('Content for section 2')
    await page.keyboard.press('Enter')
    await page.keyboard.press('Enter')
    
    await page.keyboard.type('### Subsection 2.1')
    await page.keyboard.press('Enter')
    await page.keyboard.type('Content for subsection')
    await page.keyboard.press('Enter')
    await page.keyboard.press('Enter')

    // Insert table of contents using toolbar button
    await page.click('[data-testid="insert-toc-button"]')

    // Wait for table of contents to be inserted and populated
    await page.waitForSelector('.table-of-contents-wrapper', { timeout: 10000 })
    await page.waitForTimeout(2000) // Allow time for headings to be extracted

    // Get all heading items in the table of contents
    const tocHeadings = await page.locator('.table-of-contents-wrapper nav button span').allTextContents()
    
    console.log('Table of Contents headings found:', tocHeadings)

    // Check that we have the expected headings
    expect(tocHeadings).toContain('Main Title')
    expect(tocHeadings).toContain('Section 1')
    expect(tocHeadings).toContain('Section 2')
    expect(tocHeadings).toContain('Subsection 2.1')

    // Check for duplicates - each heading should appear only once
    const headingCounts = tocHeadings.reduce((acc: Record<string, number>, heading: string) => {
      acc[heading] = (acc[heading] || 0) + 1
      return acc
    }, {})

    console.log('Heading counts:', headingCounts)

    // Assert no duplicates
    for (const [heading, count] of Object.entries(headingCounts)) {
      expect(count, `Heading "${heading}" appears ${count} times, should appear only once`).toBe(1)
    }
  })

  test('should handle document updates without creating duplicate headings', async ({ page }) => {
    // Create a new document
    await testUtils.createDocumentFromTemplate('Blank Document')
    await testUtils.waitForEditor()

    const editor = await testUtils.getEditor()
    await editor.click()

    // Add initial content
    await page.keyboard.type('# Initial Title')
    await page.keyboard.press('Enter')
    await page.keyboard.type('Some content')
    await page.keyboard.press('Enter')
    await page.keyboard.press('Enter')

    // Insert table of contents using toolbar button
    await page.click('[data-testid="insert-toc-button"]')

    // Wait for initial table of contents
    await page.waitForSelector('.table-of-contents-wrapper', { timeout: 10000 })
    await page.waitForTimeout(1000)

    // Get initial headings count
    let tocHeadings = await page.locator('.table-of-contents-wrapper nav button span').allTextContents()
    console.log('Initial TOC headings:', tocHeadings)

    // Add more content after the table of contents
    await page.keyboard.press('End') // Go to end of document
    await page.keyboard.press('Enter')
    await page.keyboard.type('## New Section')
    await page.keyboard.press('Enter')
    await page.keyboard.type('New content')
    await page.keyboard.press('Enter')

    // Wait for table of contents to update
    await page.waitForTimeout(2000)

    // Get updated headings
    tocHeadings = await page.locator('.table-of-contents-wrapper nav button span').allTextContents()
    console.log('Updated TOC headings:', tocHeadings)

    // Check that we have both headings without duplicates
    expect(tocHeadings).toContain('Initial Title')
    expect(tocHeadings).toContain('New Section')

    // Check for duplicates
    const headingCounts = tocHeadings.reduce((acc: Record<string, number>, heading: string) => {
      acc[heading] = (acc[heading] || 0) + 1
      return acc
    }, {})

    console.log('Final heading counts:', headingCounts)

    for (const [heading, count] of Object.entries(headingCounts)) {
      expect(count, `Heading "${heading}" appears ${count} times after update, should appear only once`).toBe(1)
    }
  })

  test('should not create multiple table of contents when command is run multiple times', async ({ page }) => {
    // Create a new document
    await testUtils.createDocumentFromTemplate('Blank Document')
    await testUtils.waitForEditor()

    const editor = await testUtils.getEditor()
    await editor.click()

    // Add some content
    await page.keyboard.type('# Test Title')
    await page.keyboard.press('Enter')
    await page.keyboard.type('Content')
    await page.keyboard.press('Enter')
    await page.keyboard.press('Enter')

    // Insert first table of contents using toolbar button
    await page.click('[data-testid="insert-toc-button"]')

    await page.waitForSelector('.table-of-contents-wrapper', { timeout: 10000 })
    await page.waitForTimeout(1000)

    // Try to insert another table of contents
    await page.keyboard.press('End')
    await page.keyboard.press('Enter')
    await page.click('[data-testid="insert-toc-button"]')

    await page.waitForTimeout(2000)

    // Check that only one table of contents exists
    const tocElements = await page.locator('.table-of-contents-wrapper').count()
    expect(tocElements).toBe(1)
  })

  test('should handle empty document gracefully', async ({ page }) => {
    // Create a new document
    await testUtils.createDocumentFromTemplate('Blank Document')
    await testUtils.waitForEditor()

    const editor = await testUtils.getEditor()
    await editor.click()

    // Insert table of contents in empty document using toolbar button
    await page.click('[data-testid="insert-toc-button"]')

    await page.waitForSelector('.table-of-contents-wrapper', { timeout: 10000 })
    await page.waitForTimeout(1000)

    // Should show "No headings found" message
    const noHeadingsMessage = await page.locator('.table-of-contents-wrapper .text-neutral-500')
    await expect(noHeadingsMessage).toBeVisible()
    await expect(noHeadingsMessage).toContainText('No headings found')
  })
})
