import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-166: Document Version History Overflow', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('version history should scroll when there are many versions', async ({ page }) => {
    // Create a new document first
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Wait for navigation to complete
    await page.waitForLoadState('networkidle')
    
    // Wait for the document to load
    await page.waitForSelector('[data-testid="eko-document-editor"]', { timeout: 60000 })
    
    // Open the history panel directly by clicking the history button
    const historyButton = page.locator('[data-testid="history-button"]')
    await historyButton.click()
    
    // Wait for history panel to load
    await page.waitForSelector('[data-testid="history-panel"]', { timeout: 30000 })
    
    // Check if the history panel is visible
    const historyPanel = page.locator('[data-testid="history-panel"]')
    await expect(historyPanel).toBeVisible()
    
    // Create multiple versions to test scrolling
    // We'll simulate this by making several edits and auto-saves
    const editor = page.locator('.ProseMirror')
    await expect(editor).toBeVisible()
    
    // Make several edits to create version history
    for (let i = 0; i < 10; i++) {
      await editor.click()
      await editor.type(`\n\nEdit ${i + 1}: This is a test edit to create version history.`)
      
      // Wait a bit for auto-save to potentially trigger
      await page.waitForTimeout(1000)
      
      // Force a manual save to create a version
      await page.keyboard.press('Control+S')
      await page.waitForTimeout(500)
    }
    
    // Wait for versions to appear in the history panel
    await page.waitForTimeout(2000)
    
    // Check that version items are present
    const versionItems = page.locator('[data-testid="version-item"]')
    const versionCount = await versionItems.count()
    
    if (versionCount > 0) {
      console.log(`Found ${versionCount} versions in history`)
      
      // Check if the scroll area is properly constrained
      const scrollArea = historyPanel.locator('.scroll-area, [data-radix-scroll-area-viewport]').first()
      
      if (await scrollArea.isVisible()) {
        // Get the scroll area dimensions
        const scrollAreaBox = await scrollArea.boundingBox()
        const historyPanelBox = await historyPanel.boundingBox()
        
        if (scrollAreaBox && historyPanelBox) {
          // The scroll area should be smaller than the full history panel
          // (accounting for header and footer)
          expect(scrollAreaBox.height).toBeLessThan(historyPanelBox.height)
          
          // Check if scrolling is possible when there are many items
          if (versionCount > 5) {
            // Try to scroll within the scroll area
            await scrollArea.hover()
            await page.mouse.wheel(0, 200) // Scroll down
            await page.waitForTimeout(500)
            
            // Scroll back up
            await page.mouse.wheel(0, -200)
            await page.waitForTimeout(500)
            
            console.log('Scrolling test completed successfully')
          }
        }
      }
    } else {
      console.log('No versions found, creating a simple version')
      // If no versions exist, create at least one
      await editor.click()
      await editor.type('\n\nTest content for version history')
      await page.keyboard.press('Control+S')
      await page.waitForTimeout(1000)
    }
    
    // Verify that the history panel doesn't overflow its container
    const historyPanelBox = await historyPanel.boundingBox()
    const parentContainer = page.locator('[data-testid="tabbed-side-panel"], .tabbed-side-panel').first()
    
    if (await parentContainer.isVisible()) {
      const parentBox = await parentContainer.boundingBox()
      
      if (historyPanelBox && parentBox) {
        // History panel should not exceed its parent container
        expect(historyPanelBox.height).toBeLessThanOrEqual(parentBox.height + 10) // Small tolerance
        expect(historyPanelBox.y).toBeGreaterThanOrEqual(parentBox.y - 10) // Small tolerance
      }
    }
  })

  test('version history scroll area should have proper height constraints', async ({ page }) => {
    // Create a new document first
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Wait for navigation to complete
    await page.waitForLoadState('networkidle')
    
    // Wait for editor to load
    await page.waitForSelector('[data-testid="eko-document-editor"]', { timeout: 60000 })
    
    // Open the history panel directly by clicking the history button
    const historyButton = page.locator('[data-testid="history-button"]')
    await historyButton.click()
    await page.waitForSelector('[data-testid="history-panel"]', { timeout: 30000 })
    
    // Check that the scroll area has the correct CSS classes
    const scrollArea = page.locator('[data-testid="history-panel"] .scroll-area, [data-testid="history-panel"] [data-radix-scroll-area-viewport]').first()
    
    if (await scrollArea.isVisible()) {
      // Verify that the scroll area has proper styling for height constraints
      const scrollAreaElement = await scrollArea.elementHandle()
      if (scrollAreaElement) {
        const computedStyle = await page.evaluate((element) => {
          const style = window.getComputedStyle(element)
          return {
            overflow: style.overflow,
            overflowY: style.overflowY,
            height: style.height,
            maxHeight: style.maxHeight,
            minHeight: style.minHeight,
            flex: style.flex,
            flexGrow: style.flexGrow,
            flexShrink: style.flexShrink
          }
        }, scrollAreaElement)
        
        console.log('Scroll area computed style:', computedStyle)
        
        // The scroll area should have proper overflow handling
        expect(computedStyle.overflowY === 'auto' || computedStyle.overflowY === 'scroll' || computedStyle.overflow === 'hidden').toBeTruthy()
      }
    }
  })
})
