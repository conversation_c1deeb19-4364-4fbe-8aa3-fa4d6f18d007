import { test, expect } from '@playwright/test';
import { TestUtils } from '../helpers/tests/test-utils';
import { getTestEntity, getTestModel } from '../test-config';

test.describe('EKO-169: Promise Status Overview Changes', () => {
    let testUtils: TestUtils;

    test.beforeEach(async ({ page }) => {
        testUtils = new TestUtils(page);
        await testUtils.login();
    });

    test('should not show Promise Status Overview section', async ({ page }) => {
        // Navigate to promises page
        await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);

        // Wait for page to load
        await expect(page.getByTestId('promises-page-content')).toBeVisible({ timeout: 60000 });

        // Verify that the old Promise Status Overview is not present
        // The old component had data-testid="promises-chart"
        await expect(page.getByTestId('promises-chart')).not.toBeVisible();

        // Verify that elements with "Promise Status Overview" text are not present
        await expect(page.locator('text=Promise Status Overview')).not.toBeVisible();
    });

    test('should show 5-year promise trends chart', async ({ page }) => {
        // Navigate to promises page
        await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);

        // Wait for page to load
        await expect(page.getByTestId('promises-page-content')).toBeVisible({ timeout: 60000 });

        // Verify that the new 5-year chart is present
        await expect(page.getByTestId('promises-five-year-chart')).toBeVisible();

        // Verify the chart has the correct title
        await expect(page.locator('text=Promise Trends (Last 5 Years)')).toBeVisible();

        // Check for summary statistics
        await expect(page.locator('text=Total Kept')).toBeVisible();
        await expect(page.locator('text=Total Broken')).toBeVisible();
        await expect(page.locator('text=Total Uncertain')).toBeVisible();
    });

    test('should show year-based bars in 5-year chart', async ({ page }) => {
        // Navigate to promises page
        await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);

        // Wait for page to load and chart to render
        await expect(page.getByTestId('promises-five-year-chart')).toBeVisible({ timeout: 60000 });

        // Get current year and check for chart bars
        const currentYear = new Date().getFullYear();
        const years = Array.from({ length: 5 }, (_, i) => currentYear - i);

        // Wait for chart to load and check for at least one chart bar from recent years
        await page.waitForTimeout(2000); // Allow chart to render
        
        // Verify chart bars exist for at least one of the years
        const chartBars = page.locator('[data-testid*="chart-bar-"]');
        await expect(chartBars.first()).toBeVisible({ timeout: 10000 });
    });

    test('claims page should have timeline and summary sections', async ({ page }) => {
        // Navigate to claims page
        await page.goto(`/customer/dashboard/gw/claims?entity=${getTestEntity()}&model=${getTestModel()}`);

        // Wait for page to load
        await expect(page.getByTestId('claims-page-content')).toBeVisible({ timeout: 60000 });

        // Verify claims summary section is present (similar to promises)
        await expect(page.getByTestId('claims-summary')).toBeVisible();
        await expect(page.locator('text=Claims Summary')).toBeVisible();

        // Check for summary statistics using test IDs
        await expect(page.getByTestId('valid-claims-count')).toBeVisible();
        await expect(page.getByTestId('invalid-claims-count')).toBeVisible();
        await expect(page.getByTestId('greenwashing-claims-count')).toBeVisible();

        // Verify claims timeline is present
        await expect(page.getByTestId('claims-timeline')).toBeVisible();
        await expect(page.locator('text=Claims Timeline')).toBeVisible();
    });

    test('claims page should maintain existing filters', async ({ page }) => {
        // Navigate to claims page
        await page.goto(`/customer/dashboard/gw/claims?entity=${getTestEntity()}&model=${getTestModel()}`);

        // Wait for page to load
        await expect(page.getByTestId('claims-page-content')).toBeVisible({ timeout: 60000 });

        // Verify existing filter section is still present
        await expect(page.getByTestId('claims-filters')).toBeVisible();
        await expect(page.getByTestId('esg-claims-filter')).toBeVisible();
        await expect(page.getByTestId('confidence-filter')).toBeVisible();
        await expect(page.getByTestId('importance-filter')).toBeVisible();
    });

    test('promises page should still have timeline and assessment history', async ({ page }) => {
        // Navigate to promises page
        await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);

        // Wait for page to load
        await expect(page.getByTestId('promises-page-content')).toBeVisible({ timeout: 60000 });

        // Verify existing components are still present
        await expect(page.getByTestId('promise-timeline')).toBeVisible();
        await expect(page.locator('text=Promise Timeline')).toBeVisible();

        // Check for promises list
        await expect(page.getByTestId('promises-list')).toBeVisible();
    });

    test('promises page should maintain summary section', async ({ page }) => {
        // Navigate to promises page
        await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);

        // Wait for page to load
        await expect(page.getByTestId('promises-page-content')).toBeVisible({ timeout: 60000 });

        // Verify summary section is still present
        await expect(page.getByTestId('promises-summary')).toBeVisible();
        await expect(page.locator('text=Promise Summary')).toBeVisible();

        // Check for summary counts
        await expect(page.getByTestId('kept-promises-count')).toBeVisible();
    });

    test('assessment history should not show summary boxes', async ({ page }) => {
        // Navigate to promises page
        await page.goto(`/customer/dashboard/gw/promises?entity=${getTestEntity()}&model=${getTestModel()}`);

        // Wait for page to load
        await expect(page.getByTestId('promises-page-content')).toBeVisible({ timeout: 60000 });

        // Verify assessment history is present
        await expect(page.getByTestId('promise-assessment-history')).toBeVisible();

        // Verify that the summary boxes are NOT present
        await expect(page.locator('text=Total Promises')).not.toBeVisible();
        await expect(page.locator('text=Recent Success Rate')).not.toBeVisible();
        await expect(page.locator('text=Avg Confidence')).not.toBeVisible();

        // Verify that the assessment history still shows the title
        await expect(page.locator('text=Assessment History')).toBeVisible();
    });
});
