import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-178: Editable Document Titles', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should allow editing document title by clicking on it', async ({ page }) => {
    // Navigate to documents page
    await page.goto('/customer/documents')
    await page.waitForLoadState('networkidle')

    // Click on first document if available
    const firstDocument = page.locator('div').filter({ hasText: /^Documents/ }).getByRole('button').first()
    if (await firstDocument.isVisible()) {
      await firstDocument.click()
    }

    // Wait for document page to load
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(3000)

    // Find the editable title component
    const editableTitle = page.getByTestId('document-title-editable')
    await expect(editableTitle).toBeVisible({ timeout: 15000 })

    // Click on the title to start editing
    await editableTitle.click()

    // Wait for edit mode to activate
    await page.waitForTimeout(1000)

    // Check that input field is now visible and focused
    const titleInput = editableTitle.locator('input')
    await expect(titleInput).toBeVisible()

    // Type new title
    const newTitle = `Test Document ${Date.now()}`
    await titleInput.fill(newTitle)

    // Press Enter to save (new component saves on Enter, not with buttons)
    await titleInput.press('Enter')

    // Wait for save to complete
    await page.waitForTimeout(2000)

    // Verify the title has been updated (input should be gone, showing div with new title)
    await expect(editableTitle.locator('input')).not.toBeVisible()
    await expect(editableTitle).toContainText(newTitle)
  })

  test('should cancel editing when escape key is pressed', async ({ page }) => {
    // Navigate to documents page and open a document
    await page.goto('/customer/documents')
    await page.waitForLoadState('networkidle')

    // Use existing document or create new one
    const firstDocument = page.locator('[data-testid="document-item"]').first()
    if (await firstDocument.isVisible()) {
      await firstDocument.click()
    }

    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)

    const editableTitle = page.getByTestId('document-title-editable')
    await expect(editableTitle).toBeVisible({ timeout: 10000 })

    // Get original title
    const originalTitle = await editableTitle.textContent()

    // Start editing
    await editableTitle.click()
    await page.waitForTimeout(500)

    const titleInput = editableTitle.locator('input')
    await expect(titleInput).toBeVisible()

    // Type new title but don't save
    await titleInput.fill('This should be cancelled')

    // Press Escape to cancel
    await titleInput.press('Escape')

    // Wait for edit mode to exit
    await page.waitForTimeout(500)

    // Verify original title is restored and input is gone
    await expect(editableTitle.locator('input')).not.toBeVisible()
    await expect(editableTitle).toContainText(originalTitle || 'Untitled Document')
  })

  test('should handle empty title validation', async ({ page }) => {
    // Navigate to documents and open a document
    await page.goto('/customer/documents')
    await page.waitForLoadState('networkidle')

    const firstDocument = page.locator('[data-testid="document-item"]').first()
    if (await firstDocument.isVisible()) {
      await firstDocument.click()
    }

    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)

    const editableTitle = page.getByTestId('document-title-editable')
    await expect(editableTitle).toBeVisible({ timeout: 10000 })

    // Start editing
    await editableTitle.click()
    await page.waitForTimeout(500)

    const titleInput = editableTitle.locator('input')
    await expect(titleInput).toBeVisible()

    // Clear the title (empty string)
    await titleInput.fill('')

    // Try to save by pressing Enter
    await titleInput.press('Enter')

    // Wait for validation
    await page.waitForTimeout(1000)

    // Verify error toast appears
    await expect(page.locator('.sonner-toast')).toContainText('Title cannot be empty')

    // Verify we're still in edit mode (input still visible)
    await expect(titleInput).toBeVisible()
  })

  test('should handle long title validation', async ({ page }) => {
    // Navigate to documents and open a document
    await page.goto('/customer/documents')
    await page.waitForLoadState('networkidle')

    const firstDocument = page.locator('[data-testid="document-item"]').first()
    if (await firstDocument.isVisible()) {
      await firstDocument.click()
    }

    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)

    const editableTitle = page.getByTestId('document-title-editable')
    await expect(editableTitle).toBeVisible({ timeout: 10000 })

    // Start editing
    await editableTitle.click()
    await page.waitForTimeout(500)

    const titleInput = editableTitle.locator('input')
    await expect(titleInput).toBeVisible()

    // Enter a very long title (over 200 characters)
    const longTitle = 'A'.repeat(250)
    await titleInput.fill(longTitle)

    // Try to save by pressing Enter
    await titleInput.press('Enter')

    // Wait for validation
    await page.waitForTimeout(1000)

    // Verify error toast appears
    await expect(page.locator('.sonner-toast')).toContainText('Title is too long')

    // Verify we're still in edit mode (input still visible)
    await expect(titleInput).toBeVisible()
  })

  test('should exit edit mode when clicking outside', async ({ page }) => {
    // Navigate to documents and open a document
    await page.goto('/customer/documents')
    await page.waitForLoadState('networkidle')

    const firstDocument = page.locator('[data-testid="document-item"]').first()
    if (await firstDocument.isVisible()) {
      await firstDocument.click()
    }

    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)

    const editableTitle = page.getByTestId('document-title-editable')
    await expect(editableTitle).toBeVisible({ timeout: 10000 })

    // Start editing
    await editableTitle.click()
    await page.waitForTimeout(500)

    const titleInput = editableTitle.locator('input')
    await expect(titleInput).toBeVisible()

    // Type new title
    const newTitle = `Updated Title ${Date.now()}`
    await titleInput.fill(newTitle)

    // Click outside the editable area (on the back button)
    const backButton = page.getByTestId('back-button')
    await backButton.click({ force: true })

    // Wait for save to complete (clicking outside should save)
    await page.waitForTimeout(1000)

    // We should be back on documents page, so navigate back to verify the title was saved
    await page.waitForLoadState('networkidle')
    
    // Go back to the document to verify title was saved
    if (await firstDocument.isVisible()) {
      await firstDocument.click()
      await page.waitForLoadState('networkidle')
      await page.waitForTimeout(2000)

      const updatedEditableTitle = page.getByTestId('document-title-editable')
      await expect(updatedEditableTitle).toContainText(newTitle)
    }
  })
})