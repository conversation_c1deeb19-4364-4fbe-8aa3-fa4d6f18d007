/**
 * Playwright test for EKO-174: Scores are not showing up
 * 
 * This test verifies that scores are properly displayed in the customer app
 * when they exist in the xfer_score table.
 */

import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-174: Score Display Tests', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('should display scores when they exist in xfer_score', async ({ page }) => {
    // Navigate to a page that should display scores
    // This assumes there's a scores or dashboard page in the customer app
    await page.goto('/dashboard');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Look for score-related elements
    // These selectors should be updated based on the actual customer app UI
    const scoreElements = page.locator('[data-testid*="score"], [data-testid*="rating"]');
    
    // Wait for score elements to be visible (with generous timeout)
    await expect(scoreElements.first()).toBeVisible({ timeout: 60000 });
    
    // Verify that score values are displayed
    const scoreTexts = await scoreElements.allTextContents();
    
    // Check that we have some score-related content
    expect(scoreTexts.length).toBeGreaterThan(0);
    
    // Look for specific score indicators
    const hasScoreContent = scoreTexts.some(text => 
      text.includes('score') || 
      text.includes('rating') || 
      text.includes('Great') || 
      text.includes('Good') || 
      text.includes('Poor') ||
      /\d+/.test(text) // Contains numbers
    );
    
    expect(hasScoreContent).toBe(true);
  });

  test('should handle missing scores gracefully', async ({ page }) => {
    // This test verifies that the app doesn't break when scores are missing
    // Navigate to a page that might not have scores
    await page.goto('/dashboard');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check that the page loads without errors
    const errorMessages = page.locator('[data-testid="error"], .error, [role="alert"]');
    await expect(errorMessages).toHaveCount(0);
    
    // Check that the page has some content (not completely broken)
    const mainContent = page.locator('main, [data-testid="main-content"], .main-content');
    await expect(mainContent).toBeVisible();
  });

  test('should display score details when available', async ({ page }) => {
    // Navigate to a detailed view that should show score breakdown
    await page.goto('/dashboard');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Look for detailed score information
    const scoreDetails = page.locator('[data-testid*="score-detail"], [data-testid*="score-breakdown"]');
    
    // If score details exist, verify they contain meaningful information
    const detailCount = await scoreDetails.count();
    
    if (detailCount > 0) {
      // Verify score details are visible
      await expect(scoreDetails.first()).toBeVisible();
      
      // Check for specific score components that should be in XferScoreModel
      const detailTexts = await scoreDetails.allTextContents();
      const hasDetailContent = detailTexts.some(text => 
        text.includes('red flags') || 
        text.includes('green flags') || 
        text.includes('rating') ||
        text.includes('serious') ||
        text.includes('minor') ||
        text.includes('major')
      );
      
      expect(hasDetailContent).toBe(true);
    }
  });

  test('should show loading states while fetching scores', async ({ page }) => {
    // Navigate to dashboard and check for loading states
    await page.goto('/dashboard');
    
    // Look for loading indicators
    const loadingIndicators = page.locator('[data-testid*="loading"], .loading, .spinner');
    
    // Loading indicators might appear briefly, so we don't assert they must be visible
    // Instead, we verify the page eventually loads content
    await page.waitForLoadState('networkidle');
    
    // Verify that loading indicators are gone and content is visible
    const mainContent = page.locator('main, [data-testid="main-content"], .main-content');
    await expect(mainContent).toBeVisible();
  });

  test('should navigate to score-related pages without errors', async ({ page }) => {
    // Test navigation to various pages that might display scores
    const pagesToTest = [
      '/dashboard',
      '/entities', // If there's an entities page
      '/reports',  // If there's a reports page
    ];

    for (const pagePath of pagesToTest) {
      try {
        await page.goto(pagePath);
        await page.waitForLoadState('networkidle');
        
        // Check that the page loads without 404 or other errors
        const pageTitle = await page.title();
        expect(pageTitle).not.toContain('404');
        expect(pageTitle).not.toContain('Error');
        
        // Check for error messages on the page
        const errorMessages = page.locator('[data-testid="error"], .error, [role="alert"]');
        await expect(errorMessages).toHaveCount(0);
        
      } catch (error) {
        // If a page doesn't exist, that's okay - just log it
        console.log(`Page ${pagePath} not accessible: ${error}`);
      }
    }
  });

  test('should display score data in correct format', async ({ page }) => {
    // Navigate to a page with scores
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Look for score values and verify they're in expected format
    const scoreElements = page.locator('[data-testid*="score"]');
    
    if (await scoreElements.count() > 0) {
      const scoreTexts = await scoreElements.allTextContents();
      
      // Check for valid score formats (0-100 range, rating text, etc.)
      const hasValidScoreFormat = scoreTexts.some(text => {
        // Check for numeric scores (0-100)
        const numericMatch = text.match(/\b(\d{1,3})\b/);
        if (numericMatch) {
          const score = parseInt(numericMatch[1]);
          return score >= 0 && score <= 100;
        }
        
        // Check for rating text
        return ['Great', 'Very Good', 'Good', 'Poor', 'Very Poor'].some(rating => 
          text.includes(rating)
        );
      });
      
      expect(hasValidScoreFormat).toBe(true);
    }
  });

  test('should handle score updates correctly', async ({ page }) => {
    // This test verifies that scores update when new data is available
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Get initial score state
    const initialScoreElements = page.locator('[data-testid*="score"]');
    const initialCount = await initialScoreElements.count();
    
    // Refresh the page to simulate score updates
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Verify scores are still displayed after refresh
    const refreshedScoreElements = page.locator('[data-testid*="score"]');
    const refreshedCount = await refreshedScoreElements.count();
    
    // The count should be consistent (scores should persist)
    expect(refreshedCount).toBe(initialCount);
  });
});

test.describe('EKO-174: Score API Integration', () => {
  test('should fetch scores from the correct API endpoint', async ({ page }) => {
    // Monitor network requests to verify score data is being fetched
    const scoreRequests: string[] = [];
    
    page.on('request', request => {
      const url = request.url();
      if (url.includes('score') || url.includes('xfer_score')) {
        scoreRequests.push(url);
      }
    });
    
    // Navigate to dashboard
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Wait a bit for any async requests
    await page.waitForTimeout(2000);
    
    // Verify that score-related API calls were made
    // This helps ensure the frontend is actually trying to fetch score data
    console.log('Score-related requests:', scoreRequests);
    
    // The test passes if no errors occur during navigation
    // The actual API calls depend on the customer app implementation
  });
});
