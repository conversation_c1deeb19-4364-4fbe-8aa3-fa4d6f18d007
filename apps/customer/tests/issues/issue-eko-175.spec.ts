import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-175: Editor Auto Save on Navigation', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should auto-save document when navigating away via router', async ({ page }) => {
    // Create a new document
    const documentId = await testUtils.createDocumentFromTemplate()
    console.log('Created document with ID:', documentId)

    // Add some content to the editor
    const testContent = 'This is test content for auto-save on navigation - EKO-175'
    await testUtils.fillEditor(testContent)

    // Wait a moment for content to be registered
    await page.waitForTimeout(1000)

    // Navigate away using the back button
    await page.locator('[data-testid="back-button"]').click()

    // Wait for navigation to complete
    await page.waitForURL('/customer/documents')
    await page.waitForLoadState('networkidle')

    // Navigate back to the document to verify content was saved
    await page.goto(`/customer/documents/${documentId}`)
    await page.waitForLoadState('networkidle')

    // Wait for editor to load and verify content was auto-saved
    const editorContent = await testUtils.getEditorText()
    expect(editorContent).toContain(testContent)
  })

  test('should auto-save document when navigating via browser back button', async ({ page }) => {
    // Create a new document
    const documentId = await testUtils.createDocumentFromTemplate()

    // Add some content to the editor
    const testContent = 'Browser back button test content - EKO-175'
    await testUtils.fillEditor(testContent)

    // Wait a moment for content to be registered
    await page.waitForTimeout(1000)

    // Navigate to another page first
    await page.goto('/customer/dashboard')
    await page.waitForLoadState('networkidle')

    // Navigate back to the document
    await page.goto(`/customer/documents/${documentId}`)
    await page.waitForLoadState('networkidle')

    // Verify the content was auto-saved
    const editorContent = await testUtils.getEditorText()
    expect(editorContent).toContain(testContent)
  })

  test('should auto-save document when navigating via navigation links', async ({ page }) => {
    // Create a new document
    const documentId = await testUtils.createDocumentFromTemplate()

    // Add some content to the editor
    const testContent = 'Navigation link test content - EKO-175'
    await testUtils.fillEditor(testContent)

    // Wait a moment for content to be registered
    await page.waitForTimeout(1000)

    // Click on a navigation link (Dashboard)
    await page.locator('a[href*="/customer/dashboard"]').first().click()
    await page.waitForLoadState('networkidle')

    // Navigate back to the document to verify content was saved
    await page.goto(`/customer/documents/${documentId}`)
    await page.waitForLoadState('networkidle')

    // Verify the content was auto-saved
    const editorContent = await testUtils.getEditorText()
    expect(editorContent).toContain(testContent)
  })

  test('should show navigation auto-save status indicators', async ({ page }) => {
    // Create a new document
    await testUtils.createDocumentFromTemplate()

    // Add some content to trigger auto-save
    const testContent = 'Status indicator test - EKO-175'
    await testUtils.fillEditor(testContent)

    // Wait a moment for content to be registered
    await page.waitForTimeout(1000)

    // Start navigation and look for saving indicators
    const backButtonPromise = page.locator('[data-testid="back-button"]').click()

    // Check for saving status (this might be very quick)
    try {
      await page.waitForSelector('text=Syncing', { timeout: 2000 })
      console.log('Syncing status detected')
    } catch (e) {
      console.log('Syncing status not detected (save was too fast)')
    }

    await backButtonPromise
    await page.waitForLoadState('networkidle')
  })

  test('should handle rapid navigation without data loss', async ({ page }) => {
    // Create a new document
    const documentId = await testUtils.createDocumentFromTemplate()

    // Add content
    const testContent = 'Rapid navigation test - EKO-175'
    await testUtils.fillEditor(testContent)

    // Rapid navigation sequence
    await page.locator('[data-testid="back-button"]').click()
    await page.waitForTimeout(100) // Brief pause

    // Navigate back quickly
    await page.goto(`/customer/documents/${documentId}`)
    await page.waitForLoadState('networkidle')

    // Verify content was preserved despite rapid navigation
    const editorContent = await testUtils.getEditorText()
    expect(editorContent).toContain(testContent)
  })

  test('should work with existing auto-save mechanisms', async ({ page }) => {
    // Create a new document
    const documentId = await testUtils.createDocumentFromTemplate()

    // Add content and wait for time-based auto-save
    const testContent = 'Time-based auto-save compatibility test - EKO-175'
    await testUtils.fillEditor(testContent)

    // Wait for time-based auto-save to potentially trigger
    await page.waitForTimeout(6000) // Wait longer than auto-save interval

    // Now navigate away to trigger navigation auto-save
    await page.locator('[data-testid="back-button"]').click()
    await page.waitForLoadState('networkidle')

    // Navigate back to verify content
    await page.goto(`/customer/documents/${documentId}`)
    await page.waitForLoadState('networkidle')

    // Verify content was saved by either mechanism
    const editorContent = await testUtils.getEditorText()
    expect(editorContent).toContain(testContent)
  })
})
