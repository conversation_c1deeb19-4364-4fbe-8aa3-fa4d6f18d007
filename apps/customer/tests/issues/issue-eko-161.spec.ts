import { test, expect } from '@playwright/test'

test.describe('EKO-161: Show progress on report-groupings', () => {
  test('should display progress indicators for report groups with descendants', async ({ page }) => {
    // Mock a simple page with report groups
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Report Group Progress Test</title>
          <style>
            .report-group { border: 1px solid #ccc; padding: 16px; margin: 8px; border-radius: 8px; }
            .progress-info { font-size: 12px; padding: 4px 8px; border-radius: 16px; border: 1px solid; }
            .progress-bar { height: 6px; background: #e5e5e5; border-radius: 3px; margin: 4px 0; }
            .progress-fill { height: 100%; background: #22c55e; border-radius: 3px; transition: width 0.3s; }
          </style>
        </head>
        <body>
          <div class="report-group" data-testid="parent-group">
            <div class="header">
              <span>Parent Group</span>
              <span class="progress-info" data-testid="progress-count">2/3</span>
            </div>
            <div class="progress-section" data-testid="progress-section">
              <div class="progress-label">Sub-section progress: 67%</div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 67%"></div>
              </div>
            </div>
            <div class="report-group" data-testid="child-group-1">
              <span>Child Group 1 (loaded)</span>
            </div>
            <div class="report-group" data-testid="child-group-2">
              <span>Child Group 2 (loaded)</span>
            </div>
            <div class="report-group" data-testid="child-group-3">
              <span>Child Group 3 (loading)</span>
            </div>
          </div>
        </body>
      </html>
    `)

    // Test that progress indicators are visible
    const parentGroup = page.getByTestId('parent-group')
    await expect(parentGroup).toBeVisible()

    // Test progress count display
    const progressCount = page.getByTestId('progress-count')
    await expect(progressCount).toBeVisible()
    await expect(progressCount).toHaveText('2/3')

    // Test progress bar section
    const progressSection = page.getByTestId('progress-section')
    await expect(progressSection).toBeVisible()

    // Verify progress bar shows correct percentage
    const progressBar = progressSection.locator('.progress-fill')
    await expect(progressBar).toHaveAttribute('style', /width:\s*67%/)
  })

  test('should hide progress bar when group is 100% complete', async ({ page }) => {
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Complete Group Test</title>
          <style>
            .report-group { border: 1px solid #ccc; padding: 16px; margin: 8px; border-radius: 8px; }
            .progress-info { font-size: 12px; padding: 4px 8px; border-radius: 16px; border: 1px solid; }
          </style>
        </head>
        <body>
          <div class="report-group" data-testid="complete-group">
            <div class="header">
              <span>Complete Group</span>
              <span class="progress-info" data-testid="progress-count">3/3</span>
            </div>
            <!-- Progress bar should be hidden for 100% completion -->
          </div>
        </body>
      </html>
    `)

    const completeGroup = page.getByTestId('complete-group')
    await expect(completeGroup).toBeVisible()

    // Progress count should still be visible
    const progressCount = page.getByTestId('progress-count')
    await expect(progressCount).toBeVisible()
    await expect(progressCount).toHaveText('3/3')

    // Progress bar section should not exist for completed groups
    const progressSection = page.getByTestId('progress-section')
    await expect(progressSection).not.toBeVisible()
  })

  test('should not show progress indicators for groups with no descendants', async ({ page }) => {
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Empty Group Test</title>
          <style>
            .report-group { border: 1px solid #ccc; padding: 16px; margin: 8px; border-radius: 8px; }
          </style>
        </head>
        <body>
          <div class="report-group" data-testid="empty-group">
            <div class="header">
              <span>Empty Group</span>
              <!-- No progress indicators should be present -->
            </div>
          </div>
        </body>
      </html>
    `)

    const emptyGroup = page.getByTestId('empty-group')
    await expect(emptyGroup).toBeVisible()

    // No progress indicators should be visible for empty groups
    const progressCount = page.getByTestId('progress-count')
    await expect(progressCount).not.toBeVisible()

    const progressSection = page.getByTestId('progress-section')
    await expect(progressSection).not.toBeVisible()
  })

  test('should show tooltip with detailed progress information', async ({ page }) => {
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Tooltip Test</title>
          <style>
            .report-group { border: 1px solid #ccc; padding: 16px; margin: 8px; border-radius: 8px; }
            .progress-info { font-size: 12px; padding: 4px 8px; border-radius: 16px; border: 1px solid; cursor: help; }
            .tooltip { 
              position: absolute; 
              background: #333; 
              color: white; 
              padding: 8px; 
              border-radius: 4px; 
              font-size: 12px;
              display: none;
              z-index: 1000;
            }
            .progress-info:hover + .tooltip { display: block; }
          </style>
        </head>
        <body>
          <div class="report-group" data-testid="tooltip-group">
            <div class="header">
              <span>Group with Tooltip</span>
              <span class="progress-info" data-testid="progress-count">2/4</span>
              <div class="tooltip" data-testid="progress-tooltip">
                2 of 4 sub-sections completed<br>
                1 loading<br>
                1 errors
              </div>
            </div>
          </div>
        </body>
      </html>
    `)

    const progressCount = page.getByTestId('progress-count')
    await expect(progressCount).toBeVisible()

    // Hover over progress count to show tooltip
    await progressCount.hover()

    // Check that tooltip becomes visible and contains detailed information
    const tooltip = page.getByTestId('progress-tooltip')
    await expect(tooltip).toContainText('2 of 4 sub-sections completed')
    await expect(tooltip).toContainText('1 loading')
    await expect(tooltip).toContainText('1 errors')
  })
})