import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-167: Editor Styling', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display Table of Contents with flat border styling', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)
    
    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Insert a Table of Contents
    await page.locator('[data-testid="insert-toc-button"]').click()
    
    // Wait for TOC to be inserted
    await expect(page.locator('.table-of-contents-wrapper')).toBeVisible()
    
    // Check that TOC uses flat border styling instead of glass-card
    const tocContainer = page.locator('.table-of-contents-wrapper > div').first()
    
    // Verify it has border styling
    await expect(tocContainer).toHaveClass(/border-2/)
    await expect(tocContainer).toHaveClass(/border-slate-200/)
    await expect(tocContainer).toHaveClass(/rounded-lg/)
    await expect(tocContainer).toHaveClass(/bg-slate-50\/30/)
    await expect(tocContainer).toHaveClass(/backdrop-blur-sm/)
    
    // Verify it doesn't have glass-card class
    await expect(tocContainer).not.toHaveClass(/glass-card/)
  })

  test('should display editor toolbar with glass morphism effects', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)
    
    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Check that toolbar has glass morphism styling
    const toolbar = page.locator('[data-testid="editor-toolbar"]')
    await expect(toolbar).toBeVisible()
    
    // Verify glass effect styling
    await expect(toolbar).toHaveClass(/glass-effect-subtle/)
    await expect(toolbar).toHaveClass(/border-slate-200\/50/)
  })

  test('should display Clean toggle switch instead of Print button', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)
    
    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()
    
    // Check that Clean toggle container exists
    const toggleContainer = page.locator('[data-testid="print-toggle-container"]')
    await expect(toggleContainer).toBeVisible()
    
    // Verify it contains a label with "Clean" text
    const label = toggleContainer.locator('label')
    await expect(label).toHaveText('Clean')
    
    // Verify it contains a switch component
    const switchElement = toggleContainer.locator('[data-testid="print-toggle-button"]')
    await expect(switchElement).toBeVisible()
    
    // Verify the switch is initially unchecked (default off)
    await expect(switchElement).not.toBeChecked()
    
    // Verify there's no Printer icon or "Print"/"Normal" text
    await expect(toggleContainer.locator('svg')).toHaveCount(0) // No printer icon
    await expect(toggleContainer).not.toHaveText(/Print|Normal/)
  })

  test('should toggle clean mode when switch is clicked', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()
    
    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)
    
    // Wait for editor to load
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible()
    
    // Verify editor is not in clean mode initially
    await expect(editor).not.toHaveClass(/eko-print-mode/)
    
    // Find and click the clean mode switch
    const cleanSwitch = page.locator('[data-testid="print-toggle-button"]')
    await expect(cleanSwitch).toBeVisible()
    await expect(cleanSwitch).not.toBeChecked()
    
    // Click the switch to enable clean mode
    await cleanSwitch.click()
    
    // Verify switch is now checked
    await expect(cleanSwitch).toBeChecked()
    
    // Verify editor has clean mode class
    await expect(editor).toHaveClass(/eko-print-mode/)
    
    // Click again to disable clean mode
    await cleanSwitch.click()
    
    // Verify switch is unchecked
    await expect(cleanSwitch).not.toBeChecked()
    
    // Verify editor no longer has clean mode class
    await expect(editor).not.toHaveClass(/eko-print-mode/)
  })

  test('should remove table of contents border in clean mode', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    // Navigate to document editor
    await page.goto(`/customer/documents/${documentId}`)

    // Wait for editor to load
    await expect(page.locator('.ProseMirror')).toBeVisible()

    // Add some content with a Table of Contents
    await testUtils.typeInEditor('# Test Document\n\nThis is test content.')
    await page.locator('[data-testid="insert-toc-button"]').click()

    // Wait for TOC to be inserted
    await expect(page.locator('.table-of-contents-wrapper')).toBeVisible()

    const tocContainer = page.locator('.table-of-contents-wrapper > div').first()

    // Verify TOC has border in normal mode
    const normalStyles = await tocContainer.evaluate(el => {
      const styles = window.getComputedStyle(el)
      return {
        borderWidth: styles.borderWidth,
        backgroundColor: styles.backgroundColor
      }
    })

    // Should have border in normal mode
    expect(normalStyles.borderWidth).not.toBe('0px')

    // Enable clean mode
    const cleanSwitch = page.locator('[data-testid="print-toggle-button"]')
    await cleanSwitch.click()

    // Verify editor is in clean mode
    const editor = await testUtils.waitForEditor()
    await expect(editor).toHaveClass(/eko-print-mode/)

    // Check that TOC clean mode styles are applied - border should be removed
    const cleanStyles = await tocContainer.evaluate(el => {
      const styles = window.getComputedStyle(el)
      return {
        borderWidth: styles.borderWidth,
        backgroundColor: styles.backgroundColor,
        backdropFilter: styles.backdropFilter
      }
    })

    // In clean mode, border should be removed
    expect(cleanStyles.borderWidth).toBe('0px')
    // Background should be transparent
    expect(cleanStyles.backgroundColor).toMatch(/rgba\(0,\s*0,\s*0,\s*0\)|transparent/)
    // Backdrop filter should be none
    expect(cleanStyles.backdropFilter).toBe('none')

    // TOC should still be visible and functional
    await expect(tocContainer).toBeVisible()
  })
})
