import { test, expect } from '@playwright/test';

test.describe('EKO-219: Auto-Save failing', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate directly to the customer app (assuming auth is handled by environment)
    await page.goto('http://localhost:3000/customer');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should verify no infinite loop errors occur', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Navigate to document creation page
    await page.goto('http://localhost:3000/customer/documents/new');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Select a template to trigger document creation
    const blankTemplate = page.getByTestId('template-blank');
    if (await blankTemplate.count() > 0) {
      await blankTemplate.click();

      // Wait for potential document creation and editor loading
      await page.waitForTimeout(5000);
    }

    // Check for infinite loop errors
    const infiniteLoopErrors = consoleErrors.filter(error =>
      error.includes('Maximum update depth exceeded') ||
      error.includes('infinite loop') ||
      error.includes('Too many re-renders')
    );

    // Check for CORS errors (should not be present)
    const corsErrors = consoleErrors.filter(error =>
      error.includes('CORS') ||
      error.includes('Access-Control-Allow-Origin') ||
      error.includes('Failed to fetch')
    );

    // Log errors for debugging
    if (infiniteLoopErrors.length > 0) {
      console.log('Infinite loop errors found:', infiniteLoopErrors);
    }

    if (corsErrors.length > 0) {
      console.log('CORS Errors found:', corsErrors);
    }

    // The test should pass - no infinite loop or CORS errors
    expect(infiniteLoopErrors.length).toBe(0);
    expect(corsErrors.length).toBe(0);
  });

  test('should verify React components render without infinite loops', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Navigate to any customer page to test React rendering
    await page.goto('http://localhost:3000/customer');
    await page.waitForLoadState('networkidle');

    // Wait for React components to render
    await page.waitForTimeout(3000);

    // Check for infinite loop errors
    const infiniteLoopErrors = consoleErrors.filter(error =>
      error.includes('Maximum update depth exceeded') ||
      error.includes('infinite loop') ||
      error.includes('Too many re-renders')
    );

    // Log errors for debugging
    if (infiniteLoopErrors.length > 0) {
      console.log('Infinite loop errors found:', infiniteLoopErrors);
    }

    // The test should pass - no infinite loop errors
    expect(infiniteLoopErrors.length).toBe(0);
  });
});
