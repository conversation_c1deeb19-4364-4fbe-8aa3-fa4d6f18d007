import { test, expect } from '@playwright/test';
import { TestUtils } from '../helpers/tests/test-utils';

test.describe('EKO-188: View Entity Analysis Report Button Removal', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('should not display the non-functional "View Entity Analysis Report" button', async ({ page }) => {
    // Navigate to the flags page with a test entity
    await page.goto('/customer/dashboard?entity=AAPL&model=sdg');
    
    // Wait for the flags page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });
    
    // Verify the "View Entity Analysis Report" button is NOT present
    const analysisReportButton = page.locator('[data-testid="entity-analysis-report"]');
    await expect(analysisReportButton).not.toBeVisible();
    
    // Also check that no button with this text exists anywhere on the page
    const buttonWithText = page.locator('button', { hasText: 'View Entity Analysis Report' });
    await expect(buttonWithText).not.toBeVisible();
    
    // Also check for any link with this text
    const linkWithText = page.locator('a', { hasText: 'View Entity Analysis Report' });
    await expect(linkWithText).not.toBeVisible();
    
    console.log('✅ Verified that the "View Entity Analysis Report" button has been successfully removed');
  });

  test('should still display other functionality on the flags page', async ({ page }) => {
    // Navigate to the flags page with a test entity
    await page.goto('/customer/dashboard?entity=AAPL&model=sdg');
    
    // Wait for the flags page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });
    
    // Verify that other important elements are still present and functional
    // Check for filter controls
    const searchInput = page.locator('input[placeholder*="Search"]');
    if (await searchInput.count() > 0) {
      await expect(searchInput.first()).toBeVisible();
    }
    
    // Check for flags list or no flags message
    const flagsList = page.locator('[data-testid="flags-list"]');
    const noFlagsMessage = page.locator('[data-testid="no-flags-message"]');
    
    // Either flags list or no flags message should be visible
    await expect(flagsList.or(noFlagsMessage)).toBeVisible({ timeout: 10000 });
    
    console.log('✅ Verified that other page functionality remains intact');
  });

  test('should not have any broken links to /customer/dashboard/flags/report', async ({ page }) => {
    // Navigate to the flags page
    await page.goto('/customer/dashboard?entity=AAPL&model=sdg');
    
    // Wait for the page to load
    await expect(page.locator('[data-testid="flags-page-content"]')).toBeVisible({ timeout: 30000 });
    
    // Check that no links point to the non-existent report route
    const reportLinks = page.locator('a[href*="/customer/dashboard/flags/report"]');
    await expect(reportLinks).toHaveCount(0);
    
    console.log('✅ Verified that no broken links to the report route exist');
  });
});
