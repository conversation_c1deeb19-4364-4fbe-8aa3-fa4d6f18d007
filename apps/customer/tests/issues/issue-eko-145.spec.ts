import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('Issue EKO-145: AI Slash Commands', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Mock AI API endpoints to prevent timeouts during testing
    await page.route('/api/ai/**', async route => {
      // Mock successful AI response to prevent timeouts
      await route.fulfill({
        status: 200,
        contentType: 'text/plain',
        body: 'data: {"content":"Mock AI response for testing"}\n\ndata: [DONE]\n\n'
      });
    });

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should show AI commands submenu when typing /ai', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('Blank Document')
    const editor = await testUtils.waitForEditor()

    // Click in editor and type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Wait for AI commands menu with a longer timeout
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 30000 })

    // Check that at least some command options are visible (reduce test scope)
    await expect(page.locator('[data-testid="ai-slash-command-improve"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="ai-slash-command-grammar"]')).toBeVisible({ timeout: 10000 })
  })

  test('should dismiss AI menu when clicking outside', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('Blank Document')
    const editor = await testUtils.waitForEditor()

    // Type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Verify menu is visible with longer timeout
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 30000 })

    // Move mouse away to hide any tooltips that might interfere
    await page.mouse.move(0, 0)
    await page.waitForTimeout(500)

    // Click outside the menu with force to bypass any tooltip overlays
    await editor.click({ position: { x: 100, y: 100 }, force: true })

    // Menu should be dismissed
    await expect(page.locator('[data-testid="ai-commands-header"]')).not.toBeVisible({ timeout: 10000 })
  })

  test('should remove slash text when executing AI command', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('Blank Document')
    const editor = await testUtils.waitForEditor()

    // Add some text first
    await testUtils.typeInEditor('Test text for improvement')
    await page.keyboard.press('Enter')

    // Type AI slash command
    await page.keyboard.type('/ai')

    // Wait for AI commands menu to appear with longer timeout
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 30000 })

    // Click on improve command - this will be mocked so it won't cause API timeouts
    await page.click('[data-testid="ai-slash-command-improve"]')

    // Wait for the command to process (mocked response)
    await page.waitForTimeout(1000)

    // The /ai text should be removed from the editor
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).not.toContainText('/ai', { timeout: 10000 })
  })

  test('should handle keyboard navigation in AI menu', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('Blank Document')
    const editor = await testUtils.waitForEditor()

    // Type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Wait for AI commands menu to appear with longer timeout
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 30000 })

    // Test keyboard navigation
    await page.keyboard.press('ArrowDown')
    await page.keyboard.press('ArrowUp')

    // Press Enter to select current option (mocked response)
    await page.keyboard.press('Enter')

    // Wait for the command to process (mocked response)
    await page.waitForTimeout(1000)

    // The /ai text should be removed
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).not.toContainText('/ai', { timeout: 10000 })
  })

  test('should dismiss AI menu with Escape key', async ({ page }) => {
    await testUtils.createDocumentFromTemplate('Blank Document')
    const editor = await testUtils.waitForEditor()

    // Type AI slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Wait for AI commands menu to appear with longer timeout
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible({ timeout: 30000 })

    // Press Escape to dismiss
    await page.keyboard.press('Escape')

    // Menu should be dismissed
    await expect(page.locator('[data-testid="ai-commands-header"]')).not.toBeVisible({ timeout: 10000 })
    
    // The /ai text should still be there since we didn't execute a command
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).toContainText('/ai', { timeout: 10000 })
  })
})