import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

// Helper function to scroll to and click EKO Report template
async function scrollAndClickEKOTemplate(page: any) {
  // Find the EKO Report template card
  const ekoTemplate = page.locator('[data-testid="template-dialog"] .grid > div').filter({ hasText: 'EKO Report' }).first()

  // First try scrollIntoViewIfNeeded
  await ekoTemplate.scrollIntoViewIfNeeded()

  // Wait for template to be visible
  await expect(ekoTemplate).toBeVisible({ timeout: 5000 })
  await expect(ekoTemplate).toBeEnabled()

  // Click the template
  await ekoTemplate.click({ force: true })

  return ekoTemplate
}

test.describe('EKO-138: On Document Creation Hide List', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
    
    // Navigate to documents page
    await page.goto('/customer/documents')
    
    // Wait for the page to load - look for more flexible indicators
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000) // Give time for UI to stabilize
  })

  test('should hide document list and show loading state when New Document is clicked', async ({ page }) => {
    // Check initial state - determine if we have documents or empty state
    const documentsGrid = page.locator('[data-testid="documents-list"]')
    const emptyState = page.locator('text=No documents yet')
    
    const hasDocuments = await documentsGrid.isVisible()
    const hasEmptyState = await emptyState.isVisible()
    
    // Click the New Document button
    await page.click('[data-testid="new-document-button"]')

    // Wait for dialog to open
    await expect(page.locator('[data-testid="template-dialog"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('text=Choose a Template')).toBeVisible()

    // Verify loading state is shown 
    await expect(page.locator('[data-testid="document-creation-loading"]')).toBeVisible()
    await expect(page.locator('text=Creating document...')).toBeVisible()
    await expect(page.locator('text=Please select a template to continue')).toBeVisible()

    // Verify the initial content is hidden (either documents list or empty state)
    if (hasDocuments) {
      await expect(documentsGrid).not.toBeVisible()
    }
    if (hasEmptyState) {
      await expect(emptyState).not.toBeVisible()
    }
  })

  test('should restore document list when template dialog is closed', async ({ page }) => {
    // Check initial state before clicking New Document
    const documentsGrid = page.locator('[data-testid="documents-list"]')
    const emptyState = page.locator('text=No documents yet')
    
    const hasDocuments = await documentsGrid.isVisible()
    const hasEmptyState = await emptyState.isVisible()

    // Click New Document button to trigger creation state
    await page.click('[data-testid="new-document-button"]')

    // Verify loading state is shown
    await expect(page.locator('[data-testid="document-creation-loading"]')).toBeVisible()

    // Close the dialog by clicking Cancel
    await page.click('[data-testid="cancel-button"]')

    // Verify loading state is hidden
    await expect(page.locator('[data-testid="document-creation-loading"]')).not.toBeVisible()

    // Verify the original state is restored
    if (hasDocuments) {
      await expect(documentsGrid).toBeVisible()
    }
    if (hasEmptyState) {
      await expect(emptyState).toBeVisible()
    }

    // Verify template dialog is closed
    await expect(page.locator('[data-testid="template-dialog"]')).not.toBeVisible()
  })

  test('should restore document list when template dialog is closed by escape key', async ({ page }) => {
    // Check initial state before clicking New Document
    const documentsGrid = page.locator('[data-testid="documents-list"]')
    const emptyState = page.locator('text=No documents yet')
    
    const hasDocuments = await documentsGrid.isVisible()
    const hasEmptyState = await emptyState.isVisible()

    // Click New Document button to trigger creation state
    await page.click('[data-testid="new-document-button"]')

    // Verify loading state is shown
    await expect(page.locator('[data-testid="document-creation-loading"]')).toBeVisible()

    // Close the dialog by pressing Escape
    await page.keyboard.press('Escape')

    // Verify template dialog is closed first (condition-based wait)
    await expect(page.locator('[data-testid="template-dialog"]')).not.toBeVisible()

    // Verify loading state is hidden
    await expect(page.locator('[data-testid="document-creation-loading"]')).not.toBeVisible()

    // Verify the original state is restored
    if (hasDocuments) {
      await expect(documentsGrid).toBeVisible()
    }
    if (hasEmptyState) {
      await expect(emptyState).toBeVisible()
    }
  })

  test('should navigate to document page when template is selected', async ({ page }) => {
    // Click New Document button
    await page.click('[data-testid="new-document-button"]')

    // Verify loading state is shown
    await expect(page.locator('[data-testid="document-creation-loading"]')).toBeVisible()

    // Wait for template dialog to be visible
    await expect(page.locator('[data-testid="template-dialog"]')).toBeVisible()

    // Select the EKO Report template using helper function
    await scrollAndClickEKOTemplate(page)

    // Wait for document creation and navigation to complete
    await page.waitForTimeout(3000) // Give time for the creation process
    
    // After template selection, document should be created and we should navigate to editor
    await expect(page.locator('[data-testid="eko-document-editor"]')).toBeVisible({ timeout: 15000 })
    
    // Verify URL changed to document editor
    await expect(page).toHaveURL(/\/customer\/documents\/[^\/]+/)
  })

  test('should show appropriate loading message during document creation', async ({ page }) => {
    // Click New Document button
    await page.click('[data-testid="new-document-button"]')

    // Verify specific loading messages are shown
    await expect(page.locator('text=Creating document...')).toBeVisible()
    await expect(page.locator('text=Please select a template to continue')).toBeVisible()

    // Verify loading spinner is present
    await expect(page.locator('[data-testid="document-creation-loading"] .animate-spin')).toBeVisible()
  })
})