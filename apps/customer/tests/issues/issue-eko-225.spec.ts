import { test, expect } from '@playwright/test';
import { TestUtils } from '../helpers/tests/test-utils';

test.describe('EKO-225: Timeline dates', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('timeline should display events in chronological order including recent years', async ({ page }) => {
    // Navigate to the dashboard timeline
    await page.goto('/customer/dashboard');
    
    // Wait for the timeline to load
    await page.waitForSelector('[data-testid="dashboard-timeline"]', { timeout: 30000 });
    
    // Get all timeline entries
    const timelineEntries = page.locator('[data-testid="timeline-entry"]');
    const entryCount = await timelineEntries.count();
    expect(entryCount).toBeGreaterThan(0);
    
    // Extract all years from timeline entries
    const timelineDates = await page.locator('[data-testid="timeline-date"]').allTextContents();
    console.log('Timeline dates found:', timelineDates);
    
    // Convert to numbers and check chronological order
    const years = timelineDates.map(date => parseInt(date.trim())).filter(year => !isNaN(year));
    
    // Verify we have years
    expect(years.length).toBeGreaterThan(0);
    
    // Check chronological ordering (ascending)
    for (let i = 1; i < years.length; i++) {
      expect(years[i]).toBeGreaterThanOrEqual(years[i - 1]);
    }
    
    console.log('✓ Timeline is in chronological order:', years);
    
    // Verify we have recent years (not just stopping at 2023)
    const hasRecentYears = years.some(year => year >= 2024);
    if (hasRecentYears) {
      console.log('✓ Timeline includes recent years (2024+)');
    } else {
      console.log('⚠ Timeline may not include most recent years');
    }
    
    // Verify bookend dates - should include earliest and latest available years
    if (years.length >= 2) {
      const earliestYear = Math.min(...years);
      const latestYear = Math.max(...years);
      
      expect(years).toContain(earliestYear);
      expect(years).toContain(latestYear);
      
      console.log(`✓ Timeline includes bookend years: ${earliestYear} (earliest) and ${latestYear} (latest)`);
    }
  });

  test('timeline should handle different entities consistently', async ({ page }) => {
    // Test with multiple entities to ensure consistent behavior
    const entities = ['john-lewis', 'colgate', 'inflexion'];
    
    for (const entity of entities) {
      console.log(`Testing timeline for entity: ${entity}`);
      
      // Navigate to entity-specific dashboard if URL pattern supports it
      await page.goto(`/customer/dashboard?entity=${entity}`);
      
      // Wait for timeline to load
      try {
        await page.waitForSelector('[data-testid="dashboard-timeline"]', { timeout: 10000 });
        
        const timelineDates = await page.locator('[data-testid="timeline-date"]').allTextContents();
        const years = timelineDates.map(date => parseInt(date.trim())).filter(year => !isNaN(year));
        
        if (years.length > 0) {
          // Check chronological ordering
          for (let i = 1; i < years.length; i++) {
            expect(years[i]).toBeGreaterThanOrEqual(years[i - 1]);
          }
          
          console.log(`✓ ${entity} timeline is chronologically ordered:`, years);
        } else {
          console.log(`- No timeline data found for ${entity}`);
        }
      } catch (error) {
        console.log(`- Could not load timeline for ${entity}:`, error);
      }
    }
  });

  test('timeline API returns chronologically sorted data', async ({ page }) => {
    // Test the API directly to ensure sorting is working
    const response = await page.request.post('/api/timeline', {
      data: {
        preamble: '',
        obj: {
          red: [
            { year: 2024, title: 'Recent Event' },
            { year: 2020, title: 'Earlier Event' },
            { year: 2022, title: 'Middle Event' }
          ],
          green: [
            { year: 2023, title: 'Another Recent Event' },
            { year: 2019, title: 'Old Event' }
          ]
        },
        version: '1.16',
        includeDisclosures: false
      }
    });

    expect(response.status()).toBe(200);
    const data = await response.json();
    
    expect(data.response).toBeDefined();
    expect(Array.isArray(data.response)).toBe(true);
    
    if (data.response.length > 1) {
      // Check that years are in ascending order
      const years = data.response.map((event: any) => parseInt(event.year)).filter((year: number) => !isNaN(year));
      
      for (let i = 1; i < years.length; i++) {
        expect(years[i]).toBeGreaterThanOrEqual(years[i - 1]);
      }
      
      console.log('✓ API returns chronologically sorted timeline:', years);
    }
  });
});