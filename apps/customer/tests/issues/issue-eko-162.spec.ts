import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-162: Nested Report Group States', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should handle nested report groups without infinite loading states', async ({ page }) => {
    test.setTimeout(300000) // 5 minutes

    // Mock API responses for report sections
    let apiCallCount = 0
    page.route('**/api/report/**', route => {
      apiCallCount++
      console.log(`API call #${apiCallCount} to ${route.request().url()}`)
      
      const mockResponse = {
        text: `<h2>Section Content ${apiCallCount}</h2><p>Generated content for API call ${apiCallCount}</p>`,
        citations: [],
      }

      // Add delay to simulate real API behavior
      setTimeout(() => {
        route.fulfill({
          status: 200,
          headers: { 'content-type': 'application/json' },
          body: JSON.stringify(mockResponse),
        })
      }, 500)
    })

    // Create a new document for testing
    await testUtils.createDocumentFromTemplate('Blank Document')
    await page.waitForLoadState('networkidle')
    await testUtils.waitForEditor()

    // Create a simplified nested structure to test loading states
    // Parent group
    await testUtils.addReportComponent('group', {
      id: 'parent-group',
      title: 'Parent Group'
    })

    // Wait for the editor to update after component creation
    await page.waitForTimeout(1000)
    
    // Wait for parent group to be visible and ready
    console.log('Waiting for parent group to be visible...')
    await expect(page.locator('text=parent-group')).toBeVisible({ timeout: 30000 })
    console.log('Component created with ID visible')
    
    // Click on the parent group content area to position cursor inside it
    // Since the components render as simple HTML, click on the title text
    const parentTitle = page.locator('text=Parent Group').last()
    await parentTitle.click()
    
    // Wait a moment for the cursor position to update
    await page.waitForTimeout(1000)
    
    // Child group 1 inside parent
    console.log('Adding child group...')
    await testUtils.addReportComponent('group', {
      id: 'child-group-1',
      title: 'Child Group 1'
    })

    // Wait for child group to be visible and ready
    console.log('Waiting for child group to be visible...')
    await expect(page.locator('text=child-group-1')).toBeVisible({ timeout: 30000 })
    await page.waitForTimeout(2000)
    
    // Add section to child group 1
    console.log('Clicking child group to add section...')
    const childTitle = page.locator('text=Child Group 1').last()
    await childTitle.click()
    await page.waitForTimeout(1000)
    
    await testUtils.addReportComponent('section', {
      id: 'section-1',
      title: 'Section 1',
      endpoint: '/api/report/section1'
    })

    // Add another section to child group 1
    console.log('Adding second section to child group...')
    await page.waitForTimeout(1000)
    await childTitle.click()
    await testUtils.addReportComponent('section', {
      id: 'section-2',
      title: 'Section 2',
      endpoint: '/api/report/section2'
    })

    // Wait for components to register
    await page.waitForTimeout(3000)

    // Monitor for infinite loading states by checking spinners
    console.log('Monitoring for infinite loading states...')
    let loadingCheckCount = 0
    const maxLoadingChecks = 20
    let hasInfiniteLoading = false
    
    while (loadingCheckCount < maxLoadingChecks) {
      const spinners = await page.locator('.animate-spin').count()
      console.log(`Loading check ${loadingCheckCount + 1}: Found ${spinners} loading spinners`)
      
      if (spinners === 0) {
        console.log('All components finished loading')
        break
      }
      
      loadingCheckCount++
      
      // If we've been checking for more than 10 iterations with spinners, it might be infinite
      if (loadingCheckCount > 10 && spinners > 0) {
        hasInfiniteLoading = true
      }
      
      await page.waitForTimeout(1000)
    }
    
    // Verify we didn't detect infinite loading
    expect(hasInfiniteLoading).toBe(false)
    console.log(`Loading checks completed. Total checks: ${loadingCheckCount}`)
    
    // Verify content loaded successfully
    const sectionContent = await page.locator('text=Section Content').count()
    console.log(`Found ${sectionContent} loaded section contents`)
    expect(sectionContent).toBeGreaterThan(0)
    
    // Check that API calls were reasonable (not infinite)
    console.log(`Total API calls made: ${apiCallCount}`)
    expect(apiCallCount).toBeLessThan(20)
    
    // Verify no error states
    const errorElements = await page.locator('.text-red-600, .border-red-200').count()
    expect(errorElements).toBe(0)
    
    console.log('Test completed successfully - no infinite loading states detected')
  })

  test('should prevent circular status updates between parent and child groups', async ({ page }) => {
    test.setTimeout(180000) // 3 minutes

    // Mock API with faster responses for this test
    page.route('**/api/report/**', route => {
      const mockResponse = {
        text: '<h2>Quick Response</h2><p>Fast loading content</p>',
        citations: [],
      }

      route.fulfill({
        status: 200,
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(mockResponse),
      })
    })

    // Track console messages for circular update detection
    const statusUpdateMessages: string[] = []
    page.on('console', msg => {
      const text = msg.text()
      if (text.includes('status') || text.includes('sync') || text.includes('circular')) {
        statusUpdateMessages.push(text)
      }
    })

    // Create a new document for testing
    await testUtils.createDocumentFromTemplate('Blank Document')
    await page.waitForLoadState('networkidle')
    await testUtils.waitForEditor()

    // Create nested structure using proper component creation
    await testUtils.addReportComponent('group', {
      id: 'parent',
      title: 'Parent'
    })

    // Wait for parent group to be rendered - use the ID in the header
    await expect(page.locator('.text-sm.font-medium').filter({ hasText: 'parent' })).toBeVisible({ timeout: 30000 })
    await page.waitForTimeout(1000)
    
    // Click on parent to add child
    const parentTitle = page.locator('text=Parent').last()
    await parentTitle.click()
    await page.waitForTimeout(500)
    
    await testUtils.addReportComponent('group', {
      id: 'child',
      title: 'Child'
    })

    // Wait for child group to be rendered - use the ID in the header
    await expect(page.locator('.text-sm.font-medium').filter({ hasText: 'child' })).toBeVisible({ timeout: 30000 })
    await page.waitForTimeout(1000)
    
    // Click on child to add section
    const childTitle = page.locator('text=Child').last()
    await childTitle.click()
    await page.waitForTimeout(500)
    
    await testUtils.addReportComponent('section', {
      id: 'section',
      title: 'Section',
      endpoint: '/api/report/test'
    })

    await page.waitForTimeout(2000)

    // Wait for content to load
    await expect(page.locator('text=Quick Response')).toBeVisible({ timeout: 60000 })

    // Wait a bit more to catch any delayed circular updates
    await page.waitForTimeout(5000)

    // Analyze console messages for patterns indicating circular updates
    const circularPatterns = [
      'Too many sync operations',
      'infinite loop',
      'circular',
      'maximum call stack'
    ]
    
    const problematicMessages = statusUpdateMessages.filter(msg => 
      circularPatterns.some(pattern => msg.toLowerCase().includes(pattern))
    )

    console.log(`Total status update messages: ${statusUpdateMessages.length}`)
    console.log(`Problematic messages: ${problematicMessages.length}`)
    
    if (problematicMessages.length > 0) {
      console.log('Problematic messages found:')
      problematicMessages.forEach(msg => console.log('  -', msg))
    }

    // Should have minimal problematic messages
    expect(problematicMessages.length).toBeLessThan(5)

    // Check that components loaded successfully
    const loadedContent = await page.locator('text=Quick Response').count()
    expect(loadedContent).toBeGreaterThan(0)

    console.log('Circular update test completed successfully')
  })
})