import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

/**
 * Test for EKO-130: Auto Saving
 * 
 * Tests that:
 * 1. When a report section finishes loading, the document should be saved
 * 2. When all report components load, an automatic save version should be created
 * 3. Auto-saves should be marked as "Automatic Save" not "Manual Save"
 */

test.describe('EKO-130: Auto Saving', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should auto-save when document content is updated', async ({ page }) => {
    // Create a simple document and test the auto-save UI behavior
    await testUtils.createDocumentFromTemplate('Blank Document')
    
    // Wait for editor to load
    await testUtils.waitForEditor()

    // Type some content to trigger auto-save
    await testUtils.typeInEditor('This is test content that should trigger auto-save.')

    // Wait for auto-save to complete - this will verify the auto-save mechanism works
    await testUtils.waitForAutoSave()
    
    // Verify that the document was actually saved by checking the save indicator
    const saveIndicator = page.locator('[data-testid="save-indicator"]')
    const indicatorText = await saveIndicator.textContent()
    // The indicator should show either "Up to date" or "Saved at [time]"
    expect(indicatorText).toMatch(/Up to date|Saved at/)
    
    // Verify the editor still contains our content
    const editorContent = await page.locator('.ProseMirror').textContent()
    expect(editorContent).toContain('This is test content that should trigger auto-save.')
  })

  test('should create auto-save version when components are added', async ({ page }) => {
    // Create ESG Report document which has components
    await testUtils.createDocumentFromTemplate('ESG Report')
    await testUtils.waitForEditor()

    // Position cursor at the end before adding component
    await page.click('.ProseMirror')
    await page.keyboard.press('End')
    await page.keyboard.press('Enter')

    // Add a report component to trigger auto-save
    await testUtils.addReportComponent('section', {
      id: 'test-section-1',
      title: 'Test Section 1',
      endpoint: '/api/report/entity/[ENTITY_ID]/test-endpoint-1/[RUN_ID]'
    })

    // Verify component was added
    await expect(page.locator('[data-id="test-section-1"]')).toBeVisible({ timeout: 20000 })

    // Wait for any auto-save to complete
    await testUtils.waitForAutoSave()
    
    // Verify the component is present and functional
    await expect(page.locator('[data-id="test-section-1"]')).toBeVisible()
    await expect(page.locator('[data-id="test-section-1"]')).toContainText('Test Section 1')
    
    // Verify save indicator shows saved state
    const saveIndicator = page.locator('[data-testid="save-indicator"]')
    // The indicator should show either "Up to date" or "Saved at [time]"
    await expect(saveIndicator).toHaveText(/Up to date|Saved at/)
  })

  test('should distinguish between manual and automatic saves', async ({ page }) => {
    // Create a document and test both manual and auto-save
    await testUtils.createDocumentFromTemplate('Blank Document')
    await testUtils.waitForEditor()

    // Try to trigger a manual save via keyboard shortcut
    await page.keyboard.press('ControlOrMeta+s')
    
    // Add content to trigger auto-save
    await testUtils.typeInEditor('This content should trigger automatic save behavior.')

    // Wait for auto-save to complete
    await testUtils.waitForAutoSave()
    
    // Verify the editor content was saved
    const editorContent = await page.locator('.ProseMirror').textContent()
    expect(editorContent).toContain('This content should trigger automatic save behavior.')
    
    // Verify the save indicator reflects the saved state
    const saveIndicator = page.locator('[data-testid="save-indicator"]')
    // The indicator should show either "Up to date" or "Saved at [time]"
    await expect(saveIndicator).toHaveText(/Up to date|Saved at/)
  })

  test('should handle multiple components being added sequentially', async ({ page }) => {
    // Create ESG Report document
    await testUtils.createDocumentFromTemplate('ESG Report')
    await testUtils.waitForEditor()

    // Wait for template components to fully load before adding new ones
    await testUtils.waitForComponentLoading('.report-section')

    console.log('Adding first sequential component...')
    // Position cursor more reliably using Control+End
    await page.click('.ProseMirror')
    await page.keyboard.press('Control+End')
    await page.keyboard.press('Enter')
    await page.waitForTimeout(500)

    // Add multiple report sections sequentially with better timing
    await testUtils.addReportComponent('section', {
      id: 'seq-section-1',
      title: 'Sequential Section 1',
      endpoint: '/api/report/entity/[ENTITY_ID]/seq-1/[RUN_ID]'
    })

    // Wait for first component to be fully rendered and auto-save to complete
    await expect(page.locator('[data-id="seq-section-1"]')).toBeVisible({ timeout: 30000 })
    await testUtils.waitForAutoSave()
    
    console.log('Adding second sequential component...')
    // Ensure cursor positioning works by positioning at the very end again
    await page.keyboard.press('Control+End')
    await page.keyboard.press('Enter')
    await page.waitForTimeout(500)

    await testUtils.addReportComponent('section', {
      id: 'seq-section-2',
      title: 'Sequential Section 2',
      endpoint: '/api/report/entity/[ENTITY_ID]/seq-2/[RUN_ID]'
    })

    // Wait for second component to be fully rendered and auto-save to complete
    await expect(page.locator('[data-id="seq-section-2"]')).toBeVisible({ timeout: 30000 })
    await testUtils.waitForAutoSave()
    
    console.log('Adding third sequential component...')
    // Position cursor for third component
    await page.keyboard.press('Control+End')
    await page.keyboard.press('Enter')
    await page.waitForTimeout(500)

    await testUtils.addReportComponent('section', {
      id: 'seq-section-3',
      title: 'Sequential Section 3',
      endpoint: '/api/report/entity/[ENTITY_ID]/seq-3/[RUN_ID]'
    })

    // Wait for third component to be fully rendered
    await expect(page.locator('[data-id="seq-section-3"]')).toBeVisible({ timeout: 30000 })

    // Verify all components are still visible (in case any were removed during the process)
    console.log('Verifying all components are present...')
    await expect(page.locator('[data-id="seq-section-1"]')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('[data-id="seq-section-2"]')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('[data-id="seq-section-3"]')).toBeVisible({ timeout: 5000 })

    // Wait for final auto-save operations to complete
    await testUtils.waitForAutoSave()

    console.log('Sequential component addition test completed successfully')
  })
})