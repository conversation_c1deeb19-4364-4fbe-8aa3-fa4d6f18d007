import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-136: Feature Flags System', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display dashboard navigation with default feature flags', async ({ page }) => {
    // Use lightweight navigation-only test by going to documents page first
    await page.goto('/customer/documents', { timeout: 30000 })
    await page.waitForLoadState('domcontentloaded')
    
    // Check the main Dashboard section is visible in navigation
    await expect(page.locator('[data-testid="nav-section-dashboard"]')).toBeVisible({ timeout: 10000 })
    // Check the Flags sub-item is visible within the Dashboard section
    await expect(page.locator('[data-testid="nav-flags"]')).toBeVisible({ timeout: 10000 })
  })

  test('should display greenwashing navigation items when feature enabled', async ({ page }) => {
    // Use documents page to test navigation without heavy dashboard data loading
    await page.goto('/customer/documents', { timeout: 30000 })
    await page.waitForLoadState('domcontentloaded')
    
    await expect(page.locator('[data-testid="nav-cherry-picking"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="nav-claims"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="nav-promises"]')).toBeVisible({ timeout: 10000 })
  })

  test('should display document creation navigation when feature enabled', async ({ page }) => {
    await page.goto('/customer/documents', { timeout: 30000 })
    await page.waitForLoadState('domcontentloaded')
    
    await expect(page.locator('[data-testid="nav-create"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="nav-view"]')).toBeVisible({ timeout: 10000 })
  })

  test('should maintain consistent navigation across page loads', async ({ page }) => {
    await page.goto('/customer/documents', { timeout: 30000 })
    await page.waitForLoadState('domcontentloaded')
    await expect(page.locator('[data-testid="nav-section-dashboard"]')).toBeVisible({ timeout: 10000 })
    
    await page.goto('/customer/documents', { timeout: 30000 })
    await page.waitForLoadState('domcontentloaded')
    await expect(page.locator('[data-testid="nav-section-documents"]')).toBeVisible({ timeout: 10000 })
  })

  test('should load page content correctly with feature flags', async ({ page }) => {
    await page.goto('/customer/documents', { timeout: 30000 })
    await page.waitForLoadState('domcontentloaded')
    
    expect(page.url()).toContain('/customer/documents')
    await expect(page.locator('[data-testid="nav-section-dashboard"]')).toBeVisible({ timeout: 10000 })
  })
})