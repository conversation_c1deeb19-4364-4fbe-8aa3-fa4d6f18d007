import { test, expect } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('EKO-192: Collapsible Impact Assessment', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display impact assessment as collapsible at bottom of flag detail', async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/customer/dashboard')
    await page.waitForLoadState('networkidle')

    // Wait for flags to load and find a flag with impact assessment data
    await page.waitForSelector('[data-testid="flag-detail-modal"]', { timeout: 60000 })
    
    // Look for a flag badge that we can click to open the modal
    const flagBadges = page.locator('[data-testid*="flag-badge"]')
    const flagCount = await flagBadges.count()
    
    if (flagCount === 0) {
      test.skip()
      return
    }

    // Click on the first flag to open the detail modal
    await flagBadges.first().click()
    await page.waitForTimeout(1000) // Allow modal to open

    // Verify the collapsible impact assessment component is present
    const impactAssessment = page.locator('[data-testid="collapsible-impact-assessment"]')
    await expect(impactAssessment).toBeVisible({ timeout: 30000 })

    // Verify the toggle button is present
    const toggleButton = page.locator('[data-testid="impact-assessment-toggle"]')
    await expect(toggleButton).toBeVisible()

    // Verify the content is initially collapsed (not visible)
    const assessmentContent = page.locator('[data-testid="impact-assessment-content"]')
    await expect(assessmentContent).not.toBeVisible()

    // Click to expand the impact assessment
    await toggleButton.click()
    await page.waitForTimeout(500) // Allow animation

    // Verify the content is now visible
    await expect(assessmentContent).toBeVisible()

    // Verify key elements are present in the expanded content
    await expect(assessmentContent.locator('text=Net Impact Score')).toBeVisible()
    await expect(assessmentContent.locator('text=Harm Assessment')).toBeVisible()
    await expect(assessmentContent.locator('text=Benefit Assessment')).toBeVisible()

    // Click to collapse the impact assessment
    await toggleButton.click()
    await page.waitForTimeout(500) // Allow animation

    // Verify the content is collapsed again
    await expect(assessmentContent).not.toBeVisible()
  })

  test('should position impact assessment at bottom of flag detail', async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/customer/dashboard')
    await page.waitForLoadState('networkidle')

    // Wait for flags to load
    await page.waitForSelector('[data-testid="flag-detail-modal"]', { timeout: 60000 })
    
    // Look for a flag badge that we can click
    const flagBadges = page.locator('[data-testid*="flag-badge"]')
    const flagCount = await flagBadges.count()
    
    if (flagCount === 0) {
      test.skip()
      return
    }

    // Click on the first flag to open the detail modal
    await flagBadges.first().click()
    await page.waitForTimeout(1000)

    // Verify the flag analysis content comes before the impact assessment
    const flagAnalysis = page.locator('[data-testid="modal-flag-analysis"]')
    const impactAssessment = page.locator('[data-testid="collapsible-impact-assessment"]')
    
    await expect(flagAnalysis).toBeVisible()
    await expect(impactAssessment).toBeVisible()

    // Get the positions of both elements
    const analysisBox = await flagAnalysis.boundingBox()
    const assessmentBox = await impactAssessment.boundingBox()

    // Verify impact assessment is positioned below the flag analysis
    expect(assessmentBox?.y).toBeGreaterThan(analysisBox?.y || 0)
  })

  test('should handle flags without impact assessment data gracefully', async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/customer/dashboard')
    await page.waitForLoadState('networkidle')

    // Wait for flags to load
    await page.waitForSelector('[data-testid="flag-detail-modal"]', { timeout: 60000 })
    
    // Look for flag badges
    const flagBadges = page.locator('[data-testid*="flag-badge"]')
    const flagCount = await flagBadges.count()
    
    if (flagCount === 0) {
      test.skip()
      return
    }

    // Try multiple flags to find one without impact assessment data
    for (let i = 0; i < Math.min(flagCount, 3); i++) {
      await flagBadges.nth(i).click()
      await page.waitForTimeout(1000)

      // Check if impact assessment is present
      const impactAssessment = page.locator('[data-testid="collapsible-impact-assessment"]')
      const isVisible = await impactAssessment.isVisible()

      if (!isVisible) {
        // This flag doesn't have impact assessment data - this is expected behavior
        console.log(`Flag ${i} correctly shows no impact assessment`)
        break
      }

      // Close modal and try next flag if this one has impact data
      await page.keyboard.press('Escape')
      await page.waitForTimeout(500)
    }
  })

  test('should maintain glass-morphism styling in collapsible component', async ({ page }) => {
    // Navigate to dashboard
    await page.goto('/customer/dashboard')
    await page.waitForLoadState('networkidle')

    // Wait for flags to load
    await page.waitForSelector('[data-testid="flag-detail-modal"]', { timeout: 60000 })
    
    const flagBadges = page.locator('[data-testid*="flag-badge"]')
    const flagCount = await flagBadges.count()
    
    if (flagCount === 0) {
      test.skip()
      return
    }

    // Click on the first flag
    await flagBadges.first().click()
    await page.waitForTimeout(1000)

    // Check if impact assessment is present
    const impactAssessment = page.locator('[data-testid="collapsible-impact-assessment"]')
    if (!(await impactAssessment.isVisible())) {
      test.skip()
      return
    }

    // Expand the assessment
    const toggleButton = page.locator('[data-testid="impact-assessment-toggle"]')
    await toggleButton.click()
    await page.waitForTimeout(500)

    // Verify glass-card styling is applied
    const glassCards = impactAssessment.locator('.glass-card')
    const cardCount = await glassCards.count()
    
    // Should have at least one glass-card element
    expect(cardCount).toBeGreaterThan(0)

    // Verify the main container has proper styling
    const mainCard = impactAssessment.locator('> .glass-card').first()
    await expect(mainCard).toHaveClass(/glass-card/)
  })
})
