import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Auto-save Functionality', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });
    
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should auto-save content changes', async ({ page }) => {
    console.log('Starting auto-save test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    const saveButton = page.locator('[data-testid="save-button"]')
    await expect(saveButton).toBeVisible({ timeout: 10000 })
    
    await testUtils.typeInEditor('Testing auto-save functionality')
    
    // First do a manual save to ensure lastSaved is set
    await saveButton.click()
    
    // Now check that save timestamp appears (either "Saved at" or "Last saved:")
    await expect(page.locator('text=/(?:(?:Saved at|Last saved):|Up to date)/')).toBeVisible({ timeout: 15000 })
    
    // Type more content to trigger auto-save
    await testUtils.typeInEditor(' - additional content')
    
    console.log('Waiting for autosave to trigger...')
    await page.waitForTimeout(8000)
    
    console.log('Checking that timestamp is still visible after auto-save')
    await expect(page.locator('text=/(?:(?:Saved at|Last saved):|Up to date)/')).toBeVisible({ timeout: 15000 })
  })

  test('should debounce rapid changes', async ({ page }) => {
    console.log('Starting debounce test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    const saveButton = page.locator('[data-testid="save-button"]')
    
    // First do a manual save to initialize lastSaved
    await editor.click()
    await page.keyboard.type('Initial content')
    await saveButton.click()
    await expect(page.locator('text=/(?:(?:Saved at|Last saved):|Up to date)/')).toBeVisible({ timeout: 15000 })
    
    // Now test rapid typing with debouncing
    await page.keyboard.type(' Rapid')
    await page.waitForTimeout(100)
    await page.keyboard.type(' typing')
    await page.waitForTimeout(100)
    await page.keyboard.type(' test')
    
    // During rapid typing, auto-save should be debounced
    await page.waitForTimeout(3000)
    
    // The save timestamp should still be visible from the initial save
    await expect(page.locator('text=/(?:(?:Saved at|Last saved):|Up to date)/')).toBeVisible()
    
    console.log('Waiting for autosave debounce to complete...')
    await page.waitForTimeout(6000)
    
    // After debounce period, auto-save should have triggered
    await expect(page.locator('text=/(?:(?:Saved at|Last saved):|Up to date)/')).toBeVisible({ timeout: 15000 })
  })

  test('should handle manual save', async ({ page }) => {
    console.log('Starting manual save test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    await editor.click()
    await page.keyboard.type('Content for manual save')
    
    const saveButton = page.locator('[data-testid="save-button"]')
    await saveButton.click()
    
    await expect(page.locator('[data-testid="save-button"]')).toBeVisible()
    await expect(page.locator('text=/(?:(?:Saved at|Last saved):|Up to date)/')).toBeVisible({ timeout: 15000 })
  })

  test('should show last saved timestamp', async ({ page }) => {
    console.log('Starting timestamp test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    // Make changes
    await editor.click()
    await page.keyboard.type('Content with timestamp')
    
    // Manually save to trigger timestamp
    const saveButton = page.locator('[data-testid="save-button"]')
    await saveButton.click()
    
    // Should show timestamp text (format: "Saved at {time}" or "Last saved: {time}")
    const timestampText = page.locator('text=/(?:(?:Saved at|Last saved):|Up to date)/')
    await expect(timestampText).toBeVisible({ timeout: 5000 })
    
    // Timestamp should contain time format
    const timestamp = await timestampText.textContent()
    console.log('Timestamp text:', timestamp)
    expect(timestamp).toMatch(/(?:Saved at|Last saved:) \d+:\d+/)
  })

  test('should preserve content in editor', async ({ page }) => {
    console.log('Starting content preservation test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    // Type content
    await editor.click()
    await page.keyboard.type('Content that should persist')
    
    // Content should be visible in editor
    await expect(editor).toContainText('Content that should persist')
    
    // Wait for potential autosave (longer than debounce period)
    await page.waitForTimeout(6000)
    
    // Content should still be there after autosave
    await expect(editor).toContainText('Content that should persist')
    
    console.log('Content preservation test completed')
  })

  test('should maintain editor functionality during typing', async ({ page }) => {
    console.log('Starting editor functionality test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    await editor.click()
    
    // Type various content types
    await page.keyboard.type('# Heading\n\nParagraph with **bold** text.\n\n- List item 1\n- List item 2')
    
    // Wait a moment for content to render
    await page.waitForTimeout(1000)
    
    // Verify content structure is maintained
    await expect(editor.locator('h1')).toContainText('Heading')
    await expect(editor.locator('strong')).toContainText('bold')
    await expect(editor.locator('ul li').first()).toContainText('List item 1')
    
    // Save button should be functional
    const saveButton = page.locator('[data-testid="save-button"]')
    await expect(saveButton).toBeVisible()
    await expect(saveButton).toBeEnabled()
    
    console.log('Editor functionality test completed')
  })
})
