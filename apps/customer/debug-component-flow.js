const { chromium } = require('@playwright/test');

async function debugComponentFlow() {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({ 
    baseURL: 'http://localhost:3000'
  });
  const page = await context.newPage();

  try {
    console.log('Starting component creation flow debug...');
    
    // Login
    console.log('Logging in...');
    await page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', 'test1_pass');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/customer/, { timeout: 45000 });
    
    // Navigate to documents and create document
    console.log('Creating document...');
    await page.goto('/customer/documents', { timeout: 60000 });
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    await page.click('[data-testid="new-document-button"]');
    await page.waitForSelector('[role="dialog"]', { timeout: 20000 });
    
    // Select EKO Report template
    const templateButton = page.locator('[data-testid^="template-"]').filter({ hasText: 'EKO Report' });
    await templateButton.first().click();
    
    // Wait for editor
    await page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 60000 });
    await page.waitForSelector('.ProseMirror', { timeout: 30000 });
    
    console.log('Document editor loaded, waiting for components to be ready...');
    
    // Wait for initial components to load
    await page.waitForSelector('.report-section, .report-group, .report-summary', { timeout: 30000 });
    await page.waitForTimeout(5000); // Give components time to stabilize
    
    console.log('Looking for Insert Report Section button...');
    
    // Check for the button
    const insertButton = page.locator('button[title*="Insert Report Section"]');
    const buttonCount = await insertButton.count();
    console.log(`Insert Report Section button count: ${buttonCount}`);
    
    if (buttonCount === 0) {
      console.log('Button not found, checking all toolbar buttons...');
      const allButtons = await page.locator('button').all();
      for (let i = 0; i < allButtons.length; i++) {
        const title = await allButtons[i].getAttribute('title');
        const text = await allButtons[i].textContent();
        if (title && (title.includes('Insert') || title.includes('Report') || title.includes('Section'))) {
          console.log(`  Button ${i}: title="${title}" text="${text}"`);
        }
      }
      return;
    }
    
    console.log('Clicking Insert Report Section button...');
    await insertButton.click();
    
    // Check if dialog opened
    const dialogExists = await page.waitForSelector('[role="dialog"]', { timeout: 10000 }).catch(() => null);
    if (!dialogExists) {
      console.log('ERROR: Dialog did not open after clicking button');
      return;
    }
    
    console.log('Dialog opened successfully! Filling form...');
    
    // Fill the form
    await page.fill('input[placeholder="component-id"]', 'custom-section');
    await page.fill('input[placeholder="Component Title"]', 'Custom Analysis');
    await page.fill('textarea[placeholder*="Additional instructions"]', 'Provide analysis on custom metrics');
    
    console.log('Form filled, clicking Create Component button...');
    
    // Click create button
    const createButton = page.locator('[data-testid="create-component-button"]');
    const createButtonExists = await createButton.count();
    console.log(`Create button count: ${createButtonExists}`);
    
    if (createButtonExists === 0) {
      console.log('Looking for alternative create buttons...');
      const buttons = await page.locator('button').all();
      for (let i = 0; i < buttons.length; i++) {
        const text = await buttons[i].textContent();
        if (text && (text.includes('Create') || text.includes('Add') || text.includes('Save'))) {
          console.log(`  Potential create button: "${text}"`);
        }
      }
      return;
    }
    
    await createButton.click();
    
    console.log('Create button clicked, waiting for dialog to close...');
    
    // Wait for dialog to close
    await page.waitForSelector('[role="dialog"]', { state: 'hidden', timeout: 10000 });
    
    console.log('Dialog closed, waiting for component to appear...');
    
    // Wait for component to appear
    await page.waitForTimeout(3000);
    
    // Check if component was created
    const customComponent = page.locator('[data-id="custom-section"]');
    const componentExists = await customComponent.count();
    console.log(`Custom component count: ${componentExists}`);
    
    if (componentExists === 0) {
      console.log('Component not found, checking all components...');
      const allComponents = await page.locator('.report-section, .report-group, .report-summary').all();
      for (let i = 0; i < allComponents.length; i++) {
        const dataId = await allComponents[i].getAttribute('data-id');
        const text = await allComponents[i].textContent();
        console.log(`  Component ${i}: data-id="${dataId}" text="${text?.substring(0, 50)}..."`);
      }
    } else {
      console.log('SUCCESS: Custom component was created!');
    }
    
    // Take screenshot
    await page.screenshot({ path: 'debug-component-flow.png', fullPage: true });
    console.log('Screenshot saved as debug-component-flow.png');
    
  } catch (error) {
    console.error('Error during debug:', error);
    await page.screenshot({ path: 'debug-component-flow-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

debugComponentFlow();