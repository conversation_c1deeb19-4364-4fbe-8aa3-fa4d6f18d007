import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import {
    AlertTriangle,
    ChevronDown,
    ChevronUp,
    Heart,
    Leaf,
    Scale,
    Shield,
    TrendingDown,
    TrendingUp,
    Users,
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { Badge } from '@ui/components/ui/badge'
import { FlagTypeV2 } from '@/types'

interface CollapsibleImpactAssessmentProps {
    flag: FlagTypeV2
}

export function CollapsibleImpactAssessment({ flag }: CollapsibleImpactAssessmentProps) {
    const [isExpanded, setIsExpanded] = useState(false)
    const modelData = flag.model

    // Don't render if no impact assessment data
    if (!modelData.impact_value_analysis?.impact_measurement) {
        return null
    }

    const toggleExpanded = () => {
        setIsExpanded(!isExpanded)
    }

    return (
        <div className="mt-6" data-testid="collapsible-impact-assessment">
            {/* Collapsible Header */}
            <Card className="glass-card">
                <CardHeader 
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={toggleExpanded}
                    data-testid="impact-assessment-toggle"
                >
                    <CardTitle className="text-lg flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Scale className="w-5 h-5" />
                            Detailed Impact Assessment
                        </div>
                        {isExpanded ? (
                            <ChevronUp className="w-5 h-5 text-muted-foreground" />
                        ) : (
                            <ChevronDown className="w-5 h-5 text-muted-foreground" />
                        )}
                    </CardTitle>
                </CardHeader>
                
                {/* Collapsible Content */}
                {isExpanded && (
                    <CardContent className="pt-0" data-testid="impact-assessment-content">
                        {/* Net Impact Overview */}
                        <Card className="glass-card mb-4">
                            <CardContent className="pt-4">
                                <div className="flex items-center justify-between mb-4">
                                    <div>
                                        <div className="text-sm text-muted-foreground">Net Impact Score</div>
                                        <div className={cn(
                                            "text-2xl font-bold flex items-center gap-2",
                                            modelData.impact_value_analysis.impact_measurement.net_impact_score > 0 ? "text-green-600" : "text-red-600"
                                        )}>
                                            {modelData.impact_value_analysis.impact_measurement.net_impact_score > 0 ? 
                                                <TrendingUp className="w-6 h-6" /> : 
                                                <TrendingDown className="w-6 h-6" />
                                            }
                                            {(Math.abs(modelData.impact_value_analysis.impact_measurement.net_impact_score) * 100).toFixed(0)}%
                                        </div>
                                        <div className="text-sm text-muted-foreground">
                                            {modelData.impact_value_analysis.impact_measurement.net_impact_score > 0 ? "Net Positive" : "Net Negative"}
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4 text-center">
                                        <div>
                                            <div className="text-lg font-bold text-red-600">
                                                {(modelData.impact_value_analysis.impact_measurement.harm_score * 100).toFixed(0)}%
                                            </div>
                                            <div className="text-xs text-muted-foreground">Avg Harm</div>
                                        </div>
                                        <div>
                                            <div className="text-lg font-bold text-green-600">
                                                {(modelData.impact_value_analysis.impact_measurement.benefit_score * 100).toFixed(0)}%
                                            </div>
                                            <div className="text-xs text-muted-foreground">Avg Benefit</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="text-sm text-muted-foreground">
                                    <strong>Event:</strong> {modelData.impact_value_analysis.impact_measurement.event_summary}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Harm and Benefit Assessments */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                            {/* Harm Assessment */}
                            <Card className="glass-card">
                                <CardHeader>
                                    <CardTitle className="text-base flex items-center gap-2 text-red-700 dark:text-red-400">
                                        <TrendingDown className="w-4 h-4" />
                                        Harm Assessment
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    {[
                                        { key: 'animals', icon: Heart, assessment: modelData.impact_value_analysis.impact_measurement.harm_assessment.animals.assessment },
                                        { key: 'humans', icon: Users, assessment: modelData.impact_value_analysis.impact_measurement.harm_assessment.humans.assessment },
                                        { key: 'environment', icon: Leaf, assessment: modelData.impact_value_analysis.impact_measurement.harm_assessment.environment.assessment }
                                    ].map(({ key, icon: Icon, assessment }) => (
                                        <div key={key} className="border-l-2 border-red-200 pl-3">
                                            <div className="flex items-center justify-between mb-1">
                                                <div className="flex items-center gap-2 text-sm font-medium">
                                                    <Icon className="w-4 h-4" />
                                                    {key.charAt(0).toUpperCase() + key.slice(1)}
                                                </div>
                                                <Badge variant="outline" className={cn(
                                                    "text-xs",
                                                    assessment.confidence === 'high' ? 'text-green-600 bg-green-50' :
                                                    assessment.confidence === 'medium' ? 'text-yellow-600 bg-yellow-50' :
                                                    'text-red-600 bg-red-50'
                                                )}>
                                                    {assessment.confidence} confidence
                                                </Badge>
                                            </div>
                                            <div className="text-lg font-bold text-red-600 mb-1">
                                                {(assessment.score * 100).toFixed(0)}%
                                            </div>
                                            <div className="text-xs text-muted-foreground mb-2">
                                                {assessment.reasoning.substring(0, 120)}...
                                            </div>
                                            <div className="text-xs space-y-1">
                                                <div><strong>Immediate:</strong> {assessment.temporal_breakdown.immediate.substring(0, 80)}...</div>
                                                <div><strong>Long-term:</strong> {assessment.temporal_breakdown.long_term.substring(0, 80)}...</div>
                                            </div>
                                        </div>
                                    ))}
                                </CardContent>
                            </Card>

                            {/* Benefit Assessment */}
                            <Card className="glass-card">
                                <CardHeader>
                                    <CardTitle className="text-base flex items-center gap-2 text-green-700 dark:text-green-400">
                                        <TrendingUp className="w-4 h-4" />
                                        Benefit Assessment
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    {[
                                        { key: 'animals', icon: Heart, assessment: modelData.impact_value_analysis.impact_measurement.benefit_assessment.animals.assessment },
                                        { key: 'humans', icon: Users, assessment: modelData.impact_value_analysis.impact_measurement.benefit_assessment.humans.assessment },
                                        { key: 'environment', icon: Leaf, assessment: modelData.impact_value_analysis.impact_measurement.benefit_assessment.environment.assessment }
                                    ].map(({ key, icon: Icon, assessment }) => (
                                        <div key={key} className="border-l-2 border-green-200 pl-3">
                                            <div className="flex items-center justify-between mb-1">
                                                <div className="flex items-center gap-2 text-sm font-medium">
                                                    <Icon className="w-4 h-4" />
                                                    {key.charAt(0).toUpperCase() + key.slice(1)}
                                                </div>
                                                <Badge variant="outline" className={cn(
                                                    "text-xs",
                                                    assessment.confidence === 'high' ? 'text-green-600 bg-green-50' :
                                                    assessment.confidence === 'medium' ? 'text-yellow-600 bg-yellow-50' :
                                                    'text-red-600 bg-red-50'
                                                )}>
                                                    {assessment.confidence} confidence
                                                </Badge>
                                            </div>
                                            <div className="text-lg font-bold text-green-600 mb-1">
                                                {(assessment.score * 100).toFixed(0)}%
                                            </div>
                                            <div className="text-xs text-muted-foreground mb-2">
                                                {assessment.reasoning.substring(0, 120)}...
                                            </div>
                                            <div className="text-xs space-y-1">
                                                <div><strong>Immediate:</strong> {assessment.temporal_breakdown.immediate.substring(0, 80)}...</div>
                                                <div><strong>Long-term:</strong> {assessment.temporal_breakdown.long_term.substring(0, 80)}...</div>
                                            </div>
                                        </div>
                                    ))}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Uncertainties and Quality */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                            {modelData.impact_value_analysis.impact_measurement.key_uncertainties.length > 0 && (
                                <Card className="glass-card">
                                    <CardHeader>
                                        <CardTitle className="text-base flex items-center gap-2">
                                            <AlertTriangle className="w-4 h-4 text-amber-500" />
                                            Key Uncertainties
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <ul className="space-y-1 text-sm text-muted-foreground">
                                            {modelData.impact_value_analysis.impact_measurement.key_uncertainties.slice(0, 3).map((uncertainty, i) => (
                                                <li key={i} className="flex items-start gap-2">
                                                    <span className="text-amber-500 mt-1">•</span>
                                                    <span>{uncertainty}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    </CardContent>
                                </Card>
                            )}

                            {modelData.impact_value_analysis.impact_measurement.review_decision && (
                                <Card className="glass-card">
                                    <CardHeader>
                                        <CardTitle className="text-base flex items-center gap-2">
                                            <Shield className="w-4 h-4" />
                                            Review Assessment
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-2 text-sm">
                                            <div className="flex justify-between">
                                                <span>Review Decision:</span>
                                                <Badge variant="outline" className={cn(
                                                  modelData.impact_value_analysis.impact_measurement.review_decision.decision === 'accept' ? 'text-green-600 bg-green-50' :
                                                    modelData.impact_value_analysis.impact_measurement.review_decision.decision === 'amend' ? 'text-yellow-600 bg-yellow-50' :
                                                    'text-red-600 bg-red-50'
                                                )}>
                                                    {modelData.impact_value_analysis.impact_measurement.review_decision.decision}
                                                </Badge>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Bias Detected:</span>
                                                <span
                                                  className={modelData.impact_value_analysis.impact_measurement.review_decision.bias_detected ? 'text-red-600' : 'text-green-600'}>
                                                    {modelData.impact_value_analysis.impact_measurement.review_decision.bias_detected ? 'Yes' : 'No'}
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Reviewer Confidence:</span>
                                                <span>{modelData.impact_value_analysis.impact_measurement.review_decision.confidence_in_review}</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </CardContent>
                )}
            </Card>
        </div>
    )
}
