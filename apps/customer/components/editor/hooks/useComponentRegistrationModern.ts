/**
 * Modern component registration hook using XState machines
 * Replaces ref-based useComponentRegistration with proper state management
 */

import { useCallback, useEffect } from 'react'
import { ReportComponent, DocumentAction } from '../context/DocumentContext'
import { useComponentRegistrationState } from './useEditorStateMachines'
import { renderLog } from '../context/utils/renderLogger'
import { schedulerService } from '../services/utils/schedulerService'

interface ComponentRegistrationProps {
  dispatch: React.Dispatch<DocumentAction>
  stateRef: React.MutableRefObject<{ components: Map<string, ReportComponent> }>
  updateGroupStatus: (groupId: string, immediate?: boolean) => void
}

export const useComponentRegistrationModern = ({ 
  dispatch, 
  stateRef, 
  updateGroupStatus 
}: ComponentRegistrationProps) => {
  renderLog(`[HOOK] useComponentRegistrationModern called`)
  
  const {
    context,
    isProcessing,
    hasError,
    actions: regActions
  } = useComponentRegistrationState()

  // Register a component with proper state machine management
  const registerComponent = useCallback(
    (component: ReportComponent) => {
      console.log(
        `ComponentRegistration.registerComponent: Registering ${component.type} ${component.id} with parent ${
          component.parentId || 'none'
        }`
      )

      // Always register the component first in the document state
      dispatch({ type: 'COMPONENT_REGISTERED', component })

      // Add to pending registrations in state machine
      regActions.addPendingRegistration(component.id)
    },
    [dispatch, regActions]
  )
  renderLog(`[HOOK] useComponentRegistrationModern: registerComponent callback created`)

  // Effect to handle post-registration processing
  useEffect(() => {
    if (context.pendingRegistrations.size === 0) return

    // Use centralized scheduler instead of raw setTimeout
    const timeoutId = schedulerService.timeout(
      () => {
        // Start batch processing in state machine
        regActions.startBatchProcessing()
        
        // Process all pending registrations
        const pendingIds = Array.from(context.pendingRegistrations)
        
        console.log(
          `ComponentRegistration: Processing ${pendingIds.length} pending registrations:`,
          pendingIds
        )

        for (const componentId of pendingIds) {
          const component = stateRef.current.components.get(componentId)
          if (!component) {
            console.warn(`ComponentRegistration: Component ${componentId} not found in state`)
            continue
          }

          // Handle orphaned components
          if (component.parentId) {
            const parent = stateRef.current.components.get(component.parentId)
            if (!parent) {
              console.log(
                `ComponentRegistration: Parent ${component.parentId} not found for ${componentId}, adding to orphaned`
              )
              regActions.addOrphanedComponent(component.parentId, component)
              continue
            }
          }

          // Update group status if this is a group component or has a group parent
          if (component.type === 'report-group') {
            console.log(`ComponentRegistration: Updating status for group ${component.id}`)
            updateGroupStatus(component.id)
          } else if (component.parentId) {
            const parent = stateRef.current.components.get(component.parentId)
            if (parent?.type === 'report-group') {
              console.log(`ComponentRegistration: Updating parent group status for ${component.parentId}`)
              updateGroupStatus(component.parentId)
            }
          }

          // Check for any orphaned children of this component
          const orphanedChildren = context.orphanedComponents.get(componentId)
          if (orphanedChildren?.length) {
            console.log(
              `ComponentRegistration: Processing ${orphanedChildren.length} orphaned children for ${componentId}`
            )
            
            for (const orphanedChild of orphanedChildren) {
              // Re-register the orphaned child
              dispatch({ type: 'COMPONENT_UPDATED', id: orphanedChild.id, updates: orphanedChild })
              
              if (orphanedChild.type === 'report-group') {
                updateGroupStatus(orphanedChild.id)
              } else if (orphanedChild.parentId) {
                const parent = stateRef.current.components.get(orphanedChild.parentId)
                if (parent?.type === 'report-group') {
                  updateGroupStatus(orphanedChild.parentId)
                }
              }
            }
            
            // Clear the orphaned children
            regActions.removeOrphanedComponent(componentId)
          }
        }

        // Finish batch processing
        regActions.finishBatchProcessing()
      },
      10, // 10ms delay for batching
      'component-registration-batch'
    )

    return () => {
      schedulerService.clearTimeout(timeoutId)
    }
  }, [context.pendingRegistrations, context.orphanedComponents, stateRef, updateGroupStatus, regActions, dispatch])

  // Unregister a component
  const unregisterComponent = useCallback(
    (componentId: string) => {
      console.log(`ComponentRegistration.unregisterComponent: Unregistering ${componentId}`)
      
      // Remove from any pending registrations
      regActions.removePendingRegistration(componentId)
      
      // Clear any registration flags
      regActions.clearRegistrationFlag(componentId)
      
      // Remove from orphaned components if present
      regActions.removeOrphanedComponent(componentId)
      
      // Remove from document state
      dispatch({ type: 'COMPONENT_REMOVED', id: componentId })
    },
    [dispatch, regActions]
  )

  // Get registration status for a component
  const isComponentPending = useCallback(
    (componentId: string) => context.pendingRegistrations.has(componentId),
    [context.pendingRegistrations]
  )

  // Get orphaned children for a component
  const getOrphanedChildren = useCallback(
    (componentId: string) => context.orphanedComponents.get(componentId) || [],
    [context.orphanedComponents]
  )

  // Check if component has registration flag
  const hasRegistrationFlag = useCallback(
    (componentId: string) => context.registrationFlags.has(componentId),
    [context.registrationFlags]
  )

  return {
    registerComponent,
    unregisterComponent,
    isComponentPending,
    getOrphanedChildren,
    hasRegistrationFlag,
    isProcessing,
    hasError,
    
    // Expose state machine actions for advanced usage
    actions: regActions,
    
    // Statistics
    stats: {
      pendingCount: context.pendingRegistrations.size,
      orphanedCount: Array.from(context.orphanedComponents.values()).reduce((sum, arr) => sum + arr.length, 0),
      flaggedCount: context.registrationFlags.size
    }
  }
}