/**
 * React hooks for XState editor state machines
 * Replaces useReducer implementation with proper XState integration
 */

import { useActor } from '@xstate/react'
import { useMemo } from 'react'
import {
  componentRegistrationMachine,
  groupManagementMachine,
  entityDocumentMachine,
  loadingOperationMachine,
  performanceDebugMachine,
  type ComponentRegistrationEvents,
  type GroupManagementEvents,
  type EntityDocumentEvents,
  type LoadingOperationEvents,
  type PerformanceDebugEvents
} from '../services/state/editorStateMachine'

/**
 * Hook for component registration state management
 * Replaces ref-based pendingRegistrations, orphanedComponents, etc.
 */
export const useComponentRegistrationState = () => {
  const [state, send] = useActor(componentRegistrationMachine)
  
  const actions = useMemo(() => ({
    addPendingRegistration: (componentId: string) => 
      send({ type: 'ADD_PENDING_REGISTRATION', componentId }),
    
    removePendingRegistration: (componentId: string) => 
      send({ type: 'REMOVE_PENDING_REGISTRATION', componentId }),
    
    clearPendingRegistrations: () => 
      send({ type: 'CLEAR_PENDING_REGISTRATIONS' }),
    
    addOrphanedComponent: (parentId: string, component: any) => 
      send({ type: 'ADD_ORPHANED_COMPONENT', parentId, component }),
    
    removeOrphanedComponent: (parentId: string) => 
      send({ type: 'REMOVE_ORPHANED_COMPONENT', parentId }),
    
    clearOrphanedComponents: () => 
      send({ type: 'CLEAR_ORPHANED_COMPONENTS' }),
    
    startBatchProcessing: () => 
      send({ type: 'START_BATCH_PROCESSING' }),
    
    finishBatchProcessing: () => 
      send({ type: 'FINISH_BATCH_PROCESSING' }),
    
    setRegistrationFlag: (componentId: string, value?: boolean) => 
      send({ type: 'SET_REGISTRATION_FLAG', componentId, value }),
    
    clearRegistrationFlag: (componentId: string) => 
      send({ type: 'CLEAR_REGISTRATION_FLAG', componentId }),
    
    retry: () => send({ type: 'RETRY' }),
    reset: () => send({ type: 'RESET' })
  }), [send])

  return {
    state: state.value,
    context: state.context,
    isProcessing: state.matches('processing'),
    hasError: state.matches('error'),
    actions
  }
}

/**
 * Hook for group management state
 * Replaces ref-based pendingGroupUpdates, updateChain, etc.
 */
export const useGroupManagementState = () => {
  const [state, send] = useActor(groupManagementMachine)
  
  const actions = useMemo(() => ({
    addPendingGroupUpdate: (groupId: string) => 
      send({ type: 'ADD_PENDING_GROUP_UPDATE', groupId }),
    
    removePendingGroupUpdate: (groupId: string) => 
      send({ type: 'REMOVE_PENDING_GROUP_UPDATE', groupId }),
    
    clearPendingGroupUpdates: () => 
      send({ type: 'CLEAR_PENDING_GROUP_UPDATES' }),
    
    addToUpdateChain: (groupId: string) => 
      send({ type: 'ADD_TO_UPDATE_CHAIN', groupId }),
    
    removeFromUpdateChain: (groupId: string) => 
      send({ type: 'REMOVE_FROM_UPDATE_CHAIN', groupId }),
    
    clearUpdateChain: () => 
      send({ type: 'CLEAR_UPDATE_CHAIN' }),
    
    setGroupTimeoutActive: (groupId: string) => 
      send({ type: 'SET_GROUP_TIMEOUT_ACTIVE', groupId }),
    
    clearGroupTimeout: (groupId: string) => 
      send({ type: 'CLEAR_GROUP_TIMEOUT', groupId }),
    
    retry: () => send({ type: 'RETRY' }),
    reset: () => send({ type: 'RESET' })
  }), [send])

  return {
    state: state.value,
    context: state.context,
    hasError: state.matches('error'),
    actions
  }
}

/**
 * Hook for entity document state
 * Replaces ref-based entityChangeListeners, hasSetInitialContent, etc.
 */
export const useEntityDocumentState = () => {
  const [state, send] = useActor(entityDocumentMachine)
  
  const actions = useMemo(() => ({
    addEntityChangeListener: (listener: (entity: string | null, run: string) => void) => 
      send({ type: 'ADD_ENTITY_CHANGE_LISTENER', listener }),
    
    removeEntityChangeListener: (listener: (entity: string | null, run: string) => void) => 
      send({ type: 'REMOVE_ENTITY_CHANGE_LISTENER', listener }),
    
    clearEntityChangeListeners: () => 
      send({ type: 'CLEAR_ENTITY_CHANGE_LISTENERS' }),
    
    setInitialContentFlag: (hasSet: boolean) => 
      send({ type: 'SET_INITIAL_CONTENT_FLAG', hasSet }),
    
    updateLastEntity: (entity: string | null) => 
      send({ type: 'UPDATE_LAST_ENTITY', entity }),
    
    updateCurrentEntity: (entity: string | null) => 
      send({ type: 'UPDATE_CURRENT_ENTITY', entity }),
    
    updateCurrentRun: (run: string | null) => 
      send({ type: 'UPDATE_CURRENT_RUN', run }),
    
    retry: () => send({ type: 'RETRY' }),
    reset: () => send({ type: 'RESET' })
  }), [send])

  return {
    state: state.value,
    context: state.context,
    hasError: state.matches('error'),
    actions
  }
}

/**
 * Hook for loading operation state
 * Replaces ref-based loadingComponents, registeredComponents, etc.
 */
export const useLoadingOperationState = () => {
  const [state, send] = useActor(loadingOperationMachine)
  
  const actions = useMemo(() => ({
    setComponentLoading: (componentId: string) => 
      send({ type: 'SET_COMPONENT_LOADING', componentId }),
    
    clearComponentLoading: (componentId: string) => 
      send({ type: 'CLEAR_COMPONENT_LOADING', componentId }),
    
    setComponentRegistered: (componentId: string) => 
      send({ type: 'SET_COMPONENT_REGISTERED', componentId }),
    
    clearComponentRegistered: (componentId: string) => 
      send({ type: 'CLEAR_COMPONENT_REGISTERED', componentId }),
    
    startSaving: () => 
      send({ type: 'START_SAVING' }),
    
    finishSaving: () => 
      send({ type: 'FINISH_SAVING' }),
    
    updateLastSaveTimestamp: () => 
      send({ type: 'UPDATE_LAST_SAVE_TIMESTAMP' }),
    
    resetLoadingState: () => 
      send({ type: 'RESET_LOADING_STATE' }),
    
    retry: () => send({ type: 'RETRY' })
  }), [send])

  return {
    state: state.value,
    context: state.context,
    isSaving: state.matches('saving'),
    hasError: state.matches('error'),
    actions
  }
}

/**
 * Hook for performance debug state
 * Replaces ref-based renderCount, lastStateSnapshot, etc.
 */
export const usePerformanceDebugState = () => {
  const [state, send] = useActor(performanceDebugMachine)
  
  const actions = useMemo(() => ({
    incrementRenderCount: () => 
      send({ type: 'INCREMENT_RENDER_COUNT' }),
    
    updateStateSnapshot: (snapshot: any) => 
      send({ type: 'UPDATE_STATE_SNAPSHOT', snapshot }),
    
    updateFunctionsSnapshot: (snapshot: Record<string, string>) => 
      send({ type: 'UPDATE_FUNCTIONS_SNAPSHOT', snapshot }),
    
    toggleDebugMode: () => 
      send({ type: 'TOGGLE_DEBUG_MODE' }),
    
    resetDebugState: () => 
      send({ type: 'RESET_DEBUG_STATE' })
  }), [send])

  return {
    state: state.value,
    context: state.context,
    debugMode: state.context.debugMode,
    renderCount: state.context.renderCount,
    actions
  }
}

/**
 * Combined hook that provides access to all editor state machines
 * Replaces the consolidated useReducer approach
 */
export const useEditorStateMachines = () => {
  const componentRegistration = useComponentRegistrationState()
  const groupManagement = useGroupManagementState()
  const entityDocument = useEntityDocumentState()
  const loadingOperation = useLoadingOperationState()
  const performanceDebug = usePerformanceDebugState()

  return {
    componentRegistration,
    groupManagement,
    entityDocument,
    loadingOperation,
    performanceDebug,
    
    // Convenience selectors
    selectors: {
      isPendingRegistration: (componentId: string) => 
        componentRegistration.context.pendingRegistrations.has(componentId),
      
      isPendingGroupUpdate: (groupId: string) => 
        groupManagement.context.pendingGroupUpdates.has(groupId),
      
      isComponentLoading: (componentId: string) => 
        loadingOperation.context.loadingComponents.has(componentId),
      
      isComponentRegistered: (componentId: string) => 
        loadingOperation.context.registeredComponents.has(componentId),
      
      hasRegistrationFlag: (componentId: string) => 
        componentRegistration.context.registrationFlags.has(componentId),
      
      isInUpdateChain: (groupId: string) => 
        groupManagement.context.updateChain.has(groupId),
      
      hasGroupTimeout: (groupId: string) => 
        groupManagement.context.groupStatusTimeouts.has(groupId)
    }
  }
}