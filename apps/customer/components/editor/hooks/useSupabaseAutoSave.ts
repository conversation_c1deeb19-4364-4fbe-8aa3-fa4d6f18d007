import { useCallback, useEffect, useRef, useState } from 'react'
import { Editor } from '@tiptap/react'
import { createClient } from '@/app/supabase/client'
import { useAuth } from '@/components/context/auth/auth-context'

interface UseSupabaseAutoSaveOptions {
  documentId: string
  editor?: Editor
  onSave?: (content: string, data?: any) => Promise<void> | void
  saveInterval?: number // in milliseconds
  createVersionInterval?: number // in milliseconds
  enabled?: boolean
}

interface AutoSaveState {
  isSaving: boolean
  lastSaved: Date | null
  hasUnsavedChanges: boolean
  error: string | null
}

export function useSupabaseAutoSave({
  documentId,
  editor,
  onSave,
  saveInterval = 5000, // 5 seconds - more reasonable interval
  createVersionInterval = 300000, // 5 minutes
  enabled = true
}: UseSupabaseAutoSaveOptions) {
  const { user } = useAuth()
  const supabase = createClient()
  const [state, setState] = useState<AutoSaveState>({
    isSaving: false,
    lastSaved: null,
    hasUnsavedChanges: false,
    error: null
  })

  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const versionTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastContentHashRef = useRef<string>('')

  // Save document content to Supabase
  const saveDocument = useCallback(async (createVersion = false, isManualSave = false) => {
    if (!editor || !documentId || !user || state.isSaving) return

    setState(prev => ({ ...prev, isSaving: true, error: null }))

    try {
      const content = editor.getHTML()
      const data = editor.getJSON()

      // Update the main document
      const { error: updateError } = await supabase
        .from('doc_documents')
        .update({
          content,
          data,
          updated_at: new Date().toISOString(),
          updated_by: user.id,
        })
        .eq('id', documentId)

      if (updateError) throw updateError

      // Create version if requested
      if (createVersion) {
        // Get the current version number
        const { data: versions, error: versionError } = await supabase
          .from('doc_versions')
          .select('version_number')
          .eq('document_id', documentId)
          .order('version_number', { ascending: false })
          .limit(1)

        if (versionError) throw versionError

        const nextVersionNumber = (versions?.[0]?.version_number || 0) + 1

        const { error: createVersionError } = await supabase
          .from('doc_versions')
          .insert({
            document_id: documentId,
            version_number: nextVersionNumber,
            title: `Version ${nextVersionNumber}`,
            content,
            data,
            user_id: user.id,
            change_summary: isManualSave ? 'Manual save' : 'Auto-save version',
            is_auto_save: !isManualSave,
          })

        if (createVersionError) throw createVersionError

        // If this is an auto-save, clean up old auto-save versions (keep only 5 most recent)
        if (!isManualSave) {
          const { data: autoSaveVersions, error: fetchError } = await supabase
            .from('doc_versions')
            .select('id, version_number')
            .eq('document_id', documentId)
            .eq('is_auto_save', true)
            .order('version_number', { ascending: false })

          if (!fetchError && autoSaveVersions && autoSaveVersions.length > 5) {
            // Delete auto-save versions beyond the 5 most recent
            const versionsToDelete = autoSaveVersions.slice(5)
            const idsToDelete = versionsToDelete.map(v => v.id)

            const { error: deleteError } = await supabase
              .from('doc_versions')
              .delete()
              .in('id', idsToDelete)

            if (deleteError) {
              console.warn('Failed to clean up old auto-save versions:', deleteError)
            }
          }
        }
      }

      // Call the external onSave callback if provided
      if (onSave) {
        await onSave(content, data)
      }

      setState(prev => ({
        ...prev,
        isSaving: false,
        lastSaved: new Date(),
        hasUnsavedChanges: false,
        error: null
      }))

      // Cache the content hash for change detection
      const textContent = editor.state.doc.textContent
      lastContentHashRef.current = `${textContent.length}:${textContent.substring(0, 100)}`

    } catch (error) {
      console.error('Error saving document:', error)
      setState(prev => ({
        ...prev,
        isSaving: false,
        error: error instanceof Error ? error.message : 'Failed to save document'
      }))
    }
  }, [editor, documentId, user, state.isSaving, onSave, supabase])

  // Check if content has changed using a simple hash comparison
  const hasContentChanged = useCallback(() => {
    if (!editor) return false
    // Use text content length + first 100 chars as a quick hash
    const doc = editor.state.doc
    const textContent = doc.textContent
    const contentHash = `${textContent.length}:${textContent.substring(0, 100)}`
    return contentHash !== lastContentHashRef.current
  }, [editor])

  // Schedule auto-save
  const scheduleAutoSave = useCallback(() => {
    if (!enabled) return

    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }

    saveTimeoutRef.current = setTimeout(() => {
      if (hasContentChanged()) {
        saveDocument(false, false) // Auto-save, no version
      }
    }, saveInterval)
  }, [enabled, hasContentChanged, saveDocument, saveInterval])

  // Schedule version creation
  const scheduleVersionCreation = useCallback(() => {
    if (!enabled) return

    if (versionTimeoutRef.current) {
      clearTimeout(versionTimeoutRef.current)
    }

    versionTimeoutRef.current = setTimeout(() => {
      if (hasContentChanged()) {
        saveDocument(true, false) // Auto-save with version
      }
      scheduleVersionCreation() // Schedule next version
    }, createVersionInterval)
  }, [enabled, hasContentChanged, saveDocument, createVersionInterval])

  // Handle editor updates
  useEffect(() => {
    if (!editor || !enabled) return

    const handleUpdate = ({ transaction }: { transaction: any }) => {
      // Only save if this is a user-initiated change, not a programmatic one
      const isUserChange = !transaction.getMeta('fromSystem') &&
                          !transaction.getMeta('addToHistory') === false &&
                          !transaction.getMeta('preventAutoSave')

      if (isUserChange && transaction.docChanged) {
        setState(prev => ({ ...prev, hasUnsavedChanges: hasContentChanged() }))
        scheduleAutoSave()
      }
    }

    // Listen to editor updates
    editor.on('update', handleUpdate)

    // Initial setup - cache content hash
    const textContent = editor.state.doc.textContent
    lastContentHashRef.current = `${textContent.length}:${textContent.substring(0, 100)}`

    return () => {
      editor.off('update', handleUpdate)
    }
  }, [editor, enabled, scheduleAutoSave])

  // Set up version creation interval
  useEffect(() => {
    if (!enabled) return

    scheduleVersionCreation()

    return () => {
      if (versionTimeoutRef.current) {
        clearTimeout(versionTimeoutRef.current)
      }
    }
  }, [enabled, scheduleVersionCreation])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
      if (versionTimeoutRef.current) {
        clearTimeout(versionTimeoutRef.current)
      }
    }
  }, [])

  // Manual save function
  const manualSave = useCallback(async (createVersion = false) => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }
    await saveDocument(createVersion, true) // Manual save
  }, [saveDocument])

  // Force save function (ignores change detection)
  const forceSave = useCallback(async (createVersion = false) => {
    if (!editor || !documentId) return

    setState(prev => ({ ...prev, isSaving: true, error: null }))
    await saveDocument(createVersion, true) // Force save is considered manual
  }, [editor, documentId, saveDocument])

  return {
    ...state,
    manualSave,
    forceSave,
  }
}
