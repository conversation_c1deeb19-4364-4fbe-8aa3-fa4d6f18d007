/**
 * Hook to integrate XState Report Content Machine with React components
 * Provides a clean interface for report components to manage content lifecycle
 */

import { useCallback, useEffect, useRef, useState } from 'react'
import { ReportContentContext, ReportContentEvent, reportContentManager } from '../services/state/reportContentMachine'
import { useReportManager } from './useReportManager'
import { useDocumentContext } from '../context/DocumentContext'
import type { ComponentStatus } from '../types'

export interface UseReportContentMachineOptions {
  componentId: string
  componentType: 'report-section' | 'report-group' | 'report-summary'
  endpoint?: string
  prompt?: string
  title?: string
  dependencies?: string[]
  parentId?: string
  headings?: 'keep' | 'remove' | 'reset'
  depth?: number
  locked?: boolean
  preserved?: boolean
  initialStatus?: string
}

export interface UseReportContentMachineReturn {
  // State
  isLoading: boolean
  hasContent: boolean
  contentInserted: boolean
  canInsertContent: boolean
  error?: string
  
  // Actions
  loadContent: () => void
  insertContent: (editor: any, getPos: () => number | undefined) => void
  refresh: () => void
  updateDependencies: (dependencies: Map<string, string>) => void
  summarize: () => void
  lock: () => void
  unlock: () => void
  preserve: () => void
  
  // Machine state
  machineState: string
  context: ReportContentContext
}

export function useReportContentMachine(
  options: UseReportContentMachineOptions
): UseReportContentMachineReturn {
  const { 
    componentId, 
    componentType, 
    endpoint, 
    prompt, 
    title,
    dependencies = [],
    parentId,
    headings = 'remove',
    depth = 1,
    locked = false,
    preserved = false,
    initialStatus
  } = options
  
  const reportManager = useReportManager()
  const { state: documentState } = useDocumentContext()
  
  // Create or get existing machine
  const machineRef = useRef<ReturnType<typeof reportContentManager.createMachine> | null>(null)
  
  // Initialize machine
  useEffect(() => {
    if (!machineRef.current) {
      machineRef.current = reportContentManager.createMachine(componentId, {
        componentId,
        componentType,
        endpoint,
        prompt,
        title,
        dependencies,
        parentId,
        headings,
        depth
      })
      
      // Send initialization event
      machineRef.current.send({
        type: 'INITIALIZE',
        componentId,
        componentType,
        config: {
          endpoint,
          prompt,
          title,
          dependencies,
          parentId,
          headings,
          depth
        }
      })
      
      // Handle initial locked/preserved state
      if (locked) {
        machineRef.current.send({ type: 'LOCK' })
      } else if (preserved) {
        machineRef.current.send({ type: 'PRESERVE' })
      }
    }
    
    return () => {
      // Cleanup is handled by the manager
    }
  }, [componentId]) // Only recreate if componentId changes

  // State tracking
  const [state, setState] = useState(() => machineRef.current?.getSnapshot())

  // Subscribe to actor state changes
  useEffect(() => {
    if (!machineRef.current) return

    const subscription = machineRef.current.subscribe((newState) => {
      setState(newState)
    })

    return () => subscription.unsubscribe()
  }, [])

  // Send function
  const send = useCallback((event: ReportContentEvent) => {
    machineRef.current?.send(event)
  }, [])
  
  // Extract useful state values
  const machineState = state ? (typeof state.value === 'string' 
    ? state.value
    : Object.keys(state.value)[0]) : 'uninitialized'
    
  const isLoading = ['loadingContent', 'generatingSummary', 'insertingContent'].includes(machineState)
  const hasContent = state?.context.hasContent || false
  const contentInserted = state?.context.contentInserted || false
  const canInsertContent = reportContentManager.getContentState(componentId)?.canInsert || false
  const error = state?.context.error
  
  // Update report manager when state changes
  useEffect(() => {
    const status = mapMachineStateToStatus(machineState, locked, preserved)
    
    // Only update if status actually changed
    const currentComponent = reportManager.components.get(componentId)
    if (currentComponent?.status !== status) {
      reportManager.updateComponent(componentId, {
        status,
        content: state?.context.content,
        error: state?.context.error,
      })
    }
  }, [machineState, componentId, state?.context.content, state?.context.error, locked, preserved])
  
  // Handle dependency updates for summaries
  useEffect(() => {
    if (componentType === 'report-summary' && dependencies.length > 0) {
      // Collect dependency content
      const dependencyContent = new Map<string, string>()
      let allReady = true
      
      dependencies.forEach(depId => {
        const dep = reportManager.components.get(depId)
        if (dep && dep.content) {
          dependencyContent.set(depId, dep.content)
        } else {
          allReady = false
        }
      })
      
      // Update machine with dependency content
      if (dependencyContent.size > 0) {
        send({ 
          type: 'DEPENDENCIES_UPDATED', 
          dependencies: dependencyContent 
        })
      }
      
      // Auto-trigger summarization when all dependencies are ready
      if (allReady && dependencyContent.size === dependencies.length && !hasContent && !contentInserted) {
        send({ type: 'SUMMARIZE' })
      }
    }
  }, [componentType, dependencies, hasContent, contentInserted, send])
  
  // Action callbacks
  const loadContent = useCallback(() => {
    send({ type: 'LOAD_CONTENT' })
  }, [send])
  
  const insertContent = useCallback((editor: any, getPos: () => number | undefined) => {
    if (!editor || !getPos) {
      console.error(`Report component ${componentId}: Cannot insert content without editor and getPos`)
      return
    }
    
    send({ 
      type: 'INSERT_CONTENT', 
      editor, 
      getPos 
    })
  }, [send, componentId])
  
  const refresh = useCallback(() => {
    send({ type: 'REFRESH' })
  }, [send])
  
  const updateDependencies = useCallback((dependencies: Map<string, string>) => {
    send({ 
      type: 'DEPENDENCIES_UPDATED', 
      dependencies 
    })
  }, [send])
  
  const summarize = useCallback(() => {
    send({ type: 'SUMMARIZE' })
  }, [send])
  
  const lock = useCallback(() => {
    send({ type: 'LOCK' })
  }, [send])
  
  const unlock = useCallback(() => {
    send({ type: 'UNLOCK' })
  }, [send])
  
  const preserve = useCallback(() => {
    send({ type: 'PRESERVE' })
  }, [send])
  
  // Auto-load content for sections when initialized
  useEffect(() => {
    if (componentType === 'report-section' && 
        machineState === 'ready' && 
        !hasContent && 
        !contentInserted &&
        endpoint) {
      loadContent()
    }
  }, [componentType, machineState, hasContent, contentInserted, endpoint, loadContent])
  
  // Auto-insert content when loaded and editor is available
  const editorRef = useRef(documentState.editor)
  editorRef.current = documentState.editor
  
  useEffect(() => {
    if (hasContent && !contentInserted && editorRef.current && machineState === 'contentLoaded') {
      // We need getPos from the component - this will be passed when ready
      console.log(`Report component ${componentId}: Content loaded, waiting for insertion trigger`)
    }
  }, [hasContent, contentInserted, machineState, componentId])
  
  return {
    // State
    isLoading,
    hasContent,
    contentInserted,
    canInsertContent,
    error,
    
    // Actions
    loadContent,
    insertContent,
    refresh,
    updateDependencies,
    summarize,
    lock,
    unlock,
    preserve,
    
    // Machine state
    machineState,
    context: state?.context,
  }
}

// Helper function to map machine states to legacy status values
function mapMachineStateToStatus(
  machineState: string, 
  locked: boolean, 
  preserved: boolean
): ComponentStatus {
  if (locked) return 'locked'
  if (preserved) return 'preserved'

  const stateMap: Record<string, ComponentStatus> = {
    'uninitialized': 'idle',
    'initialized': 'idle',
    'ready': 'idle',
    'waitingForDependencies': 'loading',
    'loadingContent': 'loading',
    'generatingSummary': 'loading',
    'contentLoaded': 'loaded',
    'insertingContent': 'loading',
    'contentInserted': 'loaded',
    'refreshing': 'loading',
    'error': 'error',
    'locked': 'locked',
    'preserved': 'preserved'
  }
  
  return stateMap[machineState] || 'idle'
}
