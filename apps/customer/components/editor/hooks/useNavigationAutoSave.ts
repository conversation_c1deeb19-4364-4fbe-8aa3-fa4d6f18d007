import { useCallback, useEffect, useRef, useState } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { Editor } from '@tiptap/react'
import { createClient } from '@/app/supabase/client'
import { useAuth } from '@/components/context/auth/auth-context'

interface UseNavigationAutoSaveOptions {
  documentId: string
  editor?: Editor
  onSave?: (content: string, data?: any) => Promise<void> | void
  enabled?: boolean
}

interface NavigationAutoSaveState {
  lastSaved: Date | null
  hasUnsavedChanges: boolean
  error: string | null
}

/**
 * Hook that provides auto-save functionality triggered by navigation events.
 * This extends the existing auto-save functionality to also save when the user
 * navigates away from the current page.
 */
export function useNavigationAutoSave({
  documentId,
  editor,
  onSave,
  enabled = true
}: UseNavigationAutoSaveOptions) {
  const { user } = useAuth()
  const supabase = createClient()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const router = useRouter()
  
  const [state, setState] = useState<NavigationAutoSaveState>({
    lastSaved: null,
    hasUnsavedChanges: false,
    error: null
  })

  // Refs to track state without causing re-renders
  const lastContentRef = useRef<string>('')
  const lastDataRef = useRef<any>(null)
  const isNavigatingRef = useRef(false)
  const savePromiseRef = useRef<Promise<void> | null>(null)
  const isSavingRef = useRef(false)
  const lastUrlRef = useRef<string>('')

  // Save document content to Supabase
  const saveDocument = useCallback(async (isNavigationSave = false) => {
    if (!editor || !documentId || !user || isSavingRef.current) return

    isSavingRef.current = true
    setState(prev => ({ ...prev, error: null }))

    try {
      const content = editor.getHTML()
      const data = editor.getJSON()

      // Check if content actually changed
      if (content === lastContentRef.current &&
          JSON.stringify(data) === JSON.stringify(lastDataRef.current)) {
        isSavingRef.current = false
        return
      }

      // Update the main document
      const { error: updateError } = await supabase
        .from('doc_documents')
        .update({
          content,
          data,
          updated_at: new Date().toISOString(),
          updated_by: user.id,
        })
        .eq('id', documentId)

      if (updateError) throw updateError

      // Update refs with saved content
      lastContentRef.current = content
      lastDataRef.current = data

      setState(prev => ({
        ...prev,
        lastSaved: new Date(),
        hasUnsavedChanges: false,
        error: null
      }))

      // Call custom onSave callback if provided
      if (onSave) {
        await onSave(content, data)
      }

      if (isNavigationSave) {
        console.log('Navigation auto-save completed successfully')
      }
    } catch (error) {
      console.error('Navigation auto-save failed:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Save failed'
      }))
    } finally {
      isSavingRef.current = false
    }
  }, [editor, documentId, user, supabase, onSave])

  // Check if content has changed (stable function, no dependencies)
  const hasContentChanged = useCallback(() => {
    if (!editor) return false

    const currentContent = editor.getHTML()
    const currentData = JSON.stringify(editor.getJSON())
    const lastData = JSON.stringify(lastDataRef.current)

    return currentContent !== lastContentRef.current || currentData !== lastData
  }, [editor])

  // Update unsaved changes state
  useEffect(() => {
    if (!enabled || !editor) return

    const updateUnsavedState = () => {
      // Check for changes directly to avoid dependency issues
      const currentContent = editor.getHTML()
      const currentData = JSON.stringify(editor.getJSON())
      const lastData = JSON.stringify(lastDataRef.current)
      const hasChanges = currentContent !== lastContentRef.current || currentData !== lastData

      setState(prev => ({ ...prev, hasUnsavedChanges: hasChanges }))
    }

    // Check for changes on editor updates
    const handleUpdate = () => {
      updateUnsavedState()
    }

    // Initial check
    updateUnsavedState()

    editor.on('update', handleUpdate)

    return () => {
      editor.off('update', handleUpdate)
    }
  }, [editor, enabled])

  // Handle navigation-triggered auto-save using router events
  useEffect(() => {
    if (!enabled || !editor) return

    // Save function that can be called synchronously
    const triggerSave = () => {
      // Check for unsaved changes directly
      const currentContent = editor.getHTML()
      const currentData = JSON.stringify(editor.getJSON())
      const lastData = JSON.stringify(lastDataRef.current)
      const hasChanges = currentContent !== lastContentRef.current || currentData !== lastData

      if (hasChanges && !isSavingRef.current) {
        console.log('Navigation detected, triggering auto-save...')
        isNavigatingRef.current = true

        // Trigger save
        const savePromise = saveDocument(true)
        savePromiseRef.current = savePromise

        savePromise.finally(() => {
          isNavigatingRef.current = false
          savePromiseRef.current = null
        })
      }
    }

    // Listen for navigation events on links and buttons
    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement

      // Check if it's a navigation element
      const isNavigation = target.closest('a[href]') ||
                          target.closest('button[data-testid="back-button"]') ||
                          target.closest('[role="button"]') ||
                          target.closest('button')

      if (isNavigation) {
        triggerSave()
      }
    }

    // Listen for form submissions
    const handleSubmit = () => {
      triggerSave()
    }

    // Listen for keyboard shortcuts that might cause navigation
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + click, or common navigation shortcuts
      if ((event.ctrlKey || event.metaKey) &&
          (event.key === 'w' || event.key === 't' || event.key === 'r')) {
        triggerSave()
      }
    }

    document.addEventListener('click', handleClick, true) // Use capture phase
    document.addEventListener('submit', handleSubmit, true)
    document.addEventListener('keydown', handleKeyDown, true)

    return () => {
      document.removeEventListener('click', handleClick, true)
      document.removeEventListener('submit', handleSubmit, true)
      document.removeEventListener('keydown', handleKeyDown, true)
    }
  }, [enabled, editor])

  // Additional navigation detection using pathname/searchParams changes
  useEffect(() => {
    if (!enabled || !editor) return

    const currentUrl = `${pathname}?${searchParams}`

    // Skip on initial mount
    if (!lastUrlRef.current) {
      lastUrlRef.current = currentUrl
      return
    }

    // If URL changed, we missed the navigation - this is a fallback
    if (currentUrl !== lastUrlRef.current) {
      lastUrlRef.current = currentUrl
      console.log('Navigation detected via URL change (fallback)')

      // This happens after navigation, so we can't save the previous document
      // But we can update our tracking
    }
  }, [pathname, searchParams, enabled, editor])

  // Handle beforeunload event for browser navigation (back/forward/close)
  useEffect(() => {
    if (!enabled || !editor) return

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Check for unsaved changes directly to avoid dependency issues
      const currentContent = editor.getHTML()
      const currentData = JSON.stringify(editor.getJSON())
      const lastData = JSON.stringify(lastDataRef.current)
      const hasChanges = currentContent !== lastContentRef.current || currentData !== lastData

      if (hasChanges && !isSavingRef.current) {
        console.log('Browser navigation detected, triggering auto-save...')

        // Try to save using sendBeacon for page unload
        if (documentId && user) {
          const content = editor.getHTML()
          const data = editor.getJSON()

          // Use sendBeacon for more reliable saving during page unload
          const saveData = {
            content,
            data,
            updated_at: new Date().toISOString(),
            updated_by: user.id,
          }

          // Attempt to send data via beacon API
          if (navigator.sendBeacon) {
            const blob = new Blob([JSON.stringify({
              documentId,
              ...saveData
            })], { type: 'application/json' })

            const sent = navigator.sendBeacon('/api/documents/auto-save', blob)
            if (sent) {
              // Update refs to reflect that content is now saved
              lastContentRef.current = content
              lastDataRef.current = data
              // Don't show dialog if beacon was sent successfully
              return
            }
          }
        }

        // Only show warning if we couldn't save via beacon
        event.preventDefault()
        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return event.returnValue
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [enabled, editor, documentId, user])

  // Manual save function
  const manualSave = useCallback(async () => {
    await saveDocument(false)
  }, [saveDocument])

  // Wait for any pending navigation save
  const waitForNavigationSave = useCallback(async () => {
    if (savePromiseRef.current) {
      await savePromiseRef.current
    }
  }, [])

  return {
    ...state,
    isSaving: isSavingRef.current,
    manualSave,
    waitForNavigationSave,
    isNavigating: isNavigatingRef.current
  }
}
