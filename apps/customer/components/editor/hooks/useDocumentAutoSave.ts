import { useEffect, useRef } from 'react'
import { createClient } from '@/app/supabase/client'
import { useAuth } from '@/components/context/auth/auth-context'
import { DocumentAction, DocumentState } from '../context/DocumentContext'

interface UseDocumentAutoSaveOptions {
  documentId: string
  state: DocumentState
  dispatch: React.Dispatch<DocumentAction>
  onSave?: (content: string, data?: any) => Promise<void> | void
  saveInterval?: number // in milliseconds
  createVersionInterval?: number // in milliseconds
  enabled?: boolean
}

export function useDocumentAutoSave({
  documentId,
  state,
  dispatch,
  onSave,
  saveInterval = 2000, // 2 seconds - more responsive
  createVersionInterval = 300000, // 5 minutes
  enabled = true
}: UseDocumentAutoSaveOptions) {
  const { user } = useAuth()
  const supabase = createClient()
  
  // Refs to track state without causing re-renders
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const versionTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastSavedContentRef = useRef<string>('')
  const lastSavedDataRef = useRef<any>(null)
  const isInitializedRef = useRef(false)
  
  // Save function
  const saveDocument = async (createVersion = false) => {
    if (!state.editor || !documentId || !user || state.isSaving || !enabled) {
      return
    }
    
    const content = state.content
    const data = state.data
    
    // Check if content actually changed
    if (content === lastSavedContentRef.current && 
        JSON.stringify(data) === JSON.stringify(lastSavedDataRef.current)) {
      return
    }
    
    dispatch({ type: 'SAVE_STARTED' })
    
    try {
      // Update the main document
      const { error: updateError } = await supabase
        .from('doc_documents')
        .update({
          content,
          data,
          updated_at: new Date().toISOString(),
          updated_by: user.id,
        })
        .eq('id', documentId)
      
      if (updateError) throw updateError
      
      // Create version if requested
      if (createVersion) {
        // Get current version count
        const { data: versionData, error: versionCountError } = await supabase
          .from('doc_versions')
          .select('version_number')
          .eq('document_id', documentId)
          .order('version_number', { ascending: false })
          .limit(1)
        
        if (versionCountError) throw versionCountError
        
        const nextVersion = (versionData?.[0]?.version_number || 0) + 1
        
        const { error: versionError } = await supabase
          .from('doc_versions')
          .insert({
            document_id: documentId,
            version_number: nextVersion,
            content,
            data,
            created_by: user.id,
            is_auto_save: true,
            change_summary: 'Auto-save version',
          })
        
        if (versionError) throw versionError
        
        // Clean up old auto-save versions (keep only 5 most recent)
        const { data: autoSaveVersions, error: fetchError } = await supabase
          .from('doc_versions')
          .select('id')
          .eq('document_id', documentId)
          .eq('is_auto_save', true)
          .order('created_at', { ascending: false })
        
        if (!fetchError && autoSaveVersions && autoSaveVersions.length > 5) {
          const versionsToDelete = autoSaveVersions.slice(5)
          const idsToDelete = versionsToDelete.map(v => v.id)
          
          await supabase
            .from('doc_versions')
            .delete()
            .in('id', idsToDelete)
        }
      }
      
      // Call external onSave callback
      if (onSave) {
        await onSave(content, data)
      }
      
      // Update refs and dispatch success
      lastSavedContentRef.current = content
      lastSavedDataRef.current = data
      dispatch({ type: 'SAVE_COMPLETED', timestamp: new Date() })
      
    } catch (error) {
      console.error('Error saving document:', error)
      dispatch({ 
        type: 'SAVE_FAILED', 
        error: error instanceof Error ? error.message : 'Failed to save document' 
      })
    }
  }
  
  // Auto-save effect - triggers on content changes
  useEffect(() => {
    if (!enabled || !state.isDirty || state.isSaving || !state.editor) {
      return
    }
    
    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }
    
    // Schedule save
    saveTimeoutRef.current = setTimeout(() => {
      saveDocument(false)
    }, saveInterval)
    
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
    }
  }, [state.isDirty, state.content, state.data, enabled, saveInterval])
  
  // Version creation interval
  useEffect(() => {
    if (!enabled) return
    
    const scheduleVersionCreation = () => {
      versionTimeoutRef.current = setTimeout(() => {
        if (state.isDirty && !state.isSaving) {
          saveDocument(true)
        }
        scheduleVersionCreation() // Schedule next version
      }, createVersionInterval)
    }
    
    scheduleVersionCreation()
    
    return () => {
      if (versionTimeoutRef.current) {
        clearTimeout(versionTimeoutRef.current)
      }
    }
  }, [enabled, createVersionInterval])
  
  // Initialize content refs when editor is first created
  useEffect(() => {
    if (state.editor && !isInitializedRef.current) {
      lastSavedContentRef.current = state.content
      lastSavedDataRef.current = state.data
      isInitializedRef.current = true
    }
  }, [state.editor, state.content, state.data])
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current)
      }
      if (versionTimeoutRef.current) {
        clearTimeout(versionTimeoutRef.current)
      }
    }
  }, [])
  
  // Manual save function
  const manualSave = async (createVersion = false) => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current)
    }
    await saveDocument(createVersion)
  }
  
  // Force save function (ignores dirty state)
  const forceSave = async (createVersion = false) => {
    if (!state.editor || !documentId) return
    
    dispatch({ type: 'SAVE_STARTED' })
    await saveDocument(createVersion)
  }
  
  return {
    manualSave,
    forceSave,
  }
}
