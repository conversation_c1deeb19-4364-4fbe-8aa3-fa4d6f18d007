/**
 * Modern group status manager using XState machines
 * Replaces ref-based useGroupStatusManager with proper state management
 */

import { useCallback, useRef } from 'react'
import { ReportComponent, DocumentAction } from '../context/DocumentContext'
import { useGroupManagementState } from './useEditorStateMachines'
import { renderLog } from '../context/utils/renderLogger'
import { schedulerService } from '../services/utils/schedulerService'
import { COMPONENT_STATUS, type ComponentStatus } from '../services/utils/constants'

interface GroupStatusManagerProps {
  dispatch: React.Dispatch<DocumentAction>
  stateRef: React.MutableRefObject<{ components: Map<string, ReportComponent> }>
}

export const useGroupStatusManagerModern = ({ dispatch, stateRef }: GroupStatusManagerProps) => {
  renderLog(`[HOOK] useGroupStatusManagerModern called`)
  
  const {
    context,
    hasError,
    actions: groupActions
  } = useGroupManagementState()

  // Keep timeout management as refs since they're imperative operations
  const groupStatusUpdateTimeouts = useRef<Map<string, string>>(new Map())

  // Helper function to get all descendants using a specific components map
  const getAllDescendantsFromState = useCallback((groupId: string, components: Map<string, ReportComponent>): ReportComponent[] => {
    const directChildren = Array.from(components.values()).filter((component) => component.parentId === groupId)
    const allDescendants: ReportComponent[] = [...directChildren]

    directChildren.forEach((child) => {
      if (child.type === 'report-group') {
        allDescendants.push(...getAllDescendantsFromState(child.id, components))
      }
    })

    return allDescendants
  }, [])

  // Check for circular updates using state machine
  const isCircularUpdate = useCallback((groupId: string): boolean => {
    if (context.updateChain.has(groupId)) {
      console.warn(`GroupStatusManager: Circular update detected for ${groupId}, breaking cycle`)
      return true
    }
    return false
  }, [context.updateChain])

  // Calculate group depth for bottom-up processing
  const getGroupDepth = useCallback((groupId: string, components: Map<string, ReportComponent>): number => {
    const group = components.get(groupId)
    if (!group || group.type !== 'report-group') return 0

    const descendants = getAllDescendantsFromState(groupId, components)
    const childGroups = descendants.filter(desc => desc.type === 'report-group')
    
    if (childGroups.length === 0) return 1
    
    return 1 + Math.max(...childGroups.map(child => getGroupDepth(child.id, components)))
  }, [getAllDescendantsFromState])

  // Process groups in depth order (deepest first)
  const processGroupsByDepth = useCallback(() => {
    const components = stateRef.current.components
    const pendingGroupIds = Array.from(context.pendingGroupUpdates)
    
    if (pendingGroupIds.length === 0) return

    // Group by depth and sort deepest first
    const groupDepths = pendingGroupIds.map(groupId => ({
      groupId,
      depth: getGroupDepth(groupId, components)
    }))
    
    groupDepths.sort((a, b) => b.depth - a.depth)
    
    console.log(`GroupStatusManager: Processing ${groupDepths.length} groups by depth:`, 
      groupDepths.map(g => `${g.groupId}(depth:${g.depth})`).join(', '))

    // Process each group
    for (const { groupId } of groupDepths) {
      if (isCircularUpdate(groupId)) continue
      
      // Add to update chain to prevent cycles
      groupActions.addToUpdateChain(groupId)
      
      try {
        updateGroupStatusImmediate(groupId)
      } finally {
        // Always remove from update chain
        groupActions.removeFromUpdateChain(groupId)
      }
    }
    
    // Clear all pending updates
    groupActions.clearPendingGroupUpdates()
  }, [context.pendingGroupUpdates, context.updateChain, stateRef, getGroupDepth, isCircularUpdate, groupActions])

  // Immediate group status update
  const updateGroupStatusImmediate = useCallback((groupId: string) => {
    const components = stateRef.current.components
    const group = components.get(groupId)
    
    if (!group || group.type !== 'report-group') {
      console.warn(`GroupStatusManager: Group ${groupId} not found or not a group`)
      return
    }

    console.log(`GroupStatusManager: Updating status for group ${groupId}`)

    // Get all direct children and descendants
    const directChildren = Array.from(components.values()).filter(
      (component) => component.parentId === groupId
    )
    const allDescendants = getAllDescendantsFromState(groupId, components)
    
    // Calculate status based on children
    let newStatus: ComponentStatus = COMPONENT_STATUS.LOADED
    let hasAnyChildren = directChildren.length > 0
    
    if (!hasAnyChildren) {
      newStatus = COMPONENT_STATUS.ERROR
    } else {
      // Check if any children are incomplete
      const hasIncompleteChildren = directChildren.some(child => 
        child.status === COMPONENT_STATUS.ERROR || 
        child.status === COMPONENT_STATUS.LOADING || 
        child.status === COMPONENT_STATUS.UNREGISTERED
      )
      
      if (hasIncompleteChildren) {
        newStatus = COMPONENT_STATUS.ERROR
      }
    }

    // Update if status changed
    if (group.status !== newStatus) {
      console.log(`GroupStatusManager: Status change for ${groupId}: ${group.status} -> ${newStatus}`)
      
      dispatch({ type: 'COMPONENT_UPDATED', id: groupId, updates: { status: newStatus } })
      
      // If this group has a parent group, schedule its update
      if (group.parentId) {
        const parent = components.get(group.parentId)
        if (parent?.type === 'report-group') {
          console.log(`GroupStatusManager: Scheduling parent update for ${group.parentId}`)
          scheduleGroupStatusUpdate(group.parentId)
        }
      }
    }
  }, [stateRef, dispatch, getAllDescendantsFromState])

  // Schedule a group status update with debouncing
  const scheduleGroupStatusUpdate = useCallback((groupId: string, immediate: boolean = false) => {
    if (immediate) {
      // Cancel any pending timeout for this group
      const existingTimeout = groupStatusUpdateTimeouts.current.get(groupId)
      if (existingTimeout) {
        schedulerService.clearTimeout(existingTimeout)
        groupStatusUpdateTimeouts.current.delete(groupId)
        groupActions.clearGroupTimeout(groupId)
      }
      
      updateGroupStatusImmediate(groupId)
      return
    }

    // Check if we already have a timeout for this group
    if (groupStatusUpdateTimeouts.current.has(groupId)) {
      console.log(`GroupStatusManager: Update already scheduled for ${groupId}`)
      return
    }

    console.log(`GroupStatusManager: Scheduling update for ${groupId}`)
    
    // Add to pending updates in state machine
    groupActions.addPendingGroupUpdate(groupId)
    groupActions.setGroupTimeoutActive(groupId)
    
    // Use centralized scheduler
    const timeoutId = schedulerService.timeout(
      () => {
        groupStatusUpdateTimeouts.current.delete(groupId)
        groupActions.clearGroupTimeout(groupId)
        
        // Process all pending updates in depth order
        processGroupsByDepth()
      },
      50, // 50ms debounce
      `group-status-${groupId}`
    )
    
    groupStatusUpdateTimeouts.current.set(groupId, timeoutId)
  }, [groupActions, updateGroupStatusImmediate, processGroupsByDepth])

  // Cancel all pending group updates
  const cancelAllGroupUpdates = useCallback(() => {
    console.log(`GroupStatusManager: Cancelling all pending updates`)
    
    // Cancel all timeouts
    for (const [groupId, timeoutId] of groupStatusUpdateTimeouts.current) {
      schedulerService.clearTimeout(timeoutId)
      groupActions.clearGroupTimeout(groupId)
    }
    
    groupStatusUpdateTimeouts.current.clear()
    groupActions.clearPendingGroupUpdates()
    groupActions.clearUpdateChain()
  }, [groupActions])

  // Get status information
  const getGroupUpdateStatus = useCallback((groupId: string) => ({
    isPending: context.pendingGroupUpdates.has(groupId),
    hasTimeout: context.groupStatusTimeouts.has(groupId),
    isInUpdateChain: context.updateChain.has(groupId)
  }), [context])

  return {
    updateGroupStatus: scheduleGroupStatusUpdate,
    updateGroupStatusImmediate,
    cancelAllGroupUpdates,
    getGroupUpdateStatus,
    isCircularUpdate,
    
    // Expose state machine actions for advanced usage
    actions: groupActions,
    
    // Statistics
    stats: {
      pendingUpdates: context.pendingGroupUpdates.size,
      activeTimeouts: context.groupStatusTimeouts.size,
      updateChainLength: context.updateChain.size
    },
    
    // Status
    hasError
  }
}