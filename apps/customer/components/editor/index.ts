// Main Provider
export { EditorProvider } from './context/providers/EditorProvider'
export { DocumentProvider } from './context/DocumentContext'

// Individual Context Providers (for advanced usage)
// Legacy ComponentProvider removed - use DocumentContext for component operations
// export { DocumentProvider, useDocument } from './context/document/_deprecated_DocumentContext'
export { VersioningProvider, useVersioning } from './context/versioning/VersioningContext'

// Main Hooks
export { useEditor, useEditorComponent } from './hooks/useEditor'

// Types
export type {
  ReportComponent,
  ComponentUpdate,
  ComponentOperations,
  DocumentState,
  DocumentOperations,
  EntityChange,
  GroupInfo,
  PerformanceMetrics,
  ComponentStatus,
  GroupStatus,
  DocumentStatus,
  EntityChangeStatus
} from './types'

// Services (for advanced usage)
export {
  logger,
  debounce,
  throttle,
  TimeoutRegistry,
  TIMEOUT_DELAYS,
  COMPONENT_STATUS,
  GROUP_STATUS,
  DOCUMENT_STATUS,
  ENTITY_CHANGE_STATUS,
  FINAL_STATES,
  READY_STATES,
  componentStateMachine,
  reportService,
  versionService
} from './services'

export type {
  TimeoutManager,
  // ComponentContext removed - legacy
  DocumentVersion
} from './services'