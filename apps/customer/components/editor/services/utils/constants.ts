// Component Status Constants
export const COMPONENT_STATUS = {
  IDLE: 'idle',
  UNREGISTERED: 'unregistered',
  REGISTERING: 'registering', 
  WAITING: 'waiting',
  LOADING: 'loading',
  LOADED: 'loaded',
  ERROR: 'error',
  PRESERVED: 'preserved',
  LOCKED: 'locked'
} as const

export type ComponentStatus = typeof COMPONENT_STATUS[keyof typeof COMPONENT_STATUS]

// Final states that don't require further processing
export const FINAL_STATES: ComponentStatus[] = [
  COMPONENT_STATUS.LOADED,
  COMPONENT_STATUS.ERROR,
  COMPONENT_STATUS.PRESERVED,
  COMPONENT_STATUS.LOCKED
]

// States that indicate a component is ready/complete
export const READY_STATES: ComponentStatus[] = [
  COMPONENT_STATUS.LOADED,
  COMPONENT_STATUS.PRESERVED,
  COMPONENT_STATUS.LOCKED
]

// Group Status Constants
export const GROUP_STATUS = {
  NOT_LOADED: 'not_loaded',
  LOADING: 'loading', 
  LOADED: 'loaded',
  ERROR: 'error'
} as const

export type GroupStatus = typeof GROUP_STATUS[keyof typeof GROUP_STATUS]

// Document Status Constants
export const DOCUMENT_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SAVING: 'saving',
  ERROR: 'error',
  LOADED: 'loaded'
} as const

export type DocumentStatus = typeof DOCUMENT_STATUS[keyof typeof DOCUMENT_STATUS]

// Entity Change Status
export const ENTITY_CHANGE_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  ERROR: 'error'
} as const

export type EntityChangeStatus = typeof ENTITY_CHANGE_STATUS[keyof typeof ENTITY_CHANGE_STATUS]

// Validation utilities
export function isValidComponentStatus(status: string): status is ComponentStatus {
  return Object.values(COMPONENT_STATUS).includes(status as ComponentStatus)
}

export function isFinalState(status: ComponentStatus): boolean {
  return FINAL_STATES.includes(status)
}

export function isReadyState(status: ComponentStatus): boolean {
  return READY_STATES.includes(status)
}