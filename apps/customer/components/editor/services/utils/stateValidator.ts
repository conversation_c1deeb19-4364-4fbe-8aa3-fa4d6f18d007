/**
 * State validation utilities for XState machines
 * Provides runtime validation and debugging support
 */

import { logger } from './logger'
import type {
  ComponentRegistrationContext,
  GroupManagementContext,
  EntityDocumentContext,
  LoadingOperationContext,
  PerformanceDebugContext
} from '../state/editorStateMachine'

// Validation result interface
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Component Registration Context Validation
export const validateComponentRegistrationContext = (
  context: ComponentRegistrationContext
): ValidationResult => {
  const errors: string[] = []
  const warnings: string[] = []

  // Validate Sets and Maps are properly initialized
  if (!(context.pendingRegistrations instanceof Set)) {
    errors.push('pendingRegistrations must be a Set')
  }

  if (!(context.orphanedComponents instanceof Map)) {
    errors.push('orphanedComponents must be a Map')
  }

  if (!(context.registrationFlags instanceof Map)) {
    errors.push('registrationFlags must be a Map')
  }

  if (typeof context.isProcessingBatch !== 'boolean') {
    errors.push('isProcessingBatch must be a boolean')
  }

  // Validate orphaned components structure
  for (const [parentId, components] of context.orphanedComponents) {
    if (typeof parentId !== 'string') {
      errors.push(`orphanedComponents key must be string, got ${typeof parentId}`)
    }
    
    if (!Array.isArray(components)) {
      errors.push(`orphanedComponents[${parentId}] must be an array`)
    }
  }

  // Validate registration flags
  for (const [componentId, flag] of context.registrationFlags) {
    if (typeof componentId !== 'string') {
      errors.push(`registrationFlags key must be string, got ${typeof componentId}`)
    }
    
    if (typeof flag !== 'boolean') {
      warnings.push(`registrationFlags[${componentId}] should be boolean, got ${typeof flag}`)
    }
  }

  // Warn about large collections
  if (context.pendingRegistrations.size > 100) {
    warnings.push(`Large number of pending registrations: ${context.pendingRegistrations.size}`)
  }

  if (context.orphanedComponents.size > 50) {
    warnings.push(`Large number of orphaned components: ${context.orphanedComponents.size}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Group Management Context Validation
export const validateGroupManagementContext = (
  context: GroupManagementContext
): ValidationResult => {
  const errors: string[] = []
  const warnings: string[] = []

  // Validate Sets and Maps
  if (!(context.pendingGroupUpdates instanceof Set)) {
    errors.push('pendingGroupUpdates must be a Set')
  }

  if (!(context.updateChain instanceof Set)) {
    errors.push('updateChain must be a Set')
  }

  if (!(context.groupStatusTimeouts instanceof Map)) {
    errors.push('groupStatusTimeouts must be a Map')
  }

  // Validate group IDs are strings
  for (const groupId of context.pendingGroupUpdates) {
    if (typeof groupId !== 'string') {
      errors.push(`pendingGroupUpdates contains non-string ID: ${typeof groupId}`)
    }
  }

  for (const groupId of context.updateChain) {
    if (typeof groupId !== 'string') {
      errors.push(`updateChain contains non-string ID: ${typeof groupId}`)
    }
  }

  // Validate timeout flags
  for (const [groupId, isActive] of context.groupStatusTimeouts) {
    if (typeof groupId !== 'string') {
      errors.push(`groupStatusTimeouts key must be string, got ${typeof groupId}`)
    }
    
    if (typeof isActive !== 'boolean') {
      errors.push(`groupStatusTimeouts[${groupId}] must be boolean, got ${typeof isActive}`)
    }
  }

  // Check for potential circular dependencies
  const chainArray = Array.from(context.updateChain)
  if (chainArray.length > 10) {
    warnings.push(`Long update chain detected: ${chainArray.length} groups`)
  }

  // Warn about stale pending updates
  if (context.pendingGroupUpdates.size > 20) {
    warnings.push(`Large number of pending group updates: ${context.pendingGroupUpdates.size}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Entity Document Context Validation
export const validateEntityDocumentContext = (
  context: EntityDocumentContext
): ValidationResult => {
  const errors: string[] = []
  const warnings: string[] = []

  // Validate Set
  if (!(context.entityChangeListeners instanceof Set)) {
    errors.push('entityChangeListeners must be a Set')
  }

  // Validate boolean flag
  if (typeof context.hasSetInitialContent !== 'boolean') {
    errors.push('hasSetInitialContent must be a boolean')
  }

  // Validate entity/run fields (can be null)
  if (context.currentEntity !== null && typeof context.currentEntity !== 'string') {
    errors.push('currentEntity must be string or null')
  }

  if (context.currentRun !== null && typeof context.currentRun !== 'string') {
    errors.push('currentRun must be string or null')
  }

  if (context.lastEntityUpdate !== null && typeof context.lastEntityUpdate !== 'string') {
    errors.push('lastEntityUpdate must be string or null')
  }

  // Validate listeners are functions
  for (const listener of context.entityChangeListeners) {
    if (typeof listener !== 'function') {
      errors.push('entityChangeListeners must contain functions')
    }
  }

  // Warn about too many listeners
  if (context.entityChangeListeners.size > 10) {
    warnings.push(`Large number of entity change listeners: ${context.entityChangeListeners.size}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Loading Operation Context Validation
export const validateLoadingOperationContext = (
  context: LoadingOperationContext
): ValidationResult => {
  const errors: string[] = []
  const warnings: string[] = []

  // Validate Sets
  if (!(context.loadingComponents instanceof Set)) {
    errors.push('loadingComponents must be a Set')
  }

  if (!(context.registeredComponents instanceof Set)) {
    errors.push('registeredComponents must be a Set')
  }

  // Validate boolean
  if (typeof context.savingInProgress !== 'boolean') {
    errors.push('savingInProgress must be a boolean')
  }

  // Validate timestamp (can be null)
  if (context.lastSaveTimestamp !== null && typeof context.lastSaveTimestamp !== 'number') {
    errors.push('lastSaveTimestamp must be number or null')
  }

  // Validate component IDs are strings
  for (const componentId of context.loadingComponents) {
    if (typeof componentId !== 'string') {
      errors.push(`loadingComponents contains non-string ID: ${typeof componentId}`)
    }
  }

  for (const componentId of context.registeredComponents) {
    if (typeof componentId !== 'string') {
      errors.push(`registeredComponents contains non-string ID: ${typeof componentId}`)
    }
  }

  // Check for components in both loading and registered
  const intersection = new Set([...context.loadingComponents].filter(id => 
    context.registeredComponents.has(id)
  ))
  
  if (intersection.size > 0) {
    warnings.push(`Components in both loading and registered: ${Array.from(intersection).join(', ')}`)
  }

  // Warn about too many loading components
  if (context.loadingComponents.size > 50) {
    warnings.push(`Large number of loading components: ${context.loadingComponents.size}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Performance Debug Context Validation
export const validatePerformanceDebugContext = (
  context: PerformanceDebugContext
): ValidationResult => {
  const errors: string[] = []
  const warnings: string[] = []

  // Validate number
  if (typeof context.renderCount !== 'number' || context.renderCount < 0) {
    errors.push('renderCount must be a non-negative number')
  }

  // Validate boolean
  if (typeof context.debugMode !== 'boolean') {
    errors.push('debugMode must be a boolean')
  }

  // Validate functions snapshot is an object
  if (typeof context.lastFunctionsSnapshot !== 'object' || context.lastFunctionsSnapshot === null) {
    errors.push('lastFunctionsSnapshot must be an object')
  } else {
    // Validate all values are strings
    for (const [key, value] of Object.entries(context.lastFunctionsSnapshot)) {
      if (typeof value !== 'string') {
        errors.push(`lastFunctionsSnapshot[${key}] must be string, got ${typeof value}`)
      }
    }
  }

  // Warn about high render count
  if (context.renderCount > 1000) {
    warnings.push(`High render count detected: ${context.renderCount}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Combined validation for all contexts
export const validateAllContexts = (contexts: {
  componentRegistration?: ComponentRegistrationContext
  groupManagement?: GroupManagementContext
  entityDocument?: EntityDocumentContext
  loadingOperation?: LoadingOperationContext
  performanceDebug?: PerformanceDebugContext
}): ValidationResult => {
  const allErrors: string[] = []
  const allWarnings: string[] = []

  if (contexts.componentRegistration) {
    const result = validateComponentRegistrationContext(contexts.componentRegistration)
    allErrors.push(...result.errors.map(e => `ComponentRegistration: ${e}`))
    allWarnings.push(...result.warnings.map(w => `ComponentRegistration: ${w}`))
  }

  if (contexts.groupManagement) {
    const result = validateGroupManagementContext(contexts.groupManagement)
    allErrors.push(...result.errors.map(e => `GroupManagement: ${e}`))
    allWarnings.push(...result.warnings.map(w => `GroupManagement: ${w}`))
  }

  if (contexts.entityDocument) {
    const result = validateEntityDocumentContext(contexts.entityDocument)
    allErrors.push(...result.errors.map(e => `EntityDocument: ${e}`))
    allWarnings.push(...result.warnings.map(w => `EntityDocument: ${w}`))
  }

  if (contexts.loadingOperation) {
    const result = validateLoadingOperationContext(contexts.loadingOperation)
    allErrors.push(...result.errors.map(e => `LoadingOperation: ${e}`))
    allWarnings.push(...result.warnings.map(w => `LoadingOperation: ${w}`))
  }

  if (contexts.performanceDebug) {
    const result = validatePerformanceDebugContext(contexts.performanceDebug)
    allErrors.push(...result.errors.map(e => `PerformanceDebug: ${e}`))
    allWarnings.push(...result.warnings.map(w => `PerformanceDebug: ${w}`))
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  }
}

// Development-only validation hook
export const useStateValidation = (
  contexts: Parameters<typeof validateAllContexts>[0],
  enabled: boolean = process.env.NODE_ENV === 'development'
) => {
  if (!enabled) return

  const result = validateAllContexts(contexts)
  
  if (!result.isValid) {
    logger.error('StateValidator', 'validation failed', 
      `Errors: ${result.errors.join(', ')}. Warnings: ${result.warnings.join(', ')}`)
    
    // In development, throw an error for invalid state
    if (process.env.NODE_ENV === 'development') {
      throw new Error(`State validation failed: ${result.errors.join(', ')}`)
    }
  } else if (result.warnings.length > 0) {
    logger.warn('StateValidator', 'validation warnings', 
      `Warnings: ${result.warnings.join(', ')}`)
  }
}

// Export individual validators for specific use cases
export const stateValidators = {
  componentRegistration: validateComponentRegistrationContext,
  groupManagement: validateGroupManagementContext,
  entityDocument: validateEntityDocumentContext,
  loadingOperation: validateLoadingOperationContext,
  performanceDebug: validatePerformanceDebugContext,
  all: validateAllContexts
}