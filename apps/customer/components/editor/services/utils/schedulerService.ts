import debounce from 'debounce'
import { LRUCache } from 'lru-cache'
import { logger } from './logger'

export interface ScheduledTask {
  id: string
  type: 'timeout' | 'interval' | 'debounced'
  callback: () => void | Promise<void>
  delay: number
  context?: string
  createdAt: Date
  lastExecuted?: Date
  executionCount: number
}

export interface DebouncedFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): void
  clear(): void
  flush(): ReturnType<T> | undefined
}

/**
 * Centralized scheduler service to manage all timeouts, intervals, and debounced functions.
 * Replaces scattered setTimeout/setInterval calls throughout the editor with a unified system.
 */
export class SchedulerService {
  private timeouts = new Map<string, NodeJS.Timeout>()
  private intervals = new Map<string, NodeJS.Timeout>()
  private debouncedFunctions = new Map<string, DebouncedFunction<any>>()
  private taskRegistry = new LRUCache<string, ScheduledTask>({ max: 1000 })
  private nextTaskId = 1

  /**
   * Schedules a one-time timeout
   */
  timeout(
    callback: () => void | Promise<void>,
    delay: number,
    context?: string
  ): string {
    const taskId = this.generateTaskId('timeout')
    
    logger.debug('SchedulerService', 'timeout', 
      `Scheduling timeout ${taskId} (delay: ${delay}ms, context: ${context || 'unknown'})`)

    const task: ScheduledTask = {
      id: taskId,
      type: 'timeout',
      callback,
      delay,
      context,
      createdAt: new Date(),
      executionCount: 0,
    }

    const timer = setTimeout(async () => {
      try {
        task.lastExecuted = new Date()
        task.executionCount++
        await callback()
        
        logger.debug('SchedulerService', 'timeout', `Timeout ${taskId} executed successfully`)
      } catch (error) {
        logger.error('SchedulerService', 'timeout', `Timeout ${taskId} failed`, error as Error)
      } finally {
        this.timeouts.delete(taskId)
        this.taskRegistry.delete(taskId)
      }
    }, delay)

    this.timeouts.set(taskId, timer)
    this.taskRegistry.set(taskId, task)

    return taskId
  }

  /**
   * Schedules a recurring interval
   */
  interval(
    callback: () => void | Promise<void>,
    delay: number,
    context?: string
  ): string {
    const taskId = this.generateTaskId('interval')
    
    logger.debug('SchedulerService', 'interval', 
      `Scheduling interval ${taskId} (delay: ${delay}ms, context: ${context || 'unknown'})`)

    const task: ScheduledTask = {
      id: taskId,
      type: 'interval',
      callback,
      delay,
      context,
      createdAt: new Date(),
      executionCount: 0,
    }

    const timer = setInterval(async () => {
      try {
        task.lastExecuted = new Date()
        task.executionCount++
        await callback()
        
        logger.debug('SchedulerService', 'interval', 
          `Interval ${taskId} executed (count: ${task.executionCount})`)
      } catch (error) {
        logger.error('SchedulerService', 'interval', `Interval ${taskId} failed`, error as Error)
      }
    }, delay)

    this.intervals.set(taskId, timer)
    this.taskRegistry.set(taskId, task)

    return taskId
  }

  /**
   * Creates a debounced function
   */
  debounced<T extends (...args: any[]) => any>(
    func: T,
    delay: number,
    options: {
      immediate?: boolean
      context?: string
    } = {}
  ): DebouncedFunction<T> {
    const taskId = this.generateTaskId('debounced')
    const { immediate = false, context } = options
    
    logger.debug('SchedulerService', 'debounced', 
      `Creating debounced function ${taskId} (delay: ${delay}ms, context: ${context || 'unknown'})`)

    const task: ScheduledTask = {
      id: taskId,
      type: 'debounced',
      callback: func,
      delay,
      context,
      createdAt: new Date(),
      executionCount: 0,
    }

    const debouncedFn = debounce(
      async (...args: Parameters<T>) => {
        try {
          task.lastExecuted = new Date()
          task.executionCount++
          const result = await func(...args)
          
          logger.debug('SchedulerService', 'debounced', 
            `Debounced function ${taskId} executed (count: ${task.executionCount})`)
          
          return result
        } catch (error) {
          logger.error('SchedulerService', 'debounced', 
            `Debounced function ${taskId} failed`, error as Error)
          throw error
        }
      },
      delay,
      { immediate }
    )

    // Store the enhanced debounced function
    const enhancedDebouncedFn = debouncedFn as DebouncedFunction<T>
    this.debouncedFunctions.set(taskId, enhancedDebouncedFn)
    this.taskRegistry.set(taskId, task)

    // Add cleanup when function is cleared
    const originalClear = enhancedDebouncedFn.clear
    enhancedDebouncedFn.clear = () => {
      originalClear.call(enhancedDebouncedFn)
      this.debouncedFunctions.delete(taskId)
      this.taskRegistry.delete(taskId)
      logger.debug('SchedulerService', 'debounced', `Debounced function ${taskId} cleared`)
    }

    return enhancedDebouncedFn
  }

  /**
   * Cancels a scheduled timeout
   */
  clearTimeout(taskId: string): boolean {
    const timer = this.timeouts.get(taskId)
    if (timer) {
      clearTimeout(timer)
      this.timeouts.delete(taskId)
      this.taskRegistry.delete(taskId)
      
      logger.debug('SchedulerService', 'clearTimeout', `Timeout ${taskId} cleared`)
      return true
    }
    return false
  }

  /**
   * Cancels a scheduled interval
   */
  clearInterval(taskId: string): boolean {
    const timer = this.intervals.get(taskId)
    if (timer) {
      clearInterval(timer)
      this.intervals.delete(taskId)
      this.taskRegistry.delete(taskId)
      
      logger.debug('SchedulerService', 'clearInterval', `Interval ${taskId} cleared`)
      return true
    }
    return false
  }

  /**
   * Clears a debounced function
   */
  clearDebounced(taskId: string): boolean {
    const debouncedFn = this.debouncedFunctions.get(taskId)
    if (debouncedFn) {
      debouncedFn.clear()
      return true
    }
    return false
  }

  /**
   * Clears all tasks for a specific context
   */
  clearContext(context: string): number {
    let clearedCount = 0

    // Clear timeouts
    for (const [taskId, task] of this.taskRegistry.entries()) {
      if (task.context === context) {
        switch (task.type) {
          case 'timeout':
            if (this.clearTimeout(taskId)) clearedCount++
            break
          case 'interval':
            if (this.clearInterval(taskId)) clearedCount++
            break
          case 'debounced':
            if (this.clearDebounced(taskId)) clearedCount++
            break
        }
      }
    }

    logger.info('SchedulerService', 'clearContext', 
      `Cleared ${clearedCount} tasks for context: ${context}`)

    return clearedCount
  }

  /**
   * Clears all scheduled tasks
   */
  clearAll(): number {
    let clearedCount = 0

    // Clear all timeouts
    this.timeouts.forEach((timer, taskId) => {
      clearTimeout(timer)
      clearedCount++
    })
    this.timeouts.clear()

    // Clear all intervals
    this.intervals.forEach((timer, taskId) => {
      clearInterval(timer)
      clearedCount++
    })
    this.intervals.clear()

    // Clear all debounced functions
    this.debouncedFunctions.forEach((debouncedFn, taskId) => {
      debouncedFn.clear()
      clearedCount++
    })
    this.debouncedFunctions.clear()

    // Clear registry
    this.taskRegistry.clear()

    logger.info('SchedulerService', 'clearAll', `Cleared ${clearedCount} total tasks`)

    return clearedCount
  }

  /**
   * Gets statistics about scheduled tasks
   */
  getStats(): {
    totalTasks: number
    timeouts: number
    intervals: number
    debouncedFunctions: number
    tasksByContext: Record<string, number>
    oldestTask?: ScheduledTask
    mostExecutedTask?: ScheduledTask
  } {
    const tasksByContext: Record<string, number> = {}
    let oldestTask: ScheduledTask | undefined
    let mostExecutedTask: ScheduledTask | undefined

    this.taskRegistry.forEach(task => {
      const context = task.context || 'unknown'
      tasksByContext[context] = (tasksByContext[context] || 0) + 1

      if (!oldestTask || task.createdAt < oldestTask.createdAt) {
        oldestTask = task
      }

      if (!mostExecutedTask || task.executionCount > mostExecutedTask.executionCount) {
        mostExecutedTask = task
      }
    })

    return {
      totalTasks: this.taskRegistry.size,
      timeouts: this.timeouts.size,
      intervals: this.intervals.size,
      debouncedFunctions: this.debouncedFunctions.size,
      tasksByContext,
      oldestTask,
      mostExecutedTask,
    }
  }

  /**
   * Gets all tasks for a specific context
   */
  getTasksForContext(context: string): ScheduledTask[] {
    const tasks: ScheduledTask[] = []
    
    this.taskRegistry.forEach(task => {
      if (task.context === context) {
        tasks.push(task)
      }
    })

    return tasks.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
  }

  /**
   * Checks if a task exists
   */
  hasTask(taskId: string): boolean {
    return this.taskRegistry.has(taskId)
  }

  /**
   * Gets a specific task
   */
  getTask(taskId: string): ScheduledTask | undefined {
    return this.taskRegistry.get(taskId)
  }

  /**
   * Generates a unique task ID
   */
  private generateTaskId(type: string): string {
    return `${type}-${this.nextTaskId++}-${Date.now()}`
  }

  /**
   * Advanced: Creates a rate-limited function
   */
  rateLimited<T extends (...args: any[]) => any>(
    func: T,
    maxCalls: number,
    windowMs: number,
    context?: string
  ): (...args: Parameters<T>) => Promise<ReturnType<T> | null> {
    const calls: number[] = []
    
    return async (...args: Parameters<T>): Promise<ReturnType<T> | null> => {
      const now = Date.now()
      
      // Remove calls outside the window
      while (calls.length > 0 && calls[0] <= now - windowMs) {
        calls.shift()
      }
      
      // Check if we can make the call
      if (calls.length >= maxCalls) {
        logger.warn('SchedulerService', 'rateLimited', 
          `Rate limit exceeded for context: ${context || 'unknown'}`)
        return null
      }
      
      calls.push(now)
      return await func(...args)
    }
  }

  /**
   * Advanced: Creates a retry mechanism with exponential backoff
   */
  withRetry<T extends (...args: any[]) => Promise<any>>(
    func: T,
    options: {
      maxAttempts?: number
      initialDelay?: number
      maxDelay?: number
      backoffFactor?: number
      context?: string
    } = {}
  ): (...args: Parameters<T>) => Promise<ReturnType<T>> {
    const {
      maxAttempts = 3,
      initialDelay = 1000,
      maxDelay = 10000,
      backoffFactor = 2,
      context
    } = options

    return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
      let lastError: Error | undefined
      let delay = initialDelay

      for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
          const result = await func(...args)
          
          if (attempt > 1) {
            logger.info('SchedulerService', 'withRetry', 
              `Function succeeded on attempt ${attempt} (context: ${context || 'unknown'})`)
          }
          
          return result
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error))
          
          if (attempt === maxAttempts) {
            logger.error('SchedulerService', 'withRetry', 
              `Function failed after ${maxAttempts} attempts (context: ${context || 'unknown'})`, lastError as Error)
            break
          }

          logger.warn('SchedulerService', 'withRetry', 
            `Attempt ${attempt} failed, retrying in ${delay}ms (context: ${context || 'unknown'})`)

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, delay))
          
          // Exponential backoff
          delay = Math.min(delay * backoffFactor, maxDelay)
        }
      }

      throw lastError || new Error('Unknown error in retry mechanism')
    }
  }
}

// Export singleton instance
export const schedulerService = new SchedulerService()