/**
 * XState v5 implementation for component state management
 * Replaces the custom ComponentStateMachine with proper XState
 */

import { setup, assign, fromPromise, createActor } from 'xstate'
import { logger } from '../utils/logger'
import type { ReportComponent } from '../../types'

// Component context for the XState machine
export interface ComponentContext {
  componentId: string
  componentType: string
  dependencies?: string[]
  hasError?: boolean
  errorMessage?: string
  parentId?: string
  children?: string[]
  data?: Record<string, any>
  retryCount?: number
  maxRetries?: number
}

// Events for the component state machine
export type ComponentEvent =
  | { type: 'REGISTER'; componentId: string; componentType: string; dependencies?: string[] }
  | { type: 'DEPENDENCIES_READY' }
  | { type: 'LOAD_SUCCESS'; data?: Record<string, any> }
  | { type: 'LOAD_FAILURE'; error: string }
  | { type: 'PRESERVE' }
  | { type: 'LOCK' }
  | { type: 'UNLOCK' }
  | { type: 'RETRY' }
  | { type: 'RESET' }

// XState machine definition
export const componentStateMachine = setup({
  types: {
    context: {} as ComponentContext,
    events: {} as ComponentEvent,
    input: {} as Partial<ComponentContext>
  },
  actors: {
    loadComponent: fromPromise(async ({ input }: { input: ComponentContext }) => {
      // Simulate component loading
      logger.debug('ComponentStateMachine', 'loadComponent', `Loading component ${input.componentId}`)
      
      // Check dependencies first
      if (input.dependencies?.length) {
        // Simulate dependency checking
        await new Promise(resolve => setTimeout(resolve, 10))
      }
      
      // Simulate actual loading
      await new Promise(resolve => setTimeout(resolve, 50))
      
      return {
        componentId: input.componentId,
        data: input.data || {},
        loadedAt: new Date().toISOString()
      }
    }),
    
    checkDependencies: fromPromise(async ({ input }: { input: ComponentContext }) => {
      // Simulate dependency checking
      logger.debug('ComponentStateMachine', 'checkDependencies', 
        `Checking dependencies for ${input.componentId}: ${input.dependencies?.join(', ') || 'none'}`)
      
      await new Promise(resolve => setTimeout(resolve, 20))
      
      // For now, assume dependencies are always ready
      return { ready: true }
    })
  }
}).createMachine({
  id: 'component',
  initial: 'idle',
  context: ({ input }) => ({
    componentId: input?.componentId || '',
    componentType: input?.componentType || '',
    dependencies: input?.dependencies,
    parentId: input?.parentId,
    children: input?.children,
    data: input?.data,
    retryCount: input?.retryCount || 0,
    maxRetries: input?.maxRetries || 3,
    hasError: false,
    errorMessage: undefined
  }),
  states: {
    idle: {
      on: {
        REGISTER: {
          target: 'registering',
          actions: assign({
            componentId: ({ event }) => event.componentId,
            componentType: ({ event }) => event.componentType,
            dependencies: ({ event }) => event.dependencies,
            retryCount: 0,
            hasError: false,
            errorMessage: undefined
          })
        }
      }
    },
    
    registering: {
      always: [
        {
          target: 'waitingForDependencies',
          guard: ({ context }) => Boolean(context.dependencies?.length)
        },
        {
          target: 'loading'
        }
      ],
      entry: () => {
        logger.debug('ComponentStateMachine', 'registering', 'Component registration started')
      }
    },
    
    waitingForDependencies: {
      invoke: {
        src: 'checkDependencies',
        input: ({ context }) => context,
        onDone: {
          target: 'loading',
          actions: () => {
            logger.debug('ComponentStateMachine', 'waitingForDependencies', 'Dependencies ready')
          }
        },
        onError: {
          target: 'error',
          actions: assign({
            hasError: true,
            errorMessage: 'Failed to check dependencies'
          })
        }
      },
      on: {
        DEPENDENCIES_READY: {
          target: 'loading'
        }
      }
    },
    
    loading: {
      invoke: {
        src: 'loadComponent',
        input: ({ context }) => context,
        onDone: {
          target: 'loaded',
          actions: assign({
            data: ({ event }) => event.output.data,
            hasError: false,
            errorMessage: undefined,
            retryCount: 0
          })
        },
        onError: {
          target: 'error',
          actions: assign({
            hasError: true,
            errorMessage: ({ event }) => (event.error as Error)?.message || 'Loading failed',
            retryCount: ({ context }) => (context.retryCount || 0) + 1
          })
        }
      },
      on: {
        LOAD_SUCCESS: {
          target: 'loaded',
          actions: assign({
            data: ({ event }) => event.data,
            hasError: false,
            errorMessage: undefined,
            retryCount: 0
          })
        },
        LOAD_FAILURE: {
          target: 'error',
          actions: assign({
            hasError: true,
            errorMessage: ({ event }) => event.error,
            retryCount: ({ context }) => (context.retryCount || 0) + 1
          })
        }
      }
    },
    
    loaded: {
      on: {
        PRESERVE: {
          target: 'preserved'
        },
        LOCK: {
          target: 'locked'
        },
        RETRY: {
          target: 'loading',
          actions: assign({
            hasError: false,
            errorMessage: undefined
          })
        }
      }
    },
    
    preserved: {
      on: {
        LOCK: {
          target: 'locked'
        },
        UNLOCK: {
          target: 'loaded'
        },
        RETRY: {
          target: 'loading',
          actions: assign({
            hasError: false,
            errorMessage: undefined
          })
        }
      }
    },
    
    locked: {
      on: {
        UNLOCK: {
          target: 'loaded'
        }
      }
    },
    
    error: {
      on: {
        RETRY: [
          {
            target: 'loading',
            guard: ({ context }) => (context.retryCount || 0) < (context.maxRetries || 3),
            actions: assign({
              hasError: false,
              errorMessage: undefined
            })
          },
          {
            // Max retries reached, stay in error state
            actions: () => {
              logger.warn('ComponentStateMachine', 'error', 'Max retries reached')
            }
          }
        ],
        RESET: {
          target: 'idle',
          actions: assign({
            hasError: false,
            errorMessage: undefined,
            retryCount: 0,
            data: undefined
          })
        }
      }
    }
  }
})

// Legacy compatibility types
export type ComponentState = 
  | 'idle' 
  | 'registering' 
  | 'waitingForDependencies' 
  | 'loading' 
  | 'loaded' 
  | 'error' 
  | 'preserved' 
  | 'locked'

// Manager for multiple component state machines
export class ComponentStateMachineManager {
  private actors = new Map<string, ReturnType<typeof createActor>>()
  private onStateChange?: (componentId: string, newState: string, context: ComponentContext) => void

  constructor(options?: {
    onStateChange?: (componentId: string, newState: string, context: ComponentContext) => void
  }) {
    this.onStateChange = options?.onStateChange
  }

  // Create or get an actor for a component
  createMachine(componentId: string, initialContext?: Partial<ComponentContext>) {
    if (this.actors.has(componentId)) {
      return this.actors.get(componentId)!
    }

    const actor = createActor(componentStateMachine, {
      input: {
        componentId,
        componentType: initialContext?.componentType || '',
        dependencies: initialContext?.dependencies,
        parentId: initialContext?.parentId,
        children: initialContext?.children,
        data: initialContext?.data,
        retryCount: 0,
        maxRetries: 3
      }
    })

    // Subscribe to state changes
    actor.subscribe((state) => {
      const stateValue = typeof state.value === 'string' ? state.value : Object.keys(state.value)[0]
      this.onStateChange?.(componentId, stateValue, state.context)
    })

    actor.start()
    this.actors.set(componentId, actor)
    
    logger.debug('ComponentStateMachineManager', 'createMachine', 
      `Created machine for component ${componentId}`)
    
    return actor
  }

  // Get existing actor
  getMachine(componentId: string) {
    return this.actors.get(componentId)
  }

  // Send event to a component's machine
  send(componentId: string, event: ComponentEvent): string | undefined {
    const actor = this.actors.get(componentId)
    if (actor) {
      actor.send(event)
      const stateValue = actor.getSnapshot().value
      return typeof stateValue === 'string' ? stateValue : Object.keys(stateValue)[0]
    } else {
      logger.warn('ComponentStateMachineManager', 'send', 
        `No machine found for component ${componentId}`)
      return undefined
    }
  }

  // Get current state of a component
  getState(componentId: string): string | undefined {
    const actor = this.actors.get(componentId)
    if (actor) {
      const stateValue = actor.getSnapshot().value
      return typeof stateValue === 'string' ? stateValue : Object.keys(stateValue)[0]
    }
    return undefined
  }

  // Get context of a component
  getContext(componentId: string): ComponentContext | undefined {
    const actor = this.actors.get(componentId)
    return actor?.getSnapshot().context
  }

  // Clean up all machines
  cleanup(): void {
    this.actors.forEach((actor, componentId) => {
      actor.stop()
    })
    this.actors.clear()
    logger.info('ComponentStateMachineManager', 'cleanup', 'All component machines cleaned up')
  }

  // Get statistics
  getStats() {
    const stats: Record<string, number> = {}
    
    this.actors.forEach((actor, componentId) => {
      const state = this.getState(componentId)
      if (state) {
        stats[state] = (stats[state] || 0) + 1
      }
    })
    
    return {
      totalMachines: this.actors.size,
      stateDistribution: stats
    }
  }
}

// Helper function to get the legacy status name for backward compatibility
export function toLegacyStatus(state: string): string {
  // Map XState states to legacy status names
  const statusMap: Record<string, string> = {
    'idle': 'IDLE',
    'registering': 'LOADING',
    'waitingForDependencies': 'WAITING', 
    'loading': 'LOADING',
    'loaded': 'LOADED',
    'error': 'ERROR',
    'preserved': 'PRESERVED',
    'locked': 'LOCKED'
  }
  
  return statusMap[state] || 'IDLE'
}

// Helper function to convert legacy status to machine state
export function fromLegacyStatus(legacyStatus: string): string {
  const stateMap: Record<string, string> = {
    'IDLE': 'idle',
    'UNREGISTERED': 'idle',
    'REGISTERING': 'registering',
    'WAITING': 'waitingForDependencies',
    'LOADING': 'loading',
    'LOADED': 'loaded',
    'ERROR': 'error',
    'PRESERVED': 'preserved',
    'LOCKED': 'locked'
  }
  
  return stateMap[legacyStatus] || 'idle'
}

// Export singleton instance
export const componentStateMachineManager = new ComponentStateMachineManager()

// Export just the new clean implementation (renamed to avoid collision)
export const componentMachineManager = componentStateMachineManager