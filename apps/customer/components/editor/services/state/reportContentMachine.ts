/**
 * XState v5 implementation for Report Content Management
 * Prevents duplicate content insertion and manages the complete lifecycle
 * of report sections, groups, and summaries
 */

import { assign, createActor, fromPromise, setup } from 'xstate'
import { logger } from '../utils/logger'
import type { CitationType } from '@/components/citation'

// Content insertion tracking
export interface ContentInsertionRecord {
  componentId: string
  contentHash: string
  insertedAt: Date
  position: number
}

// Report content context
export interface ReportContentContext {
  // Component identification
  componentId: string
  componentType: 'report-section' | 'report-group' | 'report-summary'
  parentId?: string
  
  // Content state
  content?: string
  contentHash?: string
  hasContent: boolean
  contentInserted: boolean
  insertionHistory: ContentInsertionRecord[]
  
  // Dependencies for summaries
  dependencies?: string[]
  dependencyContent?: Map<string, string>
  
  // API configuration
  endpoint?: string
  prompt?: string
  title?: string
  
  // Content processing options
  headings?: 'keep' | 'remove' | 'reset'
  depth?: number

  // Component state
  locked?: boolean
  preserved?: boolean

  // Citations
  citations?: CitationType[]
  
  // Error handling
  error?: string
  retryCount: number
  maxRetries: number
  
  // Editor reference for content insertion
  editor?: any
  getPos?: () => number | undefined
}

// Events for the content machine
export type ReportContentEvent =
  | { type: 'INITIALIZE'; componentId: string; componentType: ReportContentContext['componentType']; config: Partial<ReportContentContext> }
  | { type: 'LOAD_CONTENT' }
  | { type: 'CONTENT_LOADED'; content: string; citations?: CitationType[] }
  | { type: 'CONTENT_LOAD_FAILED'; error: string }
  | { type: 'INSERT_CONTENT'; editor: any; getPos: () => number | undefined }
  | { type: 'CONTENT_INSERTED'; position: number }
  | { type: 'CONTENT_INSERTION_FAILED'; error: string }
  | { type: 'DEPENDENCIES_UPDATED'; dependencies: Map<string, string> }
  | { type: 'SUMMARIZE' }
  | { type: 'SUMMARY_GENERATED'; content: string }
  | { type: 'SUMMARY_FAILED'; error: string }
  | { type: 'REFRESH' }
  | { type: 'CLEAR_CONTENT' }
  | { type: 'LOCK' }
  | { type: 'UNLOCK' }
  | { type: 'PRESERVE' }
  | { type: 'RETRY' }

// Helper function to generate content hash
function generateContentHash(content: string): string {
  // Simple hash function for demo - in production use crypto
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32bit integer
  }
  return hash.toString(36)
}

// Check if content was already inserted
function wasContentInserted(context: ReportContentContext, contentHash: string): boolean {
  return (context.insertionHistory || []).some(record => record.contentHash === contentHash)
}

// XState machine definition
export const reportContentMachine = setup({
  types: {
    context: {} as ReportContentContext,
    events: {} as ReportContentEvent,
    input: {} as Partial<ReportContentContext>
  },
  guards: {
    hasContent: ({ context }) => context.hasContent && !!context.content,
    contentNotInserted: ({ context }) => !context.contentInserted,
    canInsertContent: ({ context }) => {
      // Prevent insertion if content was already inserted
      if (context.contentInserted) return false
      if (!context.content) return false
      if (!context.contentHash) return false
      
      // Check insertion history to prevent duplicates
      return !wasContentInserted(context, context.contentHash)
    },
    hasEditor: ({ event }) => {
      if (event.type !== 'INSERT_CONTENT') return false
      return !!event.editor && !!event.getPos
    },
    hasDependencies: ({ context }) => {
      return context.componentType === 'report-summary' && 
             !!context.dependencies && 
             context.dependencies.length > 0
    },
    allDependenciesReady: ({ context }) => {
      if (!context.dependencies || context.dependencies.length === 0) {
        logger.debug('ReportContentMachine', 'allDependenciesReady',
          `${context.componentId}: No dependencies, returning true`)
        return true
      }

      if (!context.dependencyContent) {
        logger.debug('ReportContentMachine', 'allDependenciesReady',
          `${context.componentId}: No dependencyContent, returning false`)
        return false
      }

      const dependencyChecks = context.dependencies.map(depId => {
        const hasKey = context.dependencyContent!.has(depId)
        const content = context.dependencyContent!.get(depId)
        const hasContent = content && content.length > 0
        return { depId, hasKey, hasContent, contentLength: content?.length || 0 }
      })

      const ready = dependencyChecks.every(check => check.hasKey && check.hasContent)

      console.log(`STATE MACHINE DEBUG: ${context.componentId}: dependencies=${context.dependencies.join(', ')}, ready=${ready}`)
      console.log(`STATE MACHINE DEBUG: ${context.componentId}: dependency checks:`, dependencyChecks)
      console.log(`STATE MACHINE DEBUG: ${context.componentId}: contentKeys=[${Array.from(context.dependencyContent.keys()).join(', ')}]`)

      return ready
    },
    canRetry: ({ context }) => context.retryCount < context.maxRetries,
    isSection: ({ context }) => context.componentType === 'report-section',
    isSummary: ({ context }) => context.componentType === 'report-summary',
    isGroup: ({ context }) => context.componentType === 'report-group'
  },
  actors: {
    loadContent: fromPromise(async ({ input }: { input: ReportContentContext }) => {
      if (!input.endpoint) {
        throw new Error('No endpoint configured')
      }
      
      logger.debug('ReportContentMachine', 'loadContent', 
        `Loading content for ${input.componentId} from ${input.endpoint}`)
      
      // Simulate API call - replace with actual fetch
      const response = await fetch(input.endpoint)
      if (!response.ok) {
        throw new Error(`Failed to load content: ${response.status}`)
      }
      
      const data = await response.json()
      return {
        content: data.text || '',
        citations: data.citations || []
      }
    }),
    
    generateSummary: fromPromise(async ({ input }: { input: ReportContentContext }) => {
      if (!input.dependencyContent || input.dependencyContent.size === 0) {
        throw new Error('No content to summarize')
      }
      
      logger.debug('ReportContentMachine', 'generateSummary', 
        `Generating summary for ${input.componentId}`)
      
      // Combine dependency content
      const contentToSummarize = Array.from(input.dependencyContent.values())
        .filter(content => content.trim().length > 0)
        .join('\n\n')
      
      if (!contentToSummarize) {
        throw new Error('No valid content to summarize')
      }
      
      // Call summarize API
      const response = await fetch('/api/report/summarize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: contentToSummarize,
          prompt: input.prompt || '',
          title: input.title || ''
        })
      })
      
      if (!response.ok) {
        throw new Error(`Summary generation failed: ${response.status}`)
      }
      
      const data = await response.json()
      return data.text || ''
    }),
    
    insertContentIntoEditor: fromPromise(async ({ input }: { 
      input: ReportContentContext & { editor: any; getPos: () => number | undefined } 
    }) => {
      const { editor, getPos, content, contentHash } = input
      
      if (!editor || !getPos || !content) {
        throw new Error('Missing required parameters for content insertion')
      }
      
      const position = getPos()
      if (position === undefined) {
        throw new Error('Unable to determine insertion position')
      }
      
      logger.debug('ReportContentMachine', 'insertContent', 
        `Inserting content at position ${position} for ${input.componentId}`)
      
      // Process content based on component configuration
      let processedContent = content
      
      // Apply heading transformations if needed
      if (input.headings && input.headings !== 'keep') {
        // Apply heading transformation logic here
        processedContent = content // Placeholder - implement actual transformation
      }
      
      // Insert content into editor
      const nodeSize = editor.state.doc.nodeAt(position)?.nodeSize || 0
      const insertPos = position + nodeSize - 1
      
      editor.commands.insertContentAt(insertPos, 
        `<div class="report-content">${processedContent}</div>`, 
        { parseOptions: { preserveWhitespace: 'full' } }
      )
      
      return { position: insertPos, contentHash: contentHash || generateContentHash(content) }
    })
  }
}).createMachine({
  id: 'reportContent',
  initial: 'uninitialized',
  context: ({ input }) => ({
    componentId: input?.componentId || '',
    componentType: input?.componentType || 'report-section',
    parentId: input?.parentId,
    hasContent: false,
    contentInserted: false,
    insertionHistory: input?.insertionHistory || [],
    dependencies: input?.dependencies || [],
    dependencyContent: new Map(),
    retryCount: 0,
    maxRetries: input?.maxRetries || 3,
    endpoint: input?.endpoint,
    prompt: input?.prompt,
    title: input?.title,
    headings: input?.headings || 'remove',
    depth: input?.depth || 1,
    locked: input?.locked || false,
    preserved: input?.preserved || false,
    citations: input?.citations || []
  }),
  states: {
    uninitialized: {
      on: {
        INITIALIZE: {
          target: 'initialized',
          actions: assign({
            componentId: ({ event }) => event.componentId,
            componentType: ({ event }) => event.componentType,
            endpoint: ({ event, context }) => event.config.endpoint || context.endpoint,
            prompt: ({ event, context }) => event.config.prompt || context.prompt,
            title: ({ event, context }) => event.config.title || context.title,
            dependencies: ({ event, context }) => event.config.dependencies || context.dependencies,
            parentId: ({ event, context }) => event.config.parentId || context.parentId,
            headings: ({ event, context }) => event.config.headings || context.headings,
            depth: ({ event, context }) => event.config.depth || context.depth
          })
        }
      }
    },
    
    initialized: {
      always: [
        {
          target: 'waitingForDependencies',
          guard: 'hasDependencies'
        },
        {
          target: 'ready',
        }
      ]
    },
    
    ready: {
      on: {
        LOAD_CONTENT: {
          target: 'loadingContent',
          guard: 'isSection'
        },
        CONTENT_LOADED: {
          target: 'contentLoaded',
          actions: assign({
            content: ({ event }) => event.content,
            contentHash: ({ event }) => generateContentHash(event.content),
            citations: ({ event }) => event.citations || [],
            hasContent: true,
            error: undefined,
            retryCount: 0,
          }),
        },
        INSERT_CONTENT: [
          {
            target: 'insertingContent',
            guard: { 
              type: 'canInsertContent',
              params: {}
            }
          },
          {
            actions: () => {
              logger.warn('ReportContentMachine', 'ready', 
                'Cannot insert content - already inserted or invalid state')
            }
          }
        ],
        DEPENDENCIES_UPDATED: {
          actions: assign({
            dependencyContent: ({ event }) => event.dependencies,
          }),
        },
        SUMMARIZE: [
          {
            target: 'generatingSummary',
            guard: 'isSummary',
          },
          {
            actions: () => {
              logger.debug('ReportContentMachine', 'ready',
                'SUMMARIZE event received but not a summary component')
            },
          },
        ],
        REFRESH: {
          target: 'refreshing'
        }
      }
    },
    
    waitingForDependencies: {
      on: {
        DEPENDENCIES_UPDATED: {
          actions: assign({
            dependencyContent: ({ event }) => {
              console.log(`STATE MACHINE DEBUG: DEPENDENCIES_UPDATED: ${Array.from(event.dependencies.keys()).join(', ')}`)
              return event.dependencies
            },
          })
        },
        SUMMARIZE: [
          {
            target: 'generatingSummary',
            guard: 'allDependenciesReady'
          },
          {
            actions: () => {
              logger.debug('ReportContentMachine', 'waitingForDependencies', 
                'Not all dependencies ready yet')
            }
          }
        ]
      }
    },
    
    loadingContent: {
      invoke: {
        src: 'loadContent',
        input: ({ context }) => context,
        onDone: {
          target: 'contentLoaded',
          actions: assign({
            content: ({ event }) => event.output.content,
            contentHash: ({ event }) => generateContentHash(event.output.content),
            citations: ({ event }) => event.output.citations || [],
            hasContent: true,
            error: undefined,
            retryCount: 0
          })
        },
        onError: {
          target: 'error',
          actions: assign({
            error: ({ event }) => (event.error as Error).message,
            retryCount: ({ context }) => context.retryCount + 1
          })
        }
      }
    },
    
    generatingSummary: {
      invoke: {
        src: 'generateSummary',
        input: ({ context }) => context,
        onDone: {
          target: 'contentLoaded',
          actions: assign({
            content: ({ event }) => event.output,
            contentHash: ({ event }) => generateContentHash(event.output),
            hasContent: true,
            error: undefined,
            retryCount: 0
          })
        },
        onError: {
          target: 'error',
          actions: assign({
            error: ({ event }) => (event.error as Error).message,
            retryCount: ({ context }) => context.retryCount + 1
          })
        }
      }
    },
    
    contentLoaded: {
      on: {
        INSERT_CONTENT: [
          {
            target: 'insertingContent',
            guard: { 
              type: 'canInsertContent',
              params: {}
            }
          },
          {
            actions: () => {
              logger.warn('ReportContentMachine', 'contentLoaded', 
                'Content already inserted or duplicate detected')
            }
          }
        ],
        REFRESH: {
          target: 'refreshing'
        }
      }
    },
    
    insertingContent: {
      invoke: {
        src: 'insertContentIntoEditor',
        input: ({ context, event }) => ({
          ...context,
          editor: (event as any).editor,
          getPos: (event as any).getPos
        }),
        onDone: {
          target: 'contentInserted',
          actions: [
            assign({
              contentInserted: true,
              insertionHistory: ({ context, event }) => [
                ...(context.insertionHistory || []),
                {
                  componentId: context.componentId,
                  contentHash: event.output.contentHash,
                  insertedAt: new Date(),
                  position: event.output.position
                }
              ]
            }),
            () => {
              logger.info('ReportContentMachine', 'insertingContent', 
                'Content successfully inserted')
            }
          ]
        },
        onError: {
          target: 'contentLoaded',
          actions: [
            assign({
              error: ({ event }) => (event.error as Error).message
            }),
            () => {
              logger.error('ReportContentMachine', 'insertingContent', 
                'Failed to insert content')
            }
          ]
        }
      }
    },
    
    contentInserted: {
      entry: () => {
        logger.debug('ReportContentMachine', 'contentInserted', 
          'Component reached final state with content inserted')
      },
      on: {
        REFRESH: {
          target: 'refreshing',
        },
      }
    },
    
    refreshing: {
      entry: assign({
        content: undefined,
        contentHash: undefined,
        hasContent: false,
        contentInserted: false,
        error: undefined,
      }),
      always: [
        {
          target: 'waitingForDependencies',
          guard: 'hasDependencies'
        },
        {
          target: 'ready'
        }
      ]
    },
    
    error: {
      on: {
        RETRY: [
          {
            target: 'loadingContent',
            guard: {
              type: 'canRetry',
              params: {}
            },
            actions: assign({
              error: undefined
            })
          },
          {
            actions: () => {
              logger.error('ReportContentMachine', 'error', 
                'Maximum retry attempts reached')
            }
          }
        ],
        REFRESH: {
          target: 'refreshing',
          actions: assign({
            retryCount: 0
          })
        }
      }
    },
    
    locked: {
      entry: () => {
        logger.info('ReportContentMachine', 'locked', 'Component locked')
      },
      on: {
        UNLOCK: {
          target: 'ready'
        }
      }
    },
    
    preserved: {
      entry: () => {
        logger.info('ReportContentMachine', 'preserved', 'Component preserved')
      },
      on: {
        UNLOCK: {
          target: 'ready'
        }
      }
    }
  },
  on: {
    LOCK: {
      target: '.locked'
    },
    PRESERVE: {
      target: '.preserved'
    },
    CLEAR_CONTENT: {
      target: '.ready',
      actions: assign({
        content: undefined,
        contentHash: undefined,
        hasContent: false,
        contentInserted: false,
        // Reset insertion history for new session - duplicate prevention only within session
        insertionHistory: [],
      })
    },
    DEPENDENCIES_UPDATED: {
      actions: assign({
        dependencyContent: ({ event }) => event.dependencies,
      })
    }
  }
})

// Manager for report content machines
export class ReportContentMachineManager {
  private actors = new Map<string, ReturnType<typeof createActor>>()
  private onStateChange?: (componentId: string, state: any) => void
  
  constructor(options?: {
    onStateChange?: (componentId: string, state: any) => void
  }) {
    this.onStateChange = options?.onStateChange
  }
  
  createMachine(componentId: string, config: Partial<ReportContentContext>) {
    // Check if actor already exists and is running
    const existingActor = this.actors.get(componentId)
    if (existingActor) {
      const snapshot = existingActor.getSnapshot()
      if (snapshot.status === 'active') {
        logger.debug('ReportContentMachineManager', 'createMachine',
          `Machine already exists and is active for ${componentId}`)
        return existingActor
      } else {
        // Actor exists but is stopped - remove it and create a new one
        logger.warn('ReportContentMachineManager', 'createMachine',
          `Machine exists but is stopped for ${componentId}, removing and recreating`)
        this.actors.delete(componentId)
      }
    }
    
    const actor = createActor(reportContentMachine, {
      input: {
        componentId,
        ...config
      }
    })
    
    actor.subscribe((state) => {
      this.onStateChange?.(componentId, state)
    })
    
    actor.start()
    this.actors.set(componentId, actor)
    
    logger.info('ReportContentMachineManager', 'createMachine', 
      `Created machine for ${componentId}`)
    
    return actor
  }
  
  getMachine(componentId: string) {
    return this.actors.get(componentId)
  }
  
  send(componentId: string, event: ReportContentEvent) {
    const actor = this.actors.get(componentId)
    if (actor) {
      const snapshot = actor.getSnapshot()
      if (snapshot.status === 'active') {
        actor.send(event)
      } else {
        logger.warn('ReportContentMachineManager', 'send',
          `Actor for ${componentId} is stopped, cannot send event ${event.type}`)
      }
    } else {
      logger.warn('ReportContentMachineManager', 'send', 
        `No machine found for ${componentId}`)
    }
  }
  
  getContentState(componentId: string) {
    const actor = this.actors.get(componentId)
    if (!actor) return null
    
    const snapshot = actor.getSnapshot()

    // Calculate canInsert by checking the guard directly
    const canInsert = (() => {
      const context = snapshot.context
      // Prevent insertion if content was already inserted
      if (context.contentInserted) return false
      if (!context.content) return false
      if (!context.contentHash) return false

      // Check insertion history to prevent duplicates
      return !wasContentInserted(context, context.contentHash)
    })()

    return {
      state: snapshot.value,
      context: snapshot.context,
      hasContent: snapshot.context.hasContent,
      contentInserted: snapshot.context.contentInserted,
      canInsert,
    }
  }
  
  cleanup() {
    this.actors.forEach(actor => actor.stop())
    this.actors.clear()
  }
}

// Export singleton
export const reportContentManager = new ReportContentMachineManager()
