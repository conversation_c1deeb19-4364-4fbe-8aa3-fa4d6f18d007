/**
 * XState v5 machines for editor state management
 * Replaces ref-based state management with proper state machines
 */

import { setup, assign, fromPromise } from 'xstate'
import { logger } from '../utils/logger'

// Context interfaces for different state machines
export interface ComponentRegistrationContext {
  pendingRegistrations: Set<string>
  orphanedComponents: Map<string, any[]>
  isProcessingBatch: boolean
  registrationFlags: Map<string, boolean>
  errorMessage?: string
}

export interface GroupManagementContext {
  pendingGroupUpdates: Set<string>
  updateChain: Set<string>
  groupStatusTimeouts: Map<string, boolean>
  currentGroupId?: string
  errorMessage?: string
}

export interface EntityDocumentContext {
  entityChangeListeners: Set<(entity: string | null, run: string) => void>
  hasSetInitialContent: boolean
  lastEntityUpdate: string | null
  currentEntity: string | null
  currentRun: string | null
  errorMessage?: string
}

export interface LoadingOperationContext {
  loadingComponents: Set<string>
  registeredComponents: Set<string>
  savingInProgress: boolean
  lastSaveTimestamp: number | null
  currentComponentId?: string
  errorMessage?: string
}

export interface PerformanceDebugContext {
  renderCount: number
  lastStateSnapshot: any
  lastFunctionsSnapshot: Record<string, string>
  debugMode: boolean
}

// Event types for each machine
export type ComponentRegistrationEvents =
  | { type: 'ADD_PENDING_REGISTRATION'; componentId: string }
  | { type: 'REMOVE_PENDING_REGISTRATION'; componentId: string }
  | { type: 'CLEAR_PENDING_REGISTRATIONS' }
  | { type: 'ADD_ORPHANED_COMPONENT'; parentId: string; component: any }
  | { type: 'REMOVE_ORPHANED_COMPONENT'; parentId: string }
  | { type: 'CLEAR_ORPHANED_COMPONENTS' }
  | { type: 'START_BATCH_PROCESSING' }
  | { type: 'FINISH_BATCH_PROCESSING' }
  | { type: 'SET_REGISTRATION_FLAG'; componentId: string; value?: boolean }
  | { type: 'CLEAR_REGISTRATION_FLAG'; componentId: string }
  | { type: 'ERROR'; message: string }
  | { type: 'RETRY' }
  | { type: 'RESET' }

export type GroupManagementEvents =
  | { type: 'ADD_PENDING_GROUP_UPDATE'; groupId: string }
  | { type: 'REMOVE_PENDING_GROUP_UPDATE'; groupId: string }
  | { type: 'CLEAR_PENDING_GROUP_UPDATES' }
  | { type: 'ADD_TO_UPDATE_CHAIN'; groupId: string }
  | { type: 'REMOVE_FROM_UPDATE_CHAIN'; groupId: string }
  | { type: 'CLEAR_UPDATE_CHAIN' }
  | { type: 'SET_GROUP_TIMEOUT_ACTIVE'; groupId: string }
  | { type: 'CLEAR_GROUP_TIMEOUT'; groupId: string }
  | { type: 'ERROR'; message: string }
  | { type: 'RETRY' }
  | { type: 'RESET' }

export type EntityDocumentEvents =
  | { type: 'ADD_ENTITY_CHANGE_LISTENER'; listener: (entity: string | null, run: string) => void }
  | { type: 'REMOVE_ENTITY_CHANGE_LISTENER'; listener: (entity: string | null, run: string) => void }
  | { type: 'CLEAR_ENTITY_CHANGE_LISTENERS' }
  | { type: 'SET_INITIAL_CONTENT_FLAG'; hasSet: boolean }
  | { type: 'UPDATE_LAST_ENTITY'; entity: string | null }
  | { type: 'UPDATE_CURRENT_ENTITY'; entity: string | null }
  | { type: 'UPDATE_CURRENT_RUN'; run: string | null }
  | { type: 'ERROR'; message: string }
  | { type: 'RETRY' }
  | { type: 'RESET' }

export type LoadingOperationEvents =
  | { type: 'SET_COMPONENT_LOADING'; componentId: string }
  | { type: 'CLEAR_COMPONENT_LOADING'; componentId: string }
  | { type: 'SET_COMPONENT_REGISTERED'; componentId: string }
  | { type: 'CLEAR_COMPONENT_REGISTERED'; componentId: string }
  | { type: 'START_SAVING' }
  | { type: 'FINISH_SAVING' }
  | { type: 'UPDATE_LAST_SAVE_TIMESTAMP' }
  | { type: 'RESET_LOADING_STATE' }
  | { type: 'ERROR'; message: string }
  | { type: 'RETRY' }

export type PerformanceDebugEvents =
  | { type: 'INCREMENT_RENDER_COUNT' }
  | { type: 'UPDATE_STATE_SNAPSHOT'; snapshot: any }
  | { type: 'UPDATE_FUNCTIONS_SNAPSHOT'; snapshot: Record<string, string> }
  | { type: 'TOGGLE_DEBUG_MODE' }
  | { type: 'RESET_DEBUG_STATE' }

// Component Registration State Machine
export const componentRegistrationMachine = setup({
  types: {
    context: {} as ComponentRegistrationContext,
    events: {} as ComponentRegistrationEvents
  },
  actors: {
    processBatch: fromPromise(async ({ input }: { input: { pendingRegistrations: Set<string> } }) => {
      // Simulate batch processing
      await new Promise(resolve => setTimeout(resolve, 100))
      logger.debug('ComponentRegistrationMachine', 'processBatch', `Processed ${input.pendingRegistrations.size} registrations`)
      return true
    })
  }
}).createMachine({
  id: 'componentRegistration',
  initial: 'idle',
  context: {
    pendingRegistrations: new Set(),
    orphanedComponents: new Map(),
    isProcessingBatch: false,
    registrationFlags: new Map()
  },
  states: {
    idle: {
      on: {
        ADD_PENDING_REGISTRATION: {
          actions: assign({
            pendingRegistrations: ({ context, event }) => {
              const newSet = new Set(context.pendingRegistrations)
              newSet.add(event.componentId)
              return newSet
            }
          })
        },
        REMOVE_PENDING_REGISTRATION: {
          actions: assign({
            pendingRegistrations: ({ context, event }) => {
              const newSet = new Set(context.pendingRegistrations)
              newSet.delete(event.componentId)
              return newSet
            }
          })
        },
        CLEAR_PENDING_REGISTRATIONS: {
          actions: assign({
            pendingRegistrations: () => new Set()
          })
        },
        ADD_ORPHANED_COMPONENT: {
          actions: assign({
            orphanedComponents: ({ context, event }) => {
              const newMap = new Map(context.orphanedComponents)
              const existing = newMap.get(event.parentId) || []
              newMap.set(event.parentId, [...existing, event.component])
              return newMap
            }
          })
        },
        REMOVE_ORPHANED_COMPONENT: {
          actions: assign({
            orphanedComponents: ({ context, event }) => {
              const newMap = new Map(context.orphanedComponents)
              newMap.delete(event.parentId)
              return newMap
            }
          })
        },
        CLEAR_ORPHANED_COMPONENTS: {
          actions: assign({
            orphanedComponents: () => new Map()
          })
        },
        SET_REGISTRATION_FLAG: {
          actions: assign({
            registrationFlags: ({ context, event }) => {
              const newMap = new Map(context.registrationFlags)
              newMap.set(event.componentId, event.value ?? true)
              return newMap
            }
          })
        },
        CLEAR_REGISTRATION_FLAG: {
          actions: assign({
            registrationFlags: ({ context, event }) => {
              const newMap = new Map(context.registrationFlags)
              newMap.delete(event.componentId)
              return newMap
            }
          })
        },
        START_BATCH_PROCESSING: {
          target: 'processing'
        }
      }
    },
    processing: {
      entry: assign({
        isProcessingBatch: true
      }),
      invoke: {
        src: 'processBatch',
        input: ({ context }) => ({ pendingRegistrations: context.pendingRegistrations }),
        onDone: {
          target: 'idle',
          actions: assign({
            isProcessingBatch: false,
            pendingRegistrations: () => new Set()
          })
        },
        onError: {
          target: 'error',
          actions: assign({
            isProcessingBatch: false,
            errorMessage: ({ event }) => (event.error as Error)?.message || 'Batch processing failed'
          })
        }
      },
      on: {
        FINISH_BATCH_PROCESSING: {
          target: 'idle',
          actions: assign({
            isProcessingBatch: false
          })
        }
      }
    },
    error: {
      on: {
        RETRY: {
          target: 'idle',
          actions: assign({
            errorMessage: undefined
          })
        },
        RESET: {
          target: 'idle',
          actions: assign({
            pendingRegistrations: () => new Set(),
            orphanedComponents: () => new Map(),
            isProcessingBatch: false,
            registrationFlags: () => new Map(),
            errorMessage: undefined
          })
        }
      }
    }
  }
})

// Group Management State Machine
export const groupManagementMachine = setup({
  types: {
    context: {} as GroupManagementContext,
    events: {} as GroupManagementEvents
  }
}).createMachine({
  id: 'groupManagement',
  initial: 'idle',
  context: {
    pendingGroupUpdates: new Set(),
    updateChain: new Set(),
    groupStatusTimeouts: new Map()
  },
  states: {
    idle: {
      on: {
        ADD_PENDING_GROUP_UPDATE: {
          actions: assign({
            pendingGroupUpdates: ({ context, event }) => {
              const newSet = new Set(context.pendingGroupUpdates)
              newSet.add(event.groupId)
              return newSet
            }
          })
        },
        REMOVE_PENDING_GROUP_UPDATE: {
          actions: assign({
            pendingGroupUpdates: ({ context, event }) => {
              const newSet = new Set(context.pendingGroupUpdates)
              newSet.delete(event.groupId)
              return newSet
            }
          })
        },
        CLEAR_PENDING_GROUP_UPDATES: {
          actions: assign({
            pendingGroupUpdates: () => new Set()
          })
        },
        ADD_TO_UPDATE_CHAIN: {
          actions: assign({
            updateChain: ({ context, event }) => {
              const newSet = new Set(context.updateChain)
              newSet.add(event.groupId)
              return newSet
            }
          })
        },
        REMOVE_FROM_UPDATE_CHAIN: {
          actions: assign({
            updateChain: ({ context, event }) => {
              const newSet = new Set(context.updateChain)
              newSet.delete(event.groupId)
              return newSet
            }
          })
        },
        CLEAR_UPDATE_CHAIN: {
          actions: assign({
            updateChain: () => new Set()
          })
        },
        SET_GROUP_TIMEOUT_ACTIVE: {
          actions: assign({
            groupStatusTimeouts: ({ context, event }) => {
              const newMap = new Map(context.groupStatusTimeouts)
              newMap.set(event.groupId, true)
              return newMap
            }
          })
        },
        CLEAR_GROUP_TIMEOUT: {
          actions: assign({
            groupStatusTimeouts: ({ context, event }) => {
              const newMap = new Map(context.groupStatusTimeouts)
              newMap.delete(event.groupId)
              return newMap
            }
          })
        },
        ERROR: {
          target: 'error',
          actions: assign({
            errorMessage: ({ event }) => event.message
          })
        }
      }
    },
    error: {
      on: {
        RETRY: {
          target: 'idle',
          actions: assign({
            errorMessage: undefined
          })
        },
        RESET: {
          target: 'idle',
          actions: assign({
            pendingGroupUpdates: () => new Set(),
            updateChain: () => new Set(),
            groupStatusTimeouts: () => new Map(),
            currentGroupId: undefined,
            errorMessage: undefined
          })
        }
      }
    }
  }
})

// Entity Document State Machine
export const entityDocumentMachine = setup({
  types: {
    context: {} as EntityDocumentContext,
    events: {} as EntityDocumentEvents
  }
}).createMachine({
  id: 'entityDocument',
  initial: 'idle',
  context: {
    entityChangeListeners: new Set(),
    hasSetInitialContent: false,
    lastEntityUpdate: null,
    currentEntity: null,
    currentRun: null
  },
  states: {
    idle: {
      on: {
        ADD_ENTITY_CHANGE_LISTENER: {
          actions: assign({
            entityChangeListeners: ({ context, event }) => {
              const newSet = new Set(context.entityChangeListeners)
              newSet.add(event.listener)
              return newSet
            }
          })
        },
        REMOVE_ENTITY_CHANGE_LISTENER: {
          actions: assign({
            entityChangeListeners: ({ context, event }) => {
              const newSet = new Set(context.entityChangeListeners)
              newSet.delete(event.listener)
              return newSet
            }
          })
        },
        CLEAR_ENTITY_CHANGE_LISTENERS: {
          actions: assign({
            entityChangeListeners: () => new Set()
          })
        },
        SET_INITIAL_CONTENT_FLAG: {
          actions: assign({
            hasSetInitialContent: ({ event }) => event.hasSet
          })
        },
        UPDATE_LAST_ENTITY: {
          actions: assign({
            lastEntityUpdate: ({ event }) => event.entity
          })
        },
        UPDATE_CURRENT_ENTITY: {
          actions: assign({
            currentEntity: ({ event }) => event.entity
          })
        },
        UPDATE_CURRENT_RUN: {
          actions: assign({
            currentRun: ({ event }) => event.run
          })
        },
        ERROR: {
          target: 'error',
          actions: assign({
            errorMessage: ({ event }) => event.message
          })
        }
      }
    },
    error: {
      on: {
        RETRY: {
          target: 'idle',
          actions: assign({
            errorMessage: undefined
          })
        },
        RESET: {
          target: 'idle',
          actions: assign({
            entityChangeListeners: () => new Set(),
            hasSetInitialContent: false,
            lastEntityUpdate: null,
            currentEntity: null,
            currentRun: null,
            errorMessage: undefined
          })
        }
      }
    }
  }
})

// Loading Operation State Machine
export const loadingOperationMachine = setup({
  types: {
    context: {} as LoadingOperationContext,
    events: {} as LoadingOperationEvents
  }
}).createMachine({
  id: 'loadingOperation',
  initial: 'idle',
  context: {
    loadingComponents: new Set(),
    registeredComponents: new Set(),
    savingInProgress: false,
    lastSaveTimestamp: null
  },
  states: {
    idle: {
      on: {
        SET_COMPONENT_LOADING: {
          actions: assign({
            loadingComponents: ({ context, event }) => {
              const newSet = new Set(context.loadingComponents)
              newSet.add(event.componentId)
              return newSet
            }
          })
        },
        CLEAR_COMPONENT_LOADING: {
          actions: assign({
            loadingComponents: ({ context, event }) => {
              const newSet = new Set(context.loadingComponents)
              newSet.delete(event.componentId)
              return newSet
            }
          })
        },
        SET_COMPONENT_REGISTERED: {
          actions: assign({
            registeredComponents: ({ context, event }) => {
              const newSet = new Set(context.registeredComponents)
              newSet.add(event.componentId)
              return newSet
            }
          })
        },
        CLEAR_COMPONENT_REGISTERED: {
          actions: assign({
            registeredComponents: ({ context, event }) => {
              const newSet = new Set(context.registeredComponents)
              newSet.delete(event.componentId)
              return newSet
            }
          })
        },
        START_SAVING: {
          target: 'saving',
          actions: assign({
            savingInProgress: true
          })
        },
        RESET_LOADING_STATE: {
          actions: assign({
            loadingComponents: () => new Set(),
            registeredComponents: () => new Set(),
            savingInProgress: false,
            lastSaveTimestamp: null
          })
        }
      }
    },
    saving: {
      on: {
        FINISH_SAVING: {
          target: 'idle',
          actions: assign({
            savingInProgress: false,
            lastSaveTimestamp: () => Date.now()
          })
        },
        UPDATE_LAST_SAVE_TIMESTAMP: {
          actions: assign({
            lastSaveTimestamp: () => Date.now()
          })
        },
        ERROR: {
          target: 'error',
          actions: assign({
            savingInProgress: false,
            errorMessage: ({ event }) => event.message
          })
        }
      }
    },
    error: {
      on: {
        RETRY: {
          target: 'idle',
          actions: assign({
            errorMessage: undefined
          })
        }
      }
    }
  }
})

// Performance Debug State Machine
export const performanceDebugMachine = setup({
  types: {
    context: {} as PerformanceDebugContext,
    events: {} as PerformanceDebugEvents
  }
}).createMachine({
  id: 'performanceDebug',
  initial: 'idle',
  context: {
    renderCount: 0,
    lastStateSnapshot: null,
    lastFunctionsSnapshot: {},
    debugMode: false
  },
  states: {
    idle: {
      on: {
        INCREMENT_RENDER_COUNT: {
          actions: assign({
            renderCount: ({ context }) => context.renderCount + 1
          })
        },
        UPDATE_STATE_SNAPSHOT: {
          actions: assign({
            lastStateSnapshot: ({ event }) => event.snapshot
          })
        },
        UPDATE_FUNCTIONS_SNAPSHOT: {
          actions: assign({
            lastFunctionsSnapshot: ({ event }) => event.snapshot
          })
        },
        TOGGLE_DEBUG_MODE: {
          actions: assign({
            debugMode: ({ context }) => !context.debugMode
          })
        },
        RESET_DEBUG_STATE: {
          actions: assign({
            renderCount: 0,
            lastStateSnapshot: null,
            lastFunctionsSnapshot: {},
            debugMode: false
          })
        }
      }
    }
  }
})