import { logger } from '../utils/logger'
import type { ReportComponent } from '../../types'

/**
 * Service for managing component dependency resolution and ordering.
 * Centralizes all dependency-related logic previously scattered across hooks.
 */
export class DependencyService {
  private dependencyWaiters = new Map<string, Array<() => void>>()

  /**
   * Checks if all dependencies for a component are ready
   */
  areDependenciesReady(
    componentId: string,
    components: Map<string, ReportComponent>
  ): boolean {
    const component = components.get(componentId)
    if (!component?.dependencies?.length) {
      return true
    }

    logger.debug('DependencyService', 'areDependenciesReady', 
      `Checking dependencies for ${componentId}: ${component.dependencies.join(', ')}`)

    const readyStates = ['loaded', 'preserved', 'locked']
    const allReady = component.dependencies.every(depId => {
      const dependency = components.get(depId)
      const isReady = dependency && readyStates.includes(dependency.status)
      
      if (!isReady) {
        logger.debug('DependencyService', 'areDependenciesReady', 
          `Dependency ${depId} not ready (status: ${dependency?.status || 'not found'})`)
      }
      
      return isReady
    })

    logger.debug('DependencyService', 'areDependenciesReady', 
      `Dependencies for ${componentId} are ${allReady ? 'ready' : 'not ready'}`)

    return allReady
  }

  /**
   * Returns a promise that resolves when all dependencies are ready
   */
  waitForDependencies(
    componentId: string,
    components: Map<string, ReportComponent>
  ): Promise<void> {
    return new Promise((resolve) => {
      // Check if already ready
      if (this.areDependenciesReady(componentId, components)) {
        resolve()
        return
      }

      logger.debug('DependencyService', 'waitForDependencies', 
        `Adding waiter for ${componentId}`)

      // Add to waiters
      if (!this.dependencyWaiters.has(componentId)) {
        this.dependencyWaiters.set(componentId, [])
      }
      this.dependencyWaiters.get(componentId)!.push(resolve)
    })
  }

  /**
   * Notifies all waiters that a dependency has been resolved
   */
  notifyDependencyResolved(
    resolvedComponentId: string,
    components: Map<string, ReportComponent>
  ): void {
    logger.debug('DependencyService', 'notifyDependencyResolved', 
      `Component ${resolvedComponentId} resolved`)

    // Find all components waiting for this dependency
    const waitersToNotify: string[] = []

    this.dependencyWaiters.forEach((waiters, componentId) => {
      if (this.areDependenciesReady(componentId, components)) {
        waitersToNotify.push(componentId)
      }
    })

    // Notify waiters
    waitersToNotify.forEach(componentId => {
      const waiters = this.dependencyWaiters.get(componentId)
      if (waiters) {
        logger.debug('DependencyService', 'notifyDependencyResolved', 
          `Notifying ${waiters.length} waiters for ${componentId}`)
        
        waiters.forEach(resolve => resolve())
        this.dependencyWaiters.delete(componentId)
      }
    })
  }

  /**
   * Gets all components that depend on a given component
   */
  getDependents(
    componentId: string,
    components: Map<string, ReportComponent>
  ): ReportComponent[] {
    const dependents: ReportComponent[] = []

    components.forEach(component => {
      if (component.dependencies?.includes(componentId)) {
        dependents.push(component)
      }
    })

    return dependents
  }

  /**
   * Gets the dependency graph for all components
   */
  getDependencyGraph(components: Map<string, ReportComponent>): {
    graph: Record<string, string[]>
    cycles: string[][]
  } {
    const graph: Record<string, string[]> = {}
    
    // Build the graph
    components.forEach((component, id) => {
      graph[id] = component.dependencies || []
    })

    // Detect cycles
    const cycles = this.detectCycles(graph)

    return { graph, cycles }
  }

  /**
   * Detects circular dependencies in the component graph
   */
  private detectCycles(graph: Record<string, string[]>): string[][] {
    const cycles: string[][] = []
    const visited = new Set<string>()
    const recursionStack = new Set<string>()

    const dfs = (node: string, path: string[]): void => {
      if (recursionStack.has(node)) {
        // Found a cycle
        const cycleStart = path.indexOf(node)
        if (cycleStart !== -1) {
          cycles.push(path.slice(cycleStart))
        }
        return
      }

      if (visited.has(node)) {
        return
      }

      visited.add(node)
      recursionStack.add(node)
      path.push(node)

      const dependencies = graph[node] || []
      dependencies.forEach(dep => {
        if (graph[dep]) { // Only follow dependencies that exist
          dfs(dep, [...path])
        }
      })

      recursionStack.delete(node)
    }

    Object.keys(graph).forEach(node => {
      if (!visited.has(node)) {
        dfs(node, [])
      }
    })

    return cycles
  }

  /**
   * Gets the topological order for loading components
   */
  getLoadOrder(components: Map<string, ReportComponent>): {
    order: string[]
    errors: string[]
  } {
    const { graph, cycles } = this.getDependencyGraph(components)
    const errors: string[] = []

    if (cycles.length > 0) {
      errors.push(`Circular dependencies detected: ${cycles.map(cycle => cycle.join(' → ')).join(', ')}`)
    }

    // Kahn's algorithm for topological sorting
    const inDegree = new Map<string, number>()
    const queue: string[] = []
    const result: string[] = []

    // Initialize in-degrees
    Object.keys(graph).forEach(node => {
      inDegree.set(node, 0)
    })

    // Calculate in-degrees
    Object.entries(graph).forEach(([node, deps]) => {
      deps.forEach(dep => {
        if (inDegree.has(dep)) {
          inDegree.set(dep, (inDegree.get(dep) || 0) + 1)
        }
      })
    })

    // Find nodes with no incoming edges
    inDegree.forEach((degree, node) => {
      if (degree === 0) {
        queue.push(node)
      }
    })

    // Process the queue
    while (queue.length > 0) {
      const current = queue.shift()!
      result.push(current)

      const dependencies = graph[current] || []
      dependencies.forEach(dep => {
        if (inDegree.has(dep)) {
          const newDegree = (inDegree.get(dep) || 0) - 1
          inDegree.set(dep, newDegree)
          
          if (newDegree === 0) {
            queue.push(dep)
          }
        }
      })
    }

    return { order: result, errors }
  }

  /**
   * Validates that all dependencies exist
   */
  validateDependencies(components: Map<string, ReportComponent>): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    components.forEach((component, componentId) => {
      if (component.dependencies) {
        component.dependencies.forEach(depId => {
          if (!components.has(depId)) {
            errors.push(`Component ${componentId} depends on non-existent component ${depId}`)
          }
        })
      }
    })

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Gets the dependency chain for a component (all transitive dependencies)
   */
  getDependencyChain(
    componentId: string,
    components: Map<string, ReportComponent>
  ): string[] {
    const visited = new Set<string>()
    const chain: string[] = []

    const traverse = (id: string) => {
      if (visited.has(id)) return
      visited.add(id)

      const component = components.get(id)
      if (component?.dependencies) {
        component.dependencies.forEach(depId => {
          if (components.has(depId)) {
            traverse(depId)
            if (!chain.includes(depId)) {
              chain.push(depId)
            }
          }
        })
      }
    }

    traverse(componentId)
    return chain
  }

  /**
   * Clears all dependency waiters (useful for cleanup)
   */
  clearAllWaiters(): void {
    logger.debug('DependencyService', 'clearAllWaiters', 
      `Clearing ${this.dependencyWaiters.size} waiters`)
    this.dependencyWaiters.clear()
  }

  /**
   * Gets current waiter statistics for debugging
   */
  getWaiterStats(): {
    totalWaiters: number
    componentWaiters: Record<string, number>
  } {
    const componentWaiters: Record<string, number> = {}
    let totalWaiters = 0

    this.dependencyWaiters.forEach((waiters, componentId) => {
      componentWaiters[componentId] = waiters.length
      totalWaiters += waiters.length
    })

    return { totalWaiters, componentWaiters }
  }
}

// Export singleton instance
export const dependencyService = new DependencyService()