import { createClient } from '@/app/supabase/client'
import { logger } from '../utils/logger'
import type { ReportComponent } from '../../types'

/**
 * Service for managing report component content loading and hierarchy operations.
 * Component state is stored in document attributes, not in separate database tables.
 */
export class ComponentService {
  private supabase = createClient()

  /**
   * Loads a component's content from the API
   */
  async loadComponentContent(
    componentId: string,
    endpoint: string,
    params: Record<string, any> = {}
  ): Promise<{ content: string; error?: string }> {
    try {
      logger.info('ComponentService', 'loadComponentContent', `Loading content for ${componentId}`)
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params),
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      
      logger.info('ComponentService', 'loadComponentContent', `Successfully loaded content for ${componentId}`)
      return { content: data.content || '' }
    } catch (error) {
      logger.error('ComponentService', 'loadComponentContent', `Failed to load content for ${componentId}`, error as Error)
      return { 
        content: '', 
        error: error instanceof Error ? error.message : 'Failed to load content' 
      }
    }
  }


  /**
   * Refreshes a component's content
   */
  async refreshComponent(
    component: ReportComponent,
    params: Record<string, any> = {}
  ): Promise<{ content: string; error?: string }> {
    if (!component.endpoint) {
      return { content: '', error: 'No endpoint configured for component' }
    }

    logger.info('ComponentService', 'refreshComponent', `Refreshing component ${component.id}`)
    
    // Load fresh content
    const result = await this.loadComponentContent(
      component.id,
      component.endpoint,
      {
        ...params,
        prompt: component.prompt,
        componentId: component.id,
        componentType: component.type,
      }
    )

    return result
  }

  /**
   * Validates component hierarchy
   */
  validateHierarchy(components: Map<string, ReportComponent>): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // Check for orphaned components
    components.forEach((component, id) => {
      if (component.parentId && !components.has(component.parentId)) {
        errors.push(`Component ${id} references non-existent parent ${component.parentId}`)
      }

      // Validate children references
      if (component.children) {
        component.children.forEach(childId => {
          if (!components.has(childId)) {
            errors.push(`Component ${id} references non-existent child ${childId}`)
          }
        })
      }

      // Validate dependencies
      if (component.dependencies) {
        component.dependencies.forEach(depId => {
          if (!components.has(depId)) {
            errors.push(`Component ${id} references non-existent dependency ${depId}`)
          }
        })
      }
    })

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Gets all descendants of a component
   */
  getAllDescendants(
    componentId: string,
    components: Map<string, ReportComponent>
  ): ReportComponent[] {
    const descendants: ReportComponent[] = []
    const component = components.get(componentId)

    if (!component?.children) return descendants

    component.children.forEach(childId => {
      const child = components.get(childId)
      if (child) {
        descendants.push(child)
        // Recursively get descendants
        descendants.push(...this.getAllDescendants(childId, components))
      }
    })

    return descendants
  }

  /**
   * Gets all ancestors of a component
   */
  getAllAncestors(
    componentId: string,
    components: Map<string, ReportComponent>
  ): ReportComponent[] {
    const ancestors: ReportComponent[] = []
    let current = components.get(componentId)

    while (current?.parentId) {
      const parent = components.get(current.parentId)
      if (parent) {
        ancestors.push(parent)
        current = parent
      } else {
        break
      }
    }

    return ancestors
  }
}

// Export singleton instance
export const componentService = new ComponentService()
