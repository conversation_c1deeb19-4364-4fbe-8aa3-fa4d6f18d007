import { createClient } from '@/app/supabase/client'
import { logger } from '../utils/logger'

export interface DocumentVersion {
  id: string
  document_id: string | null
  version_number: number
  title: string | null
  content: string | null
  data: any
  user_id?: string | null
  created_at: string | null
  change_summary?: string | null
  is_auto_save: boolean | null
}

export class VersionService {
  private supabase = createClient()

  async createVersion(
    documentId: string, 
    content: string, 
    data: any, 
    options: {
      changeSummary?: string
      isAutoSave?: boolean
      createdBy?: string
    } = {}
  ): Promise<DocumentVersion> {
    logger.info('VersionService', 'createVersion', `Creating version for document ${documentId}`)
    
    try {
      // Get the next version number
      const nextVersionNumber = await this.getNextVersionNumber(documentId)
      
      const versionData = {
        document_id: documentId,
        version_number: nextVersionNumber,
        title: options.isAutoSave 
          ? `Auto-save ${nextVersionNumber}` 
          : `Version ${nextVersionNumber}`,
        content,
        data,
        user_id: options.createdBy,
        change_summary: options.changeSummary,
        is_auto_save: options.isAutoSave || false,
        created_at: new Date().toISOString()
      }

      const { data: version, error } = await this.supabase
        .from('doc_versions')
        .insert(versionData)
        .select()
        .single()

      if (error) {
        logger.error('VersionService', 'createVersion', `Failed to create version for document ${documentId}`, error)
        throw new Error(`Failed to create version: ${error.message}`)
      }

      logger.info('VersionService', 'createVersion', `Successfully created version ${version.version_number} for document ${documentId}`)
      return version
    } catch (error) {
      logger.error('VersionService', 'createVersion', `Error creating version for document ${documentId}`, error as Error)
      throw error
    }
  }

  async loadVersion(documentId: string, versionNumber: number): Promise<DocumentVersion | null> {
    logger.info('VersionService', 'loadVersion', `Loading version ${versionNumber} for document ${documentId}`)
    
    try {
      const { data: version, error } = await this.supabase
        .from('doc_versions')
        .select('*')
        .eq('document_id', documentId)
        .eq('version_number', versionNumber)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          logger.warn('VersionService', 'loadVersion', `Version ${versionNumber} not found for document ${documentId}`)
          return null
        }
        logger.error('VersionService', 'loadVersion', `Failed to load version ${versionNumber}`, error)
        throw new Error(`Failed to load version: ${error.message}`)
      }

      logger.info('VersionService', 'loadVersion', `Successfully loaded version ${versionNumber}`)
      return version
    } catch (error) {
      logger.error('VersionService', 'loadVersion', `Error loading version ${versionNumber}`, error as Error)
      throw error
    }
  }

  async listVersions(documentId: string, limit: number = 50): Promise<DocumentVersion[]> {
    logger.info('VersionService', 'listVersions', `Loading versions for document ${documentId}`)
    
    try {
      const { data: versions, error } = await this.supabase
        .from('doc_versions')
        .select('*')
        .eq('document_id', documentId)
        .order('version_number', { ascending: false })
        .limit(limit)

      if (error) {
        logger.error('VersionService', 'listVersions', `Failed to load versions for document ${documentId}`, error)
        throw new Error(`Failed to load versions: ${error.message}`)
      }

      logger.info('VersionService', 'listVersions', `Successfully loaded ${versions.length} versions`)
      return versions || []
    } catch (error) {
      logger.error('VersionService', 'listVersions', `Error loading versions for document ${documentId}`, error as Error)
      throw error
    }
  }

  async deleteVersion(documentId: string, versionNumber: number): Promise<void> {
    logger.info('VersionService', 'deleteVersion', `Deleting version ${versionNumber} for document ${documentId}`)
    
    try {
      const { error } = await this.supabase
        .from('doc_versions')
        .delete()
        .eq('document_id', documentId)
        .eq('version_number', versionNumber)

      if (error) {
        logger.error('VersionService', 'deleteVersion', `Failed to delete version ${versionNumber}`, error)
        throw new Error(`Failed to delete version: ${error.message}`)
      }

      logger.info('VersionService', 'deleteVersion', `Successfully deleted version ${versionNumber}`)
    } catch (error) {
      logger.error('VersionService', 'deleteVersion', `Error deleting version ${versionNumber}`, error as Error)
      throw error
    }
  }

  async cleanupAutoSaveVersions(documentId: string, keepCount: number = 10): Promise<void> {
    logger.info('VersionService', 'cleanupAutoSaveVersions', `Cleaning up auto-save versions for document ${documentId}`)
    
    try {
      // Get auto-save versions older than the keep count
      const { data: versionsToDelete, error: selectError } = await this.supabase
        .from('doc_versions')
        .select('version_number')
        .eq('document_id', documentId)
        .eq('is_auto_save', true)
        .order('version_number', { ascending: false })
        .range(keepCount, 1000) // Get versions beyond the keep count

      if (selectError) {
        logger.error('VersionService', 'cleanupAutoSaveVersions', 'Failed to select old versions', selectError)
        throw new Error(`Failed to select old versions: ${selectError.message}`)
      }

      if (versionsToDelete && versionsToDelete.length > 0) {
        const versionNumbers = versionsToDelete.map(v => v.version_number)
        
        const { error: deleteError } = await this.supabase
          .from('doc_versions')
          .delete()
          .eq('document_id', documentId)
          .eq('is_auto_save', true)
          .in('version_number', versionNumbers)

        if (deleteError) {
          logger.error('VersionService', 'cleanupAutoSaveVersions', 'Failed to delete old versions', deleteError)
          throw new Error(`Failed to delete old versions: ${deleteError.message}`)
        }

        logger.info('VersionService', 'cleanupAutoSaveVersions', `Deleted ${versionsToDelete.length} old auto-save versions`)
      }
    } catch (error) {
      logger.error('VersionService', 'cleanupAutoSaveVersions', `Error cleaning up auto-save versions`, error as Error)
      throw error
    }
  }

  private async getNextVersionNumber(documentId: string): Promise<number> {
    const { data: versions, error } = await this.supabase
      .from('doc_versions')
      .select('version_number')
      .eq('document_id', documentId)
      .order('version_number', { ascending: false })
      .limit(1)

    if (error) {
      logger.error('VersionService', 'getNextVersionNumber', 'Failed to get last version number', error)
      throw new Error(`Failed to get version number: ${error.message}`)
    }

    return (versions?.[0]?.version_number || 0) + 1
  }
}

export const versionService = new VersionService()
