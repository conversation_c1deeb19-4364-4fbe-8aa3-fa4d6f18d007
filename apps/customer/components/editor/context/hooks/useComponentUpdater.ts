import React, { useCallback, useRef } from 'react'
import { useComponentStatus } from './useComponentStatus'
import { useComponentSync } from './useComponentSync'
import { useComponentRelations } from './useComponentRelations'
import { logger } from '../../services/utils/logger'
import type { ReportComponent } from '../../types'
import { componentService } from '../../services/component/componentService'

interface ComponentUpdaterProps {
  documentId: string
  components: Map<string, ReportComponent>
  onSave?: (content: string, data?: any) => Promise<void> | void
  onComponentChange?: (componentId: string, component: ReportComponent) => void
}

/**
 * Simplified component updater that composes focused hooks.
 * Replaces the complex 400+ line useComponentUpdater with a clean composition.
 */
export const useComponentUpdater = ({ 
  documentId, 
  components,
  onSave,
  onComponentChange
}: ComponentUpdaterProps) => {
  const updatingComponentsRef = useRef(new Set<string>())

  // Initialize focused hooks
  const componentStatus = useComponentStatus({
    onStatusChange: (componentId, newStatus, context) => {
      logger.debug('ComponentUpdater', 'onStatusChange', 
        `Component ${componentId} status changed to ${newStatus}`)
      
      // Notify relations hook about status change
      const component = components.get(componentId)
      if (component) {
        relations.handleStatusChange(componentId, component.status as any, newStatus as any)
      }
    }
  })

  const componentSync = useComponentSync({ documentId, onSave })

  const relations = useComponentRelations({ 
    components,
    onComponentUpdate: (componentId, updates) => {
      // Prevent recursive updates
      if (updatingComponentsRef.current.has(componentId)) {
        return
      }

      const component = components.get(componentId)
      if (component) {
        const updatedComponent = { ...component, ...updates }
        onComponentChange?.(componentId, updatedComponent)
      }
    }
  })

  const updateComponent = useCallback(
    (componentId: string, updates: Partial<ReportComponent>) => {
      // Prevent recursive updates
      if (updatingComponentsRef.current.has(componentId)) {
        logger.debug('ComponentUpdater', 'updateComponent', 
          `Preventing recursive update for component ${componentId}`)
        return
      }

      const existing = components.get(componentId)
      if (!existing) {
        logger.warn('ComponentUpdater', 'updateComponent', 
          `Component ${componentId} not found`)
        return
      }

      // Check if there are actual changes
      const hasChanges = Object.keys(updates).some(key => {
        const updateKey = key as keyof ReportComponent
        return existing[updateKey] !== updates[updateKey]
      })

      if (!hasChanges) {
        logger.debug('ComponentUpdater', 'updateComponent', 
          `No changes for component ${componentId}`)
        return
      }

      updatingComponentsRef.current.add(componentId)

      try {
        logger.debug('ComponentUpdater', 'updateComponent', 
          `Updating component ${componentId}`)

        const updatedComponent = { ...existing, ...updates }

        // Handle status changes through the status hook
        if (updates.status && existing.status !== updates.status) {
          componentStatus.updateStatus(componentId, updates.status, {
            type: existing.type,
            data: updates.data,
            error: updates.error
          })
        }

        // Component state is automatically persisted in document attributes
        // No separate component saving needed since state is stored in the document

        // Notify parent about the change
        onComponentChange?.(componentId, updatedComponent)

        logger.info('ComponentUpdater', 'updateComponent', 
          `Successfully updated component ${componentId}`)

      } catch (error) {
        logger.error('ComponentUpdater', 'updateComponent', 
          `Error updating component ${componentId}`, error as Error)
      } finally {
        updatingComponentsRef.current.delete(componentId)
      }
    },
    [components, componentStatus, componentSync, onComponentChange]
  )

  const refreshComponent = useCallback(
    async (componentId: string): Promise<void> => {
      const component = components.get(componentId)
      if (!component) {
        throw new Error(`Component ${componentId} not found`)
      }

      logger.info('ComponentUpdater', 'refreshComponent', 
        `Refreshing component ${componentId}`)

      try {
        // Reset to loading state
        updateComponent(componentId, { status: 'loading', error: undefined })

        // Refresh component content if it has an endpoint
        if (component.endpoint) {
          const { content, error } = await componentService.refreshComponent(component)
          
          if (error) {
            updateComponent(componentId, { status: 'error', error })
          } else {
            updateComponent(componentId, { 
              status: 'loaded', 
              content,
              lastRefreshed: new Date() 
            })
          }
        } else {
          // Just mark as loaded if no endpoint
          updateComponent(componentId, { status: 'loaded' })
        }

      } catch (error) {
        logger.error('ComponentUpdater', 'refreshComponent', 
          `Failed to refresh component ${componentId}`, error as Error)
        
        updateComponent(componentId, { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Refresh failed' 
        })
      }
    },
    [components, updateComponent]
  )

  const retryComponent = useCallback(
    (componentId: string) => {
      logger.info('ComponentUpdater', 'retryComponent', 
        `Retrying component ${componentId}`)
      
      componentStatus.retryComponent(componentId)
    },
    [componentStatus]
  )

  const batchUpdateComponents = useCallback(
    async (updates: Array<{ id: string; updates: Partial<ReportComponent> }>) => {
      logger.info('ComponentUpdater', 'batchUpdateComponents', 
        `Batch updating ${updates.length} components`)

      updates.forEach(({ id, updates: componentUpdates }) => {
        updateComponent(id, componentUpdates)
      })
    },
    [updateComponent]
  )

  // Get component statistics
  const getStats = useCallback(() => {
    return {
      statusStats: componentStatus.getStatusStats(),
      relationshipValidation: relations.validateRelationships(),
    }
  }, [componentStatus, relations])

  return {
    updateComponent,
    refreshComponent,
    retryComponent,
    batchUpdateComponents,
    getStats,
    
    // Expose sub-hooks for advanced usage
    status: componentStatus,
    sync: componentSync,
    relations,
  }
}