import React, { useCallback, useRef } from 'react'
import { dependencyService } from '../../services/component/dependencyService'
import { schedulerService } from '../../services/utils/schedulerService'
import { logger } from '../../services/utils/logger'
import type { ReportComponent } from '../../types'
import { componentService } from '../../services/component/componentService'
import { ComponentStatus, COMPONENT_STATUS } from '../../services/utils/constants'

interface ComponentRelationsProps {
  components: Map<string, ReportComponent>
  onComponentUpdate?: (componentId: string, updates: Partial<ReportComponent>) => void
}

/**
 * Hook for managing component relationships (parent-child, dependencies).
 * Replaces the complex relationship logic from useComponentUpdater.
 */
export const useComponentRelations = ({ 
  components, 
  onComponentUpdate 
}: ComponentRelationsProps) => {
  const processingUpdatesRef = useRef(new Set<string>())

  const notifyParentGroups = useCallback(
    (componentId: string, newStatus: string, updatedComponent: ReportComponent) => {
      const component = components.get(componentId)
      if (!component?.parentId) return

      const parent = components.get(component.parentId)
      if (!parent || parent.type !== 'report-group') return

      logger.debug('ComponentRelations', 'notifyParentGroups', 
        `Notifying parent group ${parent.id} of child ${componentId} status change to ${newStatus}`)

      // Get all current children states
      const allChildrenStates = Array.from(components.values())
        .filter(comp => comp.parentId === parent.id)
        .map(comp => comp.id === componentId ? updatedComponent : comp)

      // Calculate new parent status based on children
      const hasError = allChildrenStates.some(child => child.status === COMPONENT_STATUS.ERROR)
      const hasLoading = allChildrenStates.some(child => 
        child.status === COMPONENT_STATUS.LOADING || child.status === COMPONENT_STATUS.IDLE
      )
      const readyStates = [COMPONENT_STATUS.LOADED, COMPONENT_STATUS.PRESERVED, COMPONENT_STATUS.LOCKED] as const
      const allLoaded = allChildrenStates.every(child => 
        readyStates.includes(child.status as typeof readyStates[number])
      )

      let newParentStatus: ComponentStatus
      if (hasError) {
        newParentStatus = COMPONENT_STATUS.ERROR
      } else if (hasLoading) {
        newParentStatus = COMPONENT_STATUS.LOADING
      } else if (allLoaded) {
        newParentStatus = COMPONENT_STATUS.LOADED
      } else {
        newParentStatus = COMPONENT_STATUS.LOADING
      }

      if (parent.status !== newParentStatus && onComponentUpdate) {
        logger.info('ComponentRelations', 'notifyParentGroups', 
          `Updating parent group ${parent.id} from ${parent.status} to ${newParentStatus}`)
        
        onComponentUpdate(parent.id, { status: newParentStatus })
      }
    },
    [components, onComponentUpdate]
  )

  const notifyDependentSummaries = useCallback(
    (componentId: string, newStatus: string, updatedComponent: ReportComponent) => {
      const dependentSummaries = dependencyService.getDependents(componentId, components)
        .filter(comp => comp.type === 'report-summary')

      if (dependentSummaries.length === 0) return

      logger.debug('ComponentRelations', 'notifyDependentSummaries', 
        `Notifying ${dependentSummaries.length} dependent summaries of ${componentId} status change`)

      dependentSummaries.forEach(summary => {
        if (!summary.dependencies) return

        // Get all current dependency states
        const allDependencyStates = summary.dependencies
          .map(depId => {
            const dep = components.get(depId)
            return depId === componentId ? updatedComponent : dep
          })
          .filter(Boolean) as ReportComponent[]

        // Check if dependencies are ready
        const dependenciesReady = dependencyService.areDependenciesReady(summary.id, 
          new Map([...components, [componentId, updatedComponent]]))

        if (dependenciesReady && summary.status === COMPONENT_STATUS.IDLE && onComponentUpdate) {
          logger.info('ComponentRelations', 'notifyDependentSummaries', 
            `Dependencies ready for summary ${summary.id}, triggering load`)
          
          onComponentUpdate(summary.id, { status: COMPONENT_STATUS.LOADING })
        }
      })
    },
    [components, onComponentUpdate]
  )

  const handleStatusChange = useCallback(
    (componentId: string, oldStatus: ComponentStatus, newStatus: ComponentStatus) => {
      if (processingUpdatesRef.current.has(componentId)) {
        logger.debug('ComponentRelations', 'handleStatusChange', 
          `Already processing update for ${componentId}, skipping`)
        return
      }

      processingUpdatesRef.current.add(componentId)

      try {
        const component = components.get(componentId)
        if (!component) return

        const updatedComponent: ReportComponent = { ...component, status: newStatus }

        logger.debug('ComponentRelations', 'handleStatusChange', 
          `Processing status change for ${componentId}: ${oldStatus} → ${newStatus}`)

        // Use scheduler to debounce and sequence updates
        schedulerService.timeout(() => {
          try {
            // Notify parent groups
            notifyParentGroups(componentId, newStatus, updatedComponent)
            
            // Notify dependent summaries
            notifyDependentSummaries(componentId, newStatus, updatedComponent)
            
            // Notify dependency system if component reached ready state
            if (['loaded', 'preserved', 'locked'].includes(newStatus)) {
              dependencyService.notifyDependencyResolved(componentId, components)
            }

            logger.debug('ComponentRelations', 'handleStatusChange', 
              `Completed status change processing for ${componentId}`)
          } finally {
            processingUpdatesRef.current.delete(componentId)
          }
        }, 50, `ComponentRelations-${componentId}`)

      } catch (error) {
        logger.error('ComponentRelations', 'handleStatusChange', 
          `Error processing status change for ${componentId}`, error as Error)
        processingUpdatesRef.current.delete(componentId)
      }
    },
    [components, notifyParentGroups, notifyDependentSummaries]
  )

  const checkDependencies = useCallback(
    (componentId: string): boolean => {
      return dependencyService.areDependenciesReady(componentId, components)
    },
    [components]
  )

  const waitForDependencies = useCallback(
    (componentId: string): Promise<void> => {
      return dependencyService.waitForDependencies(componentId, components)
    },
    [components]
  )

  const getDependencyChain = useCallback(
    (componentId: string): string[] => {
      return dependencyService.getDependencyChain(componentId, components)
    },
    [components]
  )

  const validateRelationships = useCallback(
    (): { valid: boolean; errors: string[] } => {
      // Validate component hierarchy
      const hierarchyResult = componentService.validateHierarchy(components)
      
      // Validate dependencies
      const dependencyResult = dependencyService.validateDependencies(components)
      
      return {
        valid: hierarchyResult.valid && dependencyResult.valid,
        errors: [...hierarchyResult.errors, ...dependencyResult.errors]
      }
    },
    [components]
  )

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      schedulerService.clearContext('ComponentRelations')
      dependencyService.clearAllWaiters()
    }
  }, [])

  return {
    handleStatusChange,
    checkDependencies,
    waitForDependencies,
    getDependencyChain,
    validateRelationships,
  }
}