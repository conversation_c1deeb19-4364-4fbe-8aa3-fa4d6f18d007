import { useCallback } from 'react'
import { Editor } from '@tiptap/react'
import { createClient } from '@/app/supabase/client'
import { DocumentAction } from '../DocumentContext'

interface DocumentVersioningProps {
  editor: Editor | null
  documentId: string
  dispatch: React.Dispatch<DocumentAction>
}

export const useDocumentVersioning = ({ editor, documentId, dispatch }: DocumentVersioningProps) => {
  // Create a manual document version
  const createDocumentVersion = useCallback(
    async (changeSummary: string) => {
      if (!editor || !documentId) {
        console.warn('DocumentVersioning.createDocumentVersion: Editor or documentId not available')
        return
      }

      try {
        const supabase = createClient()
        const content = editor.getHTML()
        const data = editor.getJSON()

        // Get the current version number
        const { data: versions, error: versionError } = await supabase
          .from('doc_versions')
          .select('version_number')
          .eq('document_id', documentId)
          .order('version_number', { ascending: false })
          .limit(1)

        if (versionError) {
          console.error('DocumentVersioning.createDocumentVersion: Error fetching versions:', versionError)
          return
        }

        const nextVersionNumber = (versions?.[0]?.version_number || 0) + 1

        const { error: createVersionError } = await supabase
          .from('doc_versions')
          .insert({
            document_id: documentId,
            version_number: nextVersionNumber,
            title: `Version ${nextVersionNumber}`,
            content,
            data,
            user_id: editor.storage?.user?.id,
            change_summary: changeSummary,
            is_auto_save: false,
          })

        if (createVersionError) {
          console.error('DocumentVersioning.createDocumentVersion: Error creating version:', createVersionError)
          return
        }

        console.log(`DocumentVersioning.createDocumentVersion: Created version ${nextVersionNumber} with summary: ${changeSummary}`)
      } catch (error) {
        console.error('DocumentVersioning.createDocumentVersion: Unexpected error:', error)
      }
    },
    [editor, documentId]
  )

  // Trigger an auto-save version
  const triggerAutoSaveVersion = useCallback(async () => {
    if (!editor || !documentId) {
      console.warn('DocumentVersioning.triggerAutoSaveVersion: Editor or documentId not available')
      return
    }

    try {
      dispatch({ type: 'SAVE_STARTED' })

      const supabase = createClient()
      const content = editor.getHTML()
      const data = editor.getJSON()
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        console.warn('DocumentVersioning.triggerAutoSaveVersion: No user available')
        dispatch({ type: 'SAVE_FAILED', error: 'No user available' })
        return
      }

      // Update the main document
      const { error: updateError } = await supabase
        .from('doc_documents')
        .update({
          content,
          data,
          updated_at: new Date().toISOString(),
          updated_by: user.id,
        })
        .eq('id', documentId)

      if (updateError) throw updateError

      // Get the current version number
      const { data: versions, error: versionError } = await supabase
        .from('doc_versions')
        .select('version_number')
        .eq('document_id', documentId)
        .order('version_number', { ascending: false })
        .limit(1)

      if (versionError) throw versionError

      const nextVersionNumber = (versions?.[0]?.version_number || 0) + 1

      // Create an auto-save version
      const { error: createVersionError } = await supabase
        .from('doc_versions')
        .insert({
          document_id: documentId,
          version_number: nextVersionNumber,
          title: `Version ${nextVersionNumber}`,
          content,
          data,
          user_id: user.id,
          change_summary: 'All report components loaded - Automatic save',
          is_auto_save: true,
        })

      if (createVersionError) throw createVersionError

      console.log(`DocumentVersioning.triggerAutoSaveVersion: Created auto-save version ${nextVersionNumber}`)
      
      dispatch({ type: 'SAVE_COMPLETED', timestamp: new Date() })

      // Clean up old auto-save versions (keep only 5 most recent)
      const { data: autoSaveVersions, error: fetchError } = await supabase
        .from('doc_versions')
        .select('id, version_number')
        .eq('document_id', documentId)
        .eq('is_auto_save', true)
        .order('version_number', { ascending: false })

      if (!fetchError && autoSaveVersions && autoSaveVersions.length > 5) {
        const versionsToDelete = autoSaveVersions.slice(5)
        const idsToDelete = versionsToDelete.map(v => v.id)

        const { error: deleteError } = await supabase
          .from('doc_versions')
          .delete()
          .in('id', idsToDelete)

        if (deleteError) {
          console.warn('Failed to clean up old auto-save versions:', deleteError)
        }
      }
    } catch (error) {
      console.error('DocumentVersioning.triggerAutoSaveVersion: Error:', error)
      dispatch({ type: 'SAVE_FAILED', error: error instanceof Error ? error.message : 'Failed to save' })
    }
  }, [editor, documentId, dispatch])

  // Trigger immediate save
  const triggerImmediateSave = useCallback(
    async (onSave?: (content: string, data?: any) => Promise<void> | void) => {
      if (!editor) {
        console.warn('DocumentVersioning.triggerImmediateSave: Editor not available')
        return
      }

      console.log('DocumentVersioning.triggerImmediateSave: Triggering immediate save')

      // Trigger immediate content change to force auto-save
      dispatch({
        type: 'CONTENT_CHANGED',
        content: editor.getHTML(),
        data: editor.getJSON(),
        source: 'system',
      })

      // Call onSave callback if provided
      if (onSave) {
        try {
          await onSave(editor.getHTML(), editor.getJSON())
        } catch (error) {
          console.error('DocumentVersioning.triggerImmediateSave: Error in onSave callback:', error)
        }
      }
    },
    [editor, dispatch]
  )

  return {
    createDocumentVersion,
    triggerAutoSaveVersion,
    triggerImmediateSave
  }
}
