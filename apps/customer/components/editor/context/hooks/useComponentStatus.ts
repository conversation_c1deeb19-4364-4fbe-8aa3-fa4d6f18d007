import React, { useCallback, useRef } from 'react'
import { componentStateMachineManager } from '../../services/state/componentStateMachine'
import { schedulerService } from '../../services/utils/schedulerService'
import { logger } from '../../services/utils/logger'
import type { ReportComponent } from '../../types'
import { componentService } from '../../services/component/componentService'

interface ComponentStatusProps {
  onStatusChange?: (componentId: string, newStatus: string, context: any) => void
}

/**
 * Simplified hook for managing component status using XState machines.
 * Replaces the complex status management in useComponentUpdater.
 */
export const useComponentStatus = ({ onStatusChange }: ComponentStatusProps = {}) => {
  const machineManagerRef = useRef(componentStateMachineManager)

  // Initialize machine manager with status change callback
  React.useEffect(() => {
    if (onStatusChange) {
      machineManagerRef.current = new (componentStateMachineManager.constructor as any)({
        onStateChange: onStatusChange
      })
    }
  }, [onStatusChange])

  const updateStatus = useCallback(
    (componentId: string, newStatus: string, context?: any) => {
      logger.debug('ComponentStatus', 'updateStatus', 
        `Updating component ${componentId} to status ${newStatus}`)

      const machine = machineManagerRef.current.getMachine(componentId)
      if (!machine) {
        // Create new machine if it doesn't exist
        machineManagerRef.current.createMachine(componentId, context)
      }

      // Map status to events
      const statusToEvent: Record<string, any> = {
        'loading': { type: 'REGISTER', componentId, componentType: context?.type || 'unknown' },
        'loaded': { type: 'LOAD_SUCCESS', data: context?.data },
        'error': { type: 'LOAD_FAILURE', error: context?.error || 'Unknown error' },
        'preserved': { type: 'PRESERVE' },
        'locked': { type: 'LOCK' }
      }

      const event = statusToEvent[newStatus]
      if (event) {
        machineManagerRef.current.send(componentId, event)
      }
    },
    []
  )

  const getStatus = useCallback(
    (componentId: string): string => {
      const state = machineManagerRef.current.getState(componentId)
      return state || 'idle'
    },
    []
  )

  const retryComponent = useCallback(
    (componentId: string) => {
      logger.info('ComponentStatus', 'retryComponent', `Retrying component ${componentId}`)
      machineManagerRef.current.send(componentId, { type: 'RETRY' })
    },
    []
  )

  const resetComponent = useCallback(
    (componentId: string) => {
      logger.info('ComponentStatus', 'resetComponent', `Resetting component ${componentId}`)
      machineManagerRef.current.send(componentId, { type: 'RESET' })
    },
    []
  )

  const getStatusStats = useCallback(() => {
    return machineManagerRef.current.getState('stats')
  }, [])

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      machineManagerRef.current.cleanup()
    }
  }, [])

  return {
    updateStatus,
    getStatus,
    retryComponent,
    resetComponent,
    getStatusStats,
  }
}