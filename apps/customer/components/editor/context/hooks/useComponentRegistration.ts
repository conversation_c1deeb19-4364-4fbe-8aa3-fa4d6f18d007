import { useRef, useCallback, useEffect } from 'react'
import { ReportComponent, DocumentAction } from '../DocumentContext'
import { renderLog } from '../utils/renderLogger'

interface ComponentRegistrationProps {
  dispatch: React.Dispatch<DocumentAction>
  stateRef: React.MutableRefObject<{ components: Map<string, ReportComponent> }>
  updateGroupStatus: (groupId: string, immediate?: boolean) => void
}

export const useComponentRegistration = ({ dispatch, stateRef, updateGroupStatus }: ComponentRegistrationProps) => {
  renderLog(`[HOOK] useComponentRegistration called`)
  // Track components that need post-registration processing
  const pendingRegistrationsRef = useRef<Set<string>>(new Set())
  
  // Track orphaned components waiting for their parents
  const orphanedComponentsRef = useRef<Map<string, ReportComponent[]>>(new Map())

  // Store updateGroupStatus in a ref to make it stable
  const updateGroupStatusRef = useRef(updateGroupStatus)
  updateGroupStatusRef.current = updateGroupStatus

  // Register a component
  const registerComponent = useCallback(
    (component: ReportComponent) => {
      console.log(
        `ComponentRegistration.registerComponent: Registering ${component.type} ${component.id} with parent ${
          component.parentId || 'none'
        }`
      )

      // Always register the component first
      dispatch({ type: 'COMPONENT_REGISTERED', component })

      // Mark this component for post-registration processing
      pendingRegistrationsRef.current.add(component.id)
    },
    [dispatch]
  )
  renderLog(`[HOOK] useComponentRegistration: registerComponent callback created with deps: dispatch`)

  // Effect to handle post-registration processing after state updates
  useEffect(() => {
    if (pendingRegistrationsRef.current.size === 0) return

    const timeoutId = setTimeout(() => {
      // Process all pending registrations
      const pendingIds = Array.from(pendingRegistrationsRef.current)
      pendingRegistrationsRef.current.clear()

      console.log(`ComponentRegistration: Processing ${pendingIds.length} pending registrations`)

      pendingIds.forEach((componentId) => {
        const component = stateRef.current.components.get(componentId)
        if (!component) {
          console.warn(`ComponentRegistration: Component ${componentId} not found after registration`)
          return
        }

        console.log(`ComponentRegistration: Post-registration processing for ${component.type} ${component.id}`)

        // Handle parent-child relationships
        if (component.parentId) {
          const parentExists = stateRef.current.components.has(component.parentId)
          if (parentExists) {
            console.log(`ComponentRegistration: Parent ${component.parentId} exists, triggering status update`)
            updateGroupStatusRef.current(component.parentId, false)
          } else {
            // Parent doesn't exist yet, add to orphaned list
            console.log(
              `ComponentRegistration: Parent ${component.parentId} not found, adding ${component.id} to orphaned list`
            )
            if (!orphanedComponentsRef.current.has(component.parentId)) {
              orphanedComponentsRef.current.set(component.parentId, [])
            }
            orphanedComponentsRef.current.get(component.parentId)!.push(component)
          }
        }

        // If this is a group component, check for orphaned children and adopt them
        if (component.type === 'report-group') {
          const orphanedChildren = orphanedComponentsRef.current.get(component.id) || []
          if (orphanedChildren.length > 0) {
            console.log(
              `ComponentRegistration: Group ${component.id} adopting ${orphanedChildren.length} orphaned children:`,
              orphanedChildren.map((c) => c.id)
            )

            // Clear the orphaned list for this parent
            orphanedComponentsRef.current.delete(component.id)
          }

          // Trigger status update for the group to evaluate its current state
          console.log(`ComponentRegistration: Triggering status update for newly registered group ${component.id}`)
          updateGroupStatusRef.current(component.id, false)
        }
      })
    }, 0) // Use 0ms timeout to run after current execution stack

    return () => clearTimeout(timeoutId)
  }, [stateRef, updateGroupStatus]) // Both stateRef and updateGroupStatus are needed

  // Cleanup function
  const cleanup = useCallback(() => {
    orphanedComponentsRef.current.clear()
    pendingRegistrationsRef.current.clear()
  }, [])

  return {
    registerComponent,
    cleanup
  }
}