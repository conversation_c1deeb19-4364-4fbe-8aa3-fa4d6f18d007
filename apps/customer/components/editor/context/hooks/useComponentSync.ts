import React, { useCallback } from 'react'
import { versioningService } from '../../services/versioning/versioningService'
import { schedulerService } from '../../services/utils/schedulerService'
import { logger } from '../../services/utils/logger'

interface ComponentSyncProps {
  documentId: string
  onSave?: (content: string, data?: any) => Promise<void> | void
}

/**
 * Hook for handling document synchronization and versioning.
 * Component state is stored in the document itself as extension attributes.
 */
export const useComponentSync = ({ documentId, onSave }: ComponentSyncProps) => {

  const createDocumentVersion = useCallback(
    async (content: string, data?: any, changeSummary?: string): Promise<void> => {
      if (!content.trim()) {
        logger.debug('ComponentSync', 'createDocumentVersion', 'Skipping version creation for empty content')
        return
      }

      try {
        logger.debug('ComponentSync', 'createDocumentVersion', 
          `Creating document version: ${changeSummary || 'Auto-save'}`)

        const result = await versioningService.createVersion({
          documentId,
          content,
          data,
          changeSummary,
          isAutoSave: !changeSummary, // Manual versions have change summaries
        })

        if (result.error) {
          throw new Error(result.error)
        }

        logger.info('ComponentSync', 'createDocumentVersion', 
          `Created version ${result.version?.versionNumber}`)
      } catch (error) {
        logger.error('ComponentSync', 'createDocumentVersion', 
          'Failed to create document version', error as Error)
        throw error
      }
    },
    [documentId]
  )

  const triggerSave = useCallback(
    async (content: string, data?: any): Promise<void> => {
      try {
        logger.debug('ComponentSync', 'triggerSave', 'Triggering document save')

        if (onSave) {
          await onSave(content, data)
        }

        // Also create a version for backup
        await createDocumentVersion(content, data, 'Manual save')

        logger.info('ComponentSync', 'triggerSave', 'Document save completed')
      } catch (error) {
        logger.error('ComponentSync', 'triggerSave', 'Document save failed', error as Error)
        throw error
      }
    },
    [onSave, createDocumentVersion]
  )

  const debouncedAutoSave = useCallback(
    schedulerService.debounced(
      async (content: string, data?: any) => {
        try {
          await createDocumentVersion(content, data)
          logger.debug('ComponentSync', 'autoSave', 'Auto-save completed')
        } catch (error) {
          logger.error('ComponentSync', 'autoSave', 'Auto-save failed', error as Error)
        }
      },
      2000, // 2 second debounce
      { context: `ComponentSync-${documentId}` }
    ),
    [documentId, createDocumentVersion]
  )

  const triggerAutoSave = useCallback(
    (content: string, data?: any) => {
      logger.debug('ComponentSync', 'triggerAutoSave', 'Triggering debounced auto-save')
      debouncedAutoSave(content, data)
    },
    [debouncedAutoSave]
  )

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      // Clear the debounced function and any pending saves
      debouncedAutoSave.clear()
      schedulerService.clearContext(`ComponentSync-${documentId}`)
    }
  }, [documentId, debouncedAutoSave])

  return {
    createDocumentVersion,
    triggerSave,
    triggerAutoSave,
  }
}