import { useRef, useCallback } from 'react'
import { ReportComponent, DocumentAction } from '../DocumentContext'
import { renderLog } from '../utils/renderLogger'

interface GroupStatusManagerProps {
  dispatch: React.Dispatch<DocumentAction>
  stateRef: React.MutableRefObject<{ components: Map<string, ReportComponent> }>
}

export const useGroupStatusManager = ({ dispatch, stateRef }: GroupStatusManagerProps) => {
  renderLog(`[HOOK] useGroupStatusManager called`)
  const groupStatusUpdateTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map())
  const pendingGroupUpdates = useRef<Set<string>>(new Set())

  // EKO-162: Add cycle detection to prevent infinite loops
  const updateChain = useRef<Set<string>>(new Set())
  const isProcessingBatch = useRef<boolean>(false)

  // Helper function to get all descendants using a specific components map
  const getAllDescendantsFromState = useCallback((groupId: string, components: Map<string, ReportComponent>): ReportComponent[] => {
    const directChildren = Array.from(components.values()).filter((component) => component.parentId === groupId)
    const allDescendants: ReportComponent[] = [...directChildren]

    directChildren.forEach((child) => {
      if (child.type === 'report-group') {
        allDescendants.push(...getAllDescendantsFromState(child.id, components))
      }
    })

    return allDescendants
  }, [])

  // Forward declare for circular dependency
  let scheduleGroupStatusUpdate: (groupId: string, immediate?: boolean) => void

  // EKO-162: Check for circular updates to prevent infinite loops
  const isCircularUpdate = useCallback((groupId: string): boolean => {
    if (updateChain.current.has(groupId)) {
      console.warn(`GroupStatusManager: Circular update detected for ${groupId}, breaking cycle`)
      return true
    }
    return false
  }, [])

  // EKO-162: Calculate group depth for bottom-up processing
  const getGroupDepth = useCallback((groupId: string, components: Map<string, ReportComponent>): number => {
    const group = components.get(groupId)
    if (!group || group.type !== 'report-group') return 0

    const descendants = getAllDescendantsFromState(groupId, components)
    const childGroups = descendants.filter(c => c.type === 'report-group')

    if (childGroups.length === 0) return 0

    return 1 + Math.max(...childGroups.map(child => getGroupDepth(child.id, components)))
  }, [getAllDescendantsFromState])

  // Internal group status management - performs the actual status update
  const updateGroupStatusInternal = useCallback(
    (groupId: string, skipParentUpdate = false) => {
      console.log(`GroupStatusManager.updateGroupStatus: Called for ${groupId}, skipParentUpdate: ${skipParentUpdate}`)

      // EKO-162: Check for circular updates
      if (isCircularUpdate(groupId)) {
        return
      }

      const currentState = stateRef.current
      const group = currentState.components.get(groupId)
      if (!group) {
        console.log(`GroupStatusManager.updateGroupStatus: ${groupId} not found in components map`)
        return
      }
      if (group.type !== 'report-group') {
        console.log(`GroupStatusManager.updateGroupStatus: ${groupId} is not a report-group (type: ${group.type})`)
        return
      }

      // Skip if group is in a final state (locked/preserved)
      if (group.status === 'locked' || group.status === 'preserved') {
        console.log(`GroupStatusManager.updateGroupStatus: ${groupId} is ${group.status}, skipping`)
        return
      }

      // EKO-162: Add to update chain for cycle detection
      updateChain.current.add(groupId)

      // Get direct children only - each group only manages its immediate children
      const directChildren = Array.from(currentState.components.values()).filter(
        (component) => component.parentId === groupId
      )
      
      console.log(
        `GroupStatusManager.updateGroupStatus: ${groupId} has ${directChildren.length} direct children:`,
        directChildren.map((c) => `${c.id}(${c.type}):${c.status}`)
      )

      if (directChildren.length === 0) {
        if (group.status !== 'loaded') {
          console.log(`GroupStatusManager.updateGroupStatus: ${groupId} has no children, setting status to 'loaded'`)
          dispatch({ type: 'COMPONENT_UPDATED', id: groupId, updates: { status: 'loaded' } })
        }
        return
      }

      // Check direct children status only
      const hasError = directChildren.some((child) => child.status === 'error')
      const hasIdleOrLoading = directChildren.some(
        (child) => child.status === 'idle' || child.status === 'loading'
      )
      const allLoaded = directChildren.every(
        (child) =>
          child.status === 'loaded' ||
          child.status === 'preserved' ||
          child.status === 'locked'
      )

      // Debug logging for status determination
      console.log(`GroupStatusManager.updateGroupStatus: ${groupId} status check:
        - Current group status: ${group.status}
        - Direct children: ${directChildren.length}
        - Has error: ${hasError}
        - Has idle/loading: ${hasIdleOrLoading}
        - All loaded: ${allLoaded}
        - Children statuses: ${directChildren.map(d => `${d.id}:${d.status}`).join(', ')}`)

      let newStatus: ReportComponent['status']
      // IMPORTANT: If any children are still loading/idle, the group should be loading
      // This prevents the invalid state of an errored group with loading children
      if (hasIdleOrLoading) {
        newStatus = 'loading'
      } else if (hasError) {
        newStatus = 'error'
      } else if (allLoaded) {
        newStatus = 'loaded'
      } else {
        newStatus = 'loading' // Default to loading for any other state
      }

      console.log(`GroupStatusManager.updateGroupStatus: ${groupId} determined new status: ${newStatus}`)

      // Only update if status actually changed
      if (group.status !== newStatus) {
        console.log(
          `GroupStatusManager.updateGroupStatus: ${groupId} updating status from '${group.status}' to '${newStatus}' based on ${directChildren.length} direct children`
        )
        dispatch({ type: 'COMPONENT_UPDATED', id: groupId, updates: { status: newStatus } })

        // EKO-162: Only update parent if not in batch processing and not skipping parent updates
        if (group.parentId && !skipParentUpdate && !isProcessingBatch.current) {
          console.log(`GroupStatusManager.updateGroupStatus: ${groupId} status changed, scheduling parent ${group.parentId} update`)
          // Use longer delay to ensure state update has time to propagate
          setTimeout(() => scheduleGroupStatusUpdate(group.parentId!, false), 50)
        }
      }

      // EKO-162: Remove from update chain when done
      updateChain.current.delete(groupId)
    },
    [dispatch, getAllDescendantsFromState, stateRef, isCircularUpdate]
  )

  // EKO-162: Batch process group updates with optimistic state updates
  const processBatchUpdate = useCallback((groupIds: string[]) => {
    if (isProcessingBatch.current) {
      console.log('GroupStatusManager: Batch processing already in progress, skipping')
      return
    }

    isProcessingBatch.current = true
    console.log(`GroupStatusManager: Processing batch update for ${groupIds.length} groups`)

    try {
      // Sort groups by depth (deepest first) for bottom-up processing
      const groupsWithDepth = groupIds.map(id => ({
        id,
        depth: getGroupDepth(id, stateRef.current.components)
      }))

      groupsWithDepth.sort((a, b) => b.depth - a.depth) // Deepest first

      console.log('GroupStatusManager: Processing groups in order:',
        groupsWithDepth.map(g => `${g.id}(depth:${g.depth})`))

      // EKO-162: Calculate all status updates with optimistic state
      const optimisticUpdates = new Map<string, string>()
      const currentComponents = stateRef.current.components

      // Helper to get direct children with optimistic updates applied
      const getChildrenWithOptimisticUpdates = (groupId: string): ReportComponent[] => {
        const directChildren = Array.from(currentComponents.values()).filter(
          component => component.parentId === groupId
        )
        return directChildren.map(d => ({
          ...d,
          status: (optimisticUpdates.get(d.id) || d.status) as ReportComponent['status']
        }))
      }

      // Calculate status for each group with optimistic updates
      for (const { id: groupId } of groupsWithDepth) {
        const group = currentComponents.get(groupId)
        if (!group || group.type !== 'report-group') continue
        if (group.status === 'locked' || group.status === 'preserved') continue

        const directChildren = getChildrenWithOptimisticUpdates(groupId)

        console.log(`GroupStatusManager: Calculating optimistic status for ${groupId}`)
        console.log(`  Direct children with optimistic updates: ${directChildren.map(d => `${d.id}:${d.status}`).join(', ')}`)

        let newStatus: string
        if (directChildren.length === 0) {
          newStatus = 'loaded'
        } else {
          const hasError = directChildren.some(d => d.status === 'error')
          const hasIdleOrLoading = directChildren.some(d => d.status === 'idle' || d.status === 'loading')
          const allLoaded = directChildren.every(d =>
            d.status === 'loaded' || d.status === 'preserved' || d.status === 'locked'
          )

          if (hasIdleOrLoading) {
            newStatus = 'loading'
          } else if (hasError) {
            newStatus = 'error'
          } else if (allLoaded) {
            newStatus = 'loaded'
          } else {
            newStatus = 'loading'
          }
        }

        console.log(`GroupStatusManager: ${groupId} optimistic status: ${group.status} → ${newStatus}`)

        if (group.status !== newStatus) {
          optimisticUpdates.set(groupId, newStatus)
        }
      }

      // Apply all updates synchronously
      console.log(`GroupStatusManager: Applying ${optimisticUpdates.size} optimistic updates`)
      for (const [groupId, newStatus] of optimisticUpdates) {
        console.log(`GroupStatusManager: Dispatching update for ${groupId}: ${newStatus}`)
        dispatch({ type: 'COMPONENT_UPDATED', id: groupId, updates: { status: newStatus as ReportComponent['status'] } })
      }

      // Clear update chain after batch processing
      updateChain.current.clear()
    } finally {
      isProcessingBatch.current = false
    }
  }, [getGroupDepth, getAllDescendantsFromState, dispatch, stateRef])

  // Debounced group status update function
  scheduleGroupStatusUpdate = useCallback((groupId: string, immediate = false) => {
    console.log(`GroupStatusManager.scheduleGroupStatusUpdate: Scheduling update for ${groupId}, immediate: ${immediate}`)

    // EKO-162: If we're in batch processing, don't schedule individual updates
    if (isProcessingBatch.current) {
      console.log(`GroupStatusManager.scheduleGroupStatusUpdate: Batch processing active, skipping individual update for ${groupId}`)
      return
    }

    // EKO-162: Handle immediate updates directly
    if (immediate) {
      console.log(`GroupStatusManager.scheduleGroupStatusUpdate: Processing immediate update for ${groupId}`)
      updateGroupStatusInternal(groupId)
      return
    }

    // Prevent scheduling if already pending
    if (pendingGroupUpdates.current.has(groupId)) {
      console.log(`GroupStatusManager.scheduleGroupStatusUpdate: Update already pending for ${groupId}, skipping`)
      return
    }

    // Clear existing timeout for this group
    const existingTimeout = groupStatusUpdateTimeouts.current.get(groupId)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
      groupStatusUpdateTimeouts.current.delete(groupId)
    }

    // Add to pending updates
    pendingGroupUpdates.current.add(groupId)

    // Debounce the update
    const timeout = setTimeout(() => {
      if (pendingGroupUpdates.current.has(groupId)) {
        // EKO-162: Check if we have multiple pending updates and batch them
        const allPending = Array.from(pendingGroupUpdates.current)
        if (allPending.length > 1) {
          console.log(`GroupStatusManager: Multiple pending updates detected, processing as batch: ${allPending}`)
          pendingGroupUpdates.current.clear()
          processBatchUpdate(allPending)
        } else {
          updateGroupStatusInternal(groupId)
          pendingGroupUpdates.current.delete(groupId)
        }
      }
      groupStatusUpdateTimeouts.current.delete(groupId)
    }, 150) // EKO-162: Increased debounce delay to 150ms to allow batching

    groupStatusUpdateTimeouts.current.set(groupId, timeout)
  }, [updateGroupStatusInternal, processBatchUpdate])

  // Public group status update function
  const updateGroupStatus = useCallback(
    (groupId: string, immediate = false) => {
      scheduleGroupStatusUpdate(groupId, immediate)
    },
    [scheduleGroupStatusUpdate]
  )
  renderLog(`[HOOK] useGroupStatusManager: updateGroupStatus callback created, deps: scheduleGroupStatusUpdate`)

  // Force update all group statuses
  const updateAllGroupStatuses = useCallback(() => {
    console.log('GroupStatusManager.updateAllGroupStatuses: Updating all group statuses')
    const groups = Array.from(stateRef.current.components.values()).filter((comp) => comp.type === 'report-group')

    // EKO-162: Use batch processing for updating all groups
    const groupIds = groups.map(g => g.id)
    if (groupIds.length > 0) {
      processBatchUpdate(groupIds)
    }
  }, [processBatchUpdate, stateRef])

  // Refresh all descendants of a group
  const refreshAllDescendants = useCallback(
    (groupId: string) => {
      console.log(`GroupStatusManager.refreshAllDescendants: Refreshing all descendants of group ${groupId}`)

      const group = stateRef.current.components.get(groupId)
      if (!group || group.type !== 'report-group') {
        console.warn(`GroupStatusManager.refreshAllDescendants: ${groupId} is not a valid report group`)
        return
      }

      const descendants = getAllDescendantsFromState(groupId, stateRef.current.components)
      console.log(
        `GroupStatusManager.refreshAllDescendants: Found ${descendants.length} descendants to refresh:`,
        descendants.map((d) => `${d.id}(${d.type})`)
      )

      // Set all descendants to 'idle' status to trigger refresh
      descendants.forEach((descendant) => {
        if (descendant.status !== 'locked' && descendant.status !== 'preserved') {
          console.log(`GroupStatusManager.refreshAllDescendants: Setting ${descendant.id} (${descendant.type}) status to 'idle'`)
          dispatch({
            type: 'COMPONENT_UPDATED',
            id: descendant.id,
            updates: { status: 'idle' }
          })
        }
      })

      // Also set the group itself to 'loading' to indicate refresh is in progress
      if (group.status !== 'locked' && group.status !== 'preserved') {
        console.log(`GroupStatusManager.refreshAllDescendants: Setting group ${groupId} status to 'loading'`)
        dispatch({
          type: 'COMPONENT_UPDATED',
          id: groupId,
          updates: { status: 'loading' }
        })
      }
    },
    [getAllDescendantsFromState, dispatch, stateRef]
  )

  // Cleanup function
  const cleanup = useCallback(() => {
    groupStatusUpdateTimeouts.current.forEach((timeout) => clearTimeout(timeout))
    groupStatusUpdateTimeouts.current.clear()
    pendingGroupUpdates.current.clear()

    // EKO-162: Clear cycle detection state
    updateChain.current.clear()
    isProcessingBatch.current = false
  }, [])

  return {
    updateGroupStatus,
    updateAllGroupStatuses,
    refreshAllDescendants,
    cleanup,
    getAllDescendantsFromState
  }
}