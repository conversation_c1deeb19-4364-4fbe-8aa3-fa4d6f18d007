/**
 * Utility for conditional React render logging based on feature flags
 */
import { DEFAULT_FEATURE_FLAGS, hasFeature, type FeatureFlagConfig } from '@/utils/feature-flags'

// Cache the flag check result to avoid side effects during render
let cachedTraceReactFlag: boolean | null = null;
let lastCheckedTime = 0;
const CHECK_INTERVAL = 5000; // Re-check every 5 seconds

// Function to check if the trace.react feature flag is enabled
const checkTraceReactFlag = (): boolean => {
  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    return false;
  }

  // Use cached result if recent (to avoid side effects during render)
  const now = Date.now();
  if (cachedTraceReactFlag !== null && (now - lastCheckedTime) < CHECK_INTERVAL) {
    return cachedTraceReactFlag;
  }

  // Only update cache outside of React render phase
  try {
    // Check localStorage for development override first
    const devOverride = localStorage.getItem('trace.react');
    if (devOverride === 'true') {
      cachedTraceReactFlag = true;
      lastCheckedTime = now;
      return true;
    }
    if (devOverride === 'false') {
      cachedTraceReactFlag = false;
      lastCheckedTime = now;
      return false;
    }
    
    // Check URL parameters for quick enable/disable
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('trace') === 'react') {
      localStorage.setItem('trace.react', 'true');
      cachedTraceReactFlag = true;
      lastCheckedTime = now;
      return true;
    }

    // Fallback to default feature flags (since we can't access auth context from here)
    const config: FeatureFlagConfig = {
      userFlags: [],
      orgFlags: [],
      defaultFlags: DEFAULT_FEATURE_FLAGS,
    };
    
    const result = hasFeature('trace.react', config);
    cachedTraceReactFlag = result;
    lastCheckedTime = now;
    return result;
  } catch (error) {
    // If any error occurs during flag check, default to false and cache it
    cachedTraceReactFlag = false;
    lastCheckedTime = now;
    return false;
  }
};

// Allow runtime toggling from console
if (typeof window !== 'undefined') {
  (window as any).enableRenderTracing = () => {
    localStorage.setItem('trace.react', 'true');
    cachedTraceReactFlag = true; // Update cache immediately
    lastCheckedTime = Date.now();
    console.log('🔍 React render tracing enabled');
    console.log('💡 To disable: disableRenderTracing() or localStorage.setItem("trace.react", "false")');
  };
  
  (window as any).disableRenderTracing = () => {
    localStorage.setItem('trace.react', 'false');
    cachedTraceReactFlag = false; // Update cache immediately
    lastCheckedTime = Date.now();
    console.log('🔇 React render tracing disabled');
  };
  
  // Add helpful console info on load
  console.log('🛠️  Render tracing controls available:');
  console.log('  • enableRenderTracing() - Enable detailed render logs');
  console.log('  • disableRenderTracing() - Disable render logs');
  console.log('  • Add ?trace=react to URL for quick enable');
}

export const renderLog = (message: string, ...args: any[]) => {
  if (checkTraceReactFlag()) {
    console.log(message, ...args);
  }
};