import React, { createContext, useContext, useReducer, useRef } from 'react'
import { Editor } from '@tiptap/react'
import { CitationType } from '@/components/citation'
import { useGroupStatusManager } from './hooks/useGroupStatusManager'
import { useComponentRegistration } from './hooks/useComponentRegistration'
import { useDocumentVersioning } from './hooks/useDocumentVersioning'
// Legacy useDependencyManager removed - using dependencyService directly
import { useDocumentInitialization } from './hooks/useDocumentInitialization'
import { useComponentUpdater } from './hooks/useComponentUpdater'
import { renderLog } from './utils/renderLogger'
import type { ReportComponent } from '../types'
import { 
  updateComponentsMap, 
  hasComponentChanged, 
  calculateGroupStatus,
  FINAL_STATES 
} from './utils/contextHelpers'

// Re-export for backward compatibility
export type { ReportComponent }

export interface DocumentState {
  // Editor state
  editor: Editor | null
  content: string
  data: any
  isDirty: boolean

  // Auto-save state
  isSaving: boolean
  lastSaved: Date | null
  saveError: string | null

  // Report state
  components: Map<string, ReportComponent>
  citations: CitationType[]

  // UI state
  showSidePanel: boolean
  activePanelTab: 'comments' | 'history' | 'share' | 'ai'

  // Fixed entity from document metadata (set at creation time)
  // Note: entity is always set (defaults to 'default' for blank documents)
  entity: string
  run: string

  // Document initialization state (EKO-118)
  isInitialized: boolean
  hasTriggeredInitialLoad: boolean
}

export type DocumentAction =
  | { type: 'EDITOR_CREATED'; editor: Editor }
  | { type: 'CONTENT_CHANGED'; content: string; data: any; source: 'user' | 'system' | 'restore' }
  | { type: 'SAVE_STARTED' }
  | { type: 'SAVE_COMPLETED'; timestamp: Date }
  | { type: 'SAVE_FAILED'; error: string }
  | { type: 'COMPONENT_REGISTERED'; component: ReportComponent }
  | { type: 'COMPONENT_UPDATED'; id: string; updates: Partial<ReportComponent> }
  | { type: 'COMPONENT_REMOVED'; id: string }
  | { type: 'CITATIONS_REGISTERED'; citations: CitationType[] }
  | { type: 'CITATION_ADDED'; citation: CitationType }
  | { type: 'UI_PANEL_TOGGLE'; show?: boolean; tab?: DocumentState['activePanelTab'] }
  | { type: 'RESET_DIRTY_STATE' }
  | { type: 'DOCUMENT_INITIALIZED' }
  | { type: 'INITIAL_LOAD_TRIGGERED' }
  | { type: 'CHILD_STATE_CHANGED'; parentId: string; childState: ReportComponent; allChildrenStates: ReportComponent[] }
  | { type: 'DEPENDENCY_STATE_CHANGED'; dependentId: string; dependencyState: ReportComponent; allDependencyStates: ReportComponent[] }


function documentReducer(state: DocumentState, action: DocumentAction): DocumentState {
  switch (action.type) {
    case 'EDITOR_CREATED':
      return {
        ...state,
        editor: action.editor,
      }

    case 'CONTENT_CHANGED': {
      // Mark as dirty if content actually changed, regardless of source
      const contentChanged =
        action.content !== state.content || JSON.stringify(action.data) !== JSON.stringify(state.data)
      const shouldMarkDirty = contentChanged && (action.source === 'user' || action.source === 'system')

      return {
        ...state,
        content: action.content,
        data: action.data,
        isDirty: shouldMarkDirty || state.isDirty,
        saveError: null, // Clear any previous save errors
      }
    }

    case 'SAVE_STARTED':
      return {
        ...state,
        isSaving: true,
        saveError: null,
      }

    case 'SAVE_COMPLETED':
      return {
        ...state,
        isSaving: false,
        lastSaved: action.timestamp,
        isDirty: false,
        saveError: null,
      }

    case 'SAVE_FAILED':
      return {
        ...state,
        isSaving: false,
        saveError: action.error,
      }

    case 'COMPONENT_REGISTERED': {
      const newComponents = updateComponentsMap(
        state.components,
        action.component.id,
        (existing) => {
          if (existing && JSON.stringify(existing) === JSON.stringify(action.component)) {
            return existing // No change
          }
          return action.component
        }
      )
      
      return newComponents === state.components ? state : { ...state, components: newComponents }
    }

    case 'COMPONENT_UPDATED': {
      const newComponents = updateComponentsMap(
        state.components,
        action.id,
        (existing) => {
          if (!existing) return null
          if (!hasComponentChanged(existing, action.updates)) {
            return existing
          }
          return { ...existing, ...action.updates }
        }
      )
      
      return newComponents === state.components ? state : { ...state, components: newComponents }
    }

    case 'COMPONENT_REMOVED': {
      const newComponents = new Map(state.components)
      newComponents.delete(action.id)

      return {
        ...state,
        components: newComponents,
      }
    }

    case 'CITATIONS_REGISTERED':
      return {
        ...state,
        citations: [
          ...state.citations,
          ...action.citations.filter(
            (newCit) => !state.citations.some((existing) => existing.doc_page_id === newCit.doc_page_id)
          ),
        ],
      }

    case 'CITATION_ADDED':
      if (state.citations.some((cit) => cit.doc_page_id === action.citation.doc_page_id)) {
        return state // Citation already exists
      }
      return {
        ...state,
        citations: [...state.citations, action.citation],
      }

    case 'UI_PANEL_TOGGLE':
      return {
        ...state,
        showSidePanel: action.show ?? !state.showSidePanel,
        activePanelTab: action.tab ?? state.activePanelTab,
      }


    case 'RESET_DIRTY_STATE':
      return {
        ...state,
        isDirty: false,
      }

    case 'DOCUMENT_INITIALIZED':
      return {
        ...state,
        isInitialized: true,
      }

    case 'INITIAL_LOAD_TRIGGERED':
      return {
        ...state,
        hasTriggeredInitialLoad: true,
      }

    case 'CHILD_STATE_CHANGED': {
      const newComponents = updateComponentsMap(
        state.components,
        action.parentId,
        (parent) => {
          if (!parent) return null
          if (parent.type !== 'report-group') return parent
          
          const newStatus = calculateGroupStatus(action.allChildrenStates)
          if (parent.status === newStatus) return parent
          
          return { ...parent, status: newStatus }
        }
      )
      
      return newComponents === state.components ? state : { ...state, components: newComponents }
    }

    case 'DEPENDENCY_STATE_CHANGED': {
      const newComponents = updateComponentsMap(
        state.components,
        action.dependentId,
        (dependent) => {
          if (!dependent) return null
          if (dependent.type !== 'report-summary') return dependent
          if (FINAL_STATES.includes(dependent.status)) return dependent
          
          const newStatus = calculateGroupStatus(action.allDependencyStates)
          if (dependent.status === newStatus) return dependent
          
          return { 
            ...dependent, 
            status: newStatus,
            error: newStatus === 'error' ? 'One or more dependencies have errors' : undefined
          }
        }
      )
      
      return newComponents === state.components ? state : { ...state, components: newComponents }
    }

    default:
      return state
  }
}

interface DocumentContextValue {
  state: DocumentState
  dispatch: React.Dispatch<DocumentAction>

  // Convenience methods
  registerComponent: (component: ReportComponent) => void
  updateComponent: (id: string, updates: Partial<ReportComponent>) => void
  removeComponent: (id: string) => void
  registerCitations: (citations: CitationType[]) => void
  addCitation: (citation: CitationType) => void
  togglePanel: (show?: boolean, tab?: DocumentState['activePanelTab']) => void

  // Dependency management
  areDependenciesReady: (componentId: string) => boolean
  waitForDependencies: (componentId: string) => Promise<void>

  // Entity is now fixed at document creation - no change listeners needed

  // Group status management
  updateGroupStatus: (groupId: string, immediate?: boolean) => void
  updateAllGroupStatuses: () => void

  // Document versioning and saving
  createDocumentVersion: (changeSummary: string) => Promise<void>
  triggerImmediateSave: () => Promise<void>
  triggerAutoSaveVersion: () => Promise<void>

  // Refresh functionality
  refreshAllDescendants: (groupId: string) => void

  // Document initialization (EKO-118)
  triggerInitialLoad: () => void

  // Check if all components are loaded
  areAllComponentsLoaded: () => boolean
}

const DocumentContext = createContext<DocumentContextValue | null>(null)

export const useDocumentContext = () => {
  const context = useContext(DocumentContext)
  if (!context) {
    throw new Error('useDocumentContext must be used within a DocumentProvider')
  }
  return context
}

interface DocumentProviderProps {
  children: React.ReactNode
  documentId: string
  onSave?: (content: string, data?: any) => Promise<void> | void
  initialEntity: string
  initialRun: string
}

export const DocumentProvider: React.FC<DocumentProviderProps> = ({
  children,
  documentId,
  onSave,
  initialEntity,
  initialRun = 'latest',
}) => {
  // Render tracking for debugging
  if (process.env.NODE_ENV === 'development') {
    const renderCount = useRef(0)
    renderCount.current++
    renderLog(`DocumentProvider render #${renderCount.current}`)
  }
  
  // Validate required props - fail fast if not provided
  // Note: initialEntity is always provided (defaults to 'default' for blank documents)
  if (!initialRun) {
    throw new Error('DocumentProvider: initialRun is required and cannot be empty')
  }
  
  // Create initial state with validated entity/run from props
  const initialState = React.useMemo(() => ({
    editor: null,
    content: '',
    data: null,
    isDirty: false,
    isSaving: false,
    lastSaved: null,
    saveError: null,
    components: new Map(),
    citations: [],
    showSidePanel: false,
    activePanelTab: 'comments' as const,
    entity: initialEntity,
    run: initialRun,
    isInitialized: false,
    hasTriggeredInitialLoad: false,
  }), [initialEntity, initialRun])
  
  const [state, dispatch] = useReducer(documentReducer, initialState)

  // Note: Entity is now fixed at document creation - no change listeners needed

  // Keep a ref to the current state to avoid stale closures in debounced functions
  const stateRef = useRef(state)
  stateRef.current = state

  // Initialize hooks
  const groupStatusManager = useGroupStatusManager({ dispatch, stateRef })
  const documentVersioning = useDocumentVersioning({ editor: state.editor, documentId, dispatch })
  const documentInitialization = useDocumentInitialization({
    editor: state.editor,
    hasTriggeredInitialLoad: state.hasTriggeredInitialLoad,
    isInitialized: state.isInitialized,
    dispatch,
    stateRef
  })
  const componentUpdater = useComponentUpdater({
    documentId,
    components: state.components,
    onSave,
    onComponentChange: (componentId: string, component: ReportComponent) => {
      dispatch({ 
        type: 'COMPONENT_UPDATED', 
        id: componentId, 
        updates: { status: component.status, data: component.data, error: component.error }
      })
    }
  })
  const componentRegistration = useComponentRegistration({
    dispatch,
    stateRef,
    updateGroupStatus: groupStatusManager.updateGroupStatus
  })

  // Entity/run are now set directly in initial state - no effect needed

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      groupStatusManager.cleanup()
      componentRegistration.cleanup()
      documentInitialization.cleanup()
    }
  }, [groupStatusManager, componentRegistration, documentInitialization])

  // Helper function to get all descendants (recursive) - delegated to group status manager
  const getAllDescendants = React.useCallback(
    (parentId: string): ReportComponent[] => {
      return groupStatusManager.getAllDescendantsFromState(parentId, state.components)
    },
    [state.components, groupStatusManager]
  )

  // Convenience methods - delegate to appropriate hooks with stable references
  const registerComponent = React.useCallback(
    (component: ReportComponent) => componentRegistration.registerComponent(component),
    [componentRegistration.registerComponent]
  )

  // Document versioning methods are handled by the useDocumentVersioning hook
  const triggerAutoSaveVersion = React.useCallback(
    () => documentVersioning.triggerAutoSaveVersion(),
    [documentVersioning.triggerAutoSaveVersion]
  )

  // Component updating is handled by the useComponentUpdater hook
  const updateComponent = React.useCallback(
    (id: string, updates: Partial<ReportComponent>) => componentUpdater.updateComponent(id, updates),
    [componentUpdater.updateComponent]
  )

  const removeComponent = React.useCallback(
    (id: string) => {
      dispatch({ type: 'COMPONENT_REMOVED', id })
    },
    [dispatch]
  )

  const registerCitations = React.useCallback(
    (citations: CitationType[]) => {
      dispatch({ type: 'CITATIONS_REGISTERED', citations })
    },
    [dispatch]
  )

  const addCitation = React.useCallback(
    (citation: CitationType) => {
      dispatch({ type: 'CITATION_ADDED', citation })
    },
    [dispatch]
  )

  const togglePanel = React.useCallback(
    (show?: boolean, tab?: DocumentState['activePanelTab']) => {
      dispatch({ type: 'UI_PANEL_TOGGLE', show, tab })
    },
    [dispatch]
  )

  // Entity is now fixed at document creation - no setEntityRun function needed

  // Dependency management now handled by the new dependencyService in focused hooks
  const areDependenciesReady = React.useCallback(
    (componentId: string) => {
      // This is now handled by the useComponentRelations hook
      return true // Simplified for now - dependency logic moved to service layer
    },
    []
  )
  const waitForDependencies = React.useCallback(
    async (componentId: string) => {
      // This is now handled by the useComponentRelations hook
      return Promise.resolve()
    },
    []
  )

  // Entity change listeners removed - entity is fixed at document creation

  // Group status management is handled by the useGroupStatusManager hook
  const updateGroupStatus = React.useCallback(
    (groupId: string, immediate?: boolean) => groupStatusManager.updateGroupStatus(groupId, immediate),
    [groupStatusManager.updateGroupStatus]
  )
  const updateAllGroupStatuses = React.useCallback(
    () => groupStatusManager.updateAllGroupStatuses(),
    [groupStatusManager.updateAllGroupStatuses]
  )

  // Document versioning methods are handled by the useDocumentVersioning hook
  const createDocumentVersion = React.useCallback(
    (changeSummary: string) => documentVersioning.createDocumentVersion(changeSummary),
    [documentVersioning.createDocumentVersion]
  )
  const triggerImmediateSave = React.useCallback(
    async () => {
      return documentVersioning.triggerImmediateSave(onSave)
    },
    [documentVersioning, onSave]
  )

  // Refresh and initialization methods are handled by their respective hooks
  const refreshAllDescendants = React.useCallback(
    (groupId: string) => groupStatusManager.refreshAllDescendants(groupId),
    [groupStatusManager.refreshAllDescendants]
  )
  const triggerInitialLoad = React.useCallback(
    () => documentInitialization.triggerInitialLoad(),
    [documentInitialization.triggerInitialLoad]
  )

  // Check if all components are loaded
  const areAllComponentsLoaded = React.useCallback((): boolean => {
    const components = Array.from(state.components.values())
    return components.length === 0 || components.every(c => FINAL_STATES.includes(c.status))
  }, [state.components])


  // Create a stable context value with only necessary dependencies
  const contextValue: DocumentContextValue = React.useMemo(() => ({
    state,
    dispatch,
    registerComponent,
    updateComponent,
    removeComponent,
    registerCitations,
    addCitation,
    togglePanel,
    areDependenciesReady,
    waitForDependencies,
    updateGroupStatus,
    updateAllGroupStatuses,
    createDocumentVersion,
    triggerImmediateSave,
    triggerAutoSaveVersion: documentVersioning.triggerAutoSaveVersion,
    refreshAllDescendants,
    triggerInitialLoad,
    areAllComponentsLoaded,
  }), [
    state,
    dispatch,
    // All the callback functions are stable due to proper useCallback usage
    registerComponent,
    updateComponent,
    removeComponent,
    registerCitations,
    addCitation,
    togglePanel,
    areDependenciesReady,
    waitForDependencies,
    updateGroupStatus,
    updateAllGroupStatuses,
    createDocumentVersion,
    triggerImmediateSave,
    documentVersioning.triggerAutoSaveVersion,
    refreshAllDescendants,
    triggerInitialLoad,
    areAllComponentsLoaded,
  ])

  return <DocumentContext.Provider value={contextValue}>{children}</DocumentContext.Provider>
}
