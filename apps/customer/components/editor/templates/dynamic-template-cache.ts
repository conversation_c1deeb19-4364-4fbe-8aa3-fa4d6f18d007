import { DynamicTemplate } from './dynamic-template-generator'

interface CacheEntry {
  templates: DynamicTemplate[]
  timestamp: number
}

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000

class DynamicTemplateCache {
  private cache: CacheEntry | null = null

  /**
   * Gets cached templates if available and not expired
   */
  getCached(): DynamicTemplate[] | null {
    if (!this.cache) {
      return null
    }

    const now = Date.now()
    if (now - this.cache.timestamp > CACHE_DURATION) {
      // Cache expired
      this.cache = null
      return null
    }

    return this.cache.templates
  }

  /**
   * Sets templates in cache
   */
  set(templates: DynamicTemplate[]): void {
    this.cache = {
      templates,
      timestamp: Date.now()
    }
  }

  /**
   * Clears the cache
   */
  clear(): void {
    this.cache = null
  }
}

// Singleton instance
export const dynamicTemplateCache = new DynamicTemplateCache()