// Utility function to find parent report-group
import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from 'react'
import { useReportManager } from '@/components/editor/hooks/useReportManager'
import { ReportComponentConfig, ReportComponentDialog } from '@/components/editor/dialogs/ReportComponentDialog'
import { AlertTriangle, GripVertical, Lock, MoreVertical, RefreshCw, Settings, Shield, Trash2 } from 'lucide-react'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import { Progress } from '@ui/components/ui/progress'
import { NodeViewContent, NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { cn } from '@utils/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { But<PERSON> } from '@ui/components/ui/button'
import { Node } from '@tiptap/core'
import { ComponentStatus } from '@/components/editor/types'

/**
 * Check if a TipTap node has meaningful content
 * @param node The TipTap node to check
 * @returns true if the node has content, false if empty
 */
function hasNodeContent(node: any): boolean {
  if (!node || !node.content) return false

  // Check if node has any content at all
  if (node.content.size === 0) return false

  // Extract text content and check if it's meaningful
  const textContent = node.textContent || ''
  const trimmedContent = textContent.trim()

  // Consider empty if only whitespace or very short content
  return trimmedContent.length > 0
}

export function findParentReportGroup(editor: any, pos: number): string | null {
  if (!editor || pos === undefined) return null

  try {
    const resolvedPos = editor.state.doc.resolve(pos)

    // Walk up the document tree to find a parent report-group
    for (let depth = resolvedPos.depth; depth >= 0; depth--) {
      const node = resolvedPos.node(depth)
      if (node.type.name === 'reportGroup' && node.attrs.id) {
        return node.attrs.id
      }
    }
  } catch (error) {
    console.warn('Error finding parent report group:', error)
  }

  return null
}

/**
 * Calculate progress percentage for a report group based on its descendants
 * @param descendants Array of descendant components
 * @param groupId The ID of the group for logging
 * @returns Progress object with percentage and status counts
 */
function calculateGroupProgress(descendants: any[], groupId?: string) {
  if (descendants.length === 0) {
    return {
      percentage: 100,
      completed: 0,
      total: 0,
      loading: 0,
      error: 0,
      pending: 0
    }
  }

  // Count statuses using the same logic as the group status manager
  const completed = descendants.filter(d => 
    d.status === 'loaded' || d.status === 'preserved' || d.status === 'locked'
  ).length
  const loading = descendants.filter(d => d.status === 'loading').length
  const error = descendants.filter(d => d.status === 'error').length
  const pending = descendants.filter(d => d.status === 'idle').length

  // Debug logging to help identify status mismatches
  console.log(`Progress calculation for group ${groupId} with ${descendants.length} descendants:`, {
    completed,
    loading,
    error,
    pending,
    total: completed + loading + error + pending,
    statuses: descendants.map(d => `${d.id}:${d.status}`).join(', ')
  })

  // Only count truly completed items (loaded/preserved/locked) in progress
  const percentage = descendants.length > 0 ? (completed / descendants.length) * 100 : 100

  return {
    percentage,
    completed,
    total: descendants.length,
    loading,
    error,
    pending
  }
}

/* ------------------------------------------------------------------
 *  Report Group React Component (formerly Report Section)
 * ------------------------------------------------------------------*/
export const ReportGroupComponent = React.memo<{
  node: any;
  updateAttributes: any;
  deleteNode: any;
  editor: any;
  getPos: () => number | undefined
}>(({ node, updateAttributes, deleteNode, editor, getPos }) => {
  const id = React.useMemo(() => {
    // Ensure we always have a valid ID
    const nodeId = node.attrs.id;
    if (!nodeId || nodeId === 'null' || nodeId === 'undefined') {
      // Generate a default ID if none provided
      const defaultId = `group-${Date.now()}`;
      console.warn(`Report group missing ID, generating default: ${defaultId}`);
      // Update the node attributes with the generated ID
      setTimeout(() => updateAttributes({ id: defaultId }), 0);
      return defaultId;
    }
    return nodeId;
  }, [node.attrs.id, updateAttributes])
  const title = React.useMemo(() => node.attrs.title, [node.attrs.title])
  const locked = React.useMemo(() => node.attrs.locked, [node.attrs.locked])
  const preserved = React.useMemo(() => node.attrs.preserved, [node.attrs.preserved])
  const reportManager = useReportManager()
  const [configDialogOpen, setConfigDialogOpen] = useState(false)

  // Track if component is already registered to prevent re-registration
  const isRegisteredRef = useRef(false)
  // Store report manager in ref to avoid dependency issues
  const reportManagerRef = useRef(reportManager)
  reportManagerRef.current = reportManager

  // Find parent report group for nested groups
  const parentGroupId = React.useMemo(() => {
    const pos = getPos()
    return pos !== undefined ? findParentReportGroup(editor, pos) : null
  }, [editor, getPos])

  // Get current component state
  const currentComponentState = reportManager.components.get(id)
  const status = currentComponentState?.status || 'idle'

  // Calculate progress based on direct children only
  const directChildren = React.useMemo(() => reportManager.getChildren(id), [reportManager, id])
  const progress = React.useMemo(() => calculateGroupProgress(directChildren, id), [directChildren, id])

  // Debug: Log status mismatch and force status update
  React.useEffect(() => {
    // Only trigger if we have children and they're all in a final state
    if (progress.total > 0) {
      const allChildrenReady = progress.completed === progress.total && progress.loading === 0 && progress.pending === 0
      const needsUpdate = allChildrenReady && (status === 'loading' || status === 'idle')

      if (needsUpdate) {
        console.warn(`GROUP STATUS MISMATCH: Group ${id} shows ${progress.completed}/${progress.total} complete (no loading/pending) but status is still '${status}'`)
        // Force an immediate status update
        console.log(`GROUP STATUS MISMATCH: Forcing immediate status update for group ${id}`)
        reportManager.updateGroupStatus(id, true) // true = immediate update
      }
    }
  }, [id, progress, status, reportManager])

  useEffect(() => {
    // Only register if not already registered
    if (isRegisteredRef.current) {
      console.log(`*** REPORT GROUP COMPONENT ${id}: Already registered, skipping registration ***`)
      return
    }

    // Check if component already exists in the report manager
    const existingComponent = reportManagerRef.current.components.get(id)

    // Set initial status - respect existing status first, then HTML attributes
    let initialStatus: ComponentStatus
    
    // Override with locked/preserved if those attributes are set
    if (preserved) {
      initialStatus = 'preserved'
    } else if (locked) {
      initialStatus = 'locked'
    } else if (existingComponent) {
      // Preserve existing status if component already exists
      initialStatus = existingComponent.status
      console.log(`*** REPORT GROUP COMPONENT ${id}: Preserving existing status '${initialStatus}' ***`)
    } else {
      // Only use default status for new components
      initialStatus = node.attrs.status || 'idle'
    }

    console.log(`*** REPORT GROUP COMPONENT ${id}: Registering with status '${initialStatus}' (existing: ${existingComponent?.status}, from HTML: '${node.attrs.status}', locked: ${locked}, preserved: ${preserved}) ***`)

    // Register this component with the report manager
    reportManagerRef.current.registerComponent({
      id,
      type: 'report-group',
      status: initialStatus,
      title,
      parentId: parentGroupId || undefined,
    })

    isRegisteredRef.current = true

    // Trigger a status update after registration to ensure proper initial state
    // This is important for groups that may have children already loaded
    // Use a longer delay to allow child components to register first
    setTimeout(() => {
      console.log(`*** REPORT GROUP COMPONENT ${id}: Triggering initial status update after registration ***`)
      reportManagerRef.current.updateGroupStatus(id, true) // true = immediate update
    }, 500)

    return () => {
      console.log(`*** REPORT GROUP COMPONENT ${id}: Unregistering ***`)
      reportManagerRef.current.removeComponent(id)
      isRegisteredRef.current = false
    }
  }, [id]) // Only depend on ID - other values can be updated separately

  // Update component attributes when they change
  useEffect(() => {
    if (!isRegisteredRef.current) return

    console.log(`*** REPORT GROUP COMPONENT ${id}: Updating component attributes ***`)
    reportManager.updateComponent(id, {
      title,
      parentId: parentGroupId || undefined,
    })
  }, [id, title, parentGroupId, reportManager])

  // EKO-162: Improved status synchronization to prevent infinite loops
  const statusSyncTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastSyncedStatusRef = useRef(node.attrs.status)
  const isSyncingRef = useRef(false)
  const syncCountRef = useRef(0)

  // Sync component status to node attributes with debouncing
  const syncComponentState = reportManager.components.get(id)
  const currentComponentStatus = syncComponentState?.status

  useEffect(() => {
    // EKO-162: Skip if we're currently in a sync operation to prevent loops
    if (isSyncingRef.current) return

    // EKO-162: Prevent excessive sync operations (max 5 per component)
    if (syncCountRef.current > 5) {
      console.warn(`Report group ${id}: Too many sync operations, skipping to prevent infinite loop`)
      return
    }

    if (currentComponentStatus && currentComponentStatus !== lastSyncedStatusRef.current) {
      // Clear any existing timeout
      if (statusSyncTimeoutRef.current) {
        clearTimeout(statusSyncTimeoutRef.current)
      }

      // EKO-162: Increased debounce delay and added sync counting
      statusSyncTimeoutRef.current = setTimeout(() => {
        if (currentComponentStatus !== node.attrs.status) {
          console.log(`Report group ${id}: Syncing status from component '${currentComponentStatus}' to node (was '${node.attrs.status}') [sync #${syncCountRef.current + 1}]`)
          isSyncingRef.current = true
          lastSyncedStatusRef.current = currentComponentStatus
          syncCountRef.current += 1
          updateAttributes({ status: currentComponentStatus })

          // Reset sync flag after a brief delay
          setTimeout(() => {
            isSyncingRef.current = false
          }, 100) // EKO-162: Increased delay to 100ms
        }
      }, 200) // EKO-162: Increased debounce to 200ms
    }

    return () => {
      if (statusSyncTimeoutRef.current) {
        clearTimeout(statusSyncTimeoutRef.current)
      }
    }
  }, [currentComponentStatus, id, updateAttributes, node.attrs.status])

  // Update tracking when node attributes change from external sources
  useEffect(() => {
    if (!isSyncingRef.current) {
      lastSyncedStatusRef.current = node.attrs.status
      // EKO-162: Reset sync count when status changes externally
      syncCountRef.current = 0
    }
  }, [node.attrs.status])

  // EKO-117: Watch for status changes to 'idle' and trigger reload
  const previousStatusRef = useRef<string | undefined>(undefined)
  const reloadTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    const currentStatus = syncComponentState?.status
    const previousStatus = previousStatusRef.current

    // If status changed to 'idle', trigger reload from database
    if (currentStatus === 'idle' && previousStatus && previousStatus !== 'idle') {
      console.log(`Report group ${id}: Status changed to 'idle' from '${previousStatus}', triggering reload`)

      // For groups, we trigger re-evaluation of the group status
      // The group status is automatically calculated based on its children
      if (!locked && !preserved) {
        // EKO-162: Clear any existing reload timeout to prevent multiple triggers
        if (reloadTimeoutRef.current) {
          clearTimeout(reloadTimeoutRef.current)
        }

        // EKO-162: Increased delay to ensure status change is fully processed
        reloadTimeoutRef.current = setTimeout(() => {
          reportManager.updateGroupStatus(id, true) // Force immediate update
          reloadTimeoutRef.current = null
        }, 250) // Increased from 100ms to 250ms
      }
    }

    // EKO-162: Update the previous status ref at the end to correctly track changes
    previousStatusRef.current = currentStatus

    return () => {
      if (reloadTimeoutRef.current) {
        clearTimeout(reloadTimeoutRef.current)
      }
    }
  }, [syncComponentState?.status, id, locked, preserved, reportManager])

  const handleMenuAction = useCallback((action: string) => {
    switch (action) {
      case 'refresh':
        if (!locked && !preserved) {
          console.log(`Report group ${id}: Refreshing all descendants`)
          reportManager.refreshAllDescendants(id)
        }
        break
      case 'delete':
        deleteNode()
        break
      case 'configure':
        setConfigDialogOpen(true)
        break
      case 'lock':
        updateAttributes({ locked: true })
        break
      case 'preserve':
        updateAttributes({ preserved: true })
        break
    }
  }, [id, locked, preserved, reportManager, updateAttributes, deleteNode])

  const handleConfigConfirm = useCallback((config: ReportComponentConfig) => {
    updateAttributes({
      id: config.id,
      title: config.title,
    })
  }, [updateAttributes])

  // Get status icon and styling based on group state
  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <RefreshCw className="w-3 h-3 text-green-600 animate-spin" />
      case 'error':
        const errorMessage = currentComponentState?.error || 'One or more child components have errors'
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <AlertTriangle className="w-3 h-3 text-red-600 cursor-help" />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-sm">{errorMessage}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      case 'loaded':
        return <span className="w-3 h-3 text-green-600">✓</span>
      default:
        return null
    }
  }

  const getStatusStyling = () => {
    switch (status) {
      case 'loading':
        return {
          border: 'border-yellow-200',
          bg: 'bg-yellow-50/30',
          header: 'bg-yellow-100 border-yellow-200',
          text: 'text-yellow-700',
        }
      case 'error':
        return {
          border: 'border-red-200',
          bg: 'bg-red-50/30',
          header: 'bg-red-100 border-red-200',
          text: 'text-red-700',
        }
      case 'loaded':
        return {
          border: 'border-green-200',
          bg: 'bg-green-50/30',
          header: 'bg-green-100 border-green-200',
          text: 'text-green-700',
        }
      default:
        return {
          border: 'border-gray-200',
          bg: 'bg-gray-50/30',
          header: 'bg-gray-100 border-gray-200',
          text: 'text-gray-700',
        }
    }
  }

  const styling = getStatusStyling()

  return (
    <NodeViewWrapper className={cn(
      'report-group relative border-2 rounded-lg p-4 my-4 backdrop-blur-sm',
      styling.border,
      styling.bg,
      // Print mode: hide all visual decorations
      'print:border-none print:bg-transparent print:backdrop-blur-none print:p-0 print:m-0 print:rounded-none',
      status === 'loading' && 'opacity-80',
    )}
    data-type="reportGroup"
    data-id={id || ''}
    data-status={status}>
      {/* Group Header with Controls */}
      <div className={cn(
        'report-group-header absolute -top-3 left-4 flex items-center gap-2 px-3 py-1 rounded-full border print:hidden',
        styling.header,
      )}>
        <GripVertical className="w-4 h-4 text-green-600 cursor-grab" />
        <span className={cn('text-sm font-medium', styling.text)}>{id}</span>
        {getStatusIcon()}
        {/* Progress information for groups with descendants */}
        {progress.total > 0 && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className={cn('text-xs px-1.5 py-0.5 rounded-full border', styling.text)}>
                  {progress.completed}/{progress.total}
                </span>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <div className="text-sm space-y-1">
                  <p>{progress.completed} of {progress.total} sub-sections completed</p>
                  {progress.loading > 0 && <p>{progress.loading} loading</p>}
                  {progress.error > 0 && <p className="text-red-500">{progress.error} errors</p>}
                  {progress.pending > 0 && <p>{progress.pending} pending</p>}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-green-600 hover:text-green-800"
              data-testid="report-group-menu-trigger"
            >
              <MoreVertical className="w-3 h-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => handleMenuAction('refresh')}
              disabled={locked || preserved || status === 'loading'}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('configure')}>
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('lock')}>
              <Lock className="w-4 h-4 mr-2" />
              Lock
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('preserve')}>
              <Shield className="w-4 h-4 mr-2" />
              Preserve
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('delete')} className="text-red-600">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Progress bar for groups with descendants */}
      {progress.total > 0 && progress.percentage < 100 && (
        <div className="mt-8 mb-2 print:hidden">
          <div className="flex justify-between text-xs text-muted-foreground mb-1">
            <span>Sub-section progress</span>
            <span>{Math.round(progress.percentage)}%</span>
          </div>
          <Progress 
            value={progress.percentage} 
            className="h-1.5 bg-opacity-30" 
          />
        </div>
      )}

      {title && hasNodeContent(node) && (
        <a id={id}>
          <span className="report-section-title block heading-2 mt-4">{title}</span>
        </a>
      )}
      <NodeViewContent className="content mt-2" />

      <ReportComponentDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        onConfirm={handleConfigConfirm}
        type="report-group"
        initialConfig={{
          id,
          title,
          type: 'report-group',
        }}
      />
    </NodeViewWrapper>
  )
})
/* ------------------------------------------------------------------
 *  TipTap Report Group Extension (formerly Report Section)
 * ------------------------------------------------------------------*/
export const ReportGroupExtension = Node.create({
  name: 'reportGroup',

  group: 'block',

  content: 'block*',

  atom: false,

  draggable: true,

  selectable: true,


  addAttributes() {
    return {
      id: {
        default: '',
      },
      title: {
        default: '',
      },
      locked: {
        default: false,
      },
      preserved: {
        default: false,
      },
      status: {
        default: 'idle',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'report-group',
        getAttrs: (element) => {
          if (typeof element === 'string') return false
          const attrs = {
            id: element.getAttribute('id'),
            title: element.getAttribute('title'),
            locked: element.getAttribute('locked') === 'true',
            preserved: element.getAttribute('preserved') === 'true',
            status: element.getAttribute('status') || 'idle',
          }
          return attrs
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['report-group', { ...HTMLAttributes, 'data-type': 'reportGroup' }, 0]
  },

  addNodeView() {
    return ReactNodeViewRenderer(ReportGroupComponent)
  },

  addKeyboardShortcuts() {
    return {
      Delete: () => {
        // Check if the current selection is a report group node
        const { selection } = this.editor.state;
        const { $from } = selection;

        // First check if we're directly inside a report group
        for (let depth = $from.depth; depth >= 0; depth--) {
          const node = $from.node(depth);
          if (node.type.name === 'reportGroup') {
            const pos = $from.before(depth);
            // Delete the entire report group node
            this.editor.chain()
              .focus()
              .deleteRange({ from: pos, to: pos + node.nodeSize })
              .run();
            return true;
          }
        }

        // If not directly inside, check if selection spans a report group
        const { $to } = selection;
        let nodeToDelete: any = null;
        let pos: number | null = null;

        this.editor.state.doc.nodesBetween($from.pos, $to.pos, (node, nodePos) => {
          if (node.type.name === 'reportGroup') {
            nodeToDelete = node;
            pos = nodePos;
            return false; // Stop iteration
          }
        });

        if (nodeToDelete && pos !== null) {
          // Delete the entire report group node
          this.editor.chain()
            .focus()
            .deleteRange({ from: pos as number, to: (pos as number) + nodeToDelete.nodeSize })
            .run();
          return true;
        }

        return false; // Let default Delete behavior handle it
      },
    };
  },
})
ReportGroupComponent.displayName = 'ReportGroupComponent'
