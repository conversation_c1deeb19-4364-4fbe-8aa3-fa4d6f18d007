import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import { Node } from '@tiptap/core'
import { NodeViewContent, NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react'
import { useReportManager } from '../../editor/hooks/useReportManager'
import { useDocumentContext } from '../../editor/context/DocumentContext'
import { CitationType } from '@/components/citation'
import { processMarkdownForTipTap } from '../../editor/utils/markdown-processor'
import {
  calculateReportGroupDepth,
  type HeadingTransform,
  transformHeadings,
} from '../../editor/utils/markdown-heading-utils'
import { Button } from '@ui/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { Toolt<PERSON>, Toolt<PERSON>Content, Toolt<PERSON><PERSON>rovider, TooltipTrigger } from '@ui/components/ui/tooltip'
import { AlertTriangle, GripVertical, Lock, MoreVertical, RefreshCw, Settings, Shield, Trash2 } from 'lucide-react'
import { cn } from '@utils/lib/utils'

import { ReportComponentConfig, ReportComponentDialog } from '../../editor/dialogs/ReportComponentDialog'
import { findParentReportGroup } from '@/components/editor/extensions/ReportGroupComponent'

/**
 * Check if a TipTap node has meaningful content
 * @param node The TipTap node to check
 * @returns true if the node has content, false if empty
 */
function hasNodeContent(node: any): boolean {
  if (!node || !node.content) return false

  // Check if node has any content at all
  if (node.content.size === 0) return false

  // Extract text content and check if it's meaningful
  const textContent = node.textContent || ''
  const trimmedContent = textContent.trim()

  // Consider empty if only whitespace or very short content
  return trimmedContent.length > 0
}

/* ------------------------------------------------------------------
 *  Report Section React Component (formerly Report Sub-Section)
 * ------------------------------------------------------------------*/
const ReportSectionComponent = React.memo<{ node: any; updateAttributes: any; deleteNode: any; editor: any; getPos: () => number | undefined }>(({ node, updateAttributes, deleteNode, editor, getPos }) => {
  const id = React.useMemo(() => {
    // Ensure we always have a valid ID
    const nodeId = node.attrs.id;
    if (!nodeId || nodeId === 'null' || nodeId === 'undefined') {
      // Generate a default ID if none provided
      const defaultId = `section-${Date.now()}`;
      console.warn(`Report section missing ID, generating default: ${defaultId}`);
      // Update the node attributes with the generated ID
      setTimeout(() => updateAttributes({ id: defaultId }), 0);
      return defaultId;
    }
    return nodeId;
  }, [node.attrs.id, updateAttributes]);
  const title = React.useMemo(() => node.attrs.title, [node.attrs.title]);
  const endpoint = React.useMemo(() => node.attrs.endpoint, [node.attrs.endpoint]);
  const prompt = React.useMemo(() => node.attrs.prompt, [node.attrs.prompt]);
  const locked = React.useMemo(() => node.attrs.locked, [node.attrs.locked]);
  const preserved = React.useMemo(() => node.attrs.preserved, [node.attrs.preserved]);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  
  // Add render tracking
  const renderCountRef = useRef(0);
  renderCountRef.current += 1;
  console.log(`[RENDER] ReportSection ${id} render #${renderCountRef.current}`);

  const reportManager = useReportManager();
  const { state, dispatch } = useDocumentContext()
  const [isLoading, setIsLoading] = useState(false);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  // Entity is now fixed from document context - no tracking needed
  const loadingRef = useRef(false) // Prevent multiple simultaneous loads

  // Find parent report group - only calculate once on mount to prevent re-renders
  const [parentGroupId] = React.useState(() => {
    const pos = getPos()
    return pos !== undefined ? findParentReportGroup(editor, pos) : null
  })

  // Track if component is already registered to prevent re-registration
  const isRegisteredRef = useRef(false);
  
  // Register component once on mount
  useEffect(() => {
    // Only register if not already registered
    if (isRegisteredRef.current) {
      console.log(`Report section ${id}: Already registered, skipping registration`);
      return;
    }
    
    // Set initial status - respect status from HTML attributes
    let initialStatus: 'idle' | 'loaded' | 'preserved' | 'locked' | 'loading' | 'error' = node.attrs.status || 'idle'

    // Override with locked/preserved if those attributes are set
    if (preserved) {
      initialStatus = 'preserved'
    } else if (locked) {
      initialStatus = 'locked'
    } else if (initialStatus === 'loading' || initialStatus === 'error') {
      // If document was saved with 'loading' status, reset to 'idle' to allow reload
      initialStatus = 'idle'
    }

    console.log(`Report section ${id}: Registering with status '${initialStatus}' (from HTML: '${node.attrs.status}', locked: ${locked}, preserved: ${preserved})`)

    reportManager.registerComponent({
      id,
      type: 'report-section',
      status: initialStatus,
      title,
      endpoint,
      prompt,
      content: '',
      parentId: parentGroupId || undefined,
      preserved: preserved || false,
      locked: locked || false,
    });
    
    isRegisteredRef.current = true;

    return () => {
      console.log(`Report section ${id}: Unregistering component`);
      reportManager.removeComponent(id);
      isRegisteredRef.current = false;
    };
  }, [id]); // Only depend on ID - other values can be updated separately
  
  // Update component attributes when they change
  useEffect(() => {
    if (!isRegisteredRef.current) return;
    
    console.log(`Report section ${id}: Updating component attributes`);
    reportManager.updateComponent(id, {
      title,
      endpoint,
      prompt,
      parentId: parentGroupId || undefined,
      preserved: preserved || false,
      locked: locked || false,
    });
  }, [title, endpoint, prompt, parentGroupId, preserved, locked, reportManager, id]);

  // Debounced status synchronization to prevent infinite loops
  const statusSyncTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isSyncingRef = useRef(false)

  // Sync component status to node attributes with debouncing
  // We use a custom effect that only runs when the component is actually updated
  // Memoize only the status value to prevent unnecessary re-renders (EKO-118)
  const currentComponentStatus = useMemo(() => {
    const component = reportManager.components.get(id)
    return component?.status
  }, [reportManager.components, id])

  // Keep the full component state for other uses, but don't use it as a dependency
  const syncComponentState = reportManager.components.get(id)

  useEffect(() => {
    // Skip if we're currently in a sync operation to prevent loops
    if (isSyncingRef.current) return

    // Only sync if status differs from node
    if (currentComponentStatus && currentComponentStatus !== node.attrs.status) {
      // Clear any existing timeout
      if (statusSyncTimeoutRef.current) {
        clearTimeout(statusSyncTimeoutRef.current)
      }

      // Debounce the status update
      statusSyncTimeoutRef.current = setTimeout(() => {
        if (currentComponentStatus !== node.attrs.status) {
          console.log(`Report section ${id}: Syncing status from component '${currentComponentStatus}' to node (was '${node.attrs.status}')`)
          isSyncingRef.current = true
          updateAttributes({ status: currentComponentStatus })

          // Reset sync flag after a brief delay
          setTimeout(() => {
            isSyncingRef.current = false
          }, 100)
        }
      }, 100) // 100ms debounce
    }

    return () => {
      if (statusSyncTimeoutRef.current) {
        clearTimeout(statusSyncTimeoutRef.current)
      }
    }
  }, [currentComponentStatus, id, updateAttributes, node.attrs.status, reportManager])

  // Update tracking when node attributes change from external sources (like document loading)
  // Removed - this tracking is unnecessary

  // EKO-117: Watch for status changes to 'idle' and trigger reload
  const previousStatusRef = useRef<string | undefined>(undefined)

  const loadContent = useCallback(async (isRefresh = false) => {
    if (!endpoint || locked || preserved) {
      console.log(`Report section ${id}: Not loading due to missing endpoint or locked/preserved state`)
      return
    }

    // Check component status - only call endpoint if status is 'idle' or undefined (not yet registered)
    const componentState = reportManager.components.get(id)
    const status = componentState?.status
    if (!isRefresh && status && status !== 'idle') {
      console.log(`Report section ${id}: Status is '${status}', skipping server call`)
      return
    }

    // Prevent multiple simultaneous loads
    if (loadingRef.current) {
      console.log(`Report section ${id}: Already loading, skipping duplicate request`)
      return
    }

    loadingRef.current = true
    setIsLoading(true);
    reportManager.updateComponent(id, { status: 'loading' });

    // Add a small random delay to prevent simultaneous API calls
    const delay = Math.random() * 1000 + 500; // 500-1500ms delay
    await new Promise(resolve => setTimeout(resolve, delay));

    // Use current entity/run from state for placeholder replacement
    let processedEndpoint = endpoint;

    try {
      // Entity and run are guaranteed to be present due to validation in DocumentProvider
      console.log(`Report section ${id}: Using entity '${state.entity}' and run '${state.run}'`);
      
      if (!state.run) {
        throw new Error(`No run set for document. Run must be set at document creation.`);
      }

      // Simple placeholder replacement
      processedEndpoint = endpoint
        .replace(/\[ENTITY_ID\]/g, state.entity)
        .replace(/\[RUN_ID\]/g, state.run);

      // Check if endpoint still has placeholders after replacement
      if (processedEndpoint.includes('[ENTITY_ID]') || processedEndpoint.includes('[RUN_ID]')) {
        throw new Error(`Endpoint contains unresolved placeholders: ${processedEndpoint}. Entity: ${state.entity}, Run: ${state.run}`);
      }

      // Ensure endpoint starts with /api if it's a relative path
      if (processedEndpoint.startsWith('/report/')) {
        processedEndpoint = '/api' + processedEndpoint;
      }

      console.log(`Loading content for ${id} from: ${processedEndpoint}`);

      const response = await fetch(processedEndpoint);
      if (!response.ok) {
        if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please wait a moment before trying again.');
        }
        throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      let newContent = '';
      let citations: CitationType[] = [];

      if (contentType?.includes('application/json')) {
        // Handle JSON response (with text and citations)
        const data = await response.json();
        newContent = data.text || '';
        citations = data.citations || [];

        // Handle notApplicable flag
        if (data.notApplicable) {
          console.log(`Report section ${id}: Section not applicable, setting status to loaded`)
          setHasLoadedOnce(true);
          reportManager.updateComponent(id, {
            status: 'loaded',
            content: '', // Store empty content for not applicable sections
            lastRefreshed: new Date()
          });

          // Trigger document save after status update
          setTimeout(() => {
            if (state.editor) {
              dispatch({
                type: 'CONTENT_CHANGED',
                content: state.editor.getHTML(),
                data: state.editor.getJSON(),
                source: 'system',
              })
            }
          }, 100);

          return; // Exit early, don't process content further
        }
      } else {
        // Handle plain text response
        newContent = await response.text();
      }

      setHasLoadedOnce(true);
      reportManager.updateComponent(id, {
        status: 'loaded',
        content: newContent,
        lastRefreshed: new Date()
      });

      // Trigger document save after content is loaded
      setTimeout(() => {
        if (state.editor) {
          dispatch({
            type: 'CONTENT_CHANGED',
            content: state.editor.getHTML(),
            data: state.editor.getJSON(),
            source: 'system',
          })
        }
      }, 100) // Small delay to ensure content is inserted

      // Register citations
      if (citations.length > 0) {
        reportManager.registerCitations(citations);
      }

      // Update the node content with the loaded text
      if (newContent && editor && getPos && !isLoading) {
        // Apply heading transformation based on component configuration
        const headingsTransform = node.attrs.headings as HeadingTransform || 'remove'
        let transformedContent = newContent

        if (headingsTransform !== 'keep') {
          // Calculate depth if needed for reset transformation
          let targetDepth = node.attrs.depth || 1
          if (headingsTransform === 'reset' && !node.attrs.depth) {
            // Auto-calculate depth based on parent report groups
            const pos = getPos()
            if (pos !== undefined) {
              const calculatedDepth = calculateReportGroupDepth(editor, pos)
              targetDepth = calculatedDepth + 1 // Start one level deeper than parent groups
            }
          }

          transformedContent = transformHeadings(newContent, headingsTransform, targetDepth)
        }

        // Process markdown content through the same pipeline as initial_content
        try {
          const processedResult = await processMarkdownForTipTap(transformedContent, citations, {
            admin: true,
            inlineCitations: true,
            badgeStyle: true,
            skipCitations: false
          });

          // Recalculate position after async processing to avoid "out of range" errors
          const currentPos = getPos();
          if (currentPos !== undefined && currentPos >= 0) {
            // Validate node structure before proceeding
            if (!node || !node.nodeSize || node.nodeSize <= 0) {
              console.warn(`Report section ${id}: Invalid node structure, skipping content insertion`)
              return
            }

            // Clear existing content first if this is a refresh
            if (isRefresh && node.content && node.content.size > 0) {
              const nodeStart = currentPos + 1;
              const nodeEnd = currentPos + node.nodeSize - 1;
              if (nodeStart < nodeEnd && nodeEnd <= editor.state.doc.content.size) {
                editor.commands.deleteRange({ from: nodeStart, to: nodeEnd })
              }
            }

            // Recalculate position again after potential deletion
            const finalPos = getPos();
            if (finalPos !== undefined && finalPos >= 0) {
              const currentNodeSize = node.nodeSize;
              if (currentNodeSize > 0) {
                const insertPos = finalPos + currentNodeSize - 1 // Insert before the closing tag
                const htmlContent = `<div class="report-content">${processedResult.html}</div>`

                // Validate insert position
                if (insertPos >= 0 && insertPos <= editor.state.doc.content.size) {
                  editor.commands.insertContentAt(insertPos, htmlContent, {
                    parseOptions: {
                      preserveWhitespace: 'full',
                    },
                  })
                } else {
                  console.warn(`Report section ${id}: Invalid insert position ${insertPos}, skipping insertion`)
                }
              }
            }
          }
        } catch (error) {
          console.error(`Report section ${id}: Failed to process markdown:`, error);

          // Fallback to raw content with position recalculation
          try {
            const fallbackPos = getPos()
            if (fallbackPos !== undefined && fallbackPos >= 0) {
              const fallbackNodeSize = node.nodeSize
              if (fallbackNodeSize > 0) {
                const fallbackInsertPos = fallbackPos + fallbackNodeSize - 1
                const htmlContent = `<div class="report-content">${newContent}</div>`

                // Validate fallback insert position
                if (fallbackInsertPos >= 0 && fallbackInsertPos <= editor.state.doc.content.size) {
                  editor.commands.insertContentAt(fallbackInsertPos, htmlContent, {
                    parseOptions: {
                      preserveWhitespace: 'full',
                    },
                  })
                } else {
                  console.warn(`Report section ${id}: Invalid fallback insert position ${fallbackInsertPos}, skipping insertion`)
                }
              }
            }
          } catch (posError) {
            console.error(`Report section ${id}: Fallback insertion also failed:`, posError)
          }
        }
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Report section ${id} loading failed:`, {
        endpoint: processedEndpoint,
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined
      });

      reportManager.updateComponent(id, {
        status: 'error',
        error: errorMessage
      });

      // Don't re-throw in useEffect context, but ensure error is visible
      // The error state is already set in reportState for UI display
    } finally {
      loadingRef.current = false
      setIsLoading(false);
    }
  }, [endpoint, id, locked, preserved, reportManager, editor, getPos, state]);

  // EKO-117: Watch for status changes to 'idle' and trigger reload
  useEffect(() => {
    const currentStatus = currentComponentStatus
    const previousStatus = previousStatusRef.current

    // Update the previous status ref
    previousStatusRef.current = currentStatus

    // If status changed to 'idle', trigger reload from database
    if (currentStatus === 'idle' && previousStatus && previousStatus !== 'idle') {
      console.log(`Report section ${id}: Status changed to 'idle' from '${previousStatus}', checking if reload allowed`)

      // Only reload if we have an endpoint and aren't locked/preserved
      if (endpoint && !locked && !preserved) {
        console.log(`Report section ${id}: Triggering reload (not locked/preserved)`)
        // Small delay to ensure status change is fully processed
        setTimeout(() => {
          loadContent(true).catch(error => {
            console.error(`Report section ${id}: Idle status reload failed:`, error)
          })
        }, 100)
      } else {
        console.log(`Report section ${id}: Skipping reload - locked: ${locked}, preserved: ${preserved}, endpoint: ${!!endpoint}`)
      }
    }
  }, [currentComponentStatus, id, endpoint, locked, preserved, loadContent])

  // EKO-118: Cancel any ongoing loads when component becomes locked or preserved
  useEffect(() => {
    if (locked || preserved) {
      console.log(`Report section ${id}: Component locked/preserved, cancelling any ongoing loads`)
      
      // Stop any loading state (status update is now handled in the action handler to avoid race conditions)
      setIsLoading(false)
      loadingRef.current = false
    }
  }, [locked, preserved, id])

  // Entity change listeners removed - entity is fixed at document creation

  // Smart loading logic: load when status is 'idle' and we have entity
  useEffect(() => {
    if (!endpoint || locked || preserved || isLoading) return;

    const currentEntity = state.entity;
    const currentRun = state.run || 'latest';

    if (currentComponentStatus !== 'idle') {
      console.log(`Report section ${id}: Status is '${currentComponentStatus}', skipping load`)
      return
    }

    // Check component status - only load if status is 'idle' or undefined (not yet registered)
    const componentState = reportManager.components.get(id)
    const status = componentState?.status
    if (status && status !== 'idle' && status !== 'error') {
      console.log(`Report section ${id}: Status is '${status}', skipping load`)
      return
    }
    
    // If status is undefined, the component is not yet registered - treat as 'idle' and continue

    // Load if we haven't loaded once (entity is guaranteed to be present)
    if (!hasLoadedOnce) {
      console.log(`Report section ${id}: Initial load for entity ${currentEntity}, run ${currentRun}`);
      loadContent(false).catch(error => {
        console.error(`Report section ${id}: Initial load failed:`, error);
      });
    }
  }, [
    endpoint,
    locked,
    preserved,
    isLoading,
    state.entity,
    state.run,
    hasLoadedOnce,
    id,
    currentComponentStatus,
    loadContent,
  ]);

  const handleMenuAction = useCallback((action: string) => {
    switch (action) {
      case 'refresh':
        if (!locked && !preserved) {
          loadContent(true).catch(error => {
            console.error(`Report section ${id}: Manual refresh failed:`, error);
          }); // Manual refresh
        }
        break;
      case 'delete':
        deleteNode();
        break;
      case 'configure':
        setConfigDialogOpen(true);
        break;
      case 'lock':
        updateAttributes({ locked: true, status: 'locked' });
        reportManager.updateComponent(id, { status: 'locked' });
        break;
      case 'preserve':
        updateAttributes({ preserved: true, status: 'preserved' });
        reportManager.updateComponent(id, { status: 'preserved' });
        break;
    }
  }, [loadContent, locked, preserved, updateAttributes, deleteNode]);

  const handleConfigConfirm = useCallback((config: ReportComponentConfig) => {
    // Check if headings or depth changed to trigger reload
    const headingsChanged = config.headings !== node.attrs.headings
    const depthChanged = config.depth !== node.attrs.depth

    updateAttributes({
      id: config.id,
      title: config.title,
      endpoint: config.endpoint,
      prompt: config.prompt,
      headings: config.headings,
      depth: config.depth,
    });

    // If headings configuration changed, reset status to trigger reload
    if (headingsChanged || depthChanged) {
      console.log(`Report section ${id}: Headings/depth configuration changed, resetting status to 'idle'`)
      reportManager.updateComponent(id, { status: 'idle' })
      updateAttributes({ status: 'idle' })
    }
  }, [updateAttributes, node.attrs.headings, node.attrs.depth, id, reportManager]);

  // Get component state for error handling
  const componentState = reportManager.components.get(id);

  const getStatusIcon = () => {
    // Check for error state first
    if (componentState?.status === 'error') {
      const errorMessage = componentState.error || 'Unknown error occurred';
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <AlertTriangle className="w-3 h-3 text-red-600 cursor-help" />
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p className="text-sm">{errorMessage}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    if (isLoading) {
      return <RefreshCw className="w-3 h-3 text-blue-600 animate-spin" />;
    }
    if (locked) {
      return <Lock className="w-3 h-3 text-blue-600" />;
    }
    if (preserved) {
      return <Shield className="w-3 h-3 text-blue-600" />;
    }
    return null;
  };

  return (
    <NodeViewWrapper 
      className={cn(
        "report-section relative border-2 border-blue-200 rounded-lg p-4 my-4",
        "bg-blue-50/30 backdrop-blur-sm",
        // Print mode: hide all visual decorations
        "print:border-none print:bg-transparent print:backdrop-blur-none print:p-0 print:m-0 print:rounded-none",
        isLoading && "opacity-60"
      )}
      data-type="reportSection"
      data-id={id || ''}
      data-status={componentState?.status || 'idle'}
    >
      {/* Section Header with Controls */}
      <div className="absolute -top-3 left-4 flex items-center gap-2 bg-blue-100 px-3 py-1 rounded-full border border-blue-200 print:hidden">
        <GripVertical className="w-4 h-4 text-blue-600 cursor-grab" />
        <span className="text-sm font-medium text-blue-700">{id}</span>
        {getStatusIcon()}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
              data-testid="report-section-menu-trigger"
            >
              <MoreVertical className="w-3 h-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => handleMenuAction('refresh')}
              disabled={locked || preserved || isLoading}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('configure')}>
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleMenuAction('lock')}
              disabled={locked}
            >
              <Lock className="w-4 h-4 mr-2" />
              Lock
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleMenuAction('preserve')}
              disabled={preserved}
            >
              <Shield className="w-4 h-4 mr-2" />
              Preserve
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleMenuAction('delete')} className="text-red-600">
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {title && hasNodeContent(node) && (
        <a id={id}>
          <span className="report-section-title block heading-3 mt-4">{title}</span>
        </a>
      )}
      <NodeViewContent className="content mt-2" />

      <ReportComponentDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        onConfirm={handleConfigConfirm}
        type="report-section"
        initialConfig={{
          id,
          title,
          endpoint,
          prompt,
          headings: node.attrs.headings || 'remove',
          depth: node.attrs.depth || 1,
          type: 'report-section',
        }}
      />
    </NodeViewWrapper>
  );
});

ReportSectionComponent.displayName = 'ReportSectionComponent';

/* ------------------------------------------------------------------
 *  TipTap Report Section Extension (formerly Report Sub-Section)
 * ------------------------------------------------------------------*/
export const ReportSectionExtension = Node.create({
  name: 'reportSection',

  group: 'block',

  content: 'block*',

  atom: false,
  
  draggable: true,
  
  selectable: true,

  addAttributes() {
    return {
      id: {
        default: '',
      },
      title: {
        default: '',
      },
      endpoint: {
        default: '',
      },
      prompt: {
        default: '',
      },
      locked: {
        default: false,
      },
      preserved: {
        default: false,
      },
      status: {
        default: 'idle',
      },
      lastRefreshed: {
        default: null,
      },
      headings: {
        default: 'remove',
      },
      depth: {
        default: 1,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'report-section',
        getAttrs: (element) => {
          if (typeof element === 'string') return false;
          return {
            id: element.getAttribute('id'),
            title: element.getAttribute('title'),
            endpoint: element.getAttribute('endpoint'),
            prompt: element.getAttribute('prompt'),
            locked: element.getAttribute('locked') === 'true',
            preserved: element.getAttribute('preserved') === 'true',
            status: element.getAttribute('status') || 'idle',
            lastRefreshed: element.getAttribute('lastRefreshed'),
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['report-section', { ...HTMLAttributes, 'data-type': 'reportSection' }, 0];
  },

  addNodeView() {
    return ReactNodeViewRenderer(ReportSectionComponent);
  },

  addKeyboardShortcuts() {
    return {
      Delete: () => {
        // Check if the current selection is a report section node
        const { selection } = this.editor.state;
        const { $from } = selection;
        
        // First check if we're directly inside a report section
        for (let depth = $from.depth; depth >= 0; depth--) {
          const node = $from.node(depth);
          if (node.type.name === 'reportSection') {
            const pos = $from.before(depth);
            // Delete the entire report section node
            this.editor.chain()
              .focus()
              .deleteRange({ from: pos, to: pos + node.nodeSize })
              .run();
            return true;
          }
        }
        
        // If not directly inside, check if selection spans a report section
        const { $to } = selection;
        let nodeToDelete: any = null;
        let pos: number | null = null;
        
        this.editor.state.doc.nodesBetween($from.pos, $to.pos, (node, nodePos) => {
          if (node.type.name === 'reportSection') {
            nodeToDelete = node;
            pos = nodePos;
            return false; // Stop iteration
          }
        });
        
        if (nodeToDelete && pos !== null) {
          // Delete the entire report section node
          this.editor.chain()
            .focus()
            .deleteRange({ from: pos as number, to: (pos as number) + nodeToDelete.nodeSize })
            .run();
          return true;
        }
        
        return false; // Let default Delete behavior handle it
      },
    };
  },
});

// Keep the old extension for backward compatibility
export const ReportSubSectionExtension = ReportSectionExtension;
