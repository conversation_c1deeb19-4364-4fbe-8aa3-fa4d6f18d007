Please update the README.md in this dir to reflect the current state of the system after any change. DO NOT detail **changes**, instead update the document to reflect the CURRENT design of the system.

Layout:

```
/components/editor/
├── context/                    # React contexts (state management)
│   ├── component/             # Component registration & status
│   ├── document/              # Document operations & state
│   ├── versioning/            # Version history & auto-save
│   ├── dependency/            # Component dependency resolution
│   └── providers/             # Combined provider orchestration
├── services/                   # Business logic layer
│   ├── supabase/              # Database operations (ReportService, VersionService)
│   ├── state/                 # State machines for transitions
│   └── utils/                 # Shared utilities (logger, memory, performance)
├── hooks/                     # Public-facing React hooks
├── types/                     # TypeScript definitions
└── tests/                     # Comprehensive test suite (17 unit tests)
```

#### What Works Well

1. **Modular contexts**: Each context has single responsibility
2. **Service layer**: Clean separation of business logic from UI
3. **Centralized utilities**: Eliminates code duplication effectively
4. **State machines**: Prevent invalid transitions and improve reliability
5. **Comprehensive testing**: Gives confidence in refactoring

#### Anti-Patterns to Avoid

1. **Monolithic contexts**: Don't mix multiple concerns in single context
2. **Direct database calls in components**: Always use service layer
3. **Manual timeout management**: Use centralized timeout utilities
4. **Duplicate logging**: Use centralized logger with configurable levels
5. **Incomplete cleanup**: Always use memory manager for resource tracking

#### Development Tips

1. **Start with service layer**: Extract business logic first
2. **Create utilities**: Consolidate common patterns
3. **Split contexts**: Separate concerns into focused contexts
4. **Add state machines**: Validate transitions and prevent invalid states
5. **Comprehensive testing**: Unit tests for utilities, integration for components
6. **Performance monitoring**: Built-in metrics and issue detection

### Integration Points

- **Supabase**: All database operations go through service layer
- **TipTap Editor**: Document context manages editor state
- **Component System**: Report sections, groups, summaries managed by component context
- **Versioning**: Auto-save and manual version creation
- **Real-time**: Dependency resolution for component loading

### Future Enhancements

- **React DevTools integration**: Custom panel for editor state debugging
- **Advanced performance metrics**: More sophisticated monitoring
- **Error boundaries**: Enhanced error handling and recovery
- **Accessibility**: ARIA support for complex component interactions
