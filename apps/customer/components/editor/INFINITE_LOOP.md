# Infinite Loop Investigation - DocumentProvider

## Problem Summary
The DocumentProvider often experiences an infinite re-render loop that causes the "Maximum update depth exceeded" error.

## Error Details
```
Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.
```

## Console Output Pattern
```
[Console] log: [RENDER] DocumentProvider render #109
[Console] log: [RENDER] DocumentProvider render #110
[Console] log: [RENDER] DocumentProvider render #111
[Console] log: [RENDER] DocumentProvider render #112
[Console] error: Maximum update depth exceeded...
```

### Potential Causes

1. **Hook-Induced State Changes**: One of the custom hooks is calling `dispatch` or causing state changes that trigger re-renders
2. **Ref Composition Issues**: A ref is being set/unset repeatedly
3. **Parent Component Re-renders**: The DocumentProvider's parent might be re-rendering and passing changing props
4. **Side Effects in Render**: Some hook might be performing side effects during render

### Hooks Under Suspicion

Based on console output, these hooks are called on every render:
- `useGroupStatusManager` - Creates `updateGroupStatus` callback
- `useComponentRegistration` - Creates `registerComponent` callback  
- `useDocumentVersioning` - Manages auto-save functionality
- `useDocumentInitialization` - Handles initial document setup
- `useComponentUpdater` - Manages component updates

## Next Steps for Investigation

1. **Isolate Hooks**: Temporarily comment out hooks one by one to identify the culprit
2. **Check Hook Dependencies**: Verify that all `useCallback` dependencies are stable
3. **Trace State Changes**: Add logging to see what's triggering `dispatch` calls
4. **Check Parent Props**: Verify that props passed to DocumentProvider are stable
5. **Ref Debugging**: Investigate any refs being passed to child components

## Code Location
- File: `apps/customer/components/editor/context/DocumentContext.tsx`
- Component: `DocumentProvider`

## Test Case
- Test: `report-minimal.spec.ts`
- Trigger: Creating a new document and navigating to editor
- Frequency: Consistent reproduction

## Current Status
The infinite loop persists despite multiple attempts to fix context value memoization. The issue appears to be at a deeper level, likely within one of the custom hooks or ref handling. Further investigation needed to identify the specific hook or mechanism causing the infinite re-renders.

---

# Advanced Debugging Tools for React Infinite Loops (2024)

## 1. React DevTools Profiler

The React DevTools Profiler is the most powerful tool for identifying infinite loops and performance issues. **Available in browser extensions for Chrome/Firefox.**

### Setup and Usage:
1. Install React DevTools extension
2. Open browser DevTools → "Profiler" tab
3. Click "Record" → trigger the infinite loop → stop recording
4. Analyze the flame graph for repeated render cycles

### Key Features for Infinite Loop Detection:
- **Flame Graph**: Shows component render hierarchy and timing
- **Hook Numbers**: Each hook gets a unique identifier for tracking
- **"Why Did This Render?"**: Shows what props/state/hooks changed
- **Phase Information**: Distinguishes between "mount", "update", and "nested-update"

### What to Look For:
- Identical component render patterns repeating infinitely
- High `actualDuration` values that don't decrease over time
- Components stuck in "update" phase instead of stabilizing

```javascript
// Add React Profiler wrapper for programmatic monitoring
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration, baseDuration, startTime, commitTime) {
  console.log('Profile:', { id, phase, actualDuration });
  if (actualDuration > 100) {
    console.warn('Expensive render detected!');
  }
}

<Profiler id="DocumentProvider" onRender={onRenderCallback}>
  <DocumentProvider>
    {children}
  </DocumentProvider>
</Profiler>
```

## 2. Why-Did-You-Render Library

**Installation**: `npm install @welldone-software/why-did-you-render`

This library monkey-patches React to log detailed information about why components re-render.

### Setup (Development Only):
```javascript
// In your app's entry point (development only)
if (process.env.NODE_ENV === 'development') {
  const whyDidYouRender = require('@welldone-software/why-did-you-render');
  whyDidYouRender(React, {
    trackAllPureComponents: true,
    trackHooks: true,
    trackExtraHooks: [
      // Track custom hooks that might cause issues
      [require('some-library'), 'useCustomHook']
    ]
  });
}
```

### Enable on Specific Components:
```javascript
// Add this to components you want to track
DocumentProvider.whyDidYouRender = {
  logOnDifferentValues: true,
  customName: 'DocumentProvider',
  diffNameColor: 'red'
};
```

### Output Analysis:
- Logs show exactly which props/state/hooks changed
- Highlights unnecessary re-renders
- Provides diff information for complex objects

## 3. Custom useWhyDidYouUpdate Hook

**Implementation for debugging specific components:**

```javascript
import { useEffect, useRef } from 'react';

function useWhyDidYouUpdate(name, props) {
  // Get a mutable ref object where we can store props for comparison next time this hook runs.
  const previousProps = useRef();

  useEffect(() => {
    if (previousProps.current) {
      // Get all keys from previous and current props
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      
      // Use this object to keep track of changed props
      const changedProps = {};
      
      // Iterate through all keys
      allKeys.forEach(key => {
        // If previous is different from current, add it to changedProps
        if (previousProps.current[key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current[key],
            to: props[key]
          };
        }
      });

      // If changedProps is not empty then output to console
      if (Object.keys(changedProps).length) {
        console.log('[why-did-you-update]', name, changedProps);
      }
    }

    // Finally update previousProps with current props for next hook call
    previousProps.current = props;
  });
}

// Usage in DocumentProvider:
function DocumentProvider({ children, ...props }) {
  useWhyDidYouUpdate('DocumentProvider', props);
  
  // Rest of component...
}
```

## 4. React Profiler API (Programmatic)

**For automated detection and logging:**

```javascript
import { Profiler } from 'react';

let renderCount = 0;
const MAX_RENDERS = 50; // Threshold for potential infinite loop

function onRenderCallback(id, phase, actualDuration, baseDuration, startTime, commitTime, interactions) {
  renderCount++;
  
  console.log(`[PROFILER] ${id} render #${renderCount}`, {
    phase,
    actualDuration,
    baseDuration,
    interactions: Array.from(interactions)
  });
  
  // Detect potential infinite loop
  if (renderCount > MAX_RENDERS && phase === 'update') {
    console.error(`🚨 POTENTIAL INFINITE LOOP DETECTED: ${id} has rendered ${renderCount} times`);
    console.trace('Call stack:');
  }
  
  // Reset counter on mount (new component instance)
  if (phase === 'mount') {
    renderCount = 0;
  }
}

// Wrap suspicious components
<Profiler id="DocumentProvider" onRender={onRenderCallback}>
  <DocumentProvider>{children}</DocumentProvider>
</Profiler>
```

## 5. Hook Dependency Analyzer

**Custom hook to analyze dependency changes:**

```javascript
import { useEffect, useRef } from 'react';

function useTrackDependencies(hookName, dependencies) {
  const prevDeps = useRef(dependencies);
  
  useEffect(() => {
    const changes = dependencies.map((dep, index) => {
      const prev = prevDeps.current[index];
      const changed = dep !== prev;
      
      if (changed) {
        console.log(`[${hookName}] Dependency ${index} changed:`, {
          from: prev,
          to: dep,
          type: typeof dep,
          isFunction: typeof dep === 'function',
          isObject: typeof dep === 'object' && dep !== null
        });
      }
      
      return { index, changed, prev, current: dep };
    });
    
    const changedCount = changes.filter(c => c.changed).length;
    if (changedCount > 0) {
      console.log(`[${hookName}] ${changedCount} dependencies changed`);
    }
    
    prevDeps.current = dependencies;
  });
}

// Usage in custom hooks:
function useDocumentInitialization(docId, config) {
  useTrackDependencies('useDocumentInitialization', [docId, config]);
  
  // Your hook logic...
}
```

---

# Advanced Tracing Techniques

## 1. State Update Tracing

**Track all dispatch calls and state changes:**

```javascript
// Enhanced useReducer with logging
function useTrackedReducer(reducer, initialState, name = 'Unknown') {
  const wrappedReducer = (state, action) => {
    console.log(`[${name}] Dispatch:`, {
      action,
      currentState: state,
      timestamp: Date.now()
    });
    
    const newState = reducer(state, action);
    
    console.log(`[${name}] State change:`, {
      from: state,
      to: newState,
      action: action.type || action
    });
    
    return newState;
  };
  
  return useReducer(wrappedReducer, initialState);
}

// Usage in DocumentProvider:
const [state, dispatch] = useTrackedReducer(documentReducer, initialState, 'DocumentProvider');
```

## 2. Ref Composition Debugging (Radix UI Issues)

```javascript
import { useEffect, useRef } from 'react';

// Ref tracker to debug Radix UI ref composition issues
function useTrackedRef(name) {
  const ref = useRef(null);
  const setRefCount = useRef(0);
  
  const trackedRef = useCallback((element) => {
    setRefCount.current++;
    console.log(`[REF-${name}] Set #${setRefCount.current}:`, {
      element,
      previousElement: ref.current,
      timestamp: Date.now()
    });
    
    if (setRefCount.current > 100) {
      console.error(`🚨 REF LOOP DETECTED: ${name} ref set ${setRefCount.current} times`);
      console.trace('Ref setting call stack:');
    }
    
    ref.current = element;
  }, [name]);
  
  return [ref, trackedRef];
}

// Usage in components with Radix UI:
function SomeRadixComponent() {
  const [elementRef, trackedSetRef] = useTrackedRef('RadixComponent');
  
  return (
    <RadixPrimitive.Root ref={trackedSetRef}>
      Content
    </RadixPrimitive.Root>
  );
}
```

## 3. Hook Execution Order Tracing

**Track the order and frequency of hook executions:**

```javascript
let globalHookCounter = 0;
const hookExecutionLog = [];

function useHookTracer(hookName) {
  const hookId = useRef(`${hookName}-${++globalHookCounter}`);
  const executionCount = useRef(0);
  
  executionCount.current++;
  
  const logEntry = {
    hookId: hookId.current,
    hookName,
    executionCount: executionCount.current,
    timestamp: Date.now()
  };
  
  hookExecutionLog.push(logEntry);
  
  console.log(`[HOOK-TRACE] ${hookId.current} execution #${executionCount.current}`);
  
  // Detect hook execution loops
  if (executionCount.current > 50) {
    console.error(`🚨 HOOK LOOP: ${hookName} executed ${executionCount.current} times`);
    
    // Show recent execution pattern
    const recentLogs = hookExecutionLog
      .filter(log => log.hookName === hookName)
      .slice(-10);
    console.table(recentLogs);
  }
  
  // Clear old logs periodically
  if (hookExecutionLog.length > 1000) {
    hookExecutionLog.splice(0, 500);
  }
}

// Add to each custom hook:
function useDocumentInitialization() {
  useHookTracer('useDocumentInitialization');
  // Hook logic...
}

function useGroupStatusManager() {
  useHookTracer('useGroupStatusManager');
  // Hook logic...
}
```

## 4. Dependency Array Deep Analysis

**Analyze complex objects in dependency arrays:**

```javascript
import { useEffect, useRef } from 'react';

function useDeepDependencyTracker(name, dependencies) {
  const prevDeps = useRef();
  
  useEffect(() => {
    if (prevDeps.current) {
      dependencies.forEach((dep, index) => {
        const prev = prevDeps.current[index];
        
        if (dep !== prev) {
          console.log(`[DEEP-DEP] ${name}[${index}] changed:`, {
            type: typeof dep,
            isObject: typeof dep === 'object' && dep !== null,
            isFunction: typeof dep === 'function',
            isArray: Array.isArray(dep),
            from: prev,
            to: dep
          });
          
          // Deep comparison for objects
          if (typeof dep === 'object' && dep !== null && typeof prev === 'object' && prev !== null) {
            const currentKeys = Object.keys(dep);
            const prevKeys = Object.keys(prev);
            
            console.log(`[DEEP-DEP] ${name}[${index}] object analysis:`, {
              currentKeys,
              prevKeys,
              keysAdded: currentKeys.filter(k => !prevKeys.includes(k)),
              keysRemoved: prevKeys.filter(k => !currentKeys.includes(k)),
              keysChanged: currentKeys.filter(k => prevKeys.includes(k) && dep[k] !== prev[k])
            });
          }
          
          // Function comparison
          if (typeof dep === 'function') {
            console.log(`[DEEP-DEP] ${name}[${index}] function changed:`, {
              prevName: prev.name || 'anonymous',
              currentName: dep.name || 'anonymous',
              prevString: prev.toString().slice(0, 100),
              currentString: dep.toString().slice(0, 100)
            });
          }
        }
      });
    }
    
    prevDeps.current = dependencies;
  });
}

// Usage in useEffect hooks:
useEffect(() => {
  // Effect logic
}, [dep1, dep2, dep3]);

useDeepDependencyTracker('DocumentProvider-mainEffect', [dep1, dep2, dep3]);
```

## 5. Component Render Chain Analysis

**Track parent-child render cascades:**

```javascript
const renderChain = new Map();

function useRenderChainTracker(componentName, parentName = null) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  
  renderCount.current++;
  const currentTime = Date.now();
  const timeSinceLastRender = currentTime - lastRenderTime.current;
  
  console.log(`[RENDER-CHAIN] ${componentName} render #${renderCount.current}`, {
    parent: parentName,
    timeSinceLastRender,
    timestamp: currentTime
  });
  
  // Track render frequency
  if (!renderChain.has(componentName)) {
    renderChain.set(componentName, []);
  }
  
  const renders = renderChain.get(componentName);
  renders.push(currentTime);
  
  // Keep only recent renders (last 100)
  if (renders.length > 100) {
    renders.splice(0, renders.length - 100);
  }
  
  // Detect rapid re-renders (more than 10 renders in 1 second)
  const recentRenders = renders.filter(time => currentTime - time < 1000);
  if (recentRenders.length > 10) {
    console.error(`🚨 RAPID RENDERS: ${componentName} rendered ${recentRenders.length} times in 1 second`);
  }
  
  lastRenderTime.current = currentTime;
}

// Usage in components:
function DocumentProvider({ children }) {
  useRenderChainTracker('DocumentProvider');
  // Component logic...
}

function ChildComponent({ data }) {
  useRenderChainTracker('ChildComponent', 'DocumentProvider');
  // Component logic...
}
```

## 6. Context Value Stability Analysis

**Verify context values aren't changing unnecessarily:**

```javascript
function useContextStabilityTracker(contextValue, contextName) {
  const prevValue = useRef();
  const changeCount = useRef(0);
  
  useEffect(() => {
    if (prevValue.current !== undefined) {
      if (prevValue.current !== contextValue) {
        changeCount.current++;
        
        console.log(`[CONTEXT-STABILITY] ${contextName} changed #${changeCount.current}:`, {
          from: prevValue.current,
          to: contextValue,
          type: typeof contextValue,
          isShallowEqual: shallowEqual(prevValue.current, contextValue)
        });
        
        // Deep analysis for objects
        if (typeof contextValue === 'object' && contextValue !== null) {
          const prevKeys = Object.keys(prevValue.current || {});
          const currentKeys = Object.keys(contextValue);
          
          console.log(`[CONTEXT-STABILITY] ${contextName} object analysis:`, {
            prevKeys,
            currentKeys,
            keysChanged: currentKeys.filter(key => 
              prevValue.current[key] !== contextValue[key]
            )
          });
        }
      }
    }
    
    prevValue.current = contextValue;
  });
  
  // Warn about frequent changes
  if (changeCount.current > 20) {
    console.warn(`⚠️ ${contextName} context changed ${changeCount.current} times - potential performance issue`);
  }
}

// Helper function for shallow comparison
function shallowEqual(obj1, obj2) {
  if (obj1 === obj2) return true;
  if (!obj1 || !obj2) return false;
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  return keys1.every(key => obj1[key] === obj2[key]);
}

// Usage in context providers:
const contextValue = useMemo(() => ({
  state,
  dispatch,
  // ... other values
}), [state, dispatch]);

useContextStabilityTracker(contextValue, 'DocumentContext');
```

---

# Systematic Investigation Framework

## Phase 1: Initial Assessment

### 1. Set Up Monitoring
```javascript
// Step 1: Add basic render counting to DocumentProvider
console.log('[INVESTIGATION] Starting infinite loop investigation');

let renderCount = 0;
function DocumentProvider({ children }) {
  renderCount++;
  console.log(`[RENDER] DocumentProvider render #${renderCount}`);
  
  // Add early exit if renders exceed threshold
  if (renderCount > 200) {
    console.error('🚨 INVESTIGATION STOPPED: Exceeded 200 renders');
    throw new Error('Infinite loop detected - investigation stopped');
  }
  
  // Your existing component code...
}
```

### 2. Baseline Measurement
```javascript
// Step 2: Identify which renders are problematic
const INVESTIGATION_THRESHOLDS = {
  SUSPICIOUS_RENDER_COUNT: 10,    // More than 10 renders is suspicious
  CRITICAL_RENDER_COUNT: 50,      // More than 50 renders is critical
  RENDER_TIME_WINDOW: 1000        // Within 1 second
};

function useInvestigationBaseline(componentName) {
  const renderTimes = useRef([]);
  const currentTime = Date.now();
  
  // Add current render time
  renderTimes.current.push(currentTime);
  
  // Keep only recent renders within time window
  renderTimes.current = renderTimes.current.filter(
    time => currentTime - time < INVESTIGATION_THRESHOLDS.RENDER_TIME_WINDOW
  );
  
  const renderCount = renderTimes.current.length;
  
  if (renderCount > INVESTIGATION_THRESHOLDS.CRITICAL_RENDER_COUNT) {
    console.error(`🚨 CRITICAL: ${componentName} rendered ${renderCount} times in ${INVESTIGATION_THRESHOLDS.RENDER_TIME_WINDOW}ms`);
    return 'CRITICAL';
  } else if (renderCount > INVESTIGATION_THRESHOLDS.SUSPICIOUS_RENDER_COUNT) {
    console.warn(`⚠️ SUSPICIOUS: ${componentName} rendered ${renderCount} times in ${INVESTIGATION_THRESHOLDS.RENDER_TIME_WINDOW}ms`);
    return 'SUSPICIOUS';
  }
  
  return 'NORMAL';
}
```

## Phase 2: Hook Isolation

### 3. Binary Search Approach
```javascript
// Step 3: Systematically disable hooks to find the culprit
const INVESTIGATION_FLAGS = {
  ENABLE_GROUP_STATUS_MANAGER: true,
  ENABLE_COMPONENT_REGISTRATION: true,
  ENABLE_DOCUMENT_VERSIONING: true,
  ENABLE_DOCUMENT_INITIALIZATION: true,
  ENABLE_COMPONENT_UPDATER: true
};

function DocumentProvider({ children }) {
  // Controlled hook activation for investigation
  const groupStatus = INVESTIGATION_FLAGS.ENABLE_GROUP_STATUS_MANAGER 
    ? useGroupStatusManager(state) 
    : null;
    
  const componentReg = INVESTIGATION_FLAGS.ENABLE_COMPONENT_REGISTRATION 
    ? useComponentRegistration(state) 
    : null;
    
  const docVersioning = INVESTIGATION_FLAGS.ENABLE_DOCUMENT_VERSIONING 
    ? useDocumentVersioning(state) 
    : null;
    
  const docInit = INVESTIGATION_FLAGS.ENABLE_DOCUMENT_INITIALIZATION 
    ? useDocumentInitialization(state) 
    : null;
    
  const componentUpd = INVESTIGATION_FLAGS.ENABLE_COMPONENT_UPDATER 
    ? useComponentUpdater(state) 
    : null;
  
  console.log('[INVESTIGATION] Active hooks:', Object.entries(INVESTIGATION_FLAGS).filter(([_, enabled]) => enabled).map(([name]) => name));
  
  // Rest of component...
}
```

### 4. Hook-by-Hook Analysis
```javascript
// Step 4: Individual hook investigation wrapper
function useInvestigationWrapper(hookName, hookFunction, ...args) {
  const [isEnabled, setIsEnabled] = useState(true);
  const errorCount = useRef(0);
  
  useEffect(() => {
    const handleError = (error) => {
      if (error.message.includes('Maximum update depth')) {
        errorCount.current++;
        console.error(`🚨 ${hookName} caused infinite loop error #${errorCount.current}`);
        
        if (errorCount.current >= 3) {
          console.error(`🔴 DISABLING ${hookName} - Multiple infinite loop errors detected`);
          setIsEnabled(false);
        }
      }
    };
    
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, [hookName]);
  
  if (!isEnabled) {
    console.warn(`⚠️ ${hookName} is DISABLED due to errors`);
    return null;
  }
  
  try {
    return hookFunction(...args);
  } catch (error) {
    console.error(`❌ ${hookName} threw error:`, error);
    throw error;
  }
}

// Usage:
const groupStatus = useInvestigationWrapper('useGroupStatusManager', useGroupStatusManager, state);
```

## Phase 3: Dependency Analysis

### 5. Dependency Stability Check
```javascript
// Step 5: Check all dependencies for stability
function useDependencyStabilityAnalysis(name, deps) {
  const stableRefs = useRef({});
  const instabilityCount = useRef({});
  
  deps.forEach((dep, index) => {
    const key = `${name}_${index}`;
    
    if (stableRefs.current[key] !== dep) {
      instabilityCount.current[key] = (instabilityCount.current[key] || 0) + 1;
      
      console.log(`[STABILITY] ${name}[${index}] changed (count: ${instabilityCount.current[key]}):`, {
        from: stableRefs.current[key],
        to: dep,
        type: typeof dep,
        isFunction: typeof dep === 'function'
      });
      
      if (instabilityCount.current[key] > 100) {
        console.error(`🚨 UNSTABLE DEPENDENCY: ${name}[${index}] changed ${instabilityCount.current[key]} times`);
      }
      
      stableRefs.current[key] = dep;
    }
  });
}

// Apply to all useCallback and useMemo dependencies
const updateGroupStatus = useCallback((groupId, status) => {
  // Function logic
}, [dependency1, dependency2]);

useDependencyStabilityAnalysis('updateGroupStatus', [dependency1, dependency2]);
```

## Phase 4: State Flow Analysis

### 6. Action Dispatch Tracing
```javascript
// Step 6: Track all dispatch calls and state changes
function useDispatchTracer(dispatch, name = 'Unknown') {
  return useCallback((action) => {
    console.log(`[DISPATCH-TRACE] ${name} dispatching:`, action);
    
    const startTime = performance.now();
    const result = dispatch(action);
    const endTime = performance.now();
    
    console.log(`[DISPATCH-TRACE] ${name} dispatch completed in ${endTime - startTime}ms`);
    
    // Check if dispatch is being called too frequently
    if (!useDispatchTracer.lastDispatchTime) {
      useDispatchTracer.lastDispatchTime = {};
    }
    
    const lastTime = useDispatchTracer.lastDispatchTime[name] || 0;
    const timeSinceLastDispatch = startTime - lastTime;
    
    if (timeSinceLastDispatch < 10) { // Less than 10ms between dispatches
      console.warn(`⚠️ RAPID DISPATCH: ${name} dispatched twice within ${timeSinceLastDispatch}ms`);
    }
    
    useDispatchTracer.lastDispatchTime[name] = startTime;
    
    return result;
  }, [dispatch, name]);
}

// Usage:
const trackedDispatch = useDispatchTracer(dispatch, 'DocumentProvider');
```

## Phase 7: Systematic Testing Protocol

### 7. Investigation Checklist
```javascript
// Step 7: Automated investigation runner
class InfiniteLoopInvestigator {
  constructor(componentName = 'DocumentProvider') {
    this.componentName = componentName;
    this.tests = [];
    this.results = {};
  }
  
  addTest(name, testFn) {
    this.tests.push({ name, testFn });
  }
  
  async runInvestigation() {
    console.log(`🔍 Starting investigation of ${this.componentName}`);
    
    for (const test of this.tests) {
      console.log(`\n📋 Running test: ${test.name}`);
      
      try {
        const result = await test.testFn();
        this.results[test.name] = { status: 'PASS', result };
        console.log(`✅ ${test.name}: PASSED`);
      } catch (error) {
        this.results[test.name] = { status: 'FAIL', error: error.message };
        console.error(`❌ ${test.name}: FAILED -`, error.message);
      }
    }
    
    this.generateReport();
  }
  
  generateReport() {
    console.log(`\n📊 Investigation Report for ${this.componentName}`);
    console.log('=' .repeat(50));
    
    Object.entries(this.results).forEach(([testName, result]) => {
      console.log(`${result.status === 'PASS' ? '✅' : '❌'} ${testName}: ${result.status}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
  }
}

// Usage:
const investigator = new InfiniteLoopInvestigator('DocumentProvider');

investigator.addTest('Context Value Stability', () => {
  // Test if context value is changing unnecessarily
});

investigator.addTest('Hook Dependencies', () => {
  // Test if hook dependencies are stable
});

investigator.addTest('Ref Composition', () => {
  // Test for Radix UI ref issues
});

// Run when component mounts
useEffect(() => {
  investigator.runInvestigation();
}, []);
```

## Investigation Workflow Summary

1. **Start with Phase 1** - Set up basic monitoring
2. **Run Phase 2** - Use binary search to isolate problematic hooks
3. **Apply Phase 3** - Analyze dependency stability
4. **Execute Phase 4** - Trace state changes and dispatches  
5. **Use Phase 7** - Run systematic testing protocol

### Quick Commands for Investigation

```javascript
// Emergency stops
window.STOP_INVESTIGATION = true; // Halt all debugging

// Quick toggles for hook isolation
window.DEBUG_HOOKS = {
  groupStatus: true,
  componentReg: false,  // Start with some disabled
  docVersioning: false,
  docInit: true,
  componentUpd: true
};

// Instant analysis
window.analyzeInfiniteLoop = () => {
  console.log('Current render count:', renderCount);
  console.log('Hook execution log:', hookExecutionLog.slice(-20));
  console.table(Object.entries(window.DEBUG_HOOKS));
};
```

---

# Practical Debugging Utilities (June 2025)

## Quick Debug Setup for DocumentProvider

### 1. Drop-in Investigation Helper
```javascript
// File: /apps/customer/debug/infinite-loop-debugger.js
// Copy this entire code block and import it in DocumentContext.tsx

export class DocumentProviderDebugger {
  constructor() {
    this.renderCount = 0;
    this.hookCalls = new Map();
    this.stateChanges = [];
    this.isDebugging = process.env.NODE_ENV === 'development';
    this.maxRenders = 100;
    
    if (this.isDebugging) {
      console.log('🔍 DocumentProvider Debugger initialized');
      this.setupGlobalCommands();
    }
  }
  
  trackRender(componentName = 'DocumentProvider') {
    if (!this.isDebugging) return;
    
    this.renderCount++;
    console.log(`[RENDER-${this.renderCount}] ${componentName}`);
    
    if (this.renderCount > this.maxRenders) {
      console.error(`🚨 INFINITE LOOP DETECTED: ${componentName} exceeded ${this.maxRenders} renders`);
      throw new Error(`Infinite loop protection: ${componentName} rendered too many times`);
    }
  }
  
  trackHook(hookName, deps = []) {
    if (!this.isDebugging) return;
    
    const callCount = (this.hookCalls.get(hookName) || 0) + 1;
    this.hookCalls.set(hookName, callCount);
    
    console.log(`[HOOK-${callCount}] ${hookName}`, { 
      dependencies: deps,
      callCount 
    });
    
    if (callCount > 50) {
      console.error(`🚨 HOOK LOOP: ${hookName} called ${callCount} times`);
    }
  }
  
  trackState(actionType, oldState, newState) {
    if (!this.isDebugging) return;
    
    this.stateChanges.push({
      actionType,
      oldState: JSON.parse(JSON.stringify(oldState)),
      newState: JSON.parse(JSON.stringify(newState)),
      timestamp: Date.now()
    });
    
    console.log(`[STATE] ${actionType}:`, {
      from: oldState,
      to: newState
    });
    
    // Keep only last 100 state changes
    if (this.stateChanges.length > 100) {
      this.stateChanges = this.stateChanges.slice(-100);
    }
  }
  
  setupGlobalCommands() {
    window.debugDocumentProvider = {
      getReport: () => this.generateReport(),
      reset: () => this.reset(),
      setMaxRenders: (max) => { this.maxRenders = max; },
      pause: () => { this.isDebugging = false; },
      resume: () => { this.isDebugging = true; }
    };
  }
  
  generateReport() {
    console.log('\n📊 DocumentProvider Debug Report');
    console.log('='.repeat(50));
    console.log(`Total renders: ${this.renderCount}`);
    console.log(`Hooks called:`, Object.fromEntries(this.hookCalls));
    console.log(`Recent state changes (last 10):`, this.stateChanges.slice(-10));
    
    return {
      renderCount: this.renderCount,
      hookCalls: Object.fromEntries(this.hookCalls),
      recentStateChanges: this.stateChanges.slice(-10)
    };
  }
  
  reset() {
    this.renderCount = 0;
    this.hookCalls.clear();
    this.stateChanges = [];
    console.log('🔄 Debugger reset');
  }
}
```

### 2. Integration with DocumentProvider
```javascript
// In DocumentContext.tsx - add at the top
import { DocumentProviderDebugger } from '../debug/infinite-loop-debugger';

const debugger = new DocumentProviderDebugger();

export function DocumentProvider({ children }) {
  debugger.trackRender('DocumentProvider');
  
  // Wrap your reducer
  const [state, originalDispatch] = useReducer(documentReducer, initialState);
  
  const dispatch = useCallback((action) => {
    const oldState = state;
    originalDispatch(action);
    // Note: newState will be the same as oldState here due to timing
    // Better to track in the reducer itself
    debugger.trackState(action.type, oldState, state);
  }, [state]);
  
  // Track each hook
  const groupStatus = useGroupStatusManager(state);
  debugger.trackHook('useGroupStatusManager', [state]);
  
  const componentReg = useComponentRegistration(state);
  debugger.trackHook('useComponentRegistration', [state]);
  
  // ... other hooks with similar tracking
  
  // Rest of your component...
}
```

### 3. Browser Console Commands
```javascript
// Available in browser console during debugging:

// Get current debug status
window.debugDocumentProvider.getReport();

// Reset counters
window.debugDocumentProvider.reset();

// Pause debugging temporarily
window.debugDocumentProvider.pause();

// Resume debugging
window.debugDocumentProvider.resume();

// Set custom render limit
window.debugDocumentProvider.setMaxRenders(200);
```

## Specific DocumentProvider Utilities

### 4. Context Value Comparator
```javascript
// Add this hook to track context value changes
function useContextValueDebugger(value, name = 'Context') {
  const prevValue = useRef();
  const changeCount = useRef(0);
  
  useEffect(() => {
    if (prevValue.current !== undefined) {
      const hasChanged = prevValue.current !== value;
      if (hasChanged) {
        changeCount.current++;
        console.log(`[CONTEXT-CHANGE-${changeCount.current}] ${name}:`, {
          previous: prevValue.current,
          current: value,
          shallowEqual: shallowCompare(prevValue.current, value)
        });
      }
    }
    prevValue.current = value;
  });
  
  return changeCount.current;
}

function shallowCompare(obj1, obj2) {
  if (obj1 === obj2) return true;
  if (!obj1 || !obj2) return false;
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  return keys1.every(key => obj1[key] === obj2[key]);
}

// Usage in DocumentProvider:
const contextValue = useMemo(() => ({
  state,
  dispatch,
  updateGroupStatus,
  registerComponent
}), [state, dispatch, updateGroupStatus, registerComponent]);

const changeCount = useContextValueDebugger(contextValue, 'DocumentContext');
```

### 5. Hook Dependency Inspector
```javascript
// Detailed dependency analysis for specific hooks
function useHookDependencyInspector(hookName, dependencies) {
  const prevDeps = useRef([]);
  const inspectionCount = useRef(0);
  
  useEffect(() => {
    inspectionCount.current++;
    
    dependencies.forEach((dep, index) => {
      const prevDep = prevDeps.current[index];
      const hasChanged = dep !== prevDep;
      
      if (hasChanged) {
        console.log(`[DEP-CHANGE] ${hookName}[${index}] changed:`, {
          inspectionCount: inspectionCount.current,
          from: prevDep,
          to: dep,
          type: typeof dep,
          // For functions, show if they're the same reference
          isSameFunction: typeof dep === 'function' && dep === prevDep,
          // For objects, do shallow comparison
          isShallowEqual: typeof dep === 'object' && dep !== null 
            ? shallowCompare(prevDep, dep) 
            : false
        });
        
        // Special handling for functions
        if (typeof dep === 'function') {
          console.log(`[FUNC-ANALYSIS] ${hookName}[${index}]:`, {
            name: dep.name || 'anonymous',
            toString: dep.toString().slice(0, 200) + '...',
            sameReference: dep === prevDep
          });
        }
      }
    });
    
    prevDeps.current = [...dependencies];
  });
}

// Usage in DocumentProvider hooks:
const updateGroupStatus = useCallback((groupId, status) => {
  // Your logic
}, [state.groups, dispatch]);

useHookDependencyInspector('updateGroupStatus', [state.groups, dispatch]);
```

### 6. Emergency Stop Switch
```javascript
// Add emergency stop functionality
let EMERGENCY_STOP = false;

function useEmergencyStop(componentName) {
  useEffect(() => {
    if (EMERGENCY_STOP) {
      console.error(`🛑 EMERGENCY STOP: ${componentName} stopped rendering`);
      throw new Error(`Emergency stop activated for ${componentName}`);
    }
  });
}

// Global commands
window.emergencyStop = () => {
  EMERGENCY_STOP = true;
  console.log('🛑 Emergency stop activated - components will stop rendering');
};

window.resume = () => {
  EMERGENCY_STOP = false;
  console.log('▶️ Emergency stop deactivated - components can render normally');
};

// Usage in DocumentProvider:
function DocumentProvider({ children }) {
  useEmergencyStop('DocumentProvider');
  // Rest of component...
}
```

### 7. Performance Monitor
```javascript
// Monitor render performance
function useRenderPerformanceMonitor(componentName) {
  const renderStartTime = useRef(performance.now());
  const renderTimes = useRef([]);
  
  useLayoutEffect(() => {
    const renderEndTime = performance.now();
    const renderDuration = renderEndTime - renderStartTime.current;
    
    renderTimes.current.push(renderDuration);
    
    console.log(`[PERF] ${componentName} render took ${renderDuration.toFixed(2)}ms`);
    
    // Keep last 50 render times
    if (renderTimes.current.length > 50) {
      renderTimes.current = renderTimes.current.slice(-50);
    }
    
    // Calculate averages
    const avgRenderTime = renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length;
    
    if (renderDuration > 100) {
      console.warn(`⚠️ SLOW RENDER: ${componentName} took ${renderDuration.toFixed(2)}ms (avg: ${avgRenderTime.toFixed(2)}ms)`);
    }
    
    // Reset for next render
    renderStartTime.current = performance.now();
  });
  
  return {
    currentRenderTime: renderTimes.current[renderTimes.current.length - 1],
    averageRenderTime: renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length,
    allRenderTimes: [...renderTimes.current]
  };
}

// Usage:
const perfMetrics = useRenderPerformanceMonitor('DocumentProvider');
```

---

# Modern Solutions and Best Practices (June 2025)

## React 19+ Specific Considerations

### 1. React Compiler Compatibility
```javascript
// Note: why-did-you-render may be incompatible with React Compiler
// Alternative approach for React Compiler environments:

// Use React's built-in performance monitoring
import { unstable_trace as trace } from 'react';

function DocumentProvider({ children }) {
  return trace('DocumentProvider', performance.now(), () => {
    // Your component logic
    
    // React Compiler will optimize this automatically
    const contextValue = useMemo(() => ({
      state,
      dispatch,
      // ... other values
    }), [state, dispatch]);
    
    return (
      <DocumentContext.Provider value={contextValue}>
        {children}
      </DocumentContext.Provider>
    );
  });
}
```

### 2. React DevTools 2025 Features
```javascript
// Enhanced profiler support for complex components
function useReact19Profiler(componentName) {
  const profilerId = `${componentName}-${Date.now()}`;
  
  return {
    ProfilerWrapper: ({ children }) => (
      <Profiler
        id={profilerId}
        onRender={(id, phase, actualDuration, baseDuration, startTime, commitTime) => {
          // Enhanced logging for React 19
          console.log(`[REACT-19-PROFILER] ${id}`, {
            phase,
            actualDuration,
            baseDuration,
            startTime,
            commitTime,
            // New React 19 features
            renderCancellationCount: window.__REACT_DEVTOOLS_GLOBAL_HOOK__?.rendererInterfaces?.get(1)?.canceledRenderCount || 0
          });
        }}
      >
        {children}
      </Profiler>
    ),
    profilerId
  };
}
```

## Advanced Infinite Loop Prevention

### 3. Circuit Breaker Pattern for React Components
```javascript
// Modern circuit breaker pattern for infinite loops
class ComponentCircuitBreaker {
  constructor(componentName, options = {}) {
    this.componentName = componentName;
    this.maxRenders = options.maxRenders || 100;
    this.timeWindow = options.timeWindow || 5000; // 5 seconds
    this.resetTimeout = options.resetTimeout || 30000; // 30 seconds
    
    this.renderCount = 0;
    this.windowStart = Date.now();
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.lastFailure = null;
  }
  
  canRender() {
    const now = Date.now();
    
    // Reset window if time has passed
    if (now - this.windowStart > this.timeWindow) {
      this.renderCount = 0;
      this.windowStart = now;
      
      // Try to close circuit if it was open long enough
      if (this.state === 'OPEN' && now - this.lastFailure > this.resetTimeout) {
        this.state = 'HALF_OPEN';
        console.log(`🔄 Circuit breaker for ${this.componentName} moving to HALF_OPEN`);
      }
    }
    
    if (this.state === 'OPEN') {
      throw new Error(`Circuit breaker OPEN for ${this.componentName}. Component disabled due to infinite loop.`);
    }
    
    this.renderCount++;
    
    if (this.renderCount > this.maxRenders) {
      this.state = 'OPEN';
      this.lastFailure = now;
      console.error(`🔴 Circuit breaker OPENED for ${this.componentName}. Infinite loop detected.`);
      throw new Error(`Circuit breaker triggered for ${this.componentName}. Too many renders.`);
    }
    
    if (this.state === 'HALF_OPEN' && this.renderCount > 5) {
      this.state = 'CLOSED';
      console.log(`✅ Circuit breaker for ${this.componentName} is now CLOSED`);
    }
    
    return true;
  }
}

// Usage in DocumentProvider:
const circuitBreaker = new ComponentCircuitBreaker('DocumentProvider', {
  maxRenders: 50,
  timeWindow: 3000,
  resetTimeout: 60000
});

function DocumentProvider({ children }) {
  circuitBreaker.canRender();
  // Rest of component...
}
```

### 4. Modern State Management Patterns
```javascript
// Use modern state management to prevent infinite loops
import { useOptimistic, use } from 'react';

function useOptimisticDocumentState(initialState) {
  // React 19's useOptimistic for preventing state loops
  const [optimisticState, addOptimistic] = useOptimistic(
    initialState,
    (state, optimisticUpdate) => {
      // Validate update to prevent loops
      if (JSON.stringify(state) === JSON.stringify(optimisticUpdate)) {
        console.warn('⚠️ Prevented redundant state update');
        return state;
      }
      return { ...state, ...optimisticUpdate };
    }
  );
  
  return [optimisticState, addOptimistic];
}

// Modern async state handling
function useAsyncDocumentInit(docId) {
  const promise = useMemo(() => {
    return fetchDocumentData(docId).catch(error => {
      console.error('Document init failed:', error);
      return null;
    });
  }, [docId]);
  
  // React 19's use() hook for promises
  const documentData = use(promise);
  
  return documentData;
}
```

### 5. TypeScript Integration for Better Debugging
```typescript
// Enhanced TypeScript types for debugging
interface DebugInfo {
  renderCount: number;
  lastRender: number;
  hookCalls: Map<string, number>;
  stateChanges: StateChange[];
}

interface StateChange {
  actionType: string;
  timestamp: number;
  payload: unknown;
}

// Generic hook debugger with TypeScript
function useTypedHookDebugger<T extends readonly unknown[]>(
  hookName: string,
  dependencies: T
): void {
  const prevDeps = useRef<T>();
  
  useEffect(() => {
    if (prevDeps.current) {
      dependencies.forEach((dep, index) => {
        if (dep !== prevDeps.current?.[index]) {
          console.log(`[TS-DEBUG] ${hookName}[${index}] changed:`, {
            from: prevDeps.current?.[index],
            to: dep,
            type: typeof dep,
          });
        }
      });
    }
    prevDeps.current = dependencies;
  });
}

// Usage with full type safety:
const updateGroupStatus = useCallback((groupId: string, status: GroupStatus) => {
  // Implementation
}, [state.groups, dispatch] as const);

useTypedHookDebugger('updateGroupStatus', [state.groups, dispatch] as const);
```

## Performance Optimization Patterns

### 6. Selective Component Rendering
```javascript
// Modern selective rendering to prevent cascade re-renders
import { memo, useMemo } from 'react';

const MemoizedDocumentProvider = memo(function DocumentProvider({ children, ...props }) {
  // Only re-render if specific props change
  const stableContextValue = useMemo(() => {
    return {
      // Only include truly necessary values
      documentId: props.documentId,
      isEditing: props.isEditing,
      // Avoid including entire state object
    };
  }, [props.documentId, props.isEditing]);
  
  return (
    <DocumentContext.Provider value={stableContextValue}>
      {children}
    </DocumentContext.Provider>
  );
}, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.documentId === nextProps.documentId &&
    prevProps.isEditing === nextProps.isEditing
  );
});
```

### 7. Modern Hook Patterns for Stability
```javascript
// Stable function references using modern patterns
function useStableCallback<T extends (...args: any[]) => any>(callback: T): T {
  const callbackRef = useRef(callback);
  
  // Update the ref on every render but keep the function reference stable
  useLayoutEffect(() => {
    callbackRef.current = callback;
  });
  
  return useCallback(((...args) => callbackRef.current(...args)) as T, []);
}

// Usage in DocumentProvider:
const updateGroupStatus = useStableCallback((groupId: string, status: string) => {
  dispatch({ type: 'UPDATE_GROUP_STATUS', payload: { groupId, status } });
});

// This callback will never change reference, preventing dependency loops
```

## Best Practices Summary (June 2025)

### 8. Debugging Checklist
```javascript
// Quick debugging checklist for infinite loops
const DEBUGGING_CHECKLIST = {
  '1. Circuit Breaker': 'Add ComponentCircuitBreaker to prevent runaway renders',
  '2. Profiler Wrapper': 'Wrap component with React.Profiler for monitoring',
  '3. Stable Callbacks': 'Use useStableCallback for functions in dependencies',
  '4. Memoized Context': 'Ensure context values are properly memoized',
  '5. Dependency Analysis': 'Use useTypedHookDebugger on all custom hooks',
  '6. Emergency Stop': 'Have window.emergencyStop() available in console',
  '7. Performance Monitor': 'Track render times with useRenderPerformanceMonitor',
  '8. State Validation': 'Validate state changes to prevent redundant updates'
};

// Print checklist in console
console.table(DEBUGGING_CHECKLIST);
```



### 9. Production Safety Measures
```javascript
// Production-safe debugging (automatically disabled in production)
const createProductionSafeDebugger = (componentName: string) => {
  if (process.env.NODE_ENV === 'production') {
    return {
      trackRender: () => {},
      trackHook: () => {},N
      trackState: () => {},
      generateReport: () => null
    };
  }
  
  return new DocumentProviderDebugger();
};

// Graceful degradation for debugging tools
const useConditionalDebugger = (enabled: boolean = process.env.NODE_ENV === 'development') => {
  return useMemo(() => {
    if (!enabled) return null;
    return new DocumentProviderDebugger();
  }, [enabled]);
};
```

This comprehensive debugging toolkit provides everything needed to identify and resolve the DocumentProvider infinite loop issue, using the latest React patterns and debugging techniques available in June 2025.
