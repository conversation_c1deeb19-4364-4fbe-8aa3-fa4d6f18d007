'use client'

import React, { useC<PERSON>back, useEffect, useState } from 'react'
import { Button } from '@ui/components/ui/button'
import { Input } from '@ui/components/ui/input'
import { ScrollArea } from '@ui/components/ui/scroll-area'
import {
  Clock,
  Edit3,
  FileText,
  FolderOpen,
  Loader2,
  MoreHorizontal,
  Plus,
  Search,
  Share2,
  Trash2,
  User,
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@ui/hooks/use-toast'
import { formatTimeAgo } from '@utils/date-utils'
import { validateDocumentCreationParams } from '@/utils/document-utils'

interface Document {
  id: string
  title: string
  createdAt: Date
  updatedAt: Date
  author: {
    id: string
    name: string
    email?: string
    avatar?: string
  }
  isOwner: boolean
  metadata?: any
}


interface DocumentListProps {
  currentUser?: {
    id: string
    name: string
    email?: string
    avatar?: string
  }
  onSelectDocument?: (documentId: string) => void
  onCreateDocument?: (entityId: string, runId: string) => void
  selectedDocumentId?: string
  className?: string
}

export function DocumentList({
  currentUser,
  onSelectDocument,
  onCreateDocument,
  selectedDocumentId,
  className
}: DocumentListProps) {
  const [documents, setDocuments] = useState<Document[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [showShared, setShowShared] = useState(false)

  const { toast } = useToast()
  const supabase = createClient()

  // Load documents from Supabase
  const loadDocuments = useCallback(async () => {
    setLoading(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      let query = supabase
        .from('doc_documents')
        .select(`
          id,
          title,
          created_at,
          updated_at,
          created_by,
          updated_by,
          metadata,
          entity_id,
          run_id
        `)
        .order('updated_at', { ascending: false })

      // Filter based on showShared
      if (!showShared) {
        // Only show documents created by current user
        query = query.eq('created_by', user.id)
      }
      // If showShared is true, RLS will handle showing accessible documents

      const { data: documents, error } = await query

      if (error) {
        throw error
      }

      // Transform documents to match expected format
      const transformedDocuments = documents?.map(doc => ({
        id: doc.id,
        title: doc.title || 'Untitled Document',
        createdAt: new Date(doc.created_at || new Date()),
        updatedAt: new Date(doc.updated_at || new Date()),
        author: {
          id: doc.created_by || 'unknown',
          name: doc.created_by === user.id
            ? (user.user_metadata?.name || user.email || 'You')
            : 'Unknown User',
          email: doc.created_by === user.id ? user.email : '<EMAIL>',
          avatar: doc.created_by === user.id ? user.user_metadata?.avatar_url : undefined
        },
        isOwner: doc.created_by === user.id,
        metadata: doc.metadata || {}
      })) || []

      setDocuments(transformedDocuments)
    } catch (error) {
      console.error('Error loading documents:', error)
      toast({
        title: 'Error',
        description: 'Failed to load documents',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [showShared, toast, supabase])

  // Load documents on mount and set up real-time subscription
  useEffect(() => {
    loadDocuments()

    // Set up real-time subscription for documents
    const channel = supabase
      .channel('doc_documents')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'doc_documents',
        },
        () => {
          // Reload documents when changes occur
          loadDocuments()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [loadDocuments, supabase])

  // Filter documents based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredDocuments(documents)
    } else {
      const filtered = documents.filter(doc =>
        doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.author.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredDocuments(filtered)
    }
  }, [documents, searchQuery])

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }


  const handleCreateDocument = async (entityId: string, runId: string) => {
    if (onCreateDocument) {
      onCreateDocument(entityId, runId)
    } else {
      // Default create behavior
      try {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
          throw new Error('User not authenticated')
        }

        const { entityId: validatedEntityId, runId: actualRunId } = await validateDocumentCreationParams(entityId, runId)

        // Generate a unique document ID
        const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        const { data: document, error } = await supabase
          .from('doc_documents')
          .insert({
            id: documentId,
            title: 'Untitled Document',
            content: '',
            entity_id: validatedEntityId,
            run_id: actualRunId,
            created_by: user.id,
            updated_by: user.id,
            metadata: {}
          })
          .select()
          .single()

        if (error) {
          throw error
        }

        toast({
          title: 'Success',
          description: 'Document created successfully'
        })

        // Reload documents and select the new one
        loadDocuments()
        if (onSelectDocument) {
          onSelectDocument(document.id)
        }
      } catch (error) {
        console.error('Error creating document:', error)
        toast({
          title: 'Error',
          description: 'Failed to create document',
          variant: 'destructive'
        })
      }
    }
  }

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const { error } = await supabase
        .from('doc_documents')
        .delete()
        .eq('id', documentId)

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Document deleted successfully'
      })

      // Reload documents
      loadDocuments()
    } catch (error) {
      console.error('Error deleting document:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete document',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className={cn('w-80 bg-background border-r flex flex-col h-full', className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold">Documents</h3>
          <Button 
            size="sm" 
            onClick={() => {
              // Document creation requires entityId and runId - parent component should handle this
              if (onCreateDocument) {
                // This will cause a TypeScript error since we don't have the required parameters
                // Parent components should provide their own "New" button with proper entity/run context
                throw new Error('Document creation requires entityId and runId parameters')
              }
            }}
            disabled={true}
            title="Document creation requires entity and run context"
          >
            <Plus className="w-3 h-3 mr-1" />
            New
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>

        {/* Filter Tabs */}
        <div className="flex gap-1 mt-3">
          <Button
            size="sm"
            variant={!showShared ? 'default' : 'outline'}
            onClick={() => setShowShared(false)}
            className="flex-1"
          >
            My Documents
          </Button>
          <Button
            size="sm"
            variant={showShared ? 'default' : 'outline'}
            onClick={() => setShowShared(true)}
            className="flex-1"
          >
            Shared
          </Button>
        </div>
      </div>

      {/* Documents List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {loading ? (
            <div className="text-center text-muted-foreground py-8">
              <Loader2 className="w-8 h-8 mx-auto mb-2 animate-spin" />
              <p className="text-sm">Loading documents...</p>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <FolderOpen className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">
                {searchQuery ? 'No documents found' : showShared ? 'No shared documents' : 'No documents yet'}
              </p>
              <p className="text-xs">
                {!searchQuery && !showShared && 'Create your first document to get started'}
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredDocuments.map((document) => (
                <div
                  key={document.id}
                  className={cn(
                    'p-3 rounded-lg border transition-colors cursor-pointer group',
                    selectedDocumentId === document.id
                      ? 'border-primary bg-primary/5'
                      : 'border-transparent hover:bg-muted/50'
                  )}
                  onClick={() => onSelectDocument?.(document.id)}
                >
                  <div className="flex items-start gap-3">
                    <FileText className="w-4 h-4 mt-0.5 text-muted-foreground" />

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-sm font-medium truncate">
                          {document.title}
                        </h4>
                        {!document.isOwner && (
                          <Share2 className="w-3 h-3 text-muted-foreground" />
                        )}
                      </div>

                      <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
                        <User className="w-3 h-3" />
                        <span className="truncate">{document.author.name}</span>
                      </div>

                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        <span>Updated {formatTimeAgo(document.updatedAt)}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="w-3 h-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onSelectDocument?.(document.id)}>
                          <Edit3 className="w-3 h-3 mr-2" />
                          Open
                        </DropdownMenuItem>
                        {document.isOwner && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteDocument(document.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="w-3 h-3 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
