import { Editor } from '@tiptap/react'
import { saveAs } from 'file-saver'
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx'
import {
  writeDocx,
  DocxSerializer,
  defaultNodes,
  defaultMarks
} from 'prosemirror-docx'

export type ExportFormat = 'pdf' | 'docx' | 'md' | 'html'

// Custom node serializer for prosemirror-docx
const nodeSerializer = {
  ...defaultNodes,
  hardBreak: defaultNodes.hard_break,
  codeBlock: defaultNodes.code_block,
  orderedList: defaultNodes.ordered_list,
  listItem: defaultNodes.list_item,
  bulletList: defaultNodes.bullet_list,
  horizontalRule: defaultNodes.horizontal_rule,
  image(state: any, node: any) {
    // For now, skip images in DOCX export
    // TODO: Implement proper image handling
    state.renderInline(node)
    state.closeBlock(node)
  },
  // Handle custom nodes
  reportSection(state: any, node: any) {
    state.renderInline(node)
    state.closeBlock(node)
  },
  reportSubSection(state: any, node: any) {
    state.renderInline(node)
    state.closeBlock(node)
  },
  citation(state: any, node: any) {
    state.renderInline(node)
    state.closeBlock(node)
  },
  references(state: any, node: any) {
    state.renderInline(node)
    state.closeBlock(node)
  },
  chart(state: any, node: any) {
    // Skip charts in DOCX export
    state.renderInline(node)
    state.closeBlock(node)
  }
}

const docxSerializer = new DocxSerializer(nodeSerializer, defaultMarks)

/**
 * Export document using prosemirror-docx with custom docx fallback
 */
async function exportToDocxFallback(editor: Editor, filename: string = 'document.docx'): Promise<void> {
  try {
    // Try prosemirror-docx first
    const opts = {
      getImageBuffer(src: string): Uint8Array {
        // Return empty buffer for now
        // TODO: Implement proper image fetching
        return new Uint8Array(0)
      }
    }

    const wordDocument = docxSerializer.serialize(editor.state.doc, opts)

    await writeDocx(wordDocument, (buffer) => {
      saveAs(new Blob([buffer]), filename)
    })
  } catch (prosemirrorError) {
    console.warn('prosemirror-docx failed, falling back to custom DOCX generation:', prosemirrorError)

    // Fallback to custom DOCX generation using docx library
    try {
      await exportToCustomDocx(editor, filename)
    } catch (customError) {
      console.error('Both DOCX export methods failed:', { prosemirrorError, customError })
      throw new Error('Failed to export document to DOCX format')
    }
  }
}

/**
 * Custom DOCX export using docx library
 */
async function exportToCustomDocx(editor: Editor, filename: string = 'document.docx'): Promise<void> {
  const json = editor.getJSON()
  const children: any[] = []

  // Convert TipTap JSON to DOCX elements
  const processNode = (node: any): any[] => {
    const elements: any[] = []

    switch (node.type) {
      case 'heading':
        const level = node.attrs?.level || 1
        const headingLevel = level === 1 ? HeadingLevel.HEADING_1 :
                           level === 2 ? HeadingLevel.HEADING_2 :
                           level === 3 ? HeadingLevel.HEADING_3 :
                           level === 4 ? HeadingLevel.HEADING_4 :
                           level === 5 ? HeadingLevel.HEADING_5 :
                           HeadingLevel.HEADING_6

        elements.push(new Paragraph({
          heading: headingLevel,
          children: processInlineContent(node.content || [])
        }))
        break

      case 'paragraph':
        elements.push(new Paragraph({
          children: processInlineContent(node.content || [])
        }))
        break

      case 'bulletList':
        // Handle bullet lists
        if (node.content) {
          node.content.forEach((listItem: any) => {
            if (listItem.content) {
              listItem.content.forEach((para: any) => {
                elements.push(new Paragraph({
                  children: processInlineContent(para.content || []),
                  bullet: { level: 0 }
                }))
              })
            }
          })
        }
        break

      case 'orderedList':
        // Handle ordered lists
        if (node.content) {
          node.content.forEach((listItem: any) => {
            if (listItem.content) {
              listItem.content.forEach((para: any) => {
                elements.push(new Paragraph({
                  children: processInlineContent(para.content || []),
                  numbering: { reference: 'default', level: 0 }
                }))
              })
            }
          })
        }
        break

      case 'blockquote':
        if (node.content) {
          node.content.forEach((child: any) => {
            const childElements = processNode(child)
            childElements.forEach(el => {
              if (el instanceof Paragraph) {
                // Add italic styling for blockquotes - just push the existing paragraph
                elements.push(el)
              }
            })
          })
        }
        break

      default:
        // Handle other node types or skip
        if (node.content) {
          node.content.forEach((child: any) => {
            elements.push(...processNode(child))
          })
        }
        break
    }

    return elements
  }

  const processInlineContent = (content: any[]): TextRun[] => {
    const runs: TextRun[] = []

    content.forEach((node: any) => {
      if (node.type === 'text') {
        const marks = node.marks || []
        const isBold = marks.some((mark: any) => mark.type === 'bold')
        const isItalic = marks.some((mark: any) => mark.type === 'italic')
        const isUnderline = marks.some((mark: any) => mark.type === 'underline')

        runs.push(new TextRun({
          text: node.text || '',
          bold: isBold,
          italics: isItalic,
          underline: isUnderline ? {} : undefined
        }))
      }
    })

    return runs.length > 0 ? runs : [new TextRun({ text: '' })]
  }

  // Process the document content
  if (json.content) {
    json.content.forEach((node: any) => {
      children.push(...processNode(node))
    })
  }

  // Create the document
  const doc = new Document({
    sections: [{
      properties: {},
      children: children.length > 0 ? children : [new Paragraph({ children: [new TextRun({ text: '' })] })]
    }]
  })

  // Generate and save the document
  const buffer = await Packer.toBuffer(doc)
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  })

  saveAs(blob, filename)
}

/**
 * Export document to Markdown
 */
function exportToMarkdown(editor: Editor, filename: string = 'document.md'): void {
  try {
    // Get the markdown content from the editor
    const markdown = editor.storage.markdown.getMarkdown()
    const blob = new Blob([markdown], { type: 'text/markdown;charset=utf-8' })
    saveAs(blob, filename)
  } catch (error) {
    console.error('Error exporting to Markdown:', error)
    throw new Error('Failed to export document to Markdown format')
  }
}

/**
 * Export document to HTML
 */
function exportToHTML(editor: Editor, filename: string = 'document.html'): void {
  try {
    const html = editor.getHTML()

    // Create a complete HTML document
    const fullHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        p {
            margin-bottom: 1rem;
        }
        blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
        }
        code {
            background-color: #f3f4f6;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        pre {
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1rem 0;
        }
        th, td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            text-align: left;
        }
        th {
            background-color: #f9fafb;
            font-weight: 600;
        }
        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    ${html}
</body>
</html>`

    const blob = new Blob([fullHTML], { type: 'text/html;charset=utf-8' })
    saveAs(blob, filename)
  } catch (error) {
    console.error('Error exporting to HTML:', error)
    throw new Error('Failed to export document to HTML format')
  }
}

/**
 * Export document to PDF (using browser print)
 */
async function exportToPDF(
  editor: Editor,
  documentId?: string,
  onSave?: (content: string, data?: any) => Promise<void> | void
): Promise<void> {
  try {
    // If we have a document ID and save function, save the content first
    if (documentId && onSave) {
      const content = editor.getHTML()
      const data = editor.getJSON()
      const result = onSave(content, data)
      if (result instanceof Promise) {
        await result
      }
    }

    // Open print page in new window
    if (documentId) {
      const printUrl = `/documents/${documentId}/print`
      const printWindow = window.open(printUrl, '_blank', 'width=800,height=600')

      if (!printWindow) {
        throw new Error('Failed to open print window. Please check your popup blocker settings.')
      }
    } else {
      // Fallback to current page print if no document ID
      window.print()
    }
  } catch (error) {
    console.error('Error exporting to PDF:', error)
    throw new Error('Failed to export document to PDF format')
  }
}

/**
 * Main export function that handles all formats
 */
export async function exportDocument(
  editor: Editor,
  format: ExportFormat,
  filename?: string,
  documentId?: string,
  onSave?: (content: string, data?: any) => Promise<void> | void
): Promise<void> {
  if (!editor) {
    throw new Error('Editor instance is required for export')
  }

  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
  const defaultFilename = `document-${timestamp}`

  try {
    switch (format) {
      case 'docx':
        await exportToDocxFallback(editor, filename || `${defaultFilename}.docx`)
        break
      case 'md':
        exportToMarkdown(editor, filename || `${defaultFilename}.md`)
        break
      case 'html':
        exportToHTML(editor, filename || `${defaultFilename}.html`)
        break
      case 'pdf':
        await exportToPDF(editor, documentId, onSave)
        break
      default:
        throw new Error(`Unsupported export format: ${format}`)
    }
  } catch (error) {
    console.error(`Export failed for format ${format}:`, error)
    throw error
  }
}

/**
 * Check if custom export extensions are available
 */
export function hasCustomExport(): boolean {
  return true // Our custom export is always available
}

/**
 * Get available export formats
 */
export function getAvailableExportFormats(): ExportFormat[] {
  return ['pdf', 'docx', 'md', 'html']
}
