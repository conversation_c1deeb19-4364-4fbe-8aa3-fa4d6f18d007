'use client'

import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@ui/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@ui/components/ui/tabs'
import { Bot, History, MessageSquare, Share2, X } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { Editor } from '@tiptap/react'

// Import existing panels
import { CommentsPanel } from './CommentsPanel'
import { HistoryPanel } from './HistoryPanel'
import { SharePanel } from './SharePanel'
import { AIChatPanel } from './AIChatPanel'
import { FeatureFlag } from '@/components/feature-flag'

interface TabbedSidePanelProps {
  editor: Editor | null
  documentId: string
  currentUser?: {
    id: string
    name: string
    email?: string
    avatar?: string
    color?: string
  }
  isOwner?: boolean
  aiProvider?: any
  onClose?: () => void
  className?: string
  defaultTab?: 'comments' | 'history' | 'share' | 'ai'
  onRestoreVersion?: (version: any) => void
  // Feature flags for controlling which tabs are available
  featureFlags?: {
    comments?: boolean
    share?: boolean
    aiChat?: boolean
  }
}

export function TabbedSidePanel({
  editor,
  documentId,
  currentUser,
  isOwner = false,
  aiProvider,
  onClose,
  className,
  defaultTab = 'comments',
  onRestoreVersion,
  featureFlags = {}
}: TabbedSidePanelProps) {
  const [activeTab, setActiveTab] = useState(defaultTab)

  // Sync activeTab with defaultTab when it changes
  useEffect(() => {
    setActiveTab(defaultTab)
  }, [defaultTab])

  const tabs = [
    // Comments tab - only show if comments feature is enabled
    ...(featureFlags.comments !== false ? [{
      id: 'comments',
      label: 'Comments',
      icon: MessageSquare,
      component: editor ? (
        <CommentsPanel
          editor={editor}
          documentId={documentId}
          currentUser={currentUser}
        />
      ) : null
    }] : []),
    // History tab - always available
    {
      id: 'history',
      label: 'History',
      icon: History,
      component: editor ? (
        <HistoryPanel
          editor={editor}
          documentId={documentId}
          currentUser={currentUser}
          onRestoreVersion={onRestoreVersion}
        />
      ) : null
    },
    // Share tab - only show if share feature is enabled
    ...(featureFlags.share !== false ? [{
      id: 'share',
      label: 'Share',
      icon: Share2,
      component: (
        <SharePanel
          documentId={documentId}
          currentUser={currentUser}
          isOwner={isOwner}
        />
      )
    }] : []),
    // AI tab - only show if AI provider exists and AI chat feature is enabled
    ...(aiProvider && featureFlags.aiChat !== false ? [{
      id: 'ai',
      label: 'AI Assistant',
      icon: Bot,
      component: (
        <AIChatPanel
          editor={editor}
          aiProvider={aiProvider}
          className="border-0 shadow-none bg-transparent"
        />
      )
    }] : [])
  ]

  return (
    <div className={cn('w-96 bg-background border-l flex flex-col h-full relative z-40', className)}>
      {/* Header with close button */}
      <div className="flex items-center justify-between p-4 border-b">
        <h3 className="font-semibold">Document Tools</h3>
        {onClose && (
          <Button variant="ghost" size="sm" data-testid="close-side-panel" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'comments' | 'history' | 'share' | 'ai')} className="flex-1 flex flex-col">
        <TabsList className={cn("grid w-full m-2 mb-0", aiProvider ? "grid-cols-4" : "grid-cols-3")}>
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="flex items-center gap-1 text-xs"
                data-testid={`${tab.id}-tab`}
              >
                <Icon className="w-3 h-3" />
                <span className="hidden sm:inline">{tab.label}</span>
              </TabsTrigger>
            )
          })}
        </TabsList>

        {/* Tab Content */}
        <div className="flex-1 overflow-hidden">
          {tabs.map((tab) => (
            <TabsContent
              key={tab.id}
              value={tab.id}
              className="h-full m-0 data-[state=active]:flex data-[state=active]:flex-col"
            >
              <div className="flex-1 overflow-hidden">
                {tab.component}
              </div>
            </TabsContent>
          ))}
        </div>
      </Tabs>
    </div>
  )
}
