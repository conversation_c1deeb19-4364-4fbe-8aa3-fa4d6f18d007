# Rich Text Editing

The editor implements a sophisticated rich text editing system built on TipTap 2.12.0 with ProseMirror as the underlying engine. The system utilizes a carefully configured set of core and custom extensions to provide comprehensive document editing capabilities.

## Core Extension Configuration

The editor foundation uses TipTap's StarterKit with specific customizations. The history extension provides undo/redo functionality, while the dropcursor is disabled to prevent conflicts with the custom drag handle implementation. Text processing capabilities include full markdown support through the tiptap-markdown extension, which handles HTML input, tight list formatting, and preserves text transformations. Typography enhancement provides smart quote conversion and other typographic improvements. Text alignment support extends to both headings and paragraphs, while link handling is configured with click-to-edit behavior disabled for better editing experience.

Additional formatting extensions provide comprehensive text styling options including underline, multicolor highlighting, subscript, and superscript formatting. The task list implementation supports nested task items with proper state management. Image insertion provides basic image handling capabilities, while the table extension offers full table creation and editing with custom CSS class support. Third-party extensions include GlobalDragHandle with 24-pixel width and 100-pixel scroll threshold configuration, and AutoJoiner for intelligent list merging.

## Custom Node Type Implementation

The system defines several custom node types with specific schemas and behaviors. The citation node implements an inline, atomic structure with page_id attributes that reference external citation databases. The node schema defines parsing and rendering rules that convert between `[^123]` markdown syntax and `<citation page_id="123"></citation>` HTML output.

Chart nodes provide block-level data visualization with support for both modern Recharts format and legacy eCharts compatibility. The schema includes data-json attributes that handle base64 encoding for complex chart configurations. The mathematics node enables inline and display mathematical expressions using LaTeX syntax, with KaTeX rendering for output. The schema differentiates between inline and display modes through specific attributes.

Column layout functionality implements a sophisticated content arrangement system with ColumnBlock containers and nested Column children. The schema supports multiple layout presets including equal-width columns (2, 3, or 4 columns), ratio-based layouts (1:2, 2:1), and centered content arrangements. The details node provides collapsible content sections with summary headers and open/closed state management.

## Content Model and Document Structure

The document follows a hierarchical schema structure starting with block-level elements including six heading levels, paragraphs, blockquotes, and code blocks. List structures support both bullet and ordered lists with nested list items, plus task lists with interactive checkboxes. Table structures implement proper table rows with both header and cell elements, while horizontal rules provide content separation.

Custom report nodes extend the base schema with ReportSection, ReportGroup, and ReportSummary elements specifically designed for structured document creation. Inline elements include text with comprehensive mark support (bold, italic, underline, multicolor highlight), links, citations, mathematical expressions, and embedded images.

The UniqueId extension automatically assigns UUID identifiers to headings and paragraphs for internal reference systems. The Accessibility extension enhances all nodes with appropriate ARIA roles and attributes for screen reader compatibility.

## Input Processing and Transformation

Content processing follows a sophisticated pipeline beginning with raw markdown input. Citation processing transforms `[^123]` and `[^123,^456]` syntax into structured citation elements by performing database lookups against doc_page_id values. Chart content receives special preservation treatment to maintain complex JSON data structures during processing.

The transformation pipeline utilizes the unified/remark/rehype ecosystem for comprehensive markdown processing. The processor chain includes remarkParse for initial parsing, remarkGfm for GitHub-flavored markdown support, remarkRehype for HTML conversion with dangerous HTML allowance, rehypeRaw for raw HTML preservation, and rehypeStringify for final HTML output generation.

Schema serialization handles complex object structures including circular references and function objects. The custom replacer function converts non-serializable elements to string representations while maintaining object structure integrity.

## Interactive Editing Features

The slash command system provides rapid content insertion through autocomplete functionality. Standard commands include heading creation (`/h1`, `/h2`, `/h3`), list generation (`/ul`, `/ol`, `/task`), content blocks (`/quote`, `/code`, `/table`, `/image`), and specialized elements (`/chart`, `/citation`, `/math`, `/details`, `/toc`). Layout commands enable multi-column arrangements (`/2col`, `/3col`, `/4col`, `/ratio`, `/centered`).

AI-powered slash commands extend functionality with the `/ai` prefix. Commands include text improvement, grammar correction, length adjustment (shorter/expand), tone modification, summarization, content continuation, and custom instruction processing. Each AI command integrates with the streaming response system for real-time feedback.

Keyboard shortcuts provide efficient editing workflows. Mathematical expression insertion uses Mod-Shift-m for inline math and Mod-Shift-M for display math. The details section toggle responds to Mod-Shift-d. Standard formatting shortcuts follow conventional patterns while maintaining compatibility with platform-specific key combinations.

## Rendering and Component Integration

Custom nodes utilize ReactNodeViewRenderer for complex interactive behavior. The mathematics component demonstrates sophisticated state management with editing and display modes. KaTeX integration handles LaTeX rendering with comprehensive error handling for malformed expressions. The component switches between display and edit states through user interaction, maintaining expression validity throughout the editing process.

Error boundaries wrap all complex components to prevent rendering failures from cascading through the application. Chart components receive special protection through React error boundaries that isolate rendering issues while maintaining editor stability.

The rendering system maintains consistency between different output formats including TipTap's native JSON schema for internal operations, HTML for display and markdown compatibility, and markdown export through the unified/remark pipeline. Data encoding ensures proper preservation of complex structures, with base64 encoding for chart JSON data, page ID references for citation database lookups, and LaTeX string storage with display mode flags for mathematical expressions.