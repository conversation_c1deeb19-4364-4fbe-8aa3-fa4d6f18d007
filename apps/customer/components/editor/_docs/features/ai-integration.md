# AI Integration

The editor implements a sophisticated AI integration system that provides both conversational assistance and direct document editing capabilities through a multi-layered architecture designed for real-time interaction and seamless content integration.

## AI Provider Architecture

The foundation of the AI system rests on the CustomAIProvider, which implements an event-driven architecture extending EventEmitter for decoupled communication between AI services and UI components. The provider maintains comprehensive state management through AIProviderState, tracking connection status, generation progress, message history, and error conditions. Configuration accepts an API key and configurable base URL (defaulting to `/api/ai`) for flexible deployment scenarios.

The provider implements a unified interface supporting both text generation and chat functionality while maintaining state persistence across operations. Comprehensive error handling ensures graceful degradation with appropriate event emission for UI responsiveness. The architecture supports both streaming and non-streaming responses through intelligent content detection and processing pipelines.

## Streaming Implementation and Response Processing

The streaming implementation utilizes a dual-mode architecture with intelligent content detection capabilities. Server-Sent Events (SSE) provide the transport mechanism with custom protocol using `data:` prefixed JSON chunks. The system performs incremental parsing by accumulating partial JSON content and attempting parsing on each chunk, allowing for real-time content updates.

Content type detection operates during streaming to automatically differentiate between edit responses and chat responses. Edit responses trigger specialized handling with description extraction and user-friendly status display. The system implements error recovery through the jsonrepair library to handle malformed JSON responses, with graceful degradation to plain text display when JSON parsing fails.

The response processing pipeline demonstrates sophisticated stream handling with accumulating content analysis. When content begins with JSON structure indicators and includes type fields, the system performs test parsing to determine response type. Edit responses receive special treatment with extracted descriptions that provide user feedback during processing, while chat responses flow through standard conversation handling.

## AI Command Types and Processing Pipelines

The AI command system implements three distinct processing pathways through the AICommandExtension. Direct commands utilize the `applyAIPatch()` method for immediate document replacement operations. Tracked commands leverage `applyAIPatchWithTracking()` to provide visual change indicators with user interaction capabilities. Streaming commands employ `runAICommand()` for contextual prompt execution with real-time feedback.

JSON Patch operations form the core of document transformation, utilizing the fast-json-patch library for precise change application. The system performs sophisticated change analysis between document states, computing differences that preserve content integrity while enabling granular modifications. Position mapping converts JSON paths to ProseMirror document positions, ensuring accurate content placement within the editor's document model.

The AISlashCommandExtension provides rapid access to AI functionality through TipTap's Suggestion plugin with `/ai` trigger configuration. The command palette offers eight predefined AI operations including text improvement, grammar correction, length adjustment, tone modification, summarization, content continuation, and custom instruction processing. Context-aware integration ensures commands operate on current editor selection and cursor position, while Tippy.js-powered floating menus provide keyboard navigation support.

## Chat Panel Integration and State Management

The AIChatPanel implements multi-layered state synchronization combining local component state with provider state management. The system maintains streaming status, content accumulation, and pending changes detection through carefully coordinated state updates. Real-time updates operate through event-driven state synchronization with the provider, ensuring consistent user interface responsiveness.

Message history preservation provides persistent conversation context with timestamp tracking for extended editing sessions. Schema integration serializes the complete TipTap schema structure, enabling AI understanding of valid document operations and content model constraints. Bulk operations support accept/reject functionality for all changes, providing efficient workflow management for extensive edits.

Event handling architecture processes multiple event types including `documentEdited` for AI patch application with change tracking, `chatChunk` for streaming content updates, `messageReceived` for message state finalization, and comprehensive error handling with display and recovery mechanisms.

## Change Tracking for AI-Generated Content

The ChangeTrackingExtension implements a visual change system that highlights AI-generated modifications through color-coded marking. Insertion marks utilize green highlighting with underline styling (`bg-green-100 text-green-800`), while deletion marks employ red highlighting with strikethrough styling (`bg-red-100 text-red-800`). Hover interactions provide dynamic floating accept/reject buttons that appear based on cursor position and change proximity.

Position tracking implements advanced hover detection with smart position calculation and viewport boundary adjustment. The system calculates optimal button placement relative to cursor position while preventing interface elements from extending beyond viewport boundaries. Change resolution operations handle both accept and reject actions through proper ProseMirror transaction management.

Bulk operations process all changes in document order, ensuring consistent application of modifications while maintaining document structure integrity. Transaction safety mechanisms guarantee proper ProseMirror transaction handling throughout the change resolution process.

## Error Handling and Fallback Mechanisms

The AI integration implements comprehensive error handling across multiple system layers. Provider-level error handling captures generation failures with descriptive error messages and proper state management. API-level error handling utilizes JSON repair capabilities for malformed responses, implements response validation before processing, and provides fallback responses for failed parsing operations.

Streaming error recovery enables graceful error injection into response streams without terminating the communication channel. UI-level error handling provides user-friendly error messages within the chat panel, implements state recovery mechanisms for generation failures, and supports retry operations through error state clearing.

The system demonstrates resilient error recovery through multiple fallback mechanisms including content type detection fallbacks, JSON parsing error recovery, network failure handling, and state synchronization error correction.

## Context Preservation and Prompt Construction

Context management ensures AI operations receive comprehensive document understanding through full document JSON serialization of TipTap structure. Schema awareness provides serialized schema information enabling AI understanding of valid operations and content model constraints. Selection context includes current selection and surrounding text content (±500 characters) for targeted operations.

Conversation history maintains the last five messages for context continuity across extended editing sessions. Prompt engineering implements structured JSON response formats with clear distinction between conversational responses (`{"type": "chat", "content": "..."}`) and document edits (`{"type": "edit", "description": "...", "patch": [...]}`).

Schema integration ensures AI operations respect TipTap schema node types, maintain attribute compliance with generated content, and adhere to parent-child relationships defined in the content model.

## TipTap Integration Architecture

The AI system integrates deeply with TipTap's command and extension architecture through proper command registration extending TipTap's command system with AI-specific operations. ProseMirror plugin integration provides state management and UI interactions within the editor framework. Event delegation ensures proper event handling through TipTap's established event system.

Key integration points include the AICommandExtension for core AI functionality with patch application, AISlashCommandExtension for slash command interface with suggestion plugin integration, ChangeTrackingExtension for visual change indicators with hover interactions, and CustomAIProvider for external service integration with comprehensive event system support.

Transaction handling implements proper ProseMirror transaction management ensuring document consistency and editor state synchronization. The system maintains proper mark application with attribute management while preserving document structure throughout AI-assisted editing operations.