## UI Components

The editor's UI component system is built on a sophisticated glass-morphism design language with comprehensive accessibility support and performance optimizations. The architecture follows atomic design principles with a layered component hierarchy spanning base UI primitives, composite interface elements, and specialized editor components.

### Design System Integration

#### **Glass-Morphism Architecture**

The editor implements a custom glass-morphism design system through specialized Tailwind CSS plugins that create translucent, layered visual effects. The system provides multiple glass effect variants with programmatic backdrop blur, gradient overlays, and dynamic lighting effects:

**Glass Effect Classes**:
- `.glass-effect-subtle` - Minimal transparency (5% white background, 8px blur)
- `.glass-effect-lit` - Enhanced lighting with radial gradient overlays
- `.glass-effect-brand` - Brand-colored glass with HSL-based gradients
- `.glass-effect-strong` - High-contrast glass for emphasis elements

**Technical Implementation**:
```css
.glass-effect-lit {
  background: rgba(230, 230, 230, 0.1);
  backdrop-filter: blur(12px);
  box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.15);
  position: relative;
}

.glass-effect-lit::before {
  content: "";
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0.7;
  pointer-events: none;
  mix-blend-mode: overlay;
}
```

The system automatically adapts to dark mode themes through CSS custom properties and data attribute selectors, ensuring consistent visual hierarchy across all lighting conditions.

#### **Component Composition Patterns**

Components follow a strict composition hierarchy using the **Provider/Consumer pattern** for context sharing and **Compound Component pattern** for complex UI elements:

**Pattern Example - GlassCard Component**:
```typescript
interface GlassCardProps {
  variant?: 'default' | 'subtle' | 'strong' | 'brand' | 'brand-strong';
  hover?: boolean;
  onClick?: () => void;
}

const GlassCard: React.FC<GlassCardProps> = ({ variant = 'default', hover = true }) => {
  const variantClasses = {
    default: 'glass-effect-lit relative group',
    brand: 'glass-effect-brand relative group',
  };
  
  return (
    <div className={cn('rounded-2xl p-6', variantClasses[variant])}>
      {/* Dynamic lighting effect on hover */}
      <div className="absolute inset-0 bg-gradient-radial opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <div className="relative z-10">{children}</div>
    </div>
  );
};
```

### Core UI Components

#### **1. Button System with Variant Management**

The button component uses **Class Variance Authority (CVA)** for type-safe variant management with extensive customization options:

**Implementation**:
```typescript
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-xl font-medium transition-colors focus-visible:ring-2",
  {
    variants: {
      variant: {
        glass: "glass-effect-subtle hover:glass-effect-lit text-foreground",
        brand: "bg-brand-gradient text-white hover:bg-brand-gradient-dark",
        "brand-outline": "border border-brand text-brand hover:bg-brand/10",
      },
      size: {
        default: "h-10 px-4 py-2",
        lg: "h-11 rounded-xl px-8",
        icon: "h-10 w-10",
      },
    },
  }
)
```

**Accessibility Features**:
- Full keyboard navigation support via focus-visible states
- ARIA attributes automatically applied through Radix UI integration
- Screen reader announcements via custom `aria-live` regions
- High contrast mode compatibility through CSS custom properties

#### **2. Aurora Background Animation System**

The AuroraBackground component creates dynamic, animated gradient effects using CSS transforms and custom animation keyframes:

**Technical Implementation**:
```typescript
const AuroraBackground: React.FC<AuroraBackgroundProps> = ({ variant = 'default' }) => {
  const variantStyles = {
    brand: 'from-brand/20 via-brand-dark/20 to-brand-light/20',
    subtle: 'from-slate-300/20 via-slate-200/20 to-slate-100/20',
  };

  return (
    <div className="relative overflow-hidden">
      <div
        className={cn('absolute inset-0 bg-gradient-to-r animate-aurora', variantStyles[variant])}
        style={{
          backgroundSize: '400% 400%',
          filter: 'blur(50px)',
          transform: 'translateZ(0)', // Hardware acceleration
        }}
      />
    </div>
  );
};
```

### Toolbar Components

#### **1. EditorToolbar - Comprehensive Text Editing Interface**

The EditorToolbar provides a feature-rich interface with **639 lines of implementation** covering text formatting, structural elements, and document operations:

**Key Features**:
- **Text Formatting Controls**: Bold, italic, underline, strikethrough, code, highlight
- **Structural Elements**: 6 heading levels, bullet/ordered lists, blockquotes, horizontal rules
- **Advanced Typography**: Superscript, subscript, text alignment (left, center, right, justify)
- **Content Insertion**: Tables, images, links, table of contents
- **Document Operations**: Save, export (PDF, Word, Markdown, HTML), print mode

**State Management Pattern**:
```typescript
const EditorToolbar = ({ editor, isSaving, onSave, onExport }) => {
  // Safe command execution with error handling
  const canUndo = () => {
    try {
      return editor.can && editor.can().undo();
    } catch {
      return editor.commands && typeof editor.commands.undo === 'function';
    }
  };

  const handleHeadingChange = (value: string) => {
    if (value === 'paragraph') {
      editor.chain().focus().setParagraph().run();
    } else {
      const level = parseInt(value) as 1 | 2 | 3 | 4 | 5 | 6;
      editor.chain().focus().toggleHeading({ level }).run();
    }
  };
};
```

**Feature Flag Integration**:
The toolbar uses feature flags for progressive enhancement and A/B testing:
```typescript
<FeatureFlag flag="document.editor.dynamic.reports">
  <Button onClick={() => openReportDialog('report-section')}>
    <BarChart3 className="w-4 h-4" />
  </Button>
</FeatureFlag>
```

#### **2. CollaborationToolbar - Real-time User Presence**

Implements real-time collaboration features with user presence indicators and session management:

**Features**:
- Active user avatars with color-coded cursors
- Real-time presence tracking via Supabase WebSocket
- Comments panel integration
- Document sharing controls with permission management

### Interactive Context Menus

#### **1. BubbleMenu - Selection-Based Actions**

The ContextBubbleMenu appears on text selection with **289 lines of sophisticated interaction logic**:

**Advanced Features**:
- **Clipboard Operations**: Copy, cut, paste with browser API integration
- **Format Application**: Bold, italic, underline with visual state indicators
- **Content Transformation**: Heading conversion, list creation, link insertion
- **Mathematical Expressions**: Inline math formula insertion
- **Emoji Integration**: Emoji picker activation via colon trigger

**Error Boundary Integration**:
```typescript
<BubbleMenuErrorBoundary>
  <BubbleMenu
    editor={editor}
    shouldShow={({ from, to }) => {
      try {
        return from !== to && !editor.isDestroyed && editor.view.dom.isConnected;
      } catch (error) {
        console.error('BubbleMenu shouldShow error:', error);
        return false;
      }
    }}
    tippyOptions={{
      appendTo: () => document.body,
      zIndex: 9999,
      onShow: (instance) => {
        if (!editor || editor.isDestroyed) {
          instance.hide();
          return false;
        }
      }
    }}
  />
</BubbleMenuErrorBoundary>
```

### Panel System Architecture

#### **1. TabbedSidePanel - Modular Interface Container**

The TabbedSidePanel implements a **164-line tabbed interface system** with dynamic tab management and feature flag integration:

**Dynamic Tab Configuration**:
```typescript
const tabs = [
  ...(featureFlags.comments !== false ? [{
    id: 'comments',
    component: <CommentsPanel editor={editor} documentId={documentId} />
  }] : []),
  {
    id: 'history',
    component: <HistoryPanel onRestoreVersion={onRestoreVersion} />
  },
  ...(aiProvider && featureFlags.aiChat !== false ? [{
    id: 'ai',
    component: <AIChatPanel editor={editor} aiProvider={aiProvider} />
  }] : [])
];
```

**Performance Optimizations**:
- Lazy loading of panel content
- Conditional rendering based on feature flags
- Memoized tab state to prevent unnecessary re-renders

### Accessibility Implementation

#### **1. AriaLiveRegion - Screen Reader Integration**

The editor includes comprehensive screen reader support through a **131-line AriaLiveRegion component**:

**Features**:
- Dual politeness levels (polite/assertive) for different announcement types
- Global announcement system accessible throughout the editor
- Message queuing with automatic cleanup after 3 seconds
- Integration with all major editor actions

**Usage Pattern**:
```typescript
const { announce } = useAriaLiveAnnouncer();

// Announce formatting changes
editor.commands.toggleBold(() => {
  announce(editor.isActive('bold') ? 'Bold applied' : 'Bold removed');
});

// Critical error announcements
announce('Error saving document', 'assertive');
```

**Pre-built Announcement Library**:
```typescript
export const EditorAnnouncements = {
  formatApplied: (format: string) => `${format} applied`,
  headingChanged: (level: number) => `Heading level ${level} applied`,
  tableInserted: () => 'Table inserted',
  saveSuccess: () => 'Document saved successfully',
  focusEntered: () => 'Entered document editor',
};
```

### Error Boundary Pattern

#### **1. EditorErrorBoundary - Graceful Error Recovery**

The error boundary system provides **79 lines of robust error handling** with user-friendly recovery options:

**Features**:
- Graceful error capture with stack trace logging
- User-friendly error messages with recovery actions
- Development-mode error details for debugging
- GitHub issue reporting integration
- Component state reset functionality

**Recovery Mechanisms**:
```typescript
class EditorErrorBoundary extends Component {
  handleReloadEditor = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <Button onClick={this.handleReloadEditor}>Reload Editor</Button>
          <Button onClick={this.handleReportIssue}>Report Issue</Button>
        </div>
      );
    }
    return this.props.children;
  }
}
```

### Animation and Interaction System

#### **1. Transition Utilities**

Custom Tailwind plugins provide **44 lines of transition utilities** for consistent micro-interactions:

**Animation Classes**:
- `.transition-fast` (150ms) - Quick feedback for buttons and hovers
- `.transition-standard` (250ms) - Default UI transitions
- `.hover-lift-subtle` - 2px vertical lift on hover
- `.hover-scale-subtle` - 1.02x scale transformation

**Implementation**:
```css
.hover-lift-subtle {
  transition: transform 250ms ease-in-out;
}
.hover-lift-subtle:hover {
  transform: translateY(-2px);
}
```

### Component Testing Patterns

#### **1. Playwright Integration Testing**

Components are tested using Playwright with comprehensive interaction testing:

**Test Patterns**:
- Visual regression testing for glass-morphism effects
- Accessibility testing with screen reader simulation
- Keyboard navigation verification
- Error boundary recovery testing
- Real-time collaboration simulation

**Example Test Structure**:
```typescript
test('should apply glass-morphism effects on hover', async ({ page }) => {
  await page.locator('.glass-card').hover();
  await expect(page.locator('.glass-card .group-hover\\:opacity-100')).toBeVisible();
});

test('should announce format changes to screen readers', async ({ page }) => {
  await page.locator('[data-testid="bold-button"]').click();
  await expect(page.locator('[aria-live="polite"]')).toContainText('Bold applied');
});
```

### Performance Optimization Techniques

#### **1. Memoization Strategies**

Components use React.memo and useMemo for performance optimization:

```typescript
const GlassCard = React.memo<GlassCardProps>(({ variant, children }) => {
  const variantClasses = useMemo(() => ({
    default: 'glass-effect-lit relative group',
    brand: 'glass-effect-brand relative group',
  }), []);

  return <div className={cn('rounded-2xl', variantClasses[variant])}>{children}</div>;
});
```

#### **2. CSS-in-JS Optimization**

The design system avoids runtime CSS generation by using Tailwind's JIT compilation with safelist classes for dynamic content:

```javascript
safelist: [
  'glass-effect-lit',
  'glass-effect-brand',
  'hover-lift-subtle',
  'transition-standard',
  // ... 100+ pre-compiled classes
]
```

### Visual Testing and Documentation

#### **1. Storybook Integration**

Components support Storybook documentation with interactive examples (referenced but not implemented in current codebase):

```typescript
// Planned Storybook stories
export const GlassCardStory = {
  name: 'Glass Card Variants',
  render: () => (
    <div className="grid grid-cols-3 gap-4">
      <GlassCard variant="subtle">Subtle Glass</GlassCard>
      <GlassCard variant="brand">Brand Glass</GlassCard>
      <GlassCard variant="strong">Strong Glass</GlassCard>
    </div>
  ),
};
```

The UI component system represents a sophisticated implementation of modern design principles with comprehensive accessibility support, performance optimizations, and robust error handling throughout the editor interface.