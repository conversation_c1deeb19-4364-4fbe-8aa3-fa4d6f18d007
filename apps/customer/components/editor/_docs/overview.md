# Overview

The Editor component is the core text editing functionality in the Customer App. It's a sophisticated, feature-rich editor built on top of TipTap (which uses ProseMirror) with extensive customizations for document creation, collaboration, and AI-powered features.

## Technology Stack

The editor leverages a carefully chosen technology stack optimized for performance, maintainability, and user experience:

### **TipTap & ProseMirror** - Rich Text Editor Core
- **TipTap** (`@tiptap/core: ^2.12.0`) - Modern headless editor framework providing a clean abstraction over ProseMirror
- **ProseMirror** (`prosemirror-state: ^1.4.3`) - Battle-tested editor engine with powerful document model
- **Why chosen**: Modular architecture with 18+ custom extensions, seamless React integration, built-in collaborative editing support, and strong TypeScript compatibility
- **Key features**: Extension system, real-time collaboration via YJS, custom node views, and command system

### **Supabase** - Backend & Real-time Infrastructure
- **Client SDK** (`@supabase/supabase-js: latest`) - JavaScript client for database operations
- **Real-time** (`y-websocket: ^3.0.0`, `yjs: ^13.6.27`) - Conflict-free collaborative editing
- **Why chosen**: Built-in WebSocket support for real-time collaboration, PostgreSQL backend with automatic versioning, integrated authentication, and scalable architecture
- **Key features**: Auto-save functionality, document versioning, user presence tracking, and collaborative sessions

### **React & TypeScript** - Component Framework
- **React** (`^18.0.0`) - Component-based UI development with hooks
- **TypeScript** (`strict: true`) - Type-safe development with full DOM API support
- **Why chosen**: Type safety prevents runtime errors in complex editor state, enhanced developer experience with IntelliSense, safe refactoring across large codebase
- **Key features**: Path mapping (`@/*` aliases), incremental compilation, custom context patterns for state management

### **Tailwind CSS with Custom Design System** - Styling Architecture
- **Core** (`tailwindcss: ^3.4.0`) - Utility-first CSS framework
- **Custom Plugins** - Modular plugins for glass-morphism effects and brand gradients
- **Why chosen**: Performance through tree-shaking, design system enforcement, extensive customization capabilities, maintainable co-located styles
- **Key features**: Glass-morphism effects, responsive design, print media support, consistent design tokens

### **Radix UI** - Accessible Component Primitives
- **Components** (`@radix-ui/react-*`) - Dropdown menus, dialogs, tooltips, form controls
- **Why chosen**: WCAG-compliant accessibility out of the box, unstyled primitives allowing full design control, complete keyboard navigation support
- **Key features**: Composable component patterns, aria attributes, focus management, keyboard shortcuts

### **AI Integration Stack** - Multi-Provider AI Support
- **Anthropic** (`@ai-sdk/anthropic: ^1.2.11`) - Claude integration for text analysis and generation
- **OpenAI** (`openai: ^4.100.0`) - GPT support for alternative AI responses
- **Google** (`@ai-sdk/google: ^1.2.18`) - Gemini integration for diverse AI capabilities
- **Why chosen**: Unified interface across providers, streaming responses for real-time feedback, context-aware prompting based on document structure