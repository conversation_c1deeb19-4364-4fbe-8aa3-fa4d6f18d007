## Services Architecture

The editor follows a clean service layer pattern, separating business logic from UI components.

### Service Layer Structure

The editor implements a comprehensive service layer architecture that separates business logic from presentation components. The service layer follows dependency injection patterns and provides type-safe interfaces for all major system operations.

```
services/
├── index.ts               # Central exports and service registry
├── supabase/              # Database operations layer
│   ├── reportService.ts   # Document CRUD operations
│   └── versionService.ts  # Version control management
├── state/                 # State management services
│   └── componentStateMachine.ts # Status transition validation
└── utils/                 # Shared service utilities
    ├── constants.ts       # Type-safe status enums
    ├── groupManager.ts    # Hierarchical status calculations
    ├── logger.ts          # Structured logging system
    ├── memoryManager.ts   # Resource lifecycle management
    ├── performanceMonitor.ts # Runtime performance tracking
    └── timeouts.ts        # Debounce/throttle utilities
```

### Service Architecture Patterns

#### Dependency Injection and Service Registry

The service layer uses a centralized export pattern with singleton instances for shared services:

```typescript
// services/index.ts
export { reportService, ReportService } from './supabase/reportService'
export { versionService, VersionService } from './supabase/versionService'
export { componentStateMachine } from './state/componentStateMachine'
export { GroupStatusManager, groupStatusManager } from './utils/groupManager'
export { MemoryManager, memoryManager } from './utils/memoryManager'
export { performanceMonitor } from './utils/performanceMonitor'
```

Services are injected into React contexts through the `EditorProvider` hierarchy:

```typescript
// EditorProvider creates service instances and injects them
const groupManagerRef = useRef<GroupStatusManager | null>(null)
const scopedMemoryRef = useRef<ScopedMemoryManager | null>(null)

if (!groupManagerRef.current) {
  groupManagerRef.current = new GroupStatusManager({
    onGroupStatusChange: (groupId, status) => {
      logger.debug('EditorProvider', 'onGroupStatusChange', 
        `Group ${groupId} status changed to ${status}`)
      onGroupStatusChange?.(groupId, status)
    }
  })
}
```

#### Service Interface Design

All services implement consistent interface patterns with standardized error handling and logging:

```typescript
// Standard service method pattern
async serviceMethod(params: ServiceParams): Promise<ServiceResult> {
  logger.info('ServiceName', 'methodName', `Operation description`)
  
  try {
    const result = await this.performOperation(params)
    logger.info('ServiceName', 'methodName', `Success message`)
    return result
  } catch (error) {
    logger.error('ServiceName', 'methodName', `Error message`, error as Error)
    throw error
  }
}
```

### Core Service Classes and Responsibilities

#### ReportService - Document Persistence Layer

The `ReportService` handles all document persistence operations with automatic retry logic and validation:

```typescript
export class ReportService {
  private supabase = createClient()

  async saveReport(documentId: string, content: string, data: any): Promise<void>
  async loadReport(documentId: string): Promise<{ content: string; data: any } | null>
  async updateComponent(documentId: string, component: ReportComponent): Promise<void>
  async deleteComponent(documentId: string, componentId: string): Promise<void>
  async loadComponents(documentId: string): Promise<ReportComponent[]>
}
```

**Key Features**:
- **Automatic Error Recovery**: Distinguishes between recoverable and non-recoverable errors
- **Type Safety**: Uses TypeScript interfaces for all data structures
- **Transaction Support**: Ensures data consistency across related operations
- **Caching Integration**: Works with memory manager for optimal performance

#### VersionService - Version Control Operations

The `VersionService` provides comprehensive version management with auto-save capabilities:

```typescript
export class VersionService {
  async createVersion(
    documentId: string, 
    content: string, 
    data: any, 
    options: {
      changeSummary?: string
      isAutoSave?: boolean
      createdBy?: string
    } = {}
  ): Promise<DocumentVersion>

  async loadVersion(documentId: string, versionNumber: number): Promise<DocumentVersion | null>
  async listVersions(documentId: string, limit?: number): Promise<DocumentVersion[]>
  async deleteVersion(documentId: string, versionNumber: number): Promise<void>
  async cleanupAutoSaveVersions(documentId: string, keepCount?: number): Promise<void>
}
```

**Version Management Features**:
- **Auto-save Management**: Automatically cleans up old auto-save versions
- **Version Numbering**: Automatic sequential version number generation
- **Metadata Tracking**: Records change summaries and authorship information
- **Performance Optimization**: Batched cleanup operations for large version histories

#### ComponentStateMachine - State Transition Validation

The state machine service ensures valid component status transitions and prevents race conditions:

```typescript
class ComponentStateMachine {
  canTransition(from: ComponentStatus, to: ComponentStatus, context: ComponentContext): boolean
  transition(from: ComponentStatus, to: ComponentStatus, context: ComponentContext): ComponentStatus
  getValidTransitions(from: ComponentStatus): ComponentStatus[]
  determineNextStatus(current: ComponentStatus, context: ComponentContext): ComponentStatus
}
```

**State Machine Features**:
- **Transition Validation**: Prevents invalid state changes using predefined rules
- **Context-Aware Decisions**: Uses component context for conditional transitions
- **Side Effect Execution**: Supports custom side effects during transitions
- **Automatic Status Determination**: Can automatically determine next valid status

#### GroupStatusManager - Hierarchical Status Calculations

The `GroupStatusManager` handles complex hierarchical status calculations with performance optimization:

```typescript
export class GroupStatusManager {
  calculateGroupStatus(
    groupId: string,
    components: Map<string, ReportComponent>,
    componentHierarchy: Map<string, string[]>
  ): GroupInfo

  queueGroupUpdate(groupId: string): void
  queueGroupUpdates(groupIds: string[]): void
  
  getAffectedGroups(
    componentId: string,
    components: Map<string, ReportComponent>,
    componentHierarchy: Map<string, string[]>
  ): string[]

  async batchUpdateGroups(
    groupIds: string[],
    components: Map<string, ReportComponent>,
    componentHierarchy: Map<string, string[]>
  ): Promise<Map<string, GroupInfo>>
}
```

**Performance Optimization Features**:
- **Debounced Updates**: Batches status updates to prevent excessive recalculation
- **Caching Strategy**: Intelligently caches group status with invalidation
- **Hierarchy Traversal**: Efficiently processes nested group structures
- **Batch Processing**: Handles multiple groups in parallel with controlled concurrency

### Service Lifecycle Management and Initialization

#### Scoped Memory Management

Services use scoped memory management for automatic resource cleanup:

```typescript
export class MemoryManager {
  register(id: string, type: ResourceType, cleanup: CleanupFunction): void
  registerTimeout(id: string, timeoutId: NodeJS.Timeout): void
  registerInterval(id: string, intervalId: NodeJS.Timeout): void
  registerListener(id: string, target: EventTarget, event: string, handler: EventListener): void
  registerSubscription(id: string, unsubscribe: () => void): void
  
  async cleanup(id: string): Promise<void>
  async cleanupByType(type: ResourceType): Promise<void>
  async cleanupAll(): Promise<void>
  
  createScope(scopeId: string): ScopedMemoryManager
}
```

#### Service Initialization Patterns

Services are initialized with dependency injection through the provider hierarchy:

```typescript
// EditorProvider manages service lifecycle
React.useEffect(() => {
  const scopedMemory = scopedMemoryRef.current
  const groupManager = groupManagerRef.current

  return () => {
    logger.info('EditorProvider', 'cleanup', `Cleaning up editor`)
    scopedMemory?.cleanupAll()
    groupManager?.clearCache()
  }
}, [reportId])
```

### Cross-Service Communication Patterns

#### Event-Driven Communication

Services communicate through typed event handlers and callback patterns:

```typescript
// Service configuration with event callbacks
const groupManager = new GroupStatusManager({
  onGroupStatusChange: (groupId: string, status: GroupStatus) => {
    logger.debug('GroupManager', 'statusChange', `${groupId} -> ${status}`)
    notifyOtherServices(groupId, status)
  }
})
```

#### Context Integration

Services integrate with React contexts through custom hooks:

```typescript
// useEditorEnhanced provides access to injected services
export function useEditorEnhanced() {
  const context = React.useContext(EditorEnhancedContext)
  if (!context) {
    throw new Error('useEditorEnhanced must be used within EditorProvider')
  }
  return context
}
```

### Service Testing Patterns and Mocking Strategies

#### Service Mocking

Services are designed for easy mocking in tests:

```typescript
// Test utilities provide service mocks
class MockReportService implements ReportService {
  async saveReport(): Promise<void> { /* mock implementation */ }
  async loadReport(): Promise<{ content: string; data: any } | null> { /* mock */ }
}

// Dependency injection enables easy mocking
const testUtils = new TestUtils(page, {
  reportService: new MockReportService(),
  versionService: new MockVersionService()
})
```

#### Integration Testing

Services support integration testing through the context system:

```typescript
test('should handle service integration', async ({ page }) => {
  const documentId = await testUtils.createDocumentFromTemplate()
  
  // Services work together through the provider system
  const editor = await testUtils.waitForEditor()
  await editor.click()
  await page.keyboard.type('Test content')
  
  // Auto-save service triggers version service
  await expect(page.locator('text=Saved')).toBeVisible({ timeout: 10000 })
})
```

### Service Configuration and Environment Handling

#### Environment-Aware Configuration

Services adapt behavior based on environment:

```typescript
class EditorLogger {
  private config: LogConfig = {
    level: 'info',
    prefix: 'Editor',
    enabled: process.env.NODE_ENV === 'development'
  }
  
  configure(config: Partial<LogConfig>): void {
    this.config = { ...this.config, ...config }
  }
}
```

#### Performance Monitoring Configuration

Services include built-in performance monitoring:

```typescript
class PerformanceMonitor {
  private isEnabled = process.env.NODE_ENV === 'development'
  
  timeFunction<T>(name: string, fn: () => T): T {
    if (!this.isEnabled) return fn()
    
    this.startTiming(name)
    try {
      return fn()
    } finally {
      this.endTiming(name)
    }
  }
}
```

### Error Handling and Recovery in Services

#### Structured Error Handling

All services implement consistent error handling patterns:

```typescript
// Standard error handling with logging and recovery
try {
  const result = await this.supabase
    .from('documents')
    .update({ content, data })
    .eq('id', documentId)

  if (error) {
    logger.error('ReportService', 'saveReport', `Failed to save report ${documentId}`, error)
    throw new Error(`Failed to save report: ${error.message}`)
  }
} catch (error) {
  logger.error('ReportService', 'saveReport', `Error saving report ${documentId}`, error as Error)
  throw error
}
```

#### Service-Level Recovery Strategies

Services implement automatic recovery for transient failures:

```typescript
// Automatic retry with exponential backoff
async performWithRetry<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      if (attempt === maxRetries) throw error
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
    }
  }
}
```

### Service Performance Optimization Techniques

#### Debouncing and Throttling

Services use sophisticated timing controls for performance:

```typescript
export const TIMEOUT_DELAYS = {
  IMMEDIATE: 0,
  QUICK: 10,
  DEBOUNCE: 50,
  MEDIUM: 100,
  SLOW: 250,
  INITIALIZATION: 1000
} as const

// Debounced group updates prevent excessive recalculation
this.debouncedProcessQueue = debounce(
  () => this.processUpdateQueue(),
  this.options.debounceDelay
)
```

#### Batch Processing

Services implement batch processing for improved performance:

```typescript
// Process updates in batches to avoid blocking
const BATCH_SIZE = 10
for (let i = 0; i < groupsToUpdate.length; i += BATCH_SIZE) {
  const batch = groupsToUpdate.slice(i, i + BATCH_SIZE)
  
  await Promise.all(
    batch.map(groupId => this.updateGroupStatusInternal(groupId))
  )
  
  // Small delay between batches to avoid blocking UI
  if (i + BATCH_SIZE < groupsToUpdate.length) {
    await new Promise(resolve => setTimeout(resolve, 0))
  }
}
```

### Service Integration with Contexts and Components

#### Context Provider Integration

Services are integrated into the React context system through the provider hierarchy:

```typescript
function EditorProviderInner({ children, groupManager, scopedMemory }: EditorProviderInnerProps) {
  const { components, componentHierarchy } = useComponents()
  const { notifyDependencyResolved } = useDependencies()
  
  // Connect component updates to dependency resolution
  React.useEffect(() => {
    const handleComponentUpdate = (componentId: string, status: ComponentStatus) => {
      if (['loaded', 'preserved', 'locked'].includes(status)) {
        notifyDependencyResolved(componentId)
      }
      
      const affectedGroups = groupManager.getAffectedGroups(
        componentId, components, componentHierarchy
      )
      
      if (affectedGroups.length > 0) {
        groupManager.queueGroupUpdates(affectedGroups)
      }
    }
  }, [components, componentHierarchy, groupManager, notifyDependencyResolved])
}
```

#### Component Hook Integration

Services provide React hooks for seamless component integration:

```typescript
// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  React.useEffect(() => {
    renderCount.current++
    const now = performance.now()
    const renderDuration = now - lastRenderTime.current
    
    performanceMonitor.recordRender(componentName, renderDuration)
    lastRenderTime.current = now
  })

  return {
    startTiming: (name: string) => performanceMonitor.startTiming(`${componentName}-${name}`),
    endTiming: (name: string) => performanceMonitor.endTiming(`${componentName}-${name}`)
  }
}
```

### Service Caching and State Management

#### Intelligent Caching Strategies

Services implement sophisticated caching with validation:

```typescript
calculateGroupStatus(groupId: string, components: Map<string, ReportComponent>): GroupInfo {
  // Check cache first
  const cached = this.groupCache.get(groupId)
  if (cached && this.isCacheValid(cached, components)) {
    return cached
  }
  
  const groupInfo = this.performCalculation(groupId, components)
  this.groupCache.set(groupId, groupInfo)
  return groupInfo
}

private isCacheValid(cached: GroupInfo, components: Map<string, ReportComponent>): boolean {
  return cached.descendants.every(id => {
    const component = components.get(id)
    return component !== undefined
  })
}
```

### Service Logging and Monitoring Patterns

#### Structured Logging System

The logging service provides structured, environment-aware logging:

```typescript
class EditorLogger {
  private formatMessage(component: string, method: string, message: string): string {
    return `${this.config.prefix}.${component}.${method}: ${message}`
  }

  error(component: string, method: string, message: string, error?: Error): void {
    if (this.shouldLog('error')) {
      console.error(this.formatMessage(component, method, message), error)
    }
  }
}
```

#### Performance Metrics Collection

Services automatically collect performance metrics for monitoring:

```typescript
interface PerformanceMetrics {
  renderCount: number
  lastRenderTime: number
  averageRenderTime: number
  componentCount: number
  componentRegistrations: number
  groupUpdates: number
  dependencyResolutions: number
  memoryUsage?: number
}

// Automatic performance issue detection
checkPerformanceIssues(): string[] {
  const issues: string[] = []
  const metrics = this.getMetrics()

  if (metrics.renderCount > 100 && metrics.averageRenderTime > 16) {
    issues.push(`High render count with slow average render time`)
  }
  
  if (metrics.groupUpdates > metrics.componentRegistrations * 2) {
    issues.push(`Excessive group updates relative to component registrations`)
  }
  
  return issues
}
```

The service layer demonstrates enterprise-grade software architecture principles while maintaining the flexibility and performance required for real-time collaborative document editing. The comprehensive service pattern enables robust testing, efficient performance monitoring, and maintainable code organization across the entire editor system.
  - Enforces business rules
  - Provides transition validation
  - Supports dependency-aware transitions

#### Utility Services

**Logger**
- Centralized logging with levels (debug, info, warn, error)
- Component-scoped logging
- Performance tracking integration

**TimeoutRegistry**
- Manages all timeouts/intervals
- Automatic cleanup on unmount
- Configurable delays
- Debounce/throttle utilities

**MemoryManager**
- Tracks resource allocation
- Scoped memory management
- Automatic cleanup
- Memory leak detection

**PerformanceMonitor**
- Method execution timing
- Render performance tracking
- Issue detection and reporting
- Decorator-based instrumentation

**GroupStatusManager**
- Calculates aggregate group status
- Manages status update queuing
- Implements debounced updates
- Caches calculations