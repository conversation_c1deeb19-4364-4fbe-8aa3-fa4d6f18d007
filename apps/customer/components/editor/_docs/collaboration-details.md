## Collaboration Features

For comprehensive documentation on the editor's collaboration features, please see [Collaboration Features](./_docs/features/collaboration.md).

The collaboration system includes:
- Real-time document synchronization with YJS
- User presence and awareness tracking
- Change tracking and conflict resolution
- Document versioning and history
- Access control and permissions
- WebSocket communication infrastructure
- AI-generated content collaboration

Key implementation files:
- `hooks/usePresence.ts` - User presence tracking
- `hooks/useSupabaseAutoSave.ts` - Auto-save functionality
- `context/versioning/VersioningContext.tsx` - Version management
- `extensions/ChangeTrackingExtension.tsx` - Change tracking for AI edits
- `components/SupabaseCollaborationToolbar.tsx` - Collaboration UI