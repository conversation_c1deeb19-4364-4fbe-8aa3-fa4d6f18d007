## Quick Start

### Basic Usage

```typescript
import { EkoDocumentEditor } from '@/components/editor'

function MyDocument() {
  const handleSave = async (content: string, data: any) => {
    // Save logic here
  }

  return (
    <EkoDocumentEditor
      documentId="doc-123"
      initialContent="# Welcome to the editor"
      onSave={handleSave}
      showToolbar={true}
      showCollaboration={true}
      showAI={true}
    />
  )
}
```

### With Custom Configuration

```typescript
<EkoDocumentEditor
  documentId={documentId}
  citations={citations}
  initialContent={markdown}
  editable={true}
  viewMode={false}
  entityId={entityId}
  runId={runId}
  user={{
    id: user.id,
    name: user.name,
    email: user.email,
    avatar: user.avatar_url
  }}
  featureFlags={{
    aiTools: true,
    aiChat: true,
    comments: true,
    share: true,
    exportWord: true
  }}
  onSave={handleSave}
/>
```

