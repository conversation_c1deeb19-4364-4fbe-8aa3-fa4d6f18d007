"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { Progress } from '@ui/components/ui/progress'
import { Separator } from '@ui/components/ui/separator'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@ui/components/ui/collapsible'
import { Button } from '@ui/components/ui/button'
import { 
  ChevronDown, 
  ChevronUp, 
  Calculator, 
  Info, 
  AlertCircle, 
  Zap,
  Target,
  Scale,
  TrendingUp,
  Activity,
  CheckCircle2,
  XCircle
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { CalculationDetails, ValidationDetails, AssessmentFeedback, BackendCalculationDetails, ScaleFactorAdjustment } from '@/types'

interface CalculationDisplayProps {
  dimension: 'animals' | 'humans' | 'environment'
  type: 'harm' | 'benefit'
  calculation_details?: CalculationDetails
  validation_details?: ValidationDetails
  reviewer_feedback?: AssessmentFeedback[]
}

const indicatorDescriptions: Record<string, string> = {
  // Animal harm indicators
  species_went_extinct: "Species driven to extinction",
  many_animals_died: "Many animals died",
  animals_died: "Animals died",
  species_population_declined: "Species population declined",
  animals_injured: "Animals injured or harmed",
  habitat_lost_for_animals: "Animal habitat destroyed",
  animals_displaced: "Animals forcibly displaced",
  
  // Animal benefit indicators
  many_animals_rescued: "Many animals rescued",
  species_reintroduced: "Species reintroduced",
  animals_rescued: "Animals rescued",
  species_population_increased: "Species population increased",
  habitat_restored_for_animals: "Habitat restored for animals",
  animals_rehabilitated: "Animals rehabilitated",
  
  // Human harm indicators
  genocide: "Genocide occurred",
  many_humans_died: "Many humans died",
  humans_died: "Human fatalities",
  human_rights_violated: "Human rights violated",
  people_displaced: "People displaced",
  humans_injured_or_ill: "Serious injuries or illness",
  public_health_harmed: "Public health degraded",
  livelihoods_lost: "Livelihoods lost",
  discrimination: "Discrimination occurred",
  money_lost: "Financial losses",
  
  // Human benefit indicators
  many_humans_saved: "Many humans saved",
  humans_saved: "Humans saved",
  human_rights_protected: "Human rights protected",
  health_outcomes_improved: "Health outcomes improved",
  jobs_created: "Jobs created",
  livelihoods_restored: "Livelihoods restored",
  infrastructure_upgraded: "Infrastructure improved",
  incomes_increased: "Incomes increased",
  education_or_training_provided: "Education provided",
  social_inclusion_enhanced: "Social inclusion enhanced",
  
  // Environment harm indicators
  climate_risk_intensified: "Climate risk worsened",
  biodiversity_loss: "Biodiversity declined",
  deforestation_occurred: "Forest cover lost",
  habitat_destroyed: "Habitat destroyed",
  ghg_emissions_increased: "GHG emissions increased",
  water_contaminated: "Water contaminated",
  pollution_increased: "Pollution increased",
  
  // Environment benefit indicators
  renewable_energy_generated: "Clean energy supplied",
  ghg_emissions_reduced: "GHG emissions reduced",
  carbon_sequestered: "Carbon sequestered",
  forest_restored: "Forest restored",
  biodiversity_enhanced: "Biodiversity improved",
  habitat_restored: "Habitat restored",
  ecosystem_services_enhanced: "Ecosystem services improved",
  water_quality_improved: "Water quality improved",
  pollution_reduced: "Pollution reduced"
}

export function CalculationDisplay({ 
  dimension, 
  type, 
  calculation_details, 
  validation_details, 
  reviewer_feedback 
}: CalculationDisplayProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  
  // Extract the specific calculation details for this dimension and type
  const details = calculation_details?.[type]?.[dimension]
  const validationDetails = calculation_details?.validation?.[type]?.[dimension]
  
  // Filter reviewer feedback for this specific dimension and type
  const specificFeedback = reviewer_feedback?.filter(
    feedback => feedback.dimension === dimension && feedback.assessment_type === type
  )
  
  if (!details && !validationDetails && !specificFeedback?.length) return null
  
  const isHarm = type === 'harm'
  
  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className="w-full justify-between text-xs p-2 h-auto hover:bg-white/10"
        >
          <span className="flex items-center gap-1">
            <Calculator className="w-3 h-3" />
            Calculation Details
          </span>
          {isOpen ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="mt-3 space-y-3">
        {/* Step 1: Triggered Indicators */}
        {details && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs font-medium">
              <div className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center">
                <span className="text-blue-500">1</span>
              </div>
              <span>Triggered Indicators</span>
            </div>
            
            <div className="ml-8 space-y-2">
              {details.triggered_indicators && details.triggered_indicators.length > 0 ? (
                <>
                  <div className="text-xs text-muted-foreground">
                    Indicators that were triggered by this event:
                  </div>
                  <div className="space-y-1">
                    {details.triggered_indicators.map((indicator, i) => (
                      <div key={i} className="flex items-start gap-2 text-xs">
                        <CheckCircle2 className="w-3 h-3 text-green-500 mt-0.5" />
                        <span className="font-medium">
                          {indicatorDescriptions[indicator] || indicator}
                        </span>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Info className="w-3 h-3" />
                  <span>No specific indicators triggered</span>
                </div>
              )}
              
            </div>
          </div>
        )}
        
        {/* Step 2: Scale Factors Applied */}
        {details?.scale_factors && (
          <>
            <Separator className="opacity-20" />
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs font-medium">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <span className="text-purple-500">2</span>
                </div>
                <span>Scale Factors Applied</span>
              </div>
              
              <div className="ml-8 space-y-3">
                <div className="space-y-2 text-xs">
                  <div><strong>Duration:</strong> {details.scale_factors.duration}</div>
                  <div><strong>Proximity to Tipping Point:</strong> {details.scale_factors.proximity_to_tipping_point}</div>
                  
                  <div className="grid grid-cols-2 gap-3 mt-2">
                    <div>
                      <div className="text-muted-foreground mb-1">Degree</div>
                      <div className="text-xs">
                        {details.scale_factors.total_destruction_death ? 'Total destruction/death' :
                         details.scale_factors.near_total_destruction ? 'Near-total destruction' :
                         details.scale_factors.severe_permanent_damage ? 'Severe permanent damage' :
                         details.scale_factors.severe_harm ? 'Severe harm' :
                         details.scale_factors.moderate_harm ? 'Moderate harm' :
                         details.scale_factors.minor_harm ? 'Minor harm' :
                         'No harm'}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-muted-foreground mb-1">Directness</div>
                      <div className="text-xs">
                        {details.scale_factors.entity_sole_direct_cause ? 'Sole direct cause' :
                         details.scale_factors.direct_action_by_entity ? 'Direct action' :
                         details.scale_factors.entity_decision_caused_impact ? 'Decision caused impact' :
                         details.scale_factors.entity_action_led_to_impact ? 'Action led to impact' :
                         details.scale_factors.entity_influenced_outcome ? 'Influenced outcome' :
                         details.scale_factors.entity_had_minor_influence ? 'Minor influence' :
                         'Third party action'}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-muted-foreground mb-1">Contribution</div>
                      <div className="text-xs">
                        {details.scale_factors.sole_contributor ? 'Sole contributor' :
                         details.scale_factors.dominant_contributor ? 'Dominant contributor' :
                         details.scale_factors.major_contributor ? 'Major contributor' :
                         details.scale_factors.significant_contributor ? 'Significant contributor' :
                         details.scale_factors.minor_contributor ? 'Minor contributor' :
                         details.scale_factors.very_minor_contributor ? 'Very minor contributor' :
                         'Not contributing'}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-muted-foreground mb-1">Authenticity</div>
                      <div className="text-xs">
                        {details.scale_factors.purely_altruistic ? 'Purely altruistic' :
                         details.scale_factors.genuine_commitment ? 'Genuine commitment' :
                         details.scale_factors.mostly_genuine_some_business ? 'Mostly genuine' :
                         details.scale_factors.balanced_genuine_business ? 'Balanced motives' :
                         details.scale_factors.primarily_business_driven ? 'Business driven' :
                         details.scale_factors.mostly_regulatory_compliance ? 'Regulatory compliance' :
                         'Marketing/greenwashing'}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-muted-foreground mb-1">Reversibility</div>
                      <div className="text-xs">
                        {details.scale_factors.fully_reversible_immediately ? 'Fully reversible immediately' :
                         details.scale_factors.reversible_within_weeks ? 'Reversible within weeks' :
                         details.scale_factors.reversible_within_months ? 'Reversible within months' :
                         details.scale_factors.reversible_within_years ? 'Reversible within years' :
                         details.scale_factors.requires_significant_effort_to_reverse ? 'Requires significant effort' :
                         details.scale_factors.takes_centuries_to_reverse ? 'Takes centuries' :
                         'Irreversible'}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-muted-foreground mb-1">Probability</div>
                      <div className="text-xs">
                        {details.scale_factors.has_already_happened ? 'Already happened' :
                         details.scale_factors.virtually_certain ? 'Virtually certain' :
                         details.scale_factors.highly_likely ? 'Highly likely' :
                         details.scale_factors.even_chance ? 'Even chance' :
                         details.scale_factors.unlikely ? 'Unlikely' :
                         details.scale_factors.very_unlikely ? 'Very unlikely' :
                         'Will not happen'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
        
        {/* Step 2a: Base Score Selection */}
        {details?.base_score !== undefined && (
          <>
            <Separator className="opacity-20" />
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs font-medium">
                <div className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center">
                  <span className="text-blue-500">2a</span>
                </div>
                <span>Base Score Selection</span>
              </div>
              
              <div className="ml-8 space-y-2">
                <div className="flex items-center justify-between p-2 bg-blue-50/50 dark:bg-blue-900/20 rounded">
                  <span className="text-xs">{details.base_score_rationale || 'Initial score from indicators'}</span>
                  <span className="text-sm font-bold text-blue-600">{details.base_score.toFixed(3)}</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Step 2b: Scale Factor Adjustments */}
        {details?.scale_factor_adjustments && details.scale_factor_adjustments.length > 0 && (
          <>
            <Separator className="opacity-20" />
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs font-medium">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center">
                  <span className="text-purple-500">2b</span>
                </div>
                <span>Scale Factor Adjustments</span>
              </div>
              
              <div className="ml-8 space-y-2">
                {details.scale_factor_adjustments.map((adjustment, i) => (
                  <div key={i} className="p-2 bg-purple-50/50 dark:bg-purple-900/20 rounded space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="font-medium">{adjustment.factor_name}</span>
                      <span className="text-purple-600">×{adjustment.multiplier}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">{adjustment.description}</div>
                    {adjustment.triggered_criteria && adjustment.triggered_criteria.length > 0 && (
                      <div className="text-xs text-muted-foreground">
                        <span className="font-medium">Criteria:</span> {adjustment.triggered_criteria.join(', ')}
                      </div>
                    )}
                    <div className="flex items-center justify-between text-xs">
                      <span>{adjustment.score_before.toFixed(3)}</span>
                      <span className="text-muted-foreground">→</span>
                      <span className="font-medium">{adjustment.score_after.toFixed(3)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Step 3: Validation & Capping */}
        {validationDetails && (
          <>
            <Separator className="opacity-20" />
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs font-medium">
                <div className="w-6 h-6 rounded-full bg-orange-500/20 flex items-center justify-center">
                  <span className="text-orange-500">3</span>
                </div>
                <span>Validation & Score Capping</span>
                {validationDetails.was_capped && (
                  <Badge variant="outline" className="text-orange-500 border-orange-500">
                    Capped
                  </Badge>
                )}
              </div>
              
              <div className="ml-8 space-y-2">
                <div className="p-3 bg-white/10 rounded space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span>Original Score:</span>
                    <span>{(validationDetails.original_score).toFixed(3)}</span>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span>Max Allowed:</span>
                    <span>{(validationDetails.max_allowed_score).toFixed(3)}</span>
                  </div>
                  <Separator className="opacity-20" />
                  <div className="flex items-center justify-between text-sm font-bold">
                    <span>Final Score:</span>
                    <span className={cn(
                      validationDetails.was_capped ? "text-orange-500" : (isHarm ? "text-red-500" : "text-green-500")
                    )}>
                      {(validationDetails.capped_score).toFixed(3)}
                    </span>
                  </div>
                </div>
                
                {validationDetails.applied_limits.length > 0 && (
                  <div className="space-y-1">
                    <div className="text-xs text-muted-foreground">Applied limits based on indicators:</div>
                    {validationDetails.applied_limits.map(([indicator, limit], i) => (
                      <div key={i} className="text-xs flex justify-between">
                        <span>{indicatorDescriptions[indicator] || indicator}:</span>
                        <span>max {limit.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                )}
                
                {validationDetails.confidence_adjusted && (
                  <div className="flex items-center gap-2 text-xs text-orange-500">
                    <AlertCircle className="w-3 h-3" />
                    <span>Confidence reduced due to capping</span>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
        
        {/* Confidence Factors */}
        {details?.confidence_factors && details.confidence_factors.length > 0 && (
          <>
            <Separator className="opacity-20" />
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs font-medium">
                <Info className="w-3 h-3" />
                <span>Confidence Factors</span>
              </div>
              <div className="ml-6 space-y-1">
                {details.confidence_factors.map((factor, i) => (
                  <div key={i} className="text-xs text-muted-foreground">
                    • {factor}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
        
        {/* Reviewer Feedback */}
        {specificFeedback && specificFeedback.length > 0 && (
          <>
            <Separator className="opacity-20" />
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs font-medium">
                <AlertCircle className="w-3 h-3 text-amber-500" />
                <span className="text-amber-600">Reviewer Feedback</span>
              </div>
              <div className="ml-6 space-y-3">
                {specificFeedback.map((feedback, i) => (
                  <div key={i} className="p-2 bg-amber-50/50 dark:bg-amber-900/20 rounded text-xs space-y-1">

                    <div className="text-muted-foreground">
                      {feedback.issue}
                    </div>
                    <div className="font-medium">Suggestion:</div>
                    <div className="text-muted-foreground">
                      {feedback.suggestion}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </CollapsibleContent>
    </Collapsible>
  )
}
