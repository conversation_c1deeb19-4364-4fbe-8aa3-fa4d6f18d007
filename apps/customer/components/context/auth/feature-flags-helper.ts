/**
 * Helper functions for feature flags in auth context
 * This provides a way to override feature flags for testing
 */

import { DEFAULT_FEATURE_FLAGS } from '@/utils/feature-flags';

/**
 * Get effective default feature flags
 * In test environment, this includes additional flags needed for testing
 */
export function getEffectiveDefaultFlags(): string[] {
  // If we're in a test environment, enable AI features
  if (typeof window !== 'undefined' && (
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1' ||
    process.env.NODE_ENV === 'test'
  )) {
    // Check if we're running tests by looking for common test indicators
    const isTestEnvironment = 
      // Playwright test runner
      window.navigator.userAgent.includes('HeadlessChrome') ||
      // Jest environment
      process.env.NODE_ENV === 'test' ||
      // CI environment
      process.env.CI === 'true';
    
    if (isTestEnvironment) {
      return [
        ...DEFAULT_FEATURE_FLAGS,
        'document.editor.ai.tools',
        'document.editor.ai.chat',
        'document.editor.ai.edit',
      ];
    }
  }
  
  return DEFAULT_FEATURE_FLAGS;
}