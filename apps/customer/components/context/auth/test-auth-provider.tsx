'use client'

import { ReactNode } from 'react'
import { AuthProvider } from './auth-context'
import { getDefaultFeatureFlagsWithEnv } from '@/utils/feature-flags-env'

interface TestAuthProviderProps {
  children: ReactNode;
}

/**
 * Test-aware auth provider that includes environment-based feature flags
 * This wrapper ensures that test feature flags are properly loaded
 */
export const TestAuthProvider = ({ children }: TestAuthProviderProps) => {
  // The feature flags will be loaded through the environment
  // This is a wrapper to ensure proper initialization
  return <AuthProvider>{children}</AuthProvider>
}

/**
 * Export the enhanced default feature flags for use in other components
 */
export const TEST_DEFAULT_FEATURE_FLAGS = getDefaultFeatureFlagsWithEnv();