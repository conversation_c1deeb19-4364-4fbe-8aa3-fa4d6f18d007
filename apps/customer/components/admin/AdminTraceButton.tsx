'use client'

import { useState } from 'react'
import { Activity } from 'lucide-react'
import { useAuth } from '@/components/context/auth/auth-context'
import { createClient } from '@/app/supabase/client'
import { cn } from '@utils/lib/utils'
import { TraceModal } from './TraceModal'

interface AdminTraceButtonProps {
  flagId: number
  flagTitle: string
  className?: string
}

interface TraceData {
  run_id: string
  operation_type: string
  start_time: string
  end_time?: string
  processing_steps: Array<{
    step_name: string
    start_time: string
    end_time?: string
    duration_ms?: number
    result?: Record<string, any>
    error?: string
    llm_call?: {
      model: string
      prompt_type: string
      input_tokens: number
      output_tokens: number
      cost: number
      response_summary: string
    }
  }>
  source_data: {
    entity_name?: string
    statement_count?: number
    effect_count?: number
    [key: string]: any
  }
  quality_metrics: {
    confidence_score: number
    data_completeness: number
    processing_errors: string[]
  }
  metadata: Record<string, any>
}

export function AdminTraceButton({ flagId, flagTitle, className }: AdminTraceButtonProps) {
  const { admin } = useAuth()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [traceData, setTraceData] = useState<TraceData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!admin) return null

  const handleTraceClick = async (e: React.MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()

    setIsLoading(true)
    setError(null)

    try {
      const supabase = createClient()

      // Get trace data directly from xfer_flags table
      const { data, error } = await supabase
        .from('xfer_flags')
        .select('trace_json')
        .eq('id', flagId)
        .single()

      if (error) {
        console.error('Error loading trace data:', error)
        setError('Failed to load trace data')
        return
      }

      if (data && (data as any).trace_json) {
        setTraceData((data as any).trace_json)
        setIsModalOpen(true)
      } else {
        setError('No trace data available for this flag')
      }
    } catch (err) {
      console.error('Error loading trace data:', err)
      setError('Failed to load trace data')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <button
        onClick={handleTraceClick}
        disabled={isLoading}
        data-testid="admin-trace-button"
        className={cn(
          "absolute top-2 right-12 z-20 p-1.5 rounded-lg pointer-events-auto",
          "glass-effect-strong backdrop-blur-md",
          "text-blue-400 hover:text-blue-300",
          "hover:glass-effect-brand transition-all duration-200",
          "shadow-md hover:shadow-lg",
          "border border-white/10 hover:border-blue-400/30",
          "opacity-0 group-hover:opacity-100",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          className
        )}
        title="View trace data"
      >
        {isLoading ? (
          <div className="animate-spin w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full" />
        ) : (
          <Activity className="w-4 h-4" />
        )}
      </button>

      {error && (
        <div className="absolute top-12 right-2 z-30 p-2 rounded-lg glass-effect-strong backdrop-blur-md border border-red-400/30 text-red-400 text-xs whitespace-nowrap">
          {error}
        </div>
      )}

      <TraceModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        traceData={traceData}
        flagTitle={flagTitle}
        flagId={flagId}
      />
    </>
  )
}
