'use client'

import React, { useState } from 'react'
import { Trash2 } from 'lucide-react'
import { createClient } from '@/app/supabase/client'
import { useAuth } from '@/components/context/auth/auth-context'
import { useToast } from '@ui/hooks/use-toast'
import { cn } from '@utils/lib/utils'

interface AdminDeleteButtonProps {
  /** The table name to delete from (e.g., 'xfer_flags') */
  tableName: string
  /** The ID of the record to delete */
  recordId: number | string
  /** Human-readable name for the record type (e.g., 'flag', 'promise') */
  recordType: string
  /** Optional callback after successful deletion */
  onDeleted?: () => void
  /** Optional custom className */
  className?: string
}

/**
 * AdminDeleteButton - A reusable delete button that appears only for admin users
 * 
 * Features:
 * - Only visible to admin users
 * - Shows on hover in top-right corner with glass-morphism design
 * - Includes confirmation dialog
 * - Uses Supabase RLS for secure deletion
 * - Proper error handling and user feedback
 */
export const AdminDeleteButton: React.FC<AdminDeleteButtonProps> = ({
  tableName,
  recordId,
  recordType,
  onDeleted,
  className,
}) => {
  const { admin } = useAuth()
  const { toast } = useToast()
  const [isDeleting, setIsDeleting] = useState(false)
  const [showConfirm, setShowConfirm] = useState(false)
  const supabase = createClient()

  // Don't render anything if user is not admin
  if (!admin) {
    return null
  }

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    
    if (!showConfirm) {
      setShowConfirm(true)
      return
    }

    setIsDeleting(true)
    try {
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', recordId)

      if (error) {
        console.error('Delete error:', error)
        toast({
          description: `Failed to delete ${recordType}: ${error.message}`,
          variant: "destructive"
        })
        return
      }

      toast({
        description: `${recordType.charAt(0).toUpperCase() + recordType.slice(1)} deleted successfully`,
      })

      // Hide the parent element (visual hack)
      const deleteButton = document.querySelector(`[data-record-id="${recordId}"]`)?.closest('.relative.group') as HTMLElement
      if (deleteButton) {
        deleteButton.style.display = 'none'
      }

      // Call optional callback
      onDeleted?.()
      
    } catch (err) {
      console.error('Unexpected error:', err)
      toast({
        description: `Unexpected error deleting ${recordType}`,
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
      setShowConfirm(false)
    }
  }

  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    e.nativeEvent.stopImmediatePropagation()
    setShowConfirm(false)
  }

  return (
    <div 
      className={cn(
        "absolute top-2 right-2 z-20 pointer-events-none",
        "opacity-0 group-hover:opacity-100 transition-opacity duration-200",
        className
      )}
      data-record-id={recordId}
    >
      {!showConfirm ? (
        // Delete icon
        <button
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            e.nativeEvent.stopImmediatePropagation()
            handleDelete(e)
          }}
          disabled={isDeleting}
          className={cn(
            "p-1.5 rounded-lg pointer-events-auto",
            "glass-effect-strong backdrop-blur-md",
            "text-red-400 hover:text-red-300",
            "hover:glass-effect-brand transition-all duration-200",
            "shadow-md hover:shadow-lg",
            "border border-white/10 hover:border-red-400/30",
            isDeleting && "opacity-50 cursor-not-allowed"
          )}
          title={`Delete ${recordType}`}
        >
          <Trash2 className="w-4 h-4" />
        </button>
      ) : (
        // Confirmation dialog
        <div 
          className={cn(
            "p-2 rounded-lg min-w-[120px] pointer-events-auto",
            "glass-effect-strong backdrop-blur-md",
            "border border-red-400/30",
            "shadow-lg"
          )}
          onClick={(e) => e.stopPropagation()}
        >
          <p className="text-xs text-white/90 mb-2 text-center">
            Delete {recordType}?
          </p>
          <div className="flex gap-1">
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                e.nativeEvent.stopImmediatePropagation()
                handleDelete(e)
              }}
              disabled={isDeleting}
              className={cn(
                "px-2 py-1 text-xs rounded",
                "bg-red-500/80 hover:bg-red-500 text-white",
                "transition-colors duration-200",
                isDeleting && "opacity-50 cursor-not-allowed"
              )}
            >
              {isDeleting ? 'Deleting...' : 'Yes'}
            </button>
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                e.nativeEvent.stopImmediatePropagation()
                handleCancel(e)
              }}
              disabled={isDeleting}
              className={cn(
                "px-2 py-1 text-xs rounded",
                "bg-gray-500/80 hover:bg-gray-500 text-white",
                "transition-colors duration-200"
              )}
            >
              No
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
