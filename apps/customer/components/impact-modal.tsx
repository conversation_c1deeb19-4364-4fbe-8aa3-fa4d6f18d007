'use client'

import React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@ui/components/ui/dialog'
import { Badge } from '@ui/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Progress } from '@ui/components/ui/progress'
import { Separator } from '@ui/components/ui/separator'
import { Button } from '@ui/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@ui/components/ui/collapsible'
import {
  AlertTriangle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Clock,
  Eye,
  Heart,
  Info,
  Leaf,
  Scale,
  Shield,
  TrendingDown,
  TrendingUp,
  Users,
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { DimensionAssessment, EventImpactMeasurement, ImpactAssessment, CalculationDetails, AssessmentFeedback } from '@/types'
import { ScaleFactorsDisplay } from './scale-factors'
import { CalculationDisplay } from './impact-calculation-display'
import { CalculationOverview } from './impact-calculation-overview'
import { ReviewerFeedback } from './impact-reviewer-amendments'

interface ImpactModalProps {
  isOpen: boolean
  onClose: () => void
  measurement?: EventImpactMeasurement
  flagTitle: string
}

const dimensionIcons = {
  animals: Heart,
  humans: Users,
  environment: Leaf
}

const confidenceColors = {
  high: 'text-green-600 bg-green-50 border-green-200',
  medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  low: 'text-red-600 bg-red-50 border-red-200'
}

const qualityColors = {
  excellent: 'text-green-600 bg-green-50 border-green-200',
  good: 'text-blue-600 bg-blue-50 border-blue-200',
  fair: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  poor: 'text-red-600 bg-red-50 border-red-200'
}

function DimensionCard({
  dimension,
  assessment,
  type,
  calculationDetails,
  reviewerFeedback
}: {
  dimension: keyof ImpactAssessment
  assessment: DimensionAssessment
  type: 'harm' | 'benefit'
  calculationDetails?: CalculationDetails
  reviewerFeedback?: AssessmentFeedback[]
}) {
  const [showScaleFactors, setShowScaleFactors] = React.useState(false)
  const Icon = dimensionIcons[dimension]
  const isHarm = type === 'harm'
  const scoreColor = isHarm
    ? assessment.score > 0.6 ? 'text-red-600' : assessment.score > 0.3 ? 'text-orange-500' : 'text-gray-600'
    : assessment.score > 0.6 ? 'text-green-600' : assessment.score > 0.3 ? 'text-blue-500' : 'text-gray-600'

  const progressColor = isHarm ? 'bg-red-500' : 'bg-green-500'
  
  return (
    <Card className="glass-card">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Icon className="w-4 h-4" />
          {dimension.charAt(0).toUpperCase() + dimension.slice(1)}
          <Badge 
            variant="outline" 
            className={cn("ml-auto text-xs", confidenceColors[assessment.confidence])}
          >
            {assessment.confidence} confidence
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Impact Score</span>
          <span className={cn("text-lg font-bold", scoreColor)}>
            {(assessment.score * 100).toFixed(0)}%
          </span>
        </div>
        
        <div className="space-y-1">
          <Progress 
            value={assessment.score * 100} 
            className="h-2"
            style={{
              '--progress-background': progressColor
            } as React.CSSProperties}
          />
        </div>
        
        <div className="text-xs text-muted-foreground">
          <p className="line-clamp-3">{assessment.reasoning}</p>
        </div>
        
        <div className="space-y-2 text-xs">
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span className="font-medium">Timeline:</span>
          </div>
          <div className="pl-4 space-y-1 text-muted-foreground">
            <div><strong>Immediate:</strong> {assessment.temporal_breakdown.immediate}</div>
            <div><strong>Medium-term:</strong> {assessment.temporal_breakdown.medium_term}</div>
            <div><strong>Long-term:</strong> {assessment.temporal_breakdown.long_term}</div>
          </div>
        </div>

        {/* Scale Factors Section */}
        {assessment.scale_factors && (
          <Collapsible open={showScaleFactors} onOpenChange={setShowScaleFactors}>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-between text-xs p-2 h-auto"
              >
                <span className="flex items-center gap-1">
                  <Scale className="w-3 h-3" />
                  Scale Factors
                </span>
                {showScaleFactors ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2">
              <div className="space-y-2 text-xs">
                <div className="space-y-2">
                  <div><strong>Duration:</strong> {assessment.scale_factors.duration}</div>
                  <div><strong>Proximity to Tipping Point:</strong> {assessment.scale_factors.proximity_to_tipping_point}</div>
                  
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <div className="text-muted-foreground mb-1">Directness</div>
                      <div className="text-xs">
                        {assessment.scale_factors.entity_sole_direct_cause ? 'Sole direct cause' :
                         assessment.scale_factors.direct_action_by_entity ? 'Direct action' :
                         assessment.scale_factors.entity_decision_caused_impact ? 'Decision caused impact' :
                         assessment.scale_factors.entity_action_led_to_impact ? 'Action led to impact' :
                         assessment.scale_factors.entity_influenced_outcome ? 'Influenced outcome' :
                         assessment.scale_factors.entity_had_minor_influence ? 'Minor influence' :
                         'Third party action'}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-muted-foreground mb-1">Authenticity</div>
                      <div className="text-xs">
                        {assessment.scale_factors.purely_altruistic ? 'Purely altruistic' :
                         assessment.scale_factors.genuine_commitment ? 'Genuine commitment' :
                         assessment.scale_factors.mostly_genuine_some_business ? 'Mostly genuine' :
                         assessment.scale_factors.balanced_genuine_business ? 'Balanced motives' :
                         assessment.scale_factors.primarily_business_driven ? 'Business driven' :
                         assessment.scale_factors.mostly_regulatory_compliance ? 'Regulatory compliance' :
                         'Marketing/greenwashing'}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-muted-foreground mb-1">Degree</div>
                      <div className="text-xs">
                        {assessment.scale_factors.total_destruction_death ? 'Total destruction/death' :
                         assessment.scale_factors.near_total_destruction ? 'Near-total destruction' :
                         assessment.scale_factors.severe_permanent_damage ? 'Severe permanent damage' :
                         assessment.scale_factors.severe_harm ? 'Severe harm' :
                         assessment.scale_factors.moderate_harm ? 'Moderate harm' :
                         assessment.scale_factors.minor_harm ? 'Minor harm' :
                         'No harm'}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-muted-foreground mb-1">Reversibility</div>
                      <div className="text-xs">
                        {assessment.scale_factors.fully_reversible_immediately ? 'Fully reversible immediately' :
                         assessment.scale_factors.reversible_within_weeks ? 'Reversible within weeks' :
                         assessment.scale_factors.reversible_within_months ? 'Reversible within months' :
                         assessment.scale_factors.reversible_within_years ? 'Reversible within years' :
                         assessment.scale_factors.requires_significant_effort_to_reverse ? 'Requires significant effort' :
                         assessment.scale_factors.takes_centuries_to_reverse ? 'Takes centuries' :
                         'Irreversible'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
        
        {/* Calculation Details Section */}
        <CalculationDisplay
          dimension={dimension}
          type={type}
          calculation_details={calculationDetails}
          reviewer_feedback={reviewerFeedback}
        />
      </CardContent>
    </Card>
  )
}

function NetImpactVisualization({ measurement }: { measurement: EventImpactMeasurement }) {
  const netScore = measurement.net_impact_score
  const isPositive = netScore > 0
  const absScore = Math.abs(netScore)
  
  return (
    <Card className="glass-card bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Scale className="w-5 h-5" />
          Net Impact Assessment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-center">
          <div className={cn(
            "text-6xl font-bold flex items-center gap-2",
            isPositive ? "text-green-600" : "text-red-600"
          )}>
            {isPositive ? <TrendingUp className="w-8 h-8" /> : <TrendingDown className="w-8 h-8" />}
            {(absScore * 100).toFixed(0)}%
          </div>
        </div>
        
        <div className="text-center">
          <p className={cn(
            "text-lg font-medium",
            isPositive ? "text-green-700" : "text-red-700"
          )}>
            {isPositive ? "Net Positive Impact" : "Net Negative Impact"}
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Based on comprehensive analysis across all dimensions
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {(measurement.harm_score * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-muted-foreground">Average Harm</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {(measurement.benefit_score * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-muted-foreground">Average Benefit</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
export function ImpactModal({ isOpen, onClose, measurement, flagTitle }: ImpactModalProps) {
  if (!measurement) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto glass-modal">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Impact Assessment Unavailable
            </DialogTitle>
          </DialogHeader>
          <div className="py-8 text-center text-muted-foreground">
            <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Detailed impact measurement data is not available for this flag.</p>
            <p className="text-sm mt-2">This feature requires enhanced impact analysis to be enabled.</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto glass-modal">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Scale className="w-5 h-5" />
            Impact Assessment: {flagTitle}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Calculation Process Overview */}
          <CalculationOverview measurement={measurement} />


          
          {/* Overview Section */}
          <NetImpactVisualization measurement={measurement} />
          
          {/* Event Summary */}
          <Card className="glass-card">
            <CardHeader>
              <CardTitle className="text-lg">Event Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{measurement.event_summary}</p>
            </CardContent>
          </Card>
          
          {/* Harm Assessment */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2 text-red-700 dark:text-red-400">
              <TrendingDown className="w-5 h-5" />
              Harm Assessment
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <DimensionCard 
                dimension="animals" 
                assessment={measurement.harm_assessment.animals.assessment} 
                type="harm"
                calculationDetails={measurement.calculation_details}
                reviewerFeedback={measurement.reviewer_feedback}
              />
              <DimensionCard 
                dimension="humans" 
                assessment={measurement.harm_assessment.humans.assessment} 
                type="harm"
                calculationDetails={measurement.calculation_details}
                reviewerFeedback={measurement.reviewer_feedback}
              />
              <DimensionCard 
                dimension="environment" 
                assessment={measurement.harm_assessment.environment.assessment} 
                type="harm"
                calculationDetails={measurement.calculation_details}
                reviewerFeedback={measurement.reviewer_feedback}
              />
            </div>
          </div>
          
          {/* Benefit Assessment */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2 text-green-700 dark:text-green-400">
              <TrendingUp className="w-5 h-5" />
              Benefit Assessment
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <DimensionCard
                dimension="animals"
                assessment={measurement.benefit_assessment.animals.assessment}
                type="benefit"
                calculationDetails={measurement.calculation_details}
                reviewerFeedback={measurement.reviewer_feedback}
              />
              <DimensionCard
                dimension="humans"
                assessment={measurement.benefit_assessment.humans.assessment}
                type="benefit"
                calculationDetails={measurement.calculation_details}
                reviewerFeedback={measurement.reviewer_feedback}
              />
              <DimensionCard
                dimension="environment"
                assessment={measurement.benefit_assessment.environment.assessment}
                type="benefit"
                calculationDetails={measurement.calculation_details}
                reviewerFeedback={measurement.reviewer_feedback}
              />
            </div>
          </div>

          {/* LLM Reviewer Feedback */}
          {(measurement.review_decision || measurement.reviewer_feedback) && (
            <ReviewerFeedback
              reviewDecision={measurement.review_decision}
              feedback={measurement.reviewer_feedback}
            />
          )}

          {/*/!* Scale Factors Overview *!/*/}
          {/*{(measurement.harm_assessment.animals.assessment.scale_factors ||*/}
          {/*  measurement.benefit_assessment.animals.assessment.scale_factors) && (*/}
          {/*  <div className="space-y-4">*/}
          {/*    <h3 className="text-lg font-semibold flex items-center gap-2 text-slate-700 dark:text-slate-300">*/}
          {/*      <Scale className="w-5 h-5" />*/}
          {/*      Scale Factors Analysis*/}
          {/*    </h3>*/}
          {/*    */}
          {/*    {(() => {*/}
          {/*      const harmScaleFactors = measurement.harm_assessment.animals.assessment.scale_factors*/}
          {/*      const benefitScaleFactors = measurement.benefit_assessment.animals.assessment.scale_factors*/}
          {/*      */}
          {/*      // Check if scale factors are identical*/}
          {/*      const scaleFactorsAreIdentical = harmScaleFactors && benefitScaleFactors && */}
          {/*        JSON.stringify(harmScaleFactors) === JSON.stringify(benefitScaleFactors)*/}
          {/*      */}
          {/*      if (scaleFactorsAreIdentical) {*/}
          {/*        // Show single unified scale factors display*/}
          {/*        return (*/}
          {/*          <div className="space-y-2">*/}
          {/*            <p className="text-sm text-muted-foreground">*/}

          {/*            </p>*/}
          {/*            <ScaleFactorsDisplay*/}
          {/*              scaleFactors={harmScaleFactors}*/}
          {/*              className="border-slate-200 dark:border-slate-700"*/}
          {/*            />*/}
          {/*          </div>*/}
          {/*        )*/}
          {/*      }*/}
          {/*      */}
          {/*      // Show separate displays with clear distinction*/}
          {/*      return (*/}
          {/*        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">*/}
          {/*          {harmScaleFactors && (*/}
          {/*            <div className="space-y-2">*/}
          {/*              <h4 className="text-md font-medium flex items-center gap-2 text-red-700 dark:text-red-400">*/}
          {/*                <TrendingDown className="w-4 h-4" />*/}
          {/*                Harm Scale Factors*/}
          {/*              </h4>*/}
          {/*              <ScaleFactorsDisplay*/}
          {/*                scaleFactors={harmScaleFactors}*/}
          {/*                className="border-red-200 dark:border-red-800"*/}
          {/*              />*/}
          {/*            </div>*/}
          {/*          )}*/}
          {/*          {benefitScaleFactors && (*/}
          {/*            <div className="space-y-2">*/}
          {/*              <h4 className="text-md font-medium flex items-center gap-2 text-green-700 dark:text-green-400">*/}
          {/*                <TrendingUp className="w-4 h-4" />*/}
          {/*                Benefit Scale Factors*/}
          {/*              </h4>*/}
          {/*              <ScaleFactorsDisplay*/}
          {/*                scaleFactors={benefitScaleFactors}*/}
          {/*                className="border-green-200 dark:border-green-800"*/}
          {/*              />*/}
          {/*            </div>*/}
          {/*          )}*/}
          {/*        </div>*/}
          {/*      )*/}
          {/*    })()}*/}
          {/*  </div>*/}
          {/*)}*/}


          {/* Uncertainties and Ethical Considerations */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {measurement.key_uncertainties.length > 0 && (
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-amber-500" />
                    Key Uncertainties
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    {measurement.key_uncertainties.map((uncertainty, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <span className="text-amber-500 mt-1">•</span>
                        <span>{uncertainty}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
            
            {measurement.ethical_considerations.length > 0 && (
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Scale className="w-5 h-5 text-purple-500" />
                    Ethical Considerations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    {measurement.ethical_considerations.map((consideration, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <span className="text-purple-500 mt-1">•</span>
                        <span>{consideration}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>
          
          {/* Metadata */}
          <Card className="glass-card bg-muted/20">
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-muted-foreground">
                <div>
                  <div className="font-medium">Model Used</div>
                  <div>{measurement.model_used}</div>
                </div>
                <div>
                  <div className="font-medium">Assessed At</div>
                  <div>{new Date(measurement.assessed_at).toLocaleDateString()}</div>
                </div>
                <div>
                  <div className="font-medium">Prompt Version</div>
                  <div>{measurement.prompt_version}</div>
                </div>
                {measurement.review_decision && (
                  <div>
                    <div className="font-medium">Review Confidence</div>
                    <div>{measurement.review_decision.confidence_in_review}</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
