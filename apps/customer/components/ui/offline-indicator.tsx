'use client'

import React from 'react'
import { WifiOff } from 'lucide-react'

interface OfflineIndicatorProps {
  isOffline: boolean
  className?: string
}

export function OfflineIndicator({ isOffline, className }: OfflineIndicatorProps) {
  if (!isOffline) return null

  return (
    <div 
      data-testid="offline-indicator" 
      className={`flex items-center gap-2 p-3 border border-orange-200 bg-orange-50 text-orange-800 rounded-md ${className || ''}`}
    >
      <WifiOff className="h-4 w-4" />
      <span>
        You're currently offline. Changes will be saved locally and synced when you reconnect.
      </span>
    </div>
  )
}