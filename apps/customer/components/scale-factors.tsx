import React from 'react'
import { ScaleFactors } from '@/types'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { 
  Target, 
  AlertTriangle, 
  Zap, 
  Shield, 
  Brain, 
  Percent, 
  Clock, 
  RotateCcw, 
  Globe,
  Activity,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { cn } from '@ui/lib/utils'

interface ScaleFactorsProps {
  scaleFactors: ScaleFactors
  className?: string
}

const getDurationColor = (duration: 'short' | 'medium' | 'long') => {
  switch (duration) {
    case 'short': return 'bg-green-500'
    case 'medium': return 'bg-yellow-500'
    case 'long': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getDurationLabel = (duration: 'short' | 'medium' | 'long') => {
  switch (duration) {
    case 'short': return 'Short-term'
    case 'medium': return 'Medium-term'
    case 'long': return 'Long-term'
    default: return 'Unknown'
  }
}

const getBooleanIcon = (value: boolean) => {
  return value ? (
    <CheckCircle className="w-4 h-4 text-green-600" />
  ) : (
    <XCircle className="w-4 h-4 text-gray-400" />
  )
}

const getBooleanText = (value: boolean) => {
  return value ? 'Yes' : 'No'
}

const getBooleanColor = (value: boolean) => {
  return value ? 'text-green-600' : 'text-gray-500'
}

interface ScaleFactorItemProps {
  icon: React.ReactNode
  label: string
  value: string | boolean
  description: string
  isBooleanValue?: boolean
}

function ScaleFactorItem({ 
  icon, 
  label, 
  value, 
  description,
  isBooleanValue = false
}: ScaleFactorItemProps) {
  const displayValue = isBooleanValue ? getBooleanText(value as boolean) : value as string
  const valueColor = isBooleanValue ? getBooleanColor(value as boolean) : 'text-slate-700 dark:text-slate-300'
  
  return (
    <div className="flex items-start gap-3 p-3 rounded-lg bg-slate-50/50 dark:bg-slate-800/50 backdrop-blur-sm">
      <div className="flex-shrink-0 p-2 rounded-full bg-white/80 dark:bg-slate-700/80 shadow-sm">
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
            {label}
          </h4>
          <span className={cn("text-sm font-semibold flex items-center gap-1", valueColor)}>
            {isBooleanValue && getBooleanIcon(value as boolean)}
            {displayValue}
          </span>
        </div>
        <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
          {description}
        </p>
      </div>
    </div>
  )
}

interface BooleanQuestionGroupProps {
  title: string
  icon: React.ReactNode
  questions: { key: keyof ScaleFactors; label: string; description: string }[]
  scaleFactors: ScaleFactors
}

function BooleanQuestionGroup({ title, icon, questions, scaleFactors }: BooleanQuestionGroupProps) {
  const trueQuestions = questions.filter(q => scaleFactors[q.key] === true)
  
  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2 mb-3">
        <div className="flex-shrink-0 p-2 rounded-full bg-white/80 dark:bg-slate-700/80 shadow-sm">
          {icon}
        </div>
        <h3 className="text-sm font-semibold text-slate-900 dark:text-slate-100">
          {title}
        </h3>
        <Badge variant="outline" className="ml-auto">
          {trueQuestions.length} / {questions.length}
        </Badge>
      </div>
      
      {trueQuestions.length > 0 ? (
        <div className="space-y-1">
          {trueQuestions.map((question) => (
            <div key={question.key} className="flex items-center gap-2 p-2 rounded bg-green-50/50 dark:bg-green-900/20">
              <CheckCircle className="w-3 h-3 text-green-600 flex-shrink-0" />
              <div className="min-w-0">
                <p className="text-xs font-medium text-slate-900 dark:text-slate-100">
                  {question.label}
                </p>
                <p className="text-xs text-slate-600 dark:text-slate-400">
                  {question.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex items-center gap-2 p-2 rounded bg-slate-50/50 dark:bg-slate-800/50">
          <XCircle className="w-3 h-3 text-gray-400 flex-shrink-0" />
          <p className="text-xs text-slate-600 dark:text-slate-400">
            None of the {title.toLowerCase()} criteria apply
          </p>
        </div>
      )}
    </div>
  )
}

export function ScaleFactorsDisplay({ scaleFactors, className }: ScaleFactorsProps) {
  return (
    <Card className={cn("glass-card", className)}>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Target className="w-5 h-5" />
          Scale Factors
          <Badge variant="outline" className="ml-auto">
            Impact Assessment
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Proximity to Tipping Point */}
        <ScaleFactorItem
          icon={<AlertTriangle className="w-4 h-4 text-orange-600" />}
          label="Tipping Point Proximity"
          value={scaleFactors.proximity_to_tipping_point}
          description="How close the impact brings us to critical environmental or social thresholds"
        />
        
        {/* Duration */}
        <div className="flex items-start gap-3 p-3 rounded-lg bg-slate-50/50 dark:bg-slate-800/50 backdrop-blur-sm">
          <div className="flex-shrink-0 p-2 rounded-full bg-white/80 dark:bg-slate-700/80 shadow-sm">
            <Clock className="w-4 h-4 text-amber-600" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
                Duration
              </h4>
              <Badge 
                variant="outline" 
                className={cn("text-white border-0", getDurationColor(scaleFactors.duration))}
              >
                {getDurationLabel(scaleFactors.duration)}
              </Badge>
            </div>
            <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
              How long the impact is expected to persist
            </p>
          </div>
        </div>

        {/* Boolean Question Groups */}
        <div className="space-y-4">
          <BooleanQuestionGroup
            title="Reversibility"
            icon={<RotateCcw className="w-4 h-4 text-teal-600" />}
            questions={[
              { key: 'is_irreversible', label: 'Completely irreversible', description: 'Impact is permanent (death, extinction, permanent damage)' },
              { key: 'takes_centuries_to_reverse', label: 'Takes centuries to reverse', description: 'Would require centuries or more to reverse' },
              { key: 'requires_significant_effort_to_reverse', label: 'Requires significant effort', description: 'Would need significant effort or resources to reverse' },
              { key: 'reversible_within_years', label: 'Reversible within years', description: 'Can be reversed within years (but not immediately)' },
              { key: 'reversible_within_months', label: 'Reversible within months', description: 'Can be reversed within months' },
              { key: 'reversible_within_weeks', label: 'Reversible within weeks', description: 'Can be reversed within weeks' },
              { key: 'fully_reversible_immediately', label: 'Fully reversible immediately', description: 'Can be fully reversed immediately with minimal effort' }
            ]}
            scaleFactors={scaleFactors}
          />

          <BooleanQuestionGroup
            title="Scale of Impact"
            icon={<Globe className="w-4 h-4 text-blue-600" />}
            questions={[
              { key: 'affects_all_global', label: 'Affects all globally', description: 'Affects all humans/animals or all ecosystems globally' },
              { key: 'affects_species_biome', label: 'Affects entire species/biomes', description: 'Affects entire species or biomes' },
              { key: 'affects_country_ecosystem', label: 'Affects countries/ecosystems', description: 'Affects entire countries or ecosystems' },
              { key: 'affects_large_population', label: 'Affects large populations', description: 'Affects large populations/cities or large forests' },
              { key: 'affects_many_beings', label: 'Affects many beings', description: 'Affects many humans/animals or many hectares' },
              { key: 'affects_multiple_individuals', label: 'Affects multiple individuals', description: 'Affects multiple individuals or small areas' },
              { key: 'affects_single_individual', label: 'Affects single individual', description: 'Affects single individuals or single hectare' }
            ]}
            scaleFactors={scaleFactors}
          />

          <BooleanQuestionGroup
            title="Directness"
            icon={<Target className="w-4 h-4 text-purple-600" />}
            questions={[
              { key: 'entity_sole_direct_cause', label: 'Sole direct cause', description: 'Entity is the sole and direct cause of this impact' },
              { key: 'direct_action_by_entity', label: 'Direct action', description: 'This was a direct action by the entity' },
              { key: 'entity_decision_caused_impact', label: 'Decision caused impact', description: 'Entity\'s decision directly caused this impact' },
              { key: 'entity_action_led_to_impact', label: 'Action led to impact', description: 'Entity\'s action led to this impact (indirect causation)' },
              { key: 'entity_influenced_outcome', label: 'Influenced outcome', description: 'Entity influenced the outcome that led to this impact' },
              { key: 'entity_had_minor_influence', label: 'Minor influence', description: 'Entity had minor influence on this impact' },
              { key: 'third_party_action', label: 'Third party action', description: 'Impact caused by third party with no entity involvement' }
            ]}
            scaleFactors={scaleFactors}
          />

          <BooleanQuestionGroup
            title="Authenticity"
            icon={<Shield className="w-4 h-4 text-green-600" />}
            questions={[
              { key: 'purely_altruistic', label: 'Purely altruistic', description: 'Action is purely altruistic with no business considerations' },
              { key: 'genuine_commitment', label: 'Genuine commitment', description: 'Represents genuine commitment to the impact' },
              { key: 'mostly_genuine_some_business', label: 'Mostly genuine', description: 'Mostly genuine with some business benefits' },
              { key: 'balanced_genuine_business', label: 'Balanced motives', description: 'Balances genuine and business motives equally' },
              { key: 'primarily_business_driven', label: 'Business driven', description: 'Primarily business driven with some genuine elements' },
              { key: 'mostly_regulatory_compliance', label: 'Regulatory compliance', description: 'Mostly driven by regulatory compliance' },
              { key: 'pure_marketing_greenwashing', label: 'Marketing/greenwashing', description: 'Pure marketing/greenwashing with no genuine intent' }
            ]}
            scaleFactors={scaleFactors}
          />

          <BooleanQuestionGroup
            title="Deliberateness"
            icon={<Brain className="w-4 h-4 text-indigo-600" />}
            questions={[
              { key: 'deliberately_planned_for_impact', label: 'Deliberately planned', description: 'Impact was deliberately planned and the primary goal' },
              { key: 'fully_intentional', label: 'Fully intentional', description: 'Impact was fully intentional' },
              { key: 'planned_with_awareness', label: 'Planned with awareness', description: 'Action was planned with clear awareness of consequences' },
              { key: 'intended_action_predictable_outcome', label: 'Predictable outcome', description: 'Intended action with predictable outcomes' },
              { key: 'knew_consequences_acted_anyway', label: 'Knew consequences', description: 'Entity acted despite knowing likely consequences' },
              { key: 'foreseeable_not_intended', label: 'Foreseeable not intended', description: 'Impact was foreseeable but not intended' },
              { key: 'completely_accidental', label: 'Completely accidental', description: 'Impact was completely accidental and unforeseeable' }
            ]}
            scaleFactors={scaleFactors}
          />

          <BooleanQuestionGroup
            title="Contribution"
            icon={<Percent className="w-4 h-4 text-red-600" />}
            questions={[
              { key: 'sole_contributor', label: 'Sole contributor', description: 'Entity is the sole contributor to this problem/solution' },
              { key: 'dominant_contributor', label: 'Dominant contributor', description: 'Entity is a dominant contributor' },
              { key: 'major_contributor', label: 'Major contributor', description: 'Entity is a major contributor' },
              { key: 'significant_contributor', label: 'Significant contributor', description: 'Entity is a significant contributor' },
              { key: 'minor_contributor', label: 'Minor contributor', description: 'Entity is a minor contributor' },
              { key: 'very_minor_contributor', label: 'Very minor contributor', description: 'Entity is a very minor contributor' },
              { key: 'not_contributing', label: 'Not contributing', description: 'Entity is not contributing to this problem/solution' }
            ]}
            scaleFactors={scaleFactors}
          />

          <BooleanQuestionGroup
            title="Scope"
            icon={<Zap className="w-4 h-4 text-yellow-600" />}
            questions={[
              { key: 'universal_impact', label: 'Universal impact', description: 'Affects all of humanity/nature universally' },
              { key: 'global_impact', label: 'Global impact', description: 'Has global scope' },
              { key: 'continental_impact', label: 'Continental impact', description: 'Has continental scope' },
              { key: 'national_impact', label: 'National impact', description: 'Has national scope' },
              { key: 'regional_impact', label: 'Regional impact', description: 'Has regional scope' },
              { key: 'local_city_impact', label: 'Local/city impact', description: 'Has local or city-level scope' },
              { key: 'individual_impact', label: 'Individual impact', description: 'Affects only individuals' }
            ]}
            scaleFactors={scaleFactors}
          />

          <BooleanQuestionGroup
            title="Degree"
            icon={<AlertTriangle className="w-4 h-4 text-red-600" />}
            questions={[
              { key: 'total_destruction_death', label: 'Total destruction/death', description: 'Causes total destruction, death, or ultimate benefit' },
              { key: 'near_total_destruction', label: 'Near-total destruction', description: 'Causes near-total destruction or maximum benefit' },
              { key: 'severe_permanent_damage', label: 'Severe permanent damage', description: 'Causes severe permanent damage or benefit' },
              { key: 'severe_harm', label: 'Severe harm', description: 'Causes severe harm or benefit' },
              { key: 'moderate_harm', label: 'Moderate harm', description: 'Causes moderate harm or benefit' },
              { key: 'minor_harm', label: 'Minor harm', description: 'Causes minor harm or benefit' },
              { key: 'no_harm', label: 'No harm', description: 'No harm or benefit from this impact' }
            ]}
            scaleFactors={scaleFactors}
          />

          <BooleanQuestionGroup
            title="Probability"
            icon={<Activity className="w-4 h-4 text-cyan-600" />}
            questions={[
              { key: 'has_already_happened', label: 'Already happened', description: 'Impact has already happened or is happening now' },
              { key: 'virtually_certain', label: 'Virtually certain', description: 'Impact is virtually certain to happen (~90% chance)' },
              { key: 'highly_likely', label: 'Highly likely', description: 'Impact is highly likely to occur (~70% chance)' },
              { key: 'even_chance', label: 'Even chance', description: 'Even chance this impact will occur (~50%)' },
              { key: 'unlikely', label: 'Unlikely', description: 'Impact is unlikely to occur (~30% chance)' },
              { key: 'very_unlikely', label: 'Very unlikely', description: 'Impact is very unlikely to occur (<10% chance)' },
              { key: 'will_not_happen', label: 'Will not happen', description: 'Impact will definitely not happen' }
            ]}
            scaleFactors={scaleFactors}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default ScaleFactorsDisplay