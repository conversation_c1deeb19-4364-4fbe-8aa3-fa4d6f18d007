"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@ui/components/ui/card'
import { Button } from '@ui/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@ui/components/ui/popover'
import { 
  HelpCircle,
  Heart,
  Users,
  Leaf
} from 'lucide-react'
import { cn } from '@utils/lib/utils'

const indicatorExamples = {
  animals: {
    harm: [
      { 
        name: "Species went extinct", 
        weight: 0.9,
        example: "The last member of a species dies due to the action"
      },
      { 
        name: "Many animals died", 
        weight: 0.6,
        example: "Thousands of animals killed in factory farming expansion"
      },
      { 
        name: "Animals injured", 
        weight: 0.4,
        example: "Wildlife injured by pollution or habitat disruption"
      }
    ],
    benefit: [
      { 
        name: "Many animals rescued", 
        weight: 0.8,
        example: "Thousands of animals saved from natural disaster"
      },
      { 
        name: "Habitat restored", 
        weight: 0.6,
        example: "Wetlands restored providing homes for wildlife"
      },
      { 
        name: "Species reintroduced", 
        weight: 0.7,
        example: "Extinct species successfully returned to the wild"
      }
    ]
  },
  humans: {
    harm: [
      { 
        name: "Genocide", 
        weight: 0.95,
        example: "Systematic extermination of a population group"
      },
      { 
        name: "Many humans died", 
        weight: 0.8,
        example: "Mass casualties from industrial accident"
      },
      { 
        name: "Livelihoods lost", 
        weight: 0.4,
        example: "Communities lose income sources permanently"
      }
    ],
    benefit: [
      { 
        name: "Many humans saved", 
        weight: 0.8,
        example: "Lives saved through medical intervention"
      },
      { 
        name: "Health improved", 
        weight: 0.6,
        example: "Community health measurably enhanced"
      },
      { 
        name: "Jobs created", 
        weight: 0.4,
        example: "Sustainable employment opportunities created"
      }
    ]
  },
  environment: {
    harm: [
      { 
        name: "Climate risk intensified", 
        weight: 0.85,
        example: "Actions that significantly increase GHG emissions"
      },
      { 
        name: "Biodiversity loss", 
        weight: 0.7,
        example: "Ecosystem diversity measurably reduced"
      },
      { 
        name: "Deforestation", 
        weight: 0.6,
        example: "Primary forest cleared for development"
      }
    ],
    benefit: [
      { 
        name: "Renewable energy", 
        weight: 0.8,
        example: "Clean energy infrastructure deployed"
      },
      { 
        name: "Carbon sequestered", 
        weight: 0.7,
        example: "Atmospheric CO2 captured and stored"
      },
      { 
        name: "Forest restored", 
        weight: 0.6,
        example: "Native forest ecosystem regenerated"
      }
    ]
  }
}

export function ImpactIndicatorsLegend() {
  const [selectedDimension, setSelectedDimension] = React.useState<'animals' | 'humans' | 'environment'>('animals')
  
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-1">
          <HelpCircle className="w-4 h-4" />
          How are scores calculated?
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96" align="end">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Impact Calculation Methodology</h4>
            <p className="text-xs text-muted-foreground mb-3">
              Base scores are determined by conditional priority logic, then adjusted by multiple scale factors including directness, reversibility, and scope.
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={selectedDimension === 'animals' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedDimension('animals')}
              className="gap-1"
            >
              <Heart className="w-3 h-3" />
              Animals
            </Button>
            <Button
              variant={selectedDimension === 'humans' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedDimension('humans')}
              className="gap-1"
            >
              <Users className="w-3 h-3" />
              Humans
            </Button>
            <Button
              variant={selectedDimension === 'environment' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedDimension('environment')}
              className="gap-1"
            >
              <Leaf className="w-3 h-3" />
              Environment
            </Button>
          </div>
          
          <div className="space-y-3">
            <div>
              <h5 className="text-sm font-medium text-red-600 mb-2">Harm Indicators</h5>
              <div className="space-y-2">
                {indicatorExamples[selectedDimension].harm.map((ind, i) => (
                  <div key={i} className="text-xs space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{ind.name}</span>
                      <span className="text-muted-foreground">Base: {ind.weight}</span>
                    </div>
                    <p className="text-muted-foreground pl-2">{ind.example}</p>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h5 className="text-sm font-medium text-green-600 mb-2">Benefit Indicators</h5>
              <div className="space-y-2">
                {indicatorExamples[selectedDimension].benefit.map((ind, i) => (
                  <div key={i} className="text-xs space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{ind.name}</span>
                      <span className="text-muted-foreground">Base: {ind.weight}</span>
                    </div>
                    <p className="text-muted-foreground pl-2">{ind.example}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="text-xs text-muted-foreground border-t pt-3">
            <p className="font-medium mb-1">Calculation Process:</p>
            <ul className="space-y-0.5 ml-3">
              <li>• Base score selected by priority (not highest weight)</li>
              <li>• Scale factors multiply base score (directness, scope, etc.)</li>
              <li>• Special caps: recycling/awareness (0.2), initiatives (0.1)</li>
              <li>• Final score validated within 0.0-1.0 range</li>
            </ul>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}