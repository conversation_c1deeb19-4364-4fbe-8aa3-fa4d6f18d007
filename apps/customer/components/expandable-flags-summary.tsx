"use client";

import React, { useState } from 'react'
import { Summarize } from '@/components/summarize'
import { Badge } from '@ui/components/ui/badge'
import { FlagTypeV2 } from '@/types'
import { useEntity } from './context/entity/entity-context'
import { truncate } from '@utils/text-utils'

interface ExpandableFlagsSummaryProps {
  flags: FlagTypeV2[];
  type: 'green' | 'red';
  model: string;
  className?: string;
  preamble: string;
  maxInitialFlags?: number;
}

export function ExpandableFlagsSummary({
  flags,
  type,
  model,
  className,
  preamble,
                                         maxInitialFlags = 10,
}: ExpandableFlagsSummaryProps) {
  const [isExpanded, setIsExpanded] = useState(false);


  const flagsForSummary = flags && flags.length > 0 ? flags.sort((a, b) => (b.model.impact || 0) - (a.model.impact || 0)).slice(0, isExpanded ? 40 : maxInitialFlags).map((item) => ({
    'id': item.id,
    'type': item.model.flag_type + '-flag',
    'issue': item.model.flag_title || item.issue || 'Untitled Flag',
    'reason': truncate(item.flag_summary || '', 1000),
  })) : []
  const hasMore = flags.length > maxInitialFlags;
  const entityContext = useEntity()

  // Create unique hash ID that changes when expansion state changes
  const hashId = `${entityContext.hash()}-${type}-flags-summary-${model}-${isExpanded ? 'expanded' : 'collapsed'}-${flags.length}`
  
  const expandedPreamble = isExpanded 
    ? preamble + " Include all actions mentioned in the data."
    : preamble;

  return (
    <div>
      <Summarize
        hashId={hashId}
        className={className}
        preamble={expandedPreamble}
        obj={flagsForSummary}
      />
      
      {hasMore && (
        <div className="mt-3 flex justify-start">
          <Badge 
            variant="secondary" 
            className="cursor-pointer hover:bg-muted/80 transition-colors"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded 
              ? 'Show summary of key actions only' 
              : `Show summary including all ${flags.length} actions`
            }
          </Badge>
        </div>
      )}
    </div>
  );
}
