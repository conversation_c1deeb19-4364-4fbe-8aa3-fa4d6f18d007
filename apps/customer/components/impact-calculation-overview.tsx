"use client"

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { 
  Calculator,
  ArrowRight,
  Scale,
  Activity,
  Target,
  Info
} from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { EventImpactMeasurement } from '@/types'
import { ImpactIndicatorsLegend } from './impact-indicators-legend'

interface CalculationOverviewProps {
  measurement: EventImpactMeasurement
}

export function CalculationOverview({ measurement }: CalculationOverviewProps) {
  const hasCalculationDetails = !!measurement.calculation_details
  
  if (!hasCalculationDetails) return null
  
  return (
    <Card className="glass-card bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/20 dark:to-purple-900/20">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Calculator className="w-5 h-5" />
          Impact Calculation Process
          <div className="ml-auto flex items-center gap-2">
            <ImpactIndicatorsLegend />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-muted-foreground">
          Impact scores are calculated through a transparent, multi-step process that ensures consistent and unbiased evaluation:
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Step 1 */}
          <div className="relative">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center flex-shrink-0">
                <Activity className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-sm mb-1">1. Indicator Analysis</h4>
                <p className="text-xs text-muted-foreground">
                  Boolean indicators identify specific impacts (e.g., "animals died", "habitat destroyed") with predefined weights
                </p>
              </div>
            </div>
            <div className="hidden md:block absolute top-5 -right-2 text-gray-400">
              <ArrowRight className="w-4 h-4" />
            </div>
          </div>
          
          {/* Step 2 */}
          <div className="relative">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
                <Scale className="w-5 h-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-sm mb-1">2. Scale Adjustment</h4>
                <p className="text-xs text-muted-foreground">
                  Factors like contribution level, directness, and authenticity modify the base score
                </p>
              </div>
            </div>
            <div className="hidden md:block absolute top-5 -right-2 text-gray-400">
              <ArrowRight className="w-4 h-4" />
            </div>
          </div>
          
          {/* Step 3 */}
          <div className="flex items-start gap-3">
            <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center flex-shrink-0">
              <Target className="w-5 h-5 text-green-600" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-sm mb-1">3. Final Score</h4>
              <p className="text-xs text-muted-foreground">
                Base severity × scale adjustments = final score, with special caps for specific scenarios
              </p>
            </div>
          </div>
        </div>
        

      </CardContent>
    </Card>
  )
}
