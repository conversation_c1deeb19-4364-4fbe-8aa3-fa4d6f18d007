'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@ui/components/ui/card'
import { Badge } from '@ui/components/ui/badge'
import { Separator } from '@ui/components/ui/separator'
import { Alert, AlertDescription } from '@ui/components/ui/alert'
import { AlertTriangle, CheckCircle, Eye, Info, RefreshCw, Shield } from 'lucide-react'
import { cn } from '@utils/lib/utils'
import { AssessmentFeedback, ReviewDecision } from '@/types'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import Markdown from 'react-markdown'

interface ReviewerFeedbackProps {
  reviewDecision?: ReviewDecision
  feedback?: AssessmentFeedback[]
}

const dimensionIcons = {
  animals: '🐾',
  humans: '👥',
  environment: '🌍'
}

export function ReviewerFeedback({ reviewDecision, feedback }: ReviewerFeedbackProps) {
  if (!reviewDecision || !feedback) return null
  
  const decision = reviewDecision?.decision
  const hasFeedback = (feedback && feedback.length > 0) ||
    (reviewDecision?.assessment_feedback && reviewDecision.assessment_feedback.length > 0)

  const feedbackList = feedback || reviewDecision?.assessment_feedback || []
  const biasDetected = reviewDecision.bias_detected
  const biasTypes = reviewDecision.bias_types || []
  const accuracyIssues = reviewDecision.accuracy_issues || []

  // Map bias types to display names
  const biasTypeMap: Record<string, boolean> = {
    'optimistic_bias': biasTypes.includes('optimistic_bias'),
    'anthropocentric_bias': biasTypes.includes('anthropocentric_bias'),
    'recency_bias': biasTypes.includes('recency_bias'),
    'contribution_bias': biasTypes.includes('contribution_bias'),
    'authenticity_bias': biasTypes.includes('authenticity_bias'),
    'directness_bias': biasTypes.includes('directness_bias'),
  }
  return (
    <Card className="glass-card bg-gradient-to-r from-amber-50/50 to-orange-50/50 dark:from-amber-900/20 dark:to-orange-900/20">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Shield className="w-5 h-5" />
          LLM Reviewer Assessment
          <span className="text-sm ml-auto">
            <Badge
              variant="outline"
              className={cn(
                reviewDecision.confidence_in_review === 'high' && "border-green-500 text-green-600",
                reviewDecision.confidence_in_review === 'medium' && "border-yellow-500 text-yellow-600",
                reviewDecision.confidence_in_review === 'low' && "border-red-500 text-red-600"
              )}
            >
              Cofidence: {reviewDecision.confidence_in_review}
            </Badge>
          </span>
          {decision && (
            <Badge 
              variant="outline" 
              className={cn(
                "",
                decision === 'accept' && "border-green-500 text-green-600",
                decision === 'amend' && "border-amber-500 text-amber-600",
                decision === 'redo' && "border-red-500 text-red-600"
              )}
            >
              {decision === 'accept' ? <CheckCircle className="w-3 h-3 mr-1" /> : 
               decision === 'amend' ? <AlertTriangle className="w-3 h-3 mr-1" /> :
               <RefreshCw className="w-3 h-3 mr-1" />}
              {decision.toUpperCase()}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Review Decision Details */}
        {reviewDecision && (
          <>
            <div className="space-y-3">
              {/* Review Reasoning */}
              <div className="space-y-2 text-xs">
                <div className="text-sm prose font-medium">Review Reasoning</div>
                <Markdown >{reviewDecision.reasoning}</Markdown>
              </div>

              {/* Bias Detection Summary */}
              <div className="space-y-2">
                <Separator />
                <div className="flex items-center gap-2 text-blue-600">
                  <Eye className="w-4 h-4" />
                  <span className="text-sm font-medium">Bias Detection</span>
                  {!biasDetected && (
                    <Badge variant="outline" className="ml-auto text-xs text-green-600 bg-green-50 border-green-200">
                      No bias detected
                    </Badge>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center gap-2">
                    <CheckCircle className={cn(
                      'w-3 h-3',
                      !biasTypeMap['optimistic_bias'] ? 'text-green-500' : 'text-red-500',
                    )} />
                    <span>Optimistic Bias</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className={cn(
                      'w-3 h-3',
                      !biasTypeMap['anthropocentric_bias'] ? 'text-green-500' : 'text-red-500',
                    )} />
                    <span>Anthropocentric Bias</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className={cn(
                      'w-3 h-3',
                      !biasTypeMap['recency_bias'] ? 'text-green-500' : 'text-red-500',
                    )} />
                    <span>Recency Bias</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className={cn(
                      "w-3 h-3",
                      !biasTypeMap['contribution_bias'] ? 'text-green-500' : 'text-red-500',
                    )} />
                    <span>Contribution Bias</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className={cn(
                      "w-3 h-3",
                      !biasTypeMap['authenticity_bias'] ? 'text-green-500' : 'text-red-500',
                    )} />
                    <span>Authenticity Bias</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className={cn(
                      "w-3 h-3",
                      !biasTypeMap['directness_bias'] ? 'text-green-500' : 'text-red-500',
                    )} />
                    <span>Directness Bias</span>
                  </div>
                </div>
              </div>


              {/* Accuracy Issues */}
              {reviewDecision.accuracy_issues.length > 0 && (
                <Alert className="border-red-200 bg-red-50/50 dark:bg-red-900/20">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="font-medium mb-1">Accuracy Issues</div>
                    <div className="text-xs space-y-1">
                      {reviewDecision.accuracy_issues.map((issue, i) => (
                        <div key={i}>• {issue}</div>
                      ))}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
              
              {/* General Comments */}
              {reviewDecision.general_comments && (
                <div className="p-3 bg-blue-50/50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Info className="w-4 h-4 text-blue-600 mt-0.5" />
                    <div className="text-sm">
                      <div className="font-medium text-blue-800 dark:text-blue-200 mb-1">Additional Comments</div>
                      <p className="text-xs text-blue-700 dark:text-blue-300">{reviewDecision.general_comments}</p>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Confidence Level */}

            </div>

            {hasFeedback && <Separator />}
          </>
        )}

        {/* Assessment Feedback */}
        {hasFeedback && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-amber-500" />
              Assessment Feedback
            </h4>
            <div className="space-y-2">
              {feedbackList.map((feedbackItem, i) => (
                <div key={i} className="p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{dimensionIcons[feedbackItem.dimension]}</span>
                      <span className="text-sm font-medium capitalize">{feedbackItem.dimension}</span>
                      <Badge variant="outline" className="text-xs">
                        {feedbackItem.assessment_type}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {feedbackItem.component}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-1 pl-7">
                    <div className="text-xs">
                      <span className="font-medium text-red-600">Issue:</span> {feedbackItem.issue}
                    </div>
                    <div className="text-xs">
                      <span className="font-medium text-green-600">Suggestion:</span> {feedbackItem.suggestion}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Info about feedback */}
        <div className="mt-4 p-3 bg-amber-50/30 dark:bg-amber-900/10 rounded-lg">
          <div className="flex items-start gap-2">
            <Info className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
            <p className="text-xs text-amber-800 dark:text-amber-200">
              An independent LLM reviewer analyzes each impact assessment for potential biases and quality issues.
              When issues are identified, feedback is provided on assessment components for re-evaluation rather than
              directly modifying calculated scores.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
