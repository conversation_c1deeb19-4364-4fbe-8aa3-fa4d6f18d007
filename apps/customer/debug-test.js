const { chromium } = require('@playwright/test');

async function debugDocumentsPage() {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({ 
    baseURL: 'http://localhost:3000'  // Use 3000 which is where the server is running
  });
  const page = await context.newPage();

  try {
    console.log('Starting debug of documents page...');
    
    // First try to login
    console.log('Navigating to login page...');
    await page.goto('/login?next=%2Fcustomer', { timeout: 30000 });
    await page.waitForLoadState('networkidle', { timeout: 30000 });

    // Fill login form
    console.log('Filling login form...');
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', 'test1_pass');
    
    // Submit and wait for navigation
    console.log('Submitting login...');
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/customer/, { timeout: 45000 });
    
    console.log('Login successful, current URL:', page.url());
    
    // Navigate to documents page
    console.log('Navigating to documents page...');
    await page.goto('/customer/documents', { timeout: 60000 });
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    console.log('Documents page loaded, current URL:', page.url());
    
    // Check for page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Check if React loaded
    const reactLoaded = await page.evaluate(() => {
      return typeof window.React !== 'undefined';
    });
    console.log('React loaded:', reactLoaded);
    
    // Check for New Document button
    const buttonExists = await page.locator('[data-testid="new-document-button"]').count();
    console.log('New Document button count:', buttonExists);
    
    if (buttonExists === 0) {
      // Check what buttons are actually on the page
      const allButtons = await page.locator('button').all();
      console.log('All buttons on page:');
      for (let i = 0; i < allButtons.length; i++) {
        const buttonText = await allButtons[i].textContent();
        const buttonId = await allButtons[i].getAttribute('data-testid');
        console.log(`  Button ${i}: "${buttonText}" (data-testid: ${buttonId})`);
      }
      
      // Check page content
      const bodyText = await page.locator('body').textContent();
      console.log('Page contains "New Document":', bodyText.includes('New Document'));
      console.log('Page contains "Documents":', bodyText.includes('Documents'));
      
      // Check for error messages
      const errorElements = await page.locator('.error, [role="alert"], .alert-error').all();
      if (errorElements.length > 0) {
        console.log('Error messages found:');
        for (let i = 0; i < errorElements.length; i++) {
          const errorText = await errorElements[i].textContent();
          console.log(`  Error ${i}: ${errorText}`);
        }
      }
    }
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'debug-documents-page.png', fullPage: true });
    console.log('Screenshot saved as debug-documents-page.png');
    
  } catch (error) {
    console.error('Error during debug:', error);
    await page.screenshot({ path: 'debug-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

debugDocumentsPage();