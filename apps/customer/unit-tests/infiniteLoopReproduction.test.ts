import { renderHook, act } from '@testing-library/react'
import { useGroupStatusManager } from '@/components/editor/context/hooks/useGroupStatusManager'
import { ReportComponent, DocumentAction } from '@/components/editor/context/DocumentContext'

// Mock console to track infinite loop warnings
const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation()

// Mock the getAllDescendantsFromState function
const mockGetAllDescendantsFromState = jest.fn()

jest.mock('../components/editor/context/hooks/useGroupStatusManager', () => {
  const actual = jest.requireActual('../components/editor/context/hooks/useGroupStatusManager')
  return {
    ...actual,
    getAllDescendantsFromState: mockGetAllDescendantsFromState
  }
})

describe('Infinite Loop Reproduction Tests', () => {
  let mockDispatch: jest.MockedFunction<React.Dispatch<DocumentAction>>
  let mockStateRef: React.MutableRefObject<{ components: Map<string, ReportComponent> }>
  let components: Map<string, ReportComponent>
  let dispatchCalls: DocumentAction[]

  beforeEach(() => {
    jest.clearAllMocks()
    jest.useFakeTimers()
    
    dispatchCalls = []
    mockDispatch = jest.fn().mockImplementation((action) => {
      dispatchCalls.push(action)
      
      // Simulate the actual state update that would happen in the real component
      if (action.type === 'COMPONENT_UPDATED') {
        const component = components.get(action.id)
        if (component) {
          components.set(action.id, { ...component, ...action.updates })
        }
      }
    })
    
    components = new Map()
    mockStateRef = { current: { components } }
    
    // Mock implementation that matches the real function
    mockGetAllDescendantsFromState.mockImplementation((groupId: string, comps: Map<string, ReportComponent>) => {
      const descendants: ReportComponent[] = []
      
      for (const [id, comp] of comps) {
        if (comp.parentId === groupId) {
          descendants.push(comp)
          // Recursively get children of children
          descendants.push(...mockGetAllDescendantsFromState(id, comps))
        }
      }
      
      return descendants
    })
  })

  afterEach(() => {
    jest.useRealTimers()
    consoleSpy.mockClear()
    consoleWarnSpy.mockClear()
  })

  const createComponent = (
    id: string, 
    type: 'report-group' | 'report-section', 
    status: 'idle' | 'loading' | 'loaded' | 'error' | 'preserved' | 'locked' = 'idle',
    parentId?: string
  ): ReportComponent => ({
    id,
    type,
    status,
    parentId,
    title: `${type} ${id}`,
    endpoint: type === 'report-section' ? '/api/test' : undefined
  })

  test('should reproduce the exact infinite loop scenario', async () => {
    // Setup the exact structure from the bug report
    const parentGroup = createComponent('parent-group', 'report-group', 'loading')
    const childGroup1 = createComponent('child-group-1', 'report-group', 'loading', 'parent-group')
    const childGroup2 = createComponent('child-group-2', 'report-group', 'loading', 'parent-group')
    const nestedChild = createComponent('nested-child', 'report-group', 'loading', 'child-group-2')
    
    const section1 = createComponent('section-1', 'report-section', 'loaded', 'child-group-1')
    const section2 = createComponent('section-2', 'report-section', 'loaded', 'child-group-1')
    const section3 = createComponent('section-3', 'report-section', 'loaded', 'child-group-2')
    const section4 = createComponent('section-4', 'report-section', 'loaded', 'nested-child')

    components.set('parent-group', parentGroup)
    components.set('child-group-1', childGroup1)
    components.set('child-group-2', childGroup2)
    components.set('nested-child', nestedChild)
    components.set('section-1', section1)
    components.set('section-2', section2)
    components.set('section-3', section3)
    components.set('section-4', section4)

    const { result } = renderHook(() => useGroupStatusManager({ dispatch: mockDispatch, stateRef: mockStateRef }))

    console.log('=== Starting infinite loop reproduction test ===')
    
    // Track the number of updates for each component
    const updateCounts = new Map<string, number>()
    const trackUpdate = (id: string) => {
      updateCounts.set(id, (updateCounts.get(id) || 0) + 1)
    }

    // Simulate the scenario: All sections are loaded, trigger group updates
    console.log('Initial state - all sections loaded, all groups loading')
    
    // This should trigger a cascade of updates
    act(() => {
      console.log('Triggering nested-child update...')
      result.current.updateGroupStatus('nested-child', true)
      trackUpdate('nested-child')
    })

    // Check what happened
    console.log('After nested-child update:')
    console.log('- nested-child status:', components.get('nested-child')?.status)
    console.log('- Dispatch calls:', dispatchCalls.length)

    // Now trigger child-group-2 update
    act(() => {
      console.log('Triggering child-group-2 update...')
      result.current.updateGroupStatus('child-group-2', true)
      trackUpdate('child-group-2')
    })

    console.log('After child-group-2 update:')
    console.log('- child-group-2 status:', components.get('child-group-2')?.status)
    console.log('- Dispatch calls:', dispatchCalls.length)

    // Trigger child-group-1 update
    act(() => {
      console.log('Triggering child-group-1 update...')
      result.current.updateGroupStatus('child-group-1', true)
      trackUpdate('child-group-1')
    })

    console.log('After child-group-1 update:')
    console.log('- child-group-1 status:', components.get('child-group-1')?.status)
    console.log('- Dispatch calls:', dispatchCalls.length)

    // Finally trigger parent update
    act(() => {
      console.log('Triggering parent-group update...')
      result.current.updateGroupStatus('parent-group', true)
      trackUpdate('parent-group')
    })

    console.log('After parent-group update:')
    console.log('- parent-group status:', components.get('parent-group')?.status)
    console.log('- Dispatch calls:', dispatchCalls.length)

    // Now simulate what happens when the parent update triggers child updates
    // This is where the infinite loop would occur
    console.log('\n=== Simulating potential infinite loop ===')
    
    let loopCount = 0
    const maxLoops = 10
    
    while (loopCount < maxLoops) {
      const initialDispatchCount = dispatchCalls.length
      
      // Check if parent-group is still not in final state
      const parentStatus = components.get('parent-group')?.status
      if (parentStatus === 'loaded') {
        console.log('Parent reached loaded state, breaking loop')
        break
      }
      
      console.log(`Loop ${loopCount + 1}: parent status is ${parentStatus}`)
      
      // Trigger updates that might cause loops
      act(() => {
        result.current.updateGroupStatus('parent-group', true)
        result.current.updateGroupStatus('child-group-2', true)
        result.current.updateGroupStatus('nested-child', true)
      })
      
      const newDispatchCount = dispatchCalls.length
      if (newDispatchCount === initialDispatchCount) {
        console.log('No new dispatches, breaking loop')
        break
      }
      
      loopCount++
      trackUpdate('loop-iteration')
    }

    console.log('\n=== Final Results ===')
    console.log('Update counts:', Object.fromEntries(updateCounts))
    console.log('Total dispatch calls:', dispatchCalls.length)
    console.log('Final statuses:')
    for (const [id, comp] of components) {
      if (comp.type === 'report-group') {
        console.log(`- ${id}: ${comp.status}`)
      }
    }

    // Analyze the results
    const parentFinalStatus = components.get('parent-group')?.status
    const childGroup1FinalStatus = components.get('child-group-1')?.status
    const childGroup2FinalStatus = components.get('child-group-2')?.status
    const nestedChildFinalStatus = components.get('nested-child')?.status

    console.log('\n=== Analysis ===')
    console.log('Expected: All groups should be "loaded" since all sections are loaded')
    console.log('Actual results:')
    console.log(`- parent-group: ${parentFinalStatus} (expected: loaded)`)
    console.log(`- child-group-1: ${childGroup1FinalStatus} (expected: loaded)`)
    console.log(`- child-group-2: ${childGroup2FinalStatus} (expected: loaded)`)
    console.log(`- nested-child: ${nestedChildFinalStatus} (expected: loaded)`)

    // Check for infinite loop indicators
    const maxUpdatesPerComponent = Math.max(...Array.from(updateCounts.values()))
    const totalUpdates = Array.from(updateCounts.values()).reduce((sum, count) => sum + count, 0)

    console.log(`\nInfinite loop indicators:`)
    console.log(`- Max updates per component: ${maxUpdatesPerComponent}`)
    console.log(`- Total updates: ${totalUpdates}`)
    console.log(`- Loop iterations: ${loopCount}`)

    // The test should identify the problem
    if (parentFinalStatus !== 'loaded' || loopCount >= maxLoops) {
      console.log('\n🚨 INFINITE LOOP DETECTED!')
      console.log('Root cause: Status updates are not propagating correctly in nested groups')
      
      // Identify which groups are stuck
      const stuckGroups = []
      if (nestedChildFinalStatus !== 'loaded') stuckGroups.push('nested-child')
      if (childGroup2FinalStatus !== 'loaded') stuckGroups.push('child-group-2')
      if (childGroup1FinalStatus !== 'loaded') stuckGroups.push('child-group-1')
      if (parentFinalStatus !== 'loaded') stuckGroups.push('parent-group')
      
      console.log('Stuck groups:', stuckGroups)
    } else {
      console.log('\n✅ No infinite loop detected - all groups reached loaded state')
    }

    // Assertions for the test
    expect(loopCount).toBeLessThan(maxLoops) // Should not hit max loops
    expect(parentFinalStatus).toBe('loaded') // Parent should eventually be loaded
    expect(totalUpdates).toBeLessThan(20) // Should not require excessive updates
  })

  test('should demonstrate the fix working correctly', async () => {
    // Same setup as above
    const parentGroup = createComponent('parent-group', 'report-group', 'loading')
    const childGroup1 = createComponent('child-group-1', 'report-group', 'loading', 'parent-group')
    const childGroup2 = createComponent('child-group-2', 'report-group', 'loading', 'parent-group')
    const nestedChild = createComponent('nested-child', 'report-group', 'loading', 'child-group-2')
    
    const section1 = createComponent('section-1', 'report-section', 'loaded', 'child-group-1')
    const section2 = createComponent('section-2', 'report-section', 'loaded', 'child-group-1')
    const section3 = createComponent('section-3', 'report-section', 'loaded', 'child-group-2')
    const section4 = createComponent('section-4', 'report-section', 'loaded', 'nested-child')

    components.set('parent-group', parentGroup)
    components.set('child-group-1', childGroup1)
    components.set('child-group-2', childGroup2)
    components.set('nested-child', nestedChild)
    components.set('section-1', section1)
    components.set('section-2', section2)
    components.set('section-3', section3)
    components.set('section-4', section4)

    const { result } = renderHook(() => useGroupStatusManager({ dispatch: mockDispatch, stateRef: mockStateRef }))

    console.log('=== Testing the fix with batch processing ===')
    
    // Use the batch update functionality
    act(() => {
      console.log('Using updateAllGroupStatuses (batch processing)...')
      result.current.updateAllGroupStatuses()
    })

    console.log('After batch update:')
    console.log('- Dispatch calls:', dispatchCalls.length)
    
    // Check final statuses
    const finalStatuses = {
      'parent-group': components.get('parent-group')?.status,
      'child-group-1': components.get('child-group-1')?.status,
      'child-group-2': components.get('child-group-2')?.status,
      'nested-child': components.get('nested-child')?.status
    }

    console.log('Final statuses after batch update:', finalStatuses)

    // With the fix, all groups should be loaded in a single batch
    expect(finalStatuses['nested-child']).toBe('loaded')
    expect(finalStatuses['child-group-1']).toBe('loaded')
    expect(finalStatuses['child-group-2']).toBe('loaded')
    expect(finalStatuses['parent-group']).toBe('loaded')
    
    // Should require minimal dispatch calls (one per group)
    expect(dispatchCalls.length).toBeLessThanOrEqual(4)
  })
})
