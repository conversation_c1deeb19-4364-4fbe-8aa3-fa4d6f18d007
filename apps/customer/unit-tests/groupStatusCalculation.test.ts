import { ReportComponent } from '@/components/editor/context/DocumentContext'

// Import the actual status calculation logic
// We'll need to extract this from useGroupStatusManager for testing
const calculateGroupStatus = (descendants: ReportComponent[]): string => {
  if (descendants.length === 0) {
    return 'idle'
  }

  const statuses = descendants.map(d => d.status)
  
  // If any descendant is in error state, group is in error
  if (statuses.some(s => s === 'error')) {
    return 'error'
  }
  
  // If any descendant is loading, group is loading
  if (statuses.some(s => s === 'loading')) {
    return 'loading'
  }
  
  // If all descendants are loaded, group is loaded
  if (statuses.every(s => s === 'loaded')) {
    return 'loaded'
  }
  
  // If any descendant is idle, group is idle
  if (statuses.some(s => s === 'idle')) {
    return 'idle'
  }
  
  // Default to idle
  return 'idle'
}

const getAllDescendants = (groupId: string, components: Map<string, ReportComponent>): ReportComponent[] => {
  const descendants: ReportComponent[] = []
  
  for (const [id, comp] of components) {
    if (comp.parentId === groupId) {
      descendants.push(comp)
      // Recursively get children of children
      descendants.push(...getAllDescendants(id, components))
    }
  }
  
  return descendants
}

describe('Group Status Calculation Logic', () => {
  const createComponent = (
    id: string, 
    type: 'report-group' | 'report-section', 
    status: 'idle' | 'loading' | 'loaded' | 'error' | 'preserved' | 'locked' = 'idle',
    parentId?: string
  ): ReportComponent => ({
    id,
    type,
    status,
    parentId,
    title: `${type} ${id}`,
    endpoint: type === 'report-section' ? '/api/test' : undefined
  })

  describe('Basic Status Calculation', () => {
    test('should return idle for empty descendants', () => {
      const result = calculateGroupStatus([])
      expect(result).toBe('idle')
    })

    test('should return loaded when all descendants are loaded', () => {
      const descendants = [
        createComponent('section1', 'report-section', 'loaded'),
        createComponent('section2', 'report-section', 'loaded')
      ]
      const result = calculateGroupStatus(descendants)
      expect(result).toBe('loaded')
    })

    test('should return loading when any descendant is loading', () => {
      const descendants = [
        createComponent('section1', 'report-section', 'loaded'),
        createComponent('section2', 'report-section', 'loading')
      ]
      const result = calculateGroupStatus(descendants)
      expect(result).toBe('loading')
    })

    test('should return error when any descendant has error', () => {
      const descendants = [
        createComponent('section1', 'report-section', 'loaded'),
        createComponent('section2', 'report-section', 'error')
      ]
      const result = calculateGroupStatus(descendants)
      expect(result).toBe('error')
    })

    test('should return idle when any descendant is idle', () => {
      const descendants = [
        createComponent('section1', 'report-section', 'loaded'),
        createComponent('section2', 'report-section', 'idle')
      ]
      const result = calculateGroupStatus(descendants)
      expect(result).toBe('idle')
    })
  })

  describe('Nested Group Status Scenarios', () => {
    test('should handle simple nested groups', () => {
      const components = new Map<string, ReportComponent>()
      
      // Setup: parent -> child-group -> section
      const parent = createComponent('parent', 'report-group', 'loading')
      const childGroup = createComponent('child-group', 'report-group', 'loading', 'parent')
      const section = createComponent('section', 'report-section', 'loaded', 'child-group')
      
      components.set('parent', parent)
      components.set('child-group', childGroup)
      components.set('section', section)

      // Test child group status (should be loaded because section is loaded)
      const childDescendants = getAllDescendants('child-group', components)
      expect(childDescendants).toHaveLength(1)
      expect(childDescendants[0].id).toBe('section')
      expect(calculateGroupStatus(childDescendants)).toBe('loaded')

      // Test parent group status (should be loading because child-group is still loading)
      const parentDescendants = getAllDescendants('parent', components)
      expect(parentDescendants).toHaveLength(2) // child-group + section
      expect(calculateGroupStatus(parentDescendants)).toBe('loading')

      // Update child group to loaded
      components.set('child-group', { ...childGroup, status: 'loaded' })
      
      // Now parent should be loaded too
      const updatedParentDescendants = getAllDescendants('parent', components)
      expect(calculateGroupStatus(updatedParentDescendants)).toBe('loaded')
    })

    test('should handle complex nested structure correctly', () => {
      const components = new Map<string, ReportComponent>()
      
      // Setup complex structure:
      //   root
      //   ├── group1
      //   │   ├── section1 (loaded)
      //   │   └── section2 (loaded)
      //   └── group2
      //       ├── section3 (loading)
      //       └── nested-group
      //           └── section4 (loaded)
      
      components.set('root', createComponent('root', 'report-group', 'loading'))
      components.set('group1', createComponent('group1', 'report-group', 'loading', 'root'))
      components.set('group2', createComponent('group2', 'report-group', 'loading', 'root'))
      components.set('nested-group', createComponent('nested-group', 'report-group', 'loading', 'group2'))
      components.set('section1', createComponent('section1', 'report-section', 'loaded', 'group1'))
      components.set('section2', createComponent('section2', 'report-section', 'loaded', 'group1'))
      components.set('section3', createComponent('section3', 'report-section', 'loading', 'group2'))
      components.set('section4', createComponent('section4', 'report-section', 'loaded', 'nested-group'))

      // Test nested-group (should be loaded)
      const nestedDescendants = getAllDescendants('nested-group', components)
      expect(nestedDescendants).toHaveLength(1)
      expect(calculateGroupStatus(nestedDescendants)).toBe('loaded')

      // Test group1 (should be loaded)
      const group1Descendants = getAllDescendants('group1', components)
      expect(group1Descendants).toHaveLength(2)
      expect(calculateGroupStatus(group1Descendants)).toBe('loaded')

      // Test group2 (should be loading because section3 is loading)
      const group2Descendants = getAllDescendants('group2', components)
      expect(group2Descendants).toHaveLength(3) // section3 + nested-group + section4
      expect(calculateGroupStatus(group2Descendants)).toBe('loading')

      // Test root (should be loading because group2 is loading)
      const rootDescendants = getAllDescendants('root', components)
      expect(rootDescendants).toHaveLength(6) // all descendants
      expect(calculateGroupStatus(rootDescendants)).toBe('loading')

      // Now load section3
      components.set('section3', createComponent('section3', 'report-section', 'loaded', 'group2'))
      
      // Update nested-group status
      components.set('nested-group', { ...components.get('nested-group')!, status: 'loaded' })
      
      // Now group2 should be loaded
      const updatedGroup2Descendants = getAllDescendants('group2', components)
      expect(calculateGroupStatus(updatedGroup2Descendants)).toBe('loaded')

      // Update group statuses
      components.set('group1', { ...components.get('group1')!, status: 'loaded' })
      components.set('group2', { ...components.get('group2')!, status: 'loaded' })
      
      // Now root should be loaded
      const updatedRootDescendants = getAllDescendants('root', components)
      expect(calculateGroupStatus(updatedRootDescendants)).toBe('loaded')
    })

    test('should identify the exact scenario causing infinite loops', () => {
      const components = new Map<string, ReportComponent>()
      
      // Recreate the exact scenario from the bug report
      components.set('parent-group', createComponent('parent-group', 'report-group', 'loading'))
      components.set('child-group-1', createComponent('child-group-1', 'report-group', 'loading', 'parent-group'))
      components.set('child-group-2', createComponent('child-group-2', 'report-group', 'loading', 'parent-group'))
      components.set('nested-child', createComponent('nested-child', 'report-group', 'loading', 'child-group-2'))
      
      components.set('section-1', createComponent('section-1', 'report-section', 'loaded', 'child-group-1'))
      components.set('section-2', createComponent('section-2', 'report-section', 'loaded', 'child-group-1'))
      components.set('section-3', createComponent('section-3', 'report-section', 'loaded', 'child-group-2'))
      components.set('section-4', createComponent('section-4', 'report-section', 'loaded', 'nested-child'))

      // All sections are loaded, let's trace the status calculation
      
      // Step 1: nested-child should be loaded
      const nestedChildDescendants = getAllDescendants('nested-child', components)
      console.log('nested-child descendants:', nestedChildDescendants.map(d => `${d.id}:${d.status}`))
      const nestedChildStatus = calculateGroupStatus(nestedChildDescendants)
      expect(nestedChildStatus).toBe('loaded')
      
      // Step 2: child-group-1 should be loaded
      const childGroup1Descendants = getAllDescendants('child-group-1', components)
      console.log('child-group-1 descendants:', childGroup1Descendants.map(d => `${d.id}:${d.status}`))
      const childGroup1Status = calculateGroupStatus(childGroup1Descendants)
      expect(childGroup1Status).toBe('loaded')
      
      // Step 3: child-group-2 should be loading because nested-child is still loading
      const childGroup2Descendants = getAllDescendants('child-group-2', components)
      console.log('child-group-2 descendants:', childGroup2Descendants.map(d => `${d.id}:${d.status}`))
      const childGroup2Status = calculateGroupStatus(childGroup2Descendants)
      expect(childGroup2Status).toBe('loading') // This is the problem!
      
      // The issue: nested-child status in components map is 'loading' but should be 'loaded'
      // This creates a situation where:
      // 1. nested-child should be loaded (all its sections are loaded)
      // 2. But nested-child status in map is still 'loading'
      // 3. So child-group-2 calculates as loading
      // 4. So parent-group calculates as loading
      // 5. Updates trigger, but nothing changes because the root cause (nested-child status) isn't updated
      
      // Fix: Update nested-child status
      components.set('nested-child', { ...components.get('nested-child')!, status: 'loaded' })
      
      // Now child-group-2 should be loaded
      const updatedChildGroup2Descendants = getAllDescendants('child-group-2', components)
      const updatedChildGroup2Status = calculateGroupStatus(updatedChildGroup2Descendants)
      expect(updatedChildGroup2Status).toBe('loaded')
      
      // Update child group statuses
      components.set('child-group-1', { ...components.get('child-group-1')!, status: 'loaded' })
      components.set('child-group-2', { ...components.get('child-group-2')!, status: 'loaded' })
      
      // Now parent should be loaded
      const parentDescendants = getAllDescendants('parent-group', components)
      const parentStatus = calculateGroupStatus(parentDescendants)
      expect(parentStatus).toBe('loaded')
    })
  })

  describe('Status Update Order Dependencies', () => {
    test('should demonstrate the importance of bottom-up updates', () => {
      const components = new Map<string, ReportComponent>()
      
      // Three-level nesting
      components.set('root', createComponent('root', 'report-group', 'loading'))
      components.set('middle', createComponent('middle', 'report-group', 'loading', 'root'))
      components.set('leaf', createComponent('leaf', 'report-group', 'loading', 'middle'))
      components.set('section', createComponent('section', 'report-section', 'loaded', 'leaf'))

      // Wrong order: Update root first (top-down)
      let rootDescendants = getAllDescendants('root', components)
      let rootStatus = calculateGroupStatus(rootDescendants)
      expect(rootStatus).toBe('loading') // Still loading because middle and leaf are loading

      // Correct order: Update leaf first (bottom-up)
      let leafDescendants = getAllDescendants('leaf', components)
      let leafStatus = calculateGroupStatus(leafDescendants)
      expect(leafStatus).toBe('loaded') // Should be loaded because section is loaded
      
      // Update leaf status in components
      components.set('leaf', { ...components.get('leaf')!, status: 'loaded' })
      
      // Now update middle
      let middleDescendants = getAllDescendants('middle', components)
      let middleStatus = calculateGroupStatus(middleDescendants)
      expect(middleStatus).toBe('loaded') // Should be loaded because leaf is now loaded
      
      // Update middle status in components
      components.set('middle', { ...components.get('middle')!, status: 'loaded' })
      
      // Finally update root
      rootDescendants = getAllDescendants('root', components)
      rootStatus = calculateGroupStatus(rootDescendants)
      expect(rootStatus).toBe('loaded') // Should be loaded because middle is now loaded
    })
  })
})
