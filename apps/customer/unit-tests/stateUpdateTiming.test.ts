/**
 * EKO-162: State Update Timing Issue Test
 *
 * This test demonstrates the root cause of the infinite loop:
 * The dispatch() call is asynchronous, but the status calculation logic
 * reads from the current state immediately, creating a timing mismatch.
 */

// Mock the DocumentContext types since we can't import them in unit tests
interface ReportComponent {
  id: string
  type: 'report-group' | 'report-section'
  status: string
  parentId?: string
  title: string
  endpoint?: string
}

describe('EKO-162: State Update Timing Issue', () => {
  const createComponent = (
    id: string, 
    type: 'report-group' | 'report-section', 
    status: string = 'idle',
    parentId?: string
  ): ReportComponent => ({
    id,
    type,
    status,
    parentId,
    title: `${type} ${id}`,
    endpoint: type === 'report-section' ? '/api/test' : undefined
  })

  const getAllDescendants = (groupId: string, components: Map<string, ReportComponent>): ReportComponent[] => {
    const descendants: ReportComponent[] = []
    
    for (const [id, comp] of components) {
      if (comp.parentId === groupId) {
        descendants.push(comp)
        descendants.push(...getAllDescendants(id, components))
      }
    }
    
    return descendants
  }

  const calculateGroupStatus = (descendants: ReportComponent[]): string => {
    if (descendants.length === 0) return 'loaded'

    const hasError = descendants.some(d => d.status === 'error')
    const hasIdleOrLoading = descendants.some(d => d.status === 'idle' || d.status === 'loading')
    const allLoaded = descendants.every(d => 
      d.status === 'loaded' || d.status === 'preserved' || d.status === 'locked'
    )

    if (hasIdleOrLoading) return 'loading'
    if (hasError) return 'error'
    if (allLoaded) return 'loaded'
    return 'loading'
  }

  test('demonstrates the exact timing issue causing infinite loops', () => {
    console.log('=== EKO-162: Demonstrating State Update Timing Issue ===')
    
    // Setup the exact nested structure from the bug
    const components = new Map<string, ReportComponent>()
    
    components.set('parent-group', createComponent('parent-group', 'report-group', 'loading'))
    components.set('child-group-1', createComponent('child-group-1', 'report-group', 'loading', 'parent-group'))
    components.set('child-group-2', createComponent('child-group-2', 'report-group', 'loading', 'parent-group'))
    components.set('nested-child', createComponent('nested-child', 'report-group', 'loading', 'child-group-2'))
    
    // All sections are loaded
    components.set('section-1', createComponent('section-1', 'report-section', 'loaded', 'child-group-1'))
    components.set('section-2', createComponent('section-2', 'report-section', 'loaded', 'child-group-1'))
    components.set('section-3', createComponent('section-3', 'report-section', 'loaded', 'child-group-2'))
    components.set('section-4', createComponent('section-4', 'report-section', 'loaded', 'nested-child'))

    console.log('\n1. Initial State - All sections loaded, all groups loading')
    for (const [id, comp] of components) {
      if (comp.type === 'report-group') {
        console.log(`   ${id}: ${comp.status}`)
      }
    }

    // Simulate the update process step by step
    console.log('\n2. Step 1: Update nested-child (deepest group)')
    const nestedChildDescendants = getAllDescendants('nested-child', components)
    const nestedChildNewStatus = calculateGroupStatus(nestedChildDescendants)
    console.log(`   nested-child descendants: ${nestedChildDescendants.map(d => `${d.id}:${d.status}`)}`)
    console.log(`   nested-child calculated status: ${nestedChildNewStatus}`)
    
    // ❌ PROBLEM: In real code, dispatch() is called here but state update is async
    // The next calculation happens before the state is actually updated
    console.log('   📝 dispatch({ type: "COMPONENT_UPDATED", id: "nested-child", updates: { status: "loaded" } })')
    console.log('   ⚠️  BUT: State update is async, so nested-child status is still "loading" in the map!')

    console.log('\n3. Step 2: Update child-group-2 (BEFORE nested-child state is updated)')
    const childGroup2Descendants = getAllDescendants('child-group-2', components)
    const childGroup2NewStatus = calculateGroupStatus(childGroup2Descendants)
    console.log(`   child-group-2 descendants: ${childGroup2Descendants.map(d => `${d.id}:${d.status}`)}`)
    console.log(`   child-group-2 calculated status: ${childGroup2NewStatus}`)
    console.log('   🚨 ISSUE: nested-child is still "loading" in the map, so child-group-2 calculates as "loading"!')

    console.log('\n4. Step 3: Update parent-group (BEFORE child groups are updated)')
    const parentDescendants = getAllDescendants('parent-group', components)
    const parentNewStatus = calculateGroupStatus(parentDescendants)
    console.log(`   parent-group descendants: ${parentDescendants.map(d => `${d.id}:${d.status}`)}`)
    console.log(`   parent-group calculated status: ${parentNewStatus}`)
    console.log('   🚨 ISSUE: All child groups are still "loading" in the map, so parent calculates as "loading"!')

    console.log('\n5. What should happen vs what actually happens:')
    console.log('   SHOULD HAPPEN:')
    console.log('   1. nested-child: loading → loaded (✓)')
    console.log('   2. child-group-2: loading → loaded (✓)')  
    console.log('   3. child-group-1: loading → loaded (✓)')
    console.log('   4. parent-group: loading → loaded (✓)')
    
    console.log('\n   ACTUALLY HAPPENS:')
    console.log('   1. nested-child: loading → loaded (dispatch called)')
    console.log('   2. child-group-2: loading → loading (because nested-child still shows "loading")')
    console.log('   3. parent-group: loading → loading (because child groups still show "loading")')
    console.log('   4. Status updates eventually propagate, triggering more updates...')
    console.log('   5. Infinite loop! 🔄')

    console.log('\n6. The Root Cause:')
    console.log('   - dispatch() calls are asynchronous')
    console.log('   - Status calculations read from current state synchronously')
    console.log('   - This creates a race condition where parent calculations happen')
    console.log('     before child state updates are applied')
    console.log('   - Result: Parent groups never see their children as "loaded"')

    console.log('\n7. The Solution:')
    console.log('   - Use batch processing with synchronous state updates')
    console.log('   - Process groups in bottom-up order (deepest first)')
    console.log('   - Apply all state changes before calculating parent statuses')
    console.log('   - OR: Use optimistic state updates in calculations')

    // Demonstrate the fix
    console.log('\n8. Demonstrating the fix with synchronous updates:')
    
    // Apply updates synchronously in bottom-up order
    components.set('nested-child', { ...components.get('nested-child')!, status: 'loaded' })
    console.log('   ✅ nested-child updated to loaded')
    
    components.set('child-group-1', { ...components.get('child-group-1')!, status: 'loaded' })
    console.log('   ✅ child-group-1 updated to loaded')
    
    // Now recalculate child-group-2 with updated nested-child
    const updatedChildGroup2Descendants = getAllDescendants('child-group-2', components)
    const updatedChildGroup2Status = calculateGroupStatus(updatedChildGroup2Descendants)
    console.log(`   child-group-2 recalculated: ${updatedChildGroup2Status}`)
    components.set('child-group-2', { ...components.get('child-group-2')!, status: updatedChildGroup2Status })
    console.log('   ✅ child-group-2 updated to loaded')
    
    // Now recalculate parent with all children updated
    const updatedParentDescendants = getAllDescendants('parent-group', components)
    const updatedParentStatus = calculateGroupStatus(updatedParentDescendants)
    console.log(`   parent-group recalculated: ${updatedParentStatus}`)
    components.set('parent-group', { ...components.get('parent-group')!, status: updatedParentStatus })
    console.log('   ✅ parent-group updated to loaded')

    console.log('\n9. Final State - All groups loaded:')
    for (const [id, comp] of components) {
      if (comp.type === 'report-group') {
        console.log(`   ${id}: ${comp.status}`)
      }
    }

    // Verify the fix worked
    expect(components.get('nested-child')?.status).toBe('loaded')
    expect(components.get('child-group-1')?.status).toBe('loaded')
    expect(components.get('child-group-2')?.status).toBe('loaded')
    expect(components.get('parent-group')?.status).toBe('loaded')

    console.log('\n✅ Fix verified: All groups reached loaded state without infinite loops!')
  })

  test('demonstrates why batch processing with optimistic updates fixes the issue', () => {
    console.log('\n=== Testing Batch Processing with Optimistic Updates ===')
    
    const components = new Map<string, ReportComponent>()
    
    // Same setup
    components.set('parent-group', createComponent('parent-group', 'report-group', 'loading'))
    components.set('child-group-1', createComponent('child-group-1', 'report-group', 'loading', 'parent-group'))
    components.set('child-group-2', createComponent('child-group-2', 'report-group', 'loading', 'parent-group'))
    components.set('nested-child', createComponent('nested-child', 'report-group', 'loading', 'child-group-2'))
    components.set('section-1', createComponent('section-1', 'report-section', 'loaded', 'child-group-1'))
    components.set('section-2', createComponent('section-2', 'report-section', 'loaded', 'child-group-1'))
    components.set('section-3', createComponent('section-3', 'report-section', 'loaded', 'child-group-2'))
    components.set('section-4', createComponent('section-4', 'report-section', 'loaded', 'nested-child'))

    // Simulate batch processing with optimistic updates
    const batchUpdates = new Map<string, string>()
    
    // Calculate all group statuses with optimistic updates
    const calculateWithOptimisticUpdates = (groupId: string, optimisticUpdates: Map<string, string>): string => {
      const descendants = getAllDescendants(groupId, components)
      
      // Apply optimistic updates to descendants
      const optimisticDescendants = descendants.map(d => ({
        ...d,
        status: optimisticUpdates.get(d.id) || d.status
      }))
      
      return calculateGroupStatus(optimisticDescendants)
    }

    // Process in depth order (deepest first)
    const groups = ['nested-child', 'child-group-1', 'child-group-2', 'parent-group']
    
    console.log('Processing groups with optimistic updates:')
    for (const groupId of groups) {
      const newStatus = calculateWithOptimisticUpdates(groupId, batchUpdates)
      batchUpdates.set(groupId, newStatus)
      console.log(`   ${groupId}: ${components.get(groupId)?.status} → ${newStatus}`)
    }

    // Apply all updates at once
    console.log('\nApplying all updates synchronously:')
    for (const [groupId, newStatus] of batchUpdates) {
      components.set(groupId, { ...components.get(groupId)!, status: newStatus })
      console.log(`   ✅ ${groupId} updated to ${newStatus}`)
    }

    // Verify all groups are loaded
    for (const groupId of groups) {
      expect(components.get(groupId)?.status).toBe('loaded')
    }

    console.log('\n✅ Batch processing with optimistic updates successful!')
  })
})
