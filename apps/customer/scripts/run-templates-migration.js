const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createDocumentTemplatesTable() {
  try {
    console.log('Creating document_templates table...')
    
    // Create the table directly using Supabase client
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS document_templates (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        description TEXT,
        category TEXT DEFAULT 'General',
        scope TEXT CHECK (scope IN ('global', 'customer', 'organisation')) DEFAULT 'global',
        content TEXT NOT NULL,
        data JSONB,
        metadata JSONB DEFAULT '{}'::jsonb,
        tags TEXT[] DEFAULT '{}',
        icon TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW(),
        created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
        updated_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
        organisation_id UUID,
        customer_id UUID
      );
    `
    
    // Try to execute using raw SQL if possible, otherwise create manually
    console.log('Attempting to create table...')
    
    // First, let's try a simple approach - just insert some templates and see if the table gets created
    const templates = [
      {
        name: 'Blank Document',
        description: 'Start with a clean slate',
        category: 'Basic',
        scope: 'global',
        content: '<p>Start writing your document here...</p>',
        data: {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Start writing your document here..."}]}]},
        metadata: {"template_type": "basic"},
        tags: ['basic', 'empty'],
        icon: 'FileText'
      },
      {
        name: 'ESG Report Template',
        description: 'Comprehensive ESG report with all sections',
        category: 'Reports',
        scope: 'global',
        content: '<report-group id="esg-report"><h1>ESG Report</h1><report-summary id="exec-summary" prompt="Executive summary of the ESG performance" title="Executive Summary" summarize="environmental,social,governance"/></report-group>',
        data: {"type":"doc","content":[{"type":"reportGroup","attrs":{"id":"esg-report"},"content":[{"type":"heading","attrs":{"level":1},"content":[{"type":"text","text":"ESG Report"}]}]}]},
        metadata: {"template_type": "report", "model": "sdg", "sections": ["environmental", "social", "governance"]},
        tags: ['report', 'esg', 'sustainability'],
        icon: 'BarChart3'
      },
      {
        name: 'EKO Report',
        description: 'Standard EKO report template with sections and groups',
        category: 'Reports',
        scope: 'global',
        content: '<report-group id="eko-report"><h1>EKO Report</h1><report-summary id="exec-summary" prompt="Executive summary" title="Executive Summary" summarize="impact,reliability,transparency"/><report-group id="impact-section"><h2>Impact Analysis</h2><report-section id="harm-impact" title="Harm Impact" endpoint="/report/entity/[ENTITY_ID]/harm/model/sdg/section/13_climate_action"/><report-section id="benefit-impact" title="Benefit Impact" endpoint="/report/entity/[ENTITY_ID]/benefit/model/sdg/section/13_climate_action"/></report-group><report-group id="reliability-section"><h2>Reliability Assessment</h2><report-section id="transparency" title="Transparency Score" endpoint="/report/entity/[ENTITY_ID]/transparency"/><report-section id="reliability" title="Reliability Analysis" endpoint="/report/entity/[ENTITY_ID]/reliability"/></report-group></report-group>',
        data: {"type":"doc","content":[{"type":"reportGroup","attrs":{"id":"eko-report"},"content":[{"type":"heading","attrs":{"level":1},"content":[{"type":"text","text":"EKO Report"}]},{"type":"reportSummary","attrs":{"id":"exec-summary","prompt":"Executive summary","title":"Executive Summary","summarize":"impact,reliability,transparency"}},{"type":"reportGroup","attrs":{"id":"impact-section"},"content":[{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Impact Analysis"}]},{"type":"reportSection","attrs":{"id":"harm-impact","title":"Harm Impact","endpoint":"/report/entity/[ENTITY_ID]/harm/model/sdg/section/13_climate_action"}},{"type":"reportSection","attrs":{"id":"benefit-impact","title":"Benefit Impact","endpoint":"/report/entity/[ENTITY_ID]/benefit/model/sdg/section/13_climate_action"}}]},{"type":"reportGroup","attrs":{"id":"reliability-section"},"content":[{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Reliability Assessment"}]},{"type":"reportSection","attrs":{"id":"transparency","title":"Transparency Score","endpoint":"/report/entity/[ENTITY_ID]/transparency"}},{"type":"reportSection","attrs":{"id":"reliability","title":"Reliability Analysis","endpoint":"/report/entity/[ENTITY_ID]/reliability"}}]}]}]},
        metadata: {"template_type": "report", "model": "sdg", "sections": ["impact", "reliability"]},
        tags: ['report', 'eko', 'standard'],
        icon: 'FileText'
      }
    ]
    
    console.log('Inserting templates...')
    
    for (const template of templates) {
      const { data, error } = await supabase
        .from('document_templates')
        .insert(template)
        .select()
        .single()
      
      if (error) {
        console.log(`Error inserting template "${template.name}":`, error)

        // If table doesn't exist, the error will tell us
        if (error.message && error.message.includes('does not exist')) {
          console.log('Table does not exist, need to create it first')
          return false
        }
      } else {
        console.log(`Successfully inserted template: ${template.name}`)
      }
    }
    
    console.log('Templates migration completed!')
    return true
    
  } catch (error) {
    console.error('Templates migration failed:', error)
    return false
  }
}

async function checkTemplatesTable() {
  try {
    console.log('Checking if document_templates table exists...')
    
    const { data, error } = await supabase
      .from('document_templates')
      .select('*')
      .limit(1)
    
    if (error) {
      console.log('Table check error:', error.message)
      return false
    } else {
      console.log('Table exists and is accessible')
      console.log(`Found ${data.length} templates`)
      return true
    }
  } catch (error) {
    console.error('Table check failed:', error)
    return false
  }
}

async function main() {
  const exists = await checkTemplatesTable()
  
  if (!exists) {
    console.log('Table does not exist, creating...')
    await createDocumentTemplatesTable()
  } else {
    console.log('Table already exists, checking templates...')
    await createDocumentTemplatesTable() // This will just insert templates
  }
}

main()
