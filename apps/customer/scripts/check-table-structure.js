const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkTableStructure() {
  try {
    console.log('Checking actual database table structures...')

    // Check doc_documents table structure
    console.log('\n=== doc_documents TABLE ===')
    try {
      // Try to get table info using information_schema
      const { data: colInfo, error: colError } = await supabase
        .rpc('exec_sql', { 
          sql: `
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'doc_documents' 
            AND table_schema = 'public'
            ORDER BY ordinal_position;
          `
        })
      
      if (colError) {
        console.log('Could not get column info via RPC, trying direct query...')
        
        // Alternative: Try to insert a test record to see what fields are expected
        const { data, error } = await supabase
          .from('doc_documents')
          .insert({
            id: 'test_structure_check',
            title: 'Test'
          })
          .select()
        
        if (error) {
          console.log('Insert error reveals table structure issues:', error)
        } else {
          console.log('Test insert successful, cleaning up...')
          await supabase
            .from('doc_documents')
            .delete()
            .eq('id', 'test_structure_check')
        }
      } else {
        console.log('Column information:', colInfo)
      }
    } catch (err) {
      console.log('Error checking doc_documents:', err.message)
    }
    
    // Check profiles table structure
    console.log('\n=== PROFILES TABLE ===')
    try {
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .limit(1)
      
      if (profilesError) {
        console.log('Profiles table error:', profilesError)
      } else {
        console.log('Profiles table accessible')
        if (profiles.length > 0) {
          console.log('Profile fields:', Object.keys(profiles[0]))
          console.log('Sample profile ID type:', typeof profiles[0].id)
        }
      }
    } catch (err) {
      console.log('Error checking profiles:', err.message)
    }
    
    // Try a simple document creation test with minimal fields
    console.log('\n=== TESTING DOCUMENT CREATION ===')
    try {
      const testId = `test_${Date.now()}`
      
      // Test with minimal required fields
      const { data: doc, error: docError } = await supabase
        .from('doc_documents')
        .insert({
          id: testId,
          title: 'Test Document'
        })
        .select()
        .single()
      
      if (docError) {
        console.log('Document creation error:', docError)
        
        // Try with different field combinations
        console.log('Trying with content field...')
        const { data: doc2, error: docError2 } = await supabase
          .from('doc_documents')
          .insert({
            id: testId + '_2',
            title: 'Test Document',
            content: 'Test content'
          })
          .select()
          .single()
        
        if (docError2) {
          console.log('Still failing:', docError2)
        } else {
          console.log('Success with content field!')
          console.log('Created document fields:', Object.keys(doc2))
          // Clean up
          await supabase.from('doc_documents').delete().eq('id', testId + '_2')
        }
      } else {
        console.log('Document creation successful!')
        console.log('Created document fields:', Object.keys(doc))
        // Clean up
        await supabase.from('doc_documents').delete().eq('id', testId)
      }
    } catch (err) {
      console.log('Error testing document creation:', err.message)
    }
    
  } catch (error) {
    console.error('Table structure check failed:', error)
  }
}

checkTableStructure()
