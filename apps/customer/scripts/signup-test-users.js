const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.test' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase URL or Anon Key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function signupTestUsers() {
  try {
    console.log('Signing up test users...')
    
    // <NAME_EMAIL>
    const { data: user1, error: error1 } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'test1_pass',
      options: {
        data: {
          name: 'Test User 1'
        }
      }
    })
    
    if (error1) {
      if (error1.message.includes('already registered')) {
        console.log('<EMAIL> already exists')
      } else {
        console.error('Error <NAME_EMAIL>:', error1.message)
      }
    } else {
      console.log('<NAME_EMAIL>:', user1.user?.id)
    }
    
    // <NAME_EMAIL>
    const { data: user2, error: error2 } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'test2_pass',
      options: {
        data: {
          name: 'Test User 2'
        }
      }
    })
    
    if (error2) {
      if (error2.message.includes('already registered')) {
        console.log('<EMAIL> already exists')
      } else {
        console.error('Error <NAME_EMAIL>:', error2.message)
      }
    } else {
      console.log('<NAME_EMAIL>:', user2.user?.id)
    }
    
    console.log('Test user signup completed!')
    console.log('Note: Users may need email confirmation if email confirmation is enabled.')
    
  } catch (error) {
    console.error('Failed to sign up test users:', error)
  }
}

// Run the script
signupTestUsers()