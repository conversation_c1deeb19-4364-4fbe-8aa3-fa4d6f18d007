const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.test' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key')
  console.error('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl)
  console.error('Service Key length:', supabaseServiceKey ? supabaseServiceKey.length : 'missing')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createTestUsers() {
  try {
    console.log('Creating test users...')
    
    // Create <EMAIL>
    const { data: user1, error: error1 } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'test1_pass',
      email_confirm: true,
      user_metadata: {
        name: 'Test User 1'
      }
    })
    
    if (error1) {
      if (error1.message.includes('already registered')) {
        console.log('<EMAIL> already exists')
      } else {
        console.error('<NAME_EMAIL>:', error1.message)
      }
    } else {
      console.log('Created <EMAIL>:', user1.user.id)
    }
    
    // Create <EMAIL>
    const { data: user2, error: error2 } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'test2_pass',
      email_confirm: true,
      user_metadata: {
        name: 'Test User 2'
      }
    })
    
    if (error2) {
      if (error2.message.includes('already registered')) {
        console.log('<EMAIL> already exists')
      } else {
        console.error('<NAME_EMAIL>:', error2.message)
      }
    } else {
      console.log('Created <EMAIL>:', user2.user.id)
    }
    
    console.log('Test user creation completed!')
    
  } catch (error) {
    console.error('Failed to create test users:', error)
  }
}

// Run the script
createTestUsers()