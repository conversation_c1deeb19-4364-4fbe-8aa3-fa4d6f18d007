const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testDocumentCreation() {
  try {
    console.log('Testing document creation...')
    
    // First, check if the table exists and its structure
    console.log('Checking table structure...')
    const { data: tableInfo, error: tableError } = await supabase
      .from('doc_documents')
      .select('*')
      .limit(1)
    
    if (tableError) {
      console.error('Table check error:', tableError)
      return
    }
    
    console.log('Table exists and is accessible')
    
    // Try to create a test document
    // Try to create a test document with UUID
    const { randomUUID } = require('crypto')
    const testDoc = {
      id: randomUUID(),
      entity_id: 'test-entity',
      run_id: 1,
      title: 'Test Document',
      content: '<p>Test content</p>',
      metadata: { test: true }
    }
    
    console.log('Attempting to insert test document...')
    const { data: document, error } = await supabase
      .from('doc_documents')
      .insert(testDoc)
      .select()
      .single()
    
    if (error) {
      console.error('Insert error:', error)
      return
    }
    
    console.log('Document created successfully:', document)
    
    // Clean up - delete the test document
    const { error: deleteError } = await supabase
      .from('doc_documents')
      .delete()
      .eq('id', testDoc.id)
    
    if (deleteError) {
      console.error('Delete error:', deleteError)
    } else {
      console.log('Test document cleaned up successfully')
    }
    
  } catch (error) {
    console.error('Test failed:', error)
  }
}

testDocumentCreation()
