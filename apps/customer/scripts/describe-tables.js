const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function describeTables() {
  try {
    console.log('Getting table descriptions from Supabase...')
    
    // Use Supabase's built-in table introspection
    console.log('\n=== CHECKING doc_documents TABLE ===')
    
    // Try to create a document with UUID
    const testUuid = crypto.randomUUID()
    console.log('Testing with UUID:', testUuid)
    
    const { data: doc, error: docError } = await supabase
      .from('doc_documents')
      .insert({
        id: testUuid,
        title: 'Test Document'
      })
      .select()
      .single()
    
    if (docError) {
      console.log('UUID test failed:', docError)
    } else {
      console.log('UUID test successful!')
      console.log('Document created:', doc)
      
      // Clean up
      await supabase.from('doc_documents').delete().eq('id', testUuid)
      console.log('Test document cleaned up')
    }
    
    // Check what happens when we try to get the current user
    console.log('\n=== CHECKING AUTH CONTEXT ===')
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError) {
      console.log('Auth error (expected with service key):', authError.message)
    } else if (user) {
      console.log('User found:', user.id)
    } else {
      console.log('No user (expected with service key)')
    }
    
    // Try to create a document with user context
    console.log('\n=== TESTING WITH USER CONTEXT ===')
    
    // First, let's see if there are any existing profiles
    const { data: existingProfiles, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1)
    
    if (profileError) {
      console.log('Profile query error:', profileError)
    } else if (existingProfiles.length > 0) {
      console.log('Found existing profile:', existingProfiles[0].id)
      
      // Try to create a document with this user ID
      const testUuid2 = crypto.randomUUID()
      const { data: doc2, error: docError2 } = await supabase
        .from('doc_documents')
        .insert({
          id: testUuid2,
          title: 'Test Document with User',
          created_by: existingProfiles[0].id,
          updated_by: existingProfiles[0].id
        })
        .select()
        .single()
      
      if (docError2) {
        console.log('Document with user creation failed:', docError2)
      } else {
        console.log('Document with user created successfully!')
        console.log('Document:', doc2)
        
        // Clean up
        await supabase.from('doc_documents').delete().eq('id', testUuid2)
        console.log('Test document with user cleaned up')
      }
    } else {
      console.log('No existing profiles found')
    }
    
  } catch (error) {
    console.error('Table description failed:', error)
  }
}

describeTables()
