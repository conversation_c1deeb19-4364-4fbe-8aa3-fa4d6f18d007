#!/usr/bin/env node

/**
 * Setup script for enabling test features
 * This script should be run before running tests to ensure all tested features are enabled
 */

const fs = require('fs');
const path = require('path');

// SQL to enable AI features for test users
const enableAIFeaturesSQL = `
-- Enable AI features for test users
DO $$
BEGIN
    -- Update <EMAIL> user's feature flags
    UPDATE profiles 
    SET feature_flags = ARRAY(
        SELECT DISTINCT unnest(
            COALESCE(feature_flags, ARRAY[]::text[]) || 
            ARRAY['document.editor.ai.chat', 'document.editor.ai.tools', 'document.editor.ai.edit']
        )
    )
    WHERE email = '<EMAIL>';

    -- Update <EMAIL> user's feature flags
    UPDATE profiles 
    SET feature_flags = ARRAY(
        SELECT DISTINCT unnest(
            COALESCE(feature_flags, ARRAY[]::text[]) || 
            ARRAY['document.editor.ai.chat', 'document.editor.ai.tools', 'document.editor.ai.edit']
        )
    )
    WHERE email = '<EMAIL>';

    -- Also update the organization's feature flags
    UPDATE acc_organisations
    SET feature_flags = ARRAY(
        SELECT DISTINCT unnest(
            COALESCE(feature_flags, ARRAY[]::text[]) || 
            ARRAY['document.editor.ai.chat', 'document.editor.ai.tools', 'document.editor.ai.edit']
        )
    )
    WHERE id IN (
        SELECT organisation 
        FROM profiles 
        WHERE email IN ('<EMAIL>', '<EMAIL>')
          AND organisation IS NOT NULL
    );
END $$;
`;

console.log('Setting up test features...');
console.log('To enable AI features for test users, run the following SQL in your database:');
console.log('----------------------------------------');
console.log(enableAIFeaturesSQL);
console.log('----------------------------------------');
console.log('');
console.log('Alternatively, set the following environment variable before running tests:');
console.log('NEXT_PUBLIC_TEST_FEATURE_FLAGS="document.editor.ai.tools,document.editor.ai.chat,document.editor.ai.edit"');
console.log('');
console.log('Note: The AI chat test (ai-edit-responses.spec.ts) requires these features to be enabled.');