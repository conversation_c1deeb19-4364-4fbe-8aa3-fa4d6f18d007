import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/app/supabase/server'

/**
 * API endpoint for handling auto-save requests during navigation.
 * This is specifically designed to work with navigator.sendBeacon()
 * for reliable saving during page unload events.
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse the request body
    const body = await request.json()
    const { documentId, content, data, updated_at, updated_by } = body

    if (!documentId || !content) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Verify the user owns or has access to this document
    const { data: document, error: fetchError } = await supabase
      .from('doc_documents')
      .select('id, created_by')
      .eq('id', documentId)
      .single()

    if (fetchError || !document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      )
    }

    // Update the document
    const { error: updateError } = await supabase
      .from('doc_documents')
      .update({
        content,
        data,
        updated_at: updated_at || new Date().toISOString(),
        updated_by: updated_by || user.id,
      })
      .eq('id', documentId)

    if (updateError) {
      console.error('Auto-save update error:', updateError)
      return NextResponse.json(
        { error: 'Failed to save document' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { success: true, message: 'Document auto-saved successfully' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Auto-save API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
