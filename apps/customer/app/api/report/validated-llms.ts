import { GoogleGenerative<PERSON>I } from '@google/generative-ai'
import { kv } from '@vercel/kv'
import crypto from 'crypto'

const version = 1.3

// Initialize the Google Generative AI client
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENERATIVE_AI_API_KEY || '')

// Model names enum
export enum LLMModel {
  GEMINI_FLASH = 'gemini-2.5-flash',
  GEMINI_FLASH_LITE = 'gemini-2.0-flash-lite',
  GEMINI_PRO = 'gemini-2.5-pro'
}

// Message interface
export interface Message {
  role: 'user' | 'assistant' | 'system'
  content: string
}

// Tool interface for function calling
export interface Tool {
  name: string
  description: string
  parameters: {
    type: 'object'
    properties: Record<string, any>
    required?: string[]
  }
  implementation: (args: any) => Promise<string> | string
}

// Tool call result interface
export interface ToolCall {
  name: string
  args: Record<string, any>
}

// Message with tool calls
export interface MessageWithTools extends Omit<Message, 'content'> {
  content?: string
  toolCalls?: ToolCall[]
  toolCallId?: string
}

// Options interface based on Python LLMOptions
export interface LLMOptions {
  autoRateLimit?: boolean
  eval?: (response: string | null) => boolean | string | Promise<boolean | string>
  evalRetry?: number
  noCache?: boolean
  temperature?: number
  cacheKey?: string
  useEitherKey?: boolean
  appendOnEvalFail?: boolean
  evalRetryMessage?: string
  metadata?: Record<string, any>
  escalateTo?: LLMModel[]
  cachePrefix?: string
  acceptFinalEvalFail?: boolean
  maxOutputTokens?: number
}

// Rate limiting state
const rateLimitState: Record<string, { lastCall: number; delay: number }> = {}

// Error types
export class LLMValidationError extends Error {
  constructor(message: string, public attempts: number, public lastResponse: string | null) {
    super(message)
    this.name = 'LLMValidationError'
  }
}

export class LLMRateLimitError extends Error {
  constructor(message: string, public retryAfter?: number) {
    super(message)
    this.name = 'LLMRateLimitError'
  }
}

/**
 * Generate cache key from content
 */
function generateCacheKey(content: string, options: LLMOptions): string {
  const prefix = options.cachePrefix || 'validated-llm'
  const hash = crypto.createHash('md5').update(JSON.stringify(content)).digest('hex')
  return options.cacheKey ? `${prefix}-${options.cacheKey}` : `${prefix}-${hash}`
}

/**
 * Handle rate limiting with exponential backoff
 */
async function handleRateLimit(model: LLMModel): Promise<void> {
  const now = Date.now()
  const state = rateLimitState[model]
  
  if (state && now - state.lastCall < state.delay) {
    const waitTime = state.delay - (now - state.lastCall)
    console.log(`[RATE_LIMIT] Waiting ${waitTime}ms for ${model}`)
    await new Promise(resolve => setTimeout(resolve, waitTime))
  }
}

/**
 * Update rate limit state on error
 */
function updateRateLimitState(model: LLMModel, error: any): void {
  const currentDelay = rateLimitState[model]?.delay || 1000
  const newDelay = Math.min(currentDelay * 2, 60000) // Max 60 seconds
  
  rateLimitState[model] = {
    lastCall: Date.now(),
    delay: newDelay
  }
  
  console.log(`[RATE_LIMIT] Updated delay for ${model}: ${newDelay}ms`)
}

/**
 * Call a single LLM model
 */
async function callSingleModel(
  model: LLMModel,
  messages: Message[],
  options: LLMOptions
): Promise<string> {
  await handleRateLimit(model)
  
  try {
    const geminiModel = genAI.getGenerativeModel({
      model,
      generationConfig: {
        maxOutputTokens: options.maxOutputTokens || 16000,
        temperature: options.temperature || 0.0,
      },
    })
    
    // Convert messages to prompt (simplified - could be enhanced)
    const prompt = messages.map(m => `${m.role}: ${m.content}`).join('\n\n')
    
    const result = await geminiModel.generateContent(prompt)
    const response = result.response
    const text = response.text()
    
    // Update successful call
    rateLimitState[model] = {
      lastCall: Date.now(),
      delay: Math.max((rateLimitState[model]?.delay || 1000) / 2, 1000) // Reduce delay on success
    }
    
    return text
  } catch (error: any) {
    // Handle rate limiting
    if (error.message?.includes('rate') || error.message?.includes('quota')) {
      updateRateLimitState(model, error)
      throw new LLMRateLimitError(`Rate limited for model ${model}`, 60)
    }
    
    throw error
  }
}

/**
 * Main validated LLM calling function
 * Reproduces the Python call_llms() functionality
 */
export async function callValidatedLLMs(
  models: LLMModel[],
  messages: Message[],
  options: LLMOptions = {}
): Promise<string | null> {
  const {
    evalRetry = 4,
    eval: evalFn,
    appendOnEvalFail = true,
    acceptFinalEvalFail = false,
    escalateTo = [],
    noCache = false,
    autoRateLimit = true
  } = options
  
  // Generate cache key
  const cacheKey = generateCacheKey(JSON.stringify({ models, messages, options }), options)
  
  // Check cache first
  if (!noCache) {
    try {
      const cached = await kv.get(cacheKey)
      if (cached) {
        console.log(`[CACHE] Hit for key: ${cacheKey}`)
        return cached as string
      }
    } catch (error) {
      console.log(`[CACHE] Error reading cache: ${error}`)
    }
  }
  
  // All models to try (main + escalation)
  const allModels = [...models, ...escalateTo]
  let lastError: Error | null = null
  let finalResponse: string | null = null
  
  // Try each model
  for (const model of allModels) {
    console.log(`[LLM] Trying model: ${model}`)
    
    let attempt = 0
    let lastResponse: string | null = null
    let currentMessages = [...messages]
    let currentOptions = { ...options }
    
    // Retry loop for current model
    while (attempt < evalRetry) {
      attempt++
      
      try {
        const response = await callSingleModel(model, currentMessages, currentOptions)
        lastResponse = response
        finalResponse = response // Track last response across all models
        
        // Evaluate response if eval function provided
        if (evalFn) {
          console.log(`[EVAL] Evaluating response (attempt ${attempt}/${evalRetry})`)
          const evalResult = await evalFn(response)
          
          if (evalResult === true) {
            console.log(`[EVAL] Validation passed on attempt ${attempt}`)
            
            // Cache successful result
            if (!noCache) {
              try {
                await kv.set(cacheKey, response, { ex: 3600 }) // 1 hour TTL
              } catch (error) {
                console.log(`[CACHE] Error writing cache: ${error}`)
              }
            }
            
            return response
          } else if (typeof evalResult === 'string') {
            console.log(`[EVAL] Validation failed with message: ${evalResult}`)
            
            if (appendOnEvalFail && attempt < evalRetry) {
              // Append failed response and eval feedback to conversation
              currentMessages = [
                ...currentMessages,
                { role: 'assistant', content: response },
                { role: 'user', content: `That response failed validation: ${evalResult}. Please try again.` }
              ]
            }
          } else {
            console.log(`[EVAL] Validation failed (returned false)`)
            
            if (appendOnEvalFail && attempt < evalRetry) {
              const retryMessage = options.evalRetryMessage || 'That response did not meet the validation criteria. Please try again.'
              currentMessages = [
                ...currentMessages,
                { role: 'assistant', content: response },
                { role: 'user', content: retryMessage }
              ]
            }
          }
        } else {
          // No evaluation function, return response
          if (!noCache) {
            try {
              await kv.set(cacheKey, response, { ex: 3600 })
            } catch (error) {
              console.log(`[CACHE] Error writing cache: ${error}`)
            }
          }
          return response
        }
        
        // Increase temperature for retry
        currentOptions.temperature = Math.min((currentOptions.temperature || 0) + 0.1, 0.9)
        
      } catch (error: any) {
        lastError = error
        console.log(`[LLM] Error on attempt ${attempt} with ${model}: ${error.message}`)
        
        // If rate limited and auto rate limiting enabled, wait and retry
        if (error instanceof LLMRateLimitError && autoRateLimit && attempt < evalRetry) {
          await new Promise(resolve => setTimeout(resolve, (error.retryAfter || 60) * 1000))
          continue
        }
        
        // Break out of retry loop for this model on non-rate-limit errors
        break
      }
    }
    
    console.log(`[LLM] Model ${model} failed after ${attempt} attempts`)
  }
  
  // All models failed
  if (acceptFinalEvalFail && finalResponse) {
    console.log(`[LLM] Accepting final eval failure, returning last response`)
    return finalResponse
  }
  
  const errorMessage = evalFn 
    ? `Validation failed after trying ${allModels.length} models with ${evalRetry} retries each`
    : `All models failed: ${lastError?.message || 'Unknown error'}`
  
  throw new LLMValidationError(errorMessage, evalRetry * allModels.length, finalResponse)
}

/**
 * Call LLMs with tool support (function calling)
 * Similar to the Python call_llms_tools function
 */
export async function callValidatedLLMsWithTools(
  models: LLMModel[],
  messages: Message[],
  tools: Tool[],
  options: LLMOptions = {},
  maxToolIterations: number = 50,
): Promise<string | null> {
  const {
    evalRetry = 4,
    eval: evalFn,
    appendOnEvalFail = true,
    acceptFinalEvalFail = false,
    escalateTo = [],
    noCache = false,
    autoRateLimit = true
  } = options
  
  // Generate cache key including tools
  const cacheKey = generateCacheKey(JSON.stringify({
    models,
    messages,
    version: version,
    tools: tools.map(t => ({ name: t.name, description: t.description, parameters: t.parameters })),
  }), options)
  
  // Check cache first
  if (!noCache) {
    try {
      const cached = await kv.get(cacheKey)
      if (cached) {
        console.log(`[CACHE] Hit for tools key: ${cacheKey}`)
        return cached as string
      }
    } catch (error) {
      console.log(`[CACHE] Error reading cache: ${error}`)
    }
  }
  
  // All models to try (main + escalation)
  const allModels = [...models, ...escalateTo]
  let lastError: Error | null = null
  let finalResponse: string | null = null
  
  // Try each model
  for (const model of allModels) {
    console.log(`[LLM] Trying model with tools: ${model}`)
    
    let attempt = 0
    let lastResponse: string | null = null
    let currentMessages = [...messages]
    let currentOptions = { ...options }
    
    // Retry loop for current model
    while (attempt < evalRetry) {
      attempt++
      
      try {
        // Execute the full tool calling loop
        const response = await executeToolCallingLoop(model, currentMessages, tools, currentOptions, maxToolIterations)
        lastResponse = response
        finalResponse = response
        
        // Evaluate response if eval function provided
        if (evalFn) {
          console.log(`[EVAL] Evaluating response (attempt ${attempt}/${evalRetry})`)
          const evalResult = await evalFn(response)
          
          if (evalResult === true) {
            console.log(`[EVAL] Validation passed on attempt ${attempt}`)
            
            // Cache successful result
            if (!noCache) {
              try {
                await kv.set(cacheKey, response, { ex: 3600 })
              } catch (error) {
                console.log(`[CACHE] Error writing cache: ${error}`)
              }
            }
            
            return response
          } else if (typeof evalResult === 'string') {
            console.log(`[EVAL] Validation failed with message: ${evalResult}`)
            
            if (appendOnEvalFail && attempt < evalRetry) {
              currentMessages = [
                ...currentMessages,
                { role: 'assistant', content: response },
                { role: 'user', content: `That response failed validation: ${evalResult}. Please try again.` }
              ]
            }
          } else {
            console.log(`[EVAL] Validation failed (returned false)`)
            
            if (appendOnEvalFail && attempt < evalRetry) {
              const retryMessage = options.evalRetryMessage || 'That response did not meet the validation criteria. Please try again.'
              currentMessages = [
                ...currentMessages,
                { role: 'assistant', content: response },
                { role: 'user', content: retryMessage }
              ]
            }
          }
        } else {
          // No evaluation function, return response
          if (!noCache) {
            try {
              await kv.set(cacheKey, response, { ex: 3600 })
            } catch (error) {
              console.log(`[CACHE] Error writing cache: ${error}`)
            }
          }
          return response
        }
        
        // Increase temperature for retry
        currentOptions.temperature = Math.min((currentOptions.temperature || 0) + 0.1, 0.9)
        
      } catch (error: any) {
        lastError = error
        console.log(`[LLM] Error on attempt ${attempt} with ${model}: ${error.message}`)
        
        // If rate limited and auto rate limiting enabled, wait and retry
        if (error instanceof LLMRateLimitError && autoRateLimit && attempt < evalRetry) {
          await new Promise(resolve => setTimeout(resolve, (error.retryAfter || 60) * 1000))
          continue
        }
        
        // Break out of retry loop for this model on non-rate-limit errors
        break
      }
    }
    
    console.log(`[LLM] Model ${model} failed after ${attempt} attempts`)
  }
  
  // All models failed
  if (acceptFinalEvalFail && finalResponse) {
    console.log(`[LLM] Accepting final eval failure, returning last response`)
    return finalResponse
  }
  
  const errorMessage = evalFn 
    ? `Validation failed after trying ${allModels.length} models with ${evalRetry} retries each`
    : `All models failed: ${lastError?.message || 'Unknown error'}`
  
  throw new LLMValidationError(errorMessage, evalRetry * allModels.length, finalResponse)
}

/**
 * Execute the tool calling loop for a single model
 */
async function executeToolCallingLoop(
  model: LLMModel,
  messages: Message[],
  tools: Tool[],
  options: LLMOptions,
  maxIterations: number
): Promise<string> {
  let currentMessages = [...messages]
  let iteration = 0
  let lastToolCalls: string[] = []
  let repetitionCount = 0
  
  // Prepare tools in Google's function calling format
  const functionDeclarations = tools.map(tool => ({
    name: tool.name,
    description: tool.description,
    parameters: tool.parameters
  }))
  
  while (iteration < maxIterations) {
    iteration++
    console.log(`[TOOLS] Tool calling iteration ${iteration}/${maxIterations}`)
    
    await handleRateLimit(model)
    
    try {
      const geminiModel = genAI.getGenerativeModel({
        model,
        generationConfig: {
          maxOutputTokens: options.maxOutputTokens || 16000,
          temperature: options.temperature || 0.0,
        },
        tools: functionDeclarations.length > 0 ? [{
          functionDeclarations: functionDeclarations as any
        }] : undefined,
      })
      
      // Convert messages to proper format for Gemini
      const contents = convertMessagesToGeminiFormat(currentMessages)
      
      const result = await geminiModel.generateContent({
        contents,
      })
      
      const response = result.response
      
      // Check if the model wants to call functions
      const functionCalls = response.functionCalls()
      
      if (functionCalls && functionCalls.length > 0) {
        console.log(`[TOOLS] Model requested ${functionCalls.length} function call(s)`)
        
        // Check for repetitive tool calls
        const currentToolSignature = functionCalls.map(fc => `${fc.name}:${JSON.stringify(fc.args)}`).join('|')
        if (lastToolCalls.includes(currentToolSignature)) {
          repetitionCount++
          console.log(`[TOOLS] Detected repetitive tool call (${repetitionCount}/3): ${functionCalls.map(fc => fc.name).join(', ')}`)
          if (repetitionCount >= 3) {
            console.log(`[TOOLS] Stopping due to repetitive tool calls`)
            return `Tool calling stopped due to repetitive calls. Last successful tool executions completed.`
          }
        } else {
          repetitionCount = 0
        }
        
        // Track recent tool calls (keep last 3)
        lastToolCalls.push(currentToolSignature)
        if (lastToolCalls.length > 3) {
          lastToolCalls.shift()
        }
        
        // Execute each function call
        const functionResponses = []
        
        for (const functionCall of functionCalls) {
          const tool = tools.find(t => t.name === functionCall.name)
          if (!tool) {
            throw new Error(`Tool '${functionCall.name}' not found`)
          }
          
          console.log(`[TOOLS] Executing tool: ${functionCall.name} with args:`, functionCall.args)
          
          try {
            const result = await tool.implementation(functionCall.args || {})
            functionResponses.push({
              name: functionCall.name,
              response: {
                name: functionCall.name,
                content: result
              }
            })
            console.log(`[TOOLS] Tool ${functionCall.name} completed successfully`)
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error)
            functionResponses.push({
              name: functionCall.name,
              response: {
                name: functionCall.name,
                content: `Error: ${errorMsg}`
              }
            })
            console.error(`[TOOLS] Tool ${functionCall.name} failed:`, error)
          }
        }
        
        // Add function call and response to conversation
        currentMessages.push({
          role: 'assistant',
          content: `I'll call the following functions: ${functionCalls.map(fc => fc.name).join(', ')}`
        })
        
        // Add function responses with guidance
        for (const fr of functionResponses) {
          currentMessages.push({
            role: 'user',
            content: `Function ${fr.name} result: ${fr.response.content}`
          })
        }
        
        // Add guidance after several iterations
        if (iteration >= 10) {
          currentMessages.push({
            role: 'user',
            content: `You've made ${iteration} tool calls. Please provide your final response now without calling more tools unless absolutely necessary.`
          })
        }
        
        // Continue the loop to get the model's next response

      } else {
        // No function calls, return the final response
        const text = response.text()
        console.log(`[TOOLS] Final response received (${text.length} chars)`)
        
        // Update successful call
        rateLimitState[model] = {
          lastCall: Date.now(),
          delay: Math.max((rateLimitState[model]?.delay || 1000) / 2, 1000)
        }
        
        return text
      }
      
    } catch (error: any) {
      // Handle rate limiting
      if (error.message?.includes('rate') || error.message?.includes('quota')) {
        updateRateLimitState(model, error)
        throw new LLMRateLimitError(`Rate limited for model ${model}`, 60)
      }
      
      throw error
    }
  }
  
  throw new Error(`Tool calling loop exceeded maximum iterations (${maxIterations})`)
}

/**
 * Convert messages to Gemini's content format
 */
function convertMessagesToGeminiFormat(messages: Message[]) {
  return messages.map(message => ({
    role: message.role === 'assistant' ? 'model' : 'user',
    parts: [{ text: message.content }]
  }))
}

/**
 * Convenience function for simple calls without validation
 */
export async function callLLM(
  model: LLMModel,
  prompt: string,
  options: Omit<LLMOptions, 'eval'> = {}
): Promise<string> {
  const messages: Message[] = [{ role: 'user', content: prompt }]
  const result = await callValidatedLLMs([model], messages, options)
  
  if (!result) {
    throw new Error('LLM returned null response')
  }
  
  return result
}

/**
 * Type-safe validation helper
 */
export function createJSONValidator<T>(
  schema: (obj: any) => obj is T,
  errorMessage?: string
) {
  return (response: string | null): boolean | string => {
    if (!response) {
      return errorMessage || 'Response is null or empty'
    }
    
    try {
      const parsed = JSON.parse(response)
      if (schema(parsed)) {
        return true
      } else {
        return errorMessage || 'Response does not match expected schema'
      }
    } catch (error) {
      return `Invalid JSON: ${error instanceof Error ? error.message : 'Unknown parsing error'}`
    }
  }
}

/**
 * Simple text validation helper
 */
export function createTextValidator(
  validator: (text: string) => boolean,
  errorMessage?: string
) {
  return (response: string | null): boolean | string => {
    if (!response) {
      return errorMessage || 'Response is null or empty'
    }
    
    if (validator(response)) {
      return true
    } else {
      return errorMessage || 'Response does not meet validation criteria'
    }
  }
}
