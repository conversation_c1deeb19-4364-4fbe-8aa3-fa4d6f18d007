'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/app/supabase/client'
import { useToast } from '@ui/hooks/use-toast'
import { DocumentTemplates } from '@/components/editor/templates/DocumentTemplates'
import { validateDocumentCreationParams } from '@/utils/document-utils'

export default function NewDocumentPage() {
  const router = useRouter()
  const { toast } = useToast()
  const supabase = createClient()

  // Set page title
  useEffect(() => {
    // Add a small delay to ensure the component is fully mounted
    const timer = setTimeout(() => {
      document.title = 'Create New Document'
    }, 100)
    
    return () => clearTimeout(timer)
  }, [])

  const handleSelectTemplate = async (template: any, entityId?: string, runId?: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        throw new Error('User not authenticated')
      }

      const { entityId: validatedEntityId, runId: actualRunId } = await validateDocumentCreationParams(entityId, runId)

      // Generate a unique document ID
      const documentId = crypto.randomUUID()

      const { data: document, error } = await supabase
        .from('doc_documents')
        .insert({
          id: documentId,
          title: template.name === 'Blank Document' ? 'Untitled Document' : template.name,
          content: template.content || '',
          initial_content: template.content || '',
          data: template.data || null,
          created_by: user.id,
          updated_by: user.id,
          entity_id: validatedEntityId,
          run_id: actualRunId,
          metadata: {
            template_id: template.id,
            entity_id: entityId,
            run_id: runId
          }
        })
        .select()
        .single()

      if (error) {
        throw error
      }

      toast({
        title: 'Success',
        description: 'Document created successfully'
      })

      // Navigate to the new document
      router.push(`/customer/documents/${document.id}`)
    } catch (error) {
      console.error('Error creating document:', error)
      toast({
        title: 'Error',
        description: 'Failed to create document',
        variant: 'destructive'
      })
    }
  }

  const handleCancel = () => {
    router.push('/customer/documents')
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <DocumentTemplates
          onSelectTemplate={handleSelectTemplate}
          onClose={handleCancel}
          className="w-full"
        />
      </div>
    </div>
  )
}
