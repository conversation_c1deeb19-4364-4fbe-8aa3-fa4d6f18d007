'use client';

import React from 'react';
import { ClaimTypeV2 } from '@/types/claim';
import { Badge } from '@ui/components/ui/badge';
import { CheckCircle2, CircleXIcon, HelpCircle, Calendar } from 'lucide-react';

interface ClaimsTimelineProps {
    claimsData: ClaimTypeV2[] | undefined | null;
}

export function ClaimsTimeline({ claimsData }: ClaimsTimelineProps) {
    if (!claimsData || claimsData.length === 0) {
        return null;
    }

    // Sort claims by year (most recent first)
    const sortedClaims = [...claimsData].sort((a, b) => {
        const yearA = a.model.claim_doc_year || new Date().getFullYear();
        const yearB = b.model.claim_doc_year || new Date().getFullYear();
        return yearB - yearA;
    });

    // Group claims by year
    const claimsByYear = sortedClaims.reduce((acc, claim) => {
        const year = claim.model.claim_doc_year || new Date().getFullYear();
        if (!acc[year]) {
            acc[year] = [];
        }
        acc[year].push(claim);
        return acc;
    }, {} as Record<number, ClaimTypeV2[]>);

    return (
        <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="claims-timeline">
            <h3 className="text-lg font-semibold mb-4">Claims Timeline</h3>
            
            <div className="relative">
                {/* Vertical timeline line */}
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>
                
                {/* Timeline entries */}
                <div className="space-y-6">
                    {Object.entries(claimsByYear)
                        .sort(([yearA], [yearB]) => Number(yearB) - Number(yearA))
                        .map(([year, claims]) => (
                            <div key={year} className="relative" data-testid="timeline-entry">
                                {/* Timeline dot */}
                                <div className="absolute left-2 w-4 h-4 bg-white dark:bg-gray-800 border-2 border-primary rounded-full"></div>
                                
                                {/* Year and summary */}
                                <div className="ml-12">
                                    <div className="flex items-center gap-3 mb-2">
                                        <h4 className="font-semibold text-lg">{year}</h4>
                                        <Badge variant="outline" className="text-xs">
                                            <Calendar className="w-3 h-3 mr-1" />
                                            {claims.length} claim{claims.length !== 1 ? 's' : ''}
                                        </Badge>
                                    </div>
                                    
                                    {/* Show first few claims for this year */}
                                    <div className="space-y-2">
                                        {claims.slice(0, 3).map((claim) => {
                                            // Determine claim status
                                            const isValid = claim.verified !== null ? claim.verified : claim.model.valid_claim;
                                            const isGreenwashing = claim.model.greenwashing || false;
                                            
                                            let statusIcon;
                                            let statusColor;
                                            
                                            if (isValid) {
                                                statusIcon = <CheckCircle2 className="w-4 h-4" />;
                                                statusColor = "text-green-600 dark:text-green-400";
                                            } else {
                                                statusIcon = <CircleXIcon className="w-4 h-4" />;
                                                statusColor = "text-red-600 dark:text-red-400";
                                            }
                                            
                                            return (
                                                <div key={claim.id} className="text-sm pl-2 border-l-2 border-gray-200 dark:border-gray-700">
                                                    <div className="flex items-start gap-2" data-testid="timeline-event">
                                                        <span className={statusColor}>{statusIcon}</span>
                                                        <div className="flex-1">
                                                            <span className="line-clamp-2">
                                                                {claim.statement_text || claim.model.text || claim.summary}
                                                            </span>
                                                            <div className="flex items-center gap-2 mt-1">
                                                                <Badge variant="secondary" className="text-xs">
                                                                    {claim.model.confidence}%
                                                                </Badge>
                                                                {claim.model.esg_claim && (
                                                                    <Badge variant="outline" className="text-xs">
                                                                        ESG
                                                                    </Badge>
                                                                )}
                                                                {isGreenwashing && (
                                                                    <Badge variant="destructive" className="text-xs">
                                                                        Greenwashing
                                                                    </Badge>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                        
                                        {claims.length > 3 && (
                                            <div className="text-sm text-muted-foreground pl-2">
                                                +{claims.length - 3} more claim{claims.length - 3 > 1 ? 's' : ''}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
}
