'use client';

import React from 'react';
import { PromiseTypeV2 } from '@/types';
import { Badge } from '@ui/components/ui/badge';
import { CheckCircle2, CircleXIcon, HelpCircle, Calendar } from 'lucide-react';

interface PromisesTimelineProps {
    promisesData: PromiseTypeV2[] | undefined | null;
}

export function PromisesTimeline({ promisesData }: PromisesTimelineProps) {
    if (!promisesData || promisesData.length === 0) {
        return null;
    }

    // Sort promises by year (most recent first)
    const sortedPromises = [...promisesData].sort((a, b) => {
        const yearA = a.model.promise_doc_year || new Date().getFullYear();
        const yearB = b.model.promise_doc_year || new Date().getFullYear();
        return yearB - yearA;
    });

    // Group promises by year
    const promisesByYear = sortedPromises.reduce((acc, promise) => {
        const year = promise.model.promise_doc_year || new Date().getFullYear();
        if (!acc[year]) {
            acc[year] = [];
        }
        acc[year].push(promise);
        return acc;
    }, {} as Record<number, PromiseTypeV2[]>);

    return (
        <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promise-timeline">
            <h3 className="text-lg font-semibold mb-4">Promise Timeline</h3>
            
            <div className="relative">
                {/* Vertical timeline line */}
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>
                
                {/* Timeline entries */}
                <div className="space-y-6">
                    {Object.entries(promisesByYear)
                        .sort(([yearA], [yearB]) => Number(yearB) - Number(yearA))
                        .map(([year, promises]) => (
                            <div key={year} className="relative" data-testid="timeline-entry">
                                {/* Timeline dot */}
                                <div className="absolute left-2 w-4 h-4 bg-white dark:bg-gray-800 border-2 border-primary rounded-full"></div>
                                
                                {/* Content */}
                                <div className="ml-10">
                                    <div className="flex items-center gap-2 mb-2">
                                        <Calendar className="w-4 h-4 text-muted-foreground" />
                                        <span className="font-semibold text-lg" data-testid="timeline-date">{year}</span>
                                        <Badge variant="secondary">{promises.length} promise{promises.length > 1 ? 's' : ''}</Badge>
                                    </div>
                                    
                                    <div className="space-y-2">
                                        {promises.slice(0, 3).map((promise, idx) => {
                                            let statusIcon;
                                            let statusColor;
                                            
                                            if (promise.kept === true) {
                                                statusIcon = <CheckCircle2 className="w-4 h-4" />;
                                                statusColor = "text-green-600 dark:text-green-400";
                                            } else if (promise.kept === false) {
                                                statusIcon = <CircleXIcon className="w-4 h-4" />;
                                                statusColor = "text-red-600 dark:text-red-400";
                                            } else {
                                                statusIcon = <HelpCircle className="w-4 h-4" />;
                                                statusColor = "text-yellow-600 dark:text-yellow-400";
                                            }
                                            
                                            return (
                                                <div key={promise.id} className="text-sm pl-2 border-l-2 border-gray-200 dark:border-gray-700">
                                                    <div className="flex items-start gap-2" data-testid="timeline-event">
                                                        <span className={statusColor}>{statusIcon}</span>
                                                        <span className="line-clamp-2 flex-1">
                                                            {promise.statement_text || promise.model.text || promise.summary}
                                                        </span>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                        
                                        {promises.length > 3 && (
                                            <div className="text-sm text-muted-foreground pl-2">
                                                +{promises.length - 3} more promise{promises.length - 3 > 1 ? 's' : ''}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
}