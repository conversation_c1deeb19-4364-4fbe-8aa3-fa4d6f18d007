'use client';

import React from 'react';
import { PromiseTypeV2 } from '@/types';
import { Badge } from '@ui/components/ui/badge';
import { CheckCircle2, CircleXIcon, HelpCircle } from 'lucide-react';

interface PromiseAssessmentHistoryProps {
    promisesData: PromiseTypeV2[] | undefined | null;
}

export function PromiseAssessmentHistory({ promisesData }: PromiseAssessmentHistoryProps) {
    if (!promisesData || promisesData.length === 0) {
        return null;
    }

    // Group promises by status and track changes over time
    const assessmentData = promisesData.map(promise => ({
        id: promise.id,
        year: promise.model.promise_doc_year || new Date().getFullYear(),
        status: promise.kept,
        confidence: promise.model.confidence,
        text: promise.statement_text || promise.model.text || promise.summary
    }));

    // Sort by year
    assessmentData.sort((a, b) => b.year - a.year);

    return (
        <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promise-assessment-history">
            <h3 className="text-lg font-semibold mb-4">Assessment History</h3>

            {/* Recent Assessments */}
            <div className="space-y-3">
                <h4 className="font-medium mb-2">Recent Assessments</h4>
                {assessmentData.slice(0, 5).map((assessment, idx) => {
                    let statusIcon;
                    let statusColor;
                    let statusText;
                    
                    if (assessment.status === true) {
                        statusIcon = <CheckCircle2 className="w-4 h-4" />;
                        statusColor = "text-green-600 dark:text-green-400";
                        statusText = "Kept";
                    } else if (assessment.status === false) {
                        statusIcon = <CircleXIcon className="w-4 h-4" />;
                        statusColor = "text-red-600 dark:text-red-400";
                        statusText = "Not Kept";
                    } else {
                        statusIcon = <HelpCircle className="w-4 h-4" />;
                        statusColor = "text-yellow-600 dark:text-yellow-400";
                        statusText = "Uncertain";
                    }
                    
                    return (
                        <div key={assessment.id} className="p-3 border rounded-md" data-testid="assessment-entry">
                            <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center gap-2">
                                    <span className={statusColor}>{statusIcon}</span>
                                    <span className="font-medium" data-testid="assessment-date">{assessment.year}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge 
                                        variant={assessment.status === true ? "default" : assessment.status === false ? "destructive" : "secondary"}
                                        data-testid="assessment-status"
                                    >
                                        {statusText}
                                    </Badge>
                                    <Badge variant="outline">
                                        {assessment.confidence}% confidence
                                    </Badge>
                                </div>
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                                {assessment.text}
                            </p>
                        </div>
                    );
                })}
                
                {assessmentData.length > 5 && (
                    <div className="text-sm text-muted-foreground text-center">
                        +{assessmentData.length - 5} more assessments
                    </div>
                )}
            </div>
        </div>
    );
}