'use client';

import React from 'react';
import { PromiseTypeV2 } from '@/types';

interface PromisesFiveYearChartProps {
    promisesData: PromiseTypeV2[] | undefined | null;
}

interface YearData {
    year: number;
    kept: number;
    broken: number;
    uncertain: number;
    total: number;
}

export function PromisesFiveYearChart({ promisesData }: PromisesFiveYearChartProps) {
    if (!promisesData || promisesData.length === 0) {
        return (
            <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-five-year-chart">
                <h3 className="text-lg font-semibold mb-4">Promise Trends (Last 5 Years)</h3>
                <p className="text-sm text-muted-foreground text-center py-8">
                    No promise data available
                </p>
            </div>
        );
    }

    // Get the last 5 years
    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 5 }, (_, i) => currentYear - i).reverse();

    // Group promises by year and calculate statistics
    const yearlyData: YearData[] = years.map(year => {
        const yearPromises = promisesData.filter(promise => 
            promise.model.promise_doc_year === year
        );

        const kept = yearPromises.filter(p => p.kept === true).length;
        const broken = yearPromises.filter(p => p.kept === false).length;
        const uncertain = yearPromises.filter(p => p.kept === null).length;

        return {
            year,
            kept,
            broken,
            uncertain,
            total: kept + broken + uncertain
        };
    });

    // Calculate maximum value for scaling
    const maxValue = Math.max(...yearlyData.map(d => d.total));
    const maxBarValue = Math.max(...yearlyData.map(d => Math.max(d.kept, d.broken, d.uncertain)));

    if (maxValue === 0) {
        return (
            <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-five-year-chart">
                <h3 className="text-lg font-semibold mb-4">Promise Trends (Last 5 Years)</h3>
                <p className="text-sm text-muted-foreground text-center py-8">
                    No promise data available for the last 5 years
                </p>
            </div>
        );
    }

    return (
        <div className="mb-6 p-4 bg-background border rounded-lg" data-testid="promises-five-year-chart">
            <h3 className="text-lg font-semibold mb-4">Promise Trends (Last 5 Years)</h3>
            
            {/* Bar Chart */}
            <div className="space-y-6">
                {yearlyData.map((data) => (
                    <div key={data.year} className="space-y-2">
                        <div className="flex justify-between items-center">
                            <span className="font-medium text-sm">{data.year}</span>
                            <span className="text-xs text-muted-foreground">
                                Total: {data.total}
                            </span>
                        </div>
                        
                        {/* Stacked horizontal bar */}
                        <div className="relative w-full h-8 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden" data-testid="chart-element">
                            {data.total > 0 && (
                                <>
                                    {/* Kept promises (green) */}
                                    <div
                                        className="absolute left-0 top-0 h-full bg-green-500 transition-all duration-300"
                                        style={{ width: `${(data.kept / data.total) * 100}%` }}
                                        data-testid={`chart-bar-kept-${data.year}`}
                                    />
                                    {/* Broken promises (red) */}
                                    <div
                                        className="absolute top-0 h-full bg-red-500 transition-all duration-300"
                                        style={{
                                            left: `${(data.kept / data.total) * 100}%`,
                                            width: `${(data.broken / data.total) * 100}%`
                                        }}
                                        data-testid={`chart-bar-broken-${data.year}`}
                                    />
                                    {/* Uncertain promises (yellow) */}
                                    <div
                                        className="absolute top-0 h-full bg-yellow-500 transition-all duration-300"
                                        style={{
                                            left: `${((data.kept + data.broken) / data.total) * 100}%`,
                                            width: `${(data.uncertain / data.total) * 100}%`
                                        }}
                                        data-testid={`chart-bar-uncertain-${data.year}`}
                                    />
                                </>
                            )}
                        </div>
                        
                        {/* Legend for this year */}
                        <div className="flex justify-between text-xs text-muted-foreground">
                            <div className="flex items-center gap-4">
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
                                    <span>Kept: {data.kept}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 bg-red-500 rounded-sm"></div>
                                    <span>Broken: {data.broken}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <div className="w-3 h-3 bg-yellow-500 rounded-sm"></div>
                                    <span>Uncertain: {data.uncertain}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Summary statistics */}
            <div className="mt-6 pt-4 border-t border-border">
                <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="space-y-1">
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                            {yearlyData.reduce((sum, d) => sum + d.kept, 0)}
                        </div>
                        <div className="text-xs text-muted-foreground">Total Kept</div>
                    </div>
                    <div className="space-y-1">
                        <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                            {yearlyData.reduce((sum, d) => sum + d.broken, 0)}
                        </div>
                        <div className="text-xs text-muted-foreground">Total Broken</div>
                    </div>
                    <div className="space-y-1">
                        <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                            {yearlyData.reduce((sum, d) => sum + d.uncertain, 0)}
                        </div>
                        <div className="text-xs text-muted-foreground">Total Uncertain</div>
                    </div>
                </div>
            </div>
        </div>
    );
}
