# AI Features Test Setup

## Issue

The test file `ai-edit-responses.spec.ts` expects the AI Chat button to be visible and clickable, but the AI features are disabled by default in the feature flags configuration. The following feature flags need to be enabled for the tests to pass:

- `document.editor.ai.chat`
- `document.editor.ai.tools`
- `document.editor.ai.edit`

## Root Cause

In `utils/feature-flags.ts`, these feature flags are commented out in the `DEFAULT_FEATURE_FLAGS` array, which means they are disabled by default. However, tests have been written that expect these features to be available.

## Solutions

### Option 1: Enable Features in Database (Recommended for CI/CD)

Run the following SQL to enable AI features for test users:

```sql
-- Enable AI features for test users
UPDATE profiles 
SET feature_flags = ARRAY(
    SELECT DISTINCT unnest(
        COALESCE(feature_flags, ARRAY[]::text[]) || 
        ARRAY['document.editor.ai.chat', 'document.editor.ai.tools', 'document.editor.ai.edit']
    )
)
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Also update organization flags if applicable
UPDATE acc_organisations
SET feature_flags = ARRAY(
    SELECT DISTINCT unnest(
        COALESCE(feature_flags, ARRAY[]::text[]) || 
        ARRAY['document.editor.ai.chat', 'document.editor.ai.tools', 'document.editor.ai.edit']
    )
)
WHERE id IN (
    SELECT organisation 
    FROM profiles 
    WHERE email IN ('<EMAIL>', '<EMAIL>')
      AND organisation IS NOT NULL
);
```

### Option 2: Environment Variable Override

Set the following environment variable before running tests:

```bash
export NEXT_PUBLIC_TEST_FEATURE_FLAGS="document.editor.ai.tools,document.editor.ai.chat,document.editor.ai.edit"
```

### Option 3: Enable Features by Default (Permanent Fix)

The most straightforward solution would be to uncomment these feature flags in `utils/feature-flags.ts` since tests are written for them:

```typescript
export const DEFAULT_FEATURE_FLAGS = [
  'dashboard.flags',
  'dashboard.greenwashing',
  'document.create',
  'document.view',
  'document.editor.history',
  'trace.react',
  "document.editor.ai.tools",    // Uncomment this
  "document.editor.ai.chat",     // Uncomment this
  "document.editor.ai.edit",     // Uncomment this
];
```

## Test Details

The failing test `ai-edit-responses.spec.ts` performs the following:

1. Creates a new document
2. Clicks the AI Chat button (which is hidden by feature flag)
3. Tests AI chat responses and edit functionality

Without the feature flags enabled, the AI Chat button is not rendered, causing the test to fail.

## Files Created

- `supabase/migrations/20250202_enable_ai_features_for_tests.sql` - Migration to enable features for test users
- `.env.test.local` - Environment variables for test environment
- `utils/feature-flags-env.ts` - Helper to read feature flags from environment
- `scripts/setup-test-features.js` - Setup script with instructions

## Recommendation

The cleanest solution is to enable these feature flags by default in `utils/feature-flags.ts` since:
1. Tests are written for these features
2. The features appear to be complete and functional
3. Having tests for disabled features creates confusion

If the features are not ready for production, consider using environment-specific feature flags instead of disabling them entirely.