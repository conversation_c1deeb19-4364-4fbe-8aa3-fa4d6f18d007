-- Enable AI features for test users
-- This migration enables the AI chat, tools, and edit features for test users

-- Update <EMAIL> user's feature flags
UPDATE profiles 
SET feature_flags = ARRAY(
    SELECT DISTINCT unnest(
        COALESCE(feature_flags, ARRAY[]::text[]) || 
        ARRAY['document.editor.ai.chat', 'document.editor.ai.tools', 'document.editor.ai.edit']
    )
)
WHERE email = '<EMAIL>';

-- Update <EMAIL> user's feature flags
UPDATE profiles 
SET feature_flags = ARRAY(
    SELECT DISTINCT unnest(
        COALESCE(feature_flags, ARRAY[]::text[]) || 
        ARRAY['document.editor.ai.chat', 'document.editor.ai.tools', 'document.editor.ai.edit']
    )
)
WHERE email = '<EMAIL>';

-- Also update the organization's feature flags if test users belong to an organization
UPDATE acc_organisations
SET feature_flags = ARRAY(
    SELECT DISTINCT unnest(
        COALESCE(feature_flags, ARRAY[]::text[]) || 
        ARRAY['document.editor.ai.chat', 'document.editor.ai.tools', 'document.editor.ai.edit']
    )
)
WHERE id IN (
    SELECT organisation 
    FROM profiles 
    WHERE email IN ('<EMAIL>', '<EMAIL>')
      AND organisation IS NOT NULL
);