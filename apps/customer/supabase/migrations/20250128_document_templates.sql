-- Create document_templates table for storing report and document templates
CREATE TABLE IF NOT EXISTS document_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  category TEXT DEFAULT 'General',
  scope TEXT CHECK (scope IN ('global', 'customer', 'organisation')) DEFAULT 'global',
  content TEXT NOT NULL, -- HTML content of the template
  data JSONB, -- TipTap JSON data of the template
  metadata JSONB DEFAULT '{}'::jsonb,
  tags TEXT[] DEFAULT '{}',
  icon TEXT, -- Icon name for the template
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  updated_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  organisation_id UUID, -- For organisation-scoped templates
  customer_id UUID -- For customer-scoped templates
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_document_templates_scope ON document_templates(scope);
CREATE INDEX IF NOT EXISTS idx_document_templates_category ON document_templates(category);
CREATE INDEX IF NOT EXISTS idx_document_templates_created_by ON document_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_document_templates_organisation_id ON document_templates(organisation_id);
CREATE INDEX IF NOT EXISTS idx_document_templates_customer_id ON document_templates(customer_id);
CREATE INDEX IF NOT EXISTS idx_document_templates_is_active ON document_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_document_templates_tags ON document_templates USING GIN(tags);

-- Enable RLS
ALTER TABLE document_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies for document_templates

-- Global templates are visible to everyone
CREATE POLICY "Global templates are visible to all authenticated users" ON document_templates
  FOR SELECT USING (
    auth.role() = 'authenticated' AND 
    scope = 'global' AND 
    is_active = true
  );

-- Users can see their own customer-scoped templates
CREATE POLICY "Users can see their own customer templates" ON document_templates
  FOR SELECT USING (
    auth.uid() = created_by AND 
    scope = 'customer' AND 
    is_active = true
  );

-- Users can see organisation templates if they belong to the same organisation
-- Note: This assumes there's a way to determine user's organisation
CREATE POLICY "Users can see organisation templates" ON document_templates
  FOR SELECT USING (
    scope = 'organisation' AND 
    is_active = true
    -- TODO: Add organisation membership check when user-organisation relationship is defined
  );

-- Users can create customer-scoped templates
CREATE POLICY "Users can create customer templates" ON document_templates
  FOR INSERT WITH CHECK (
    auth.uid() = created_by AND 
    scope = 'customer'
  );

-- Users can update their own customer-scoped templates
CREATE POLICY "Users can update their own customer templates" ON document_templates
  FOR UPDATE USING (
    auth.uid() = created_by AND 
    scope = 'customer'
  ) WITH CHECK (
    auth.uid() = updated_by AND 
    scope = 'customer'
  );

-- Users can delete their own customer-scoped templates
CREATE POLICY "Users can delete their own customer templates" ON document_templates
  FOR DELETE USING (
    auth.uid() = created_by AND 
    scope = 'customer'
  );

-- Only admins can manage global templates (this would need to be implemented with a role system)
-- For now, we'll allow any authenticated user to create global templates for testing
CREATE POLICY "Authenticated users can manage global templates" ON document_templates
  FOR ALL USING (
    auth.role() = 'authenticated' AND 
    scope = 'global'
  ) WITH CHECK (
    auth.role() = 'authenticated' AND 
    scope = 'global'
  );

-- Insert some default global templates
INSERT INTO document_templates (name, description, category, scope, content, data, metadata, tags, icon) VALUES
(
  'Blank Document',
  'Start with a clean slate',
  'Basic',
  'global',
  '<p>Start writing your document here...</p>',
  '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Start writing your document here..."}]}]}',
  '{"template_type": "basic"}',
  ARRAY['basic', 'empty'],
  'FileText'
),
(
  'ESG Report Template',
  'Comprehensive ESG report with all sections',
  'Reports',
  'global',
  '<report-group id="esg-report">
    <h1>ESG Report</h1>
    <report-summary id="exec-summary" prompt="Executive summary of the ESG performance" title="Executive Summary" summarize="environmental,social,governance"/>
    
    <report-group id="environmental">
      <h2>Environmental Impact</h2>
      <report-section id="climate-change" title="Climate Change" endpoint="/report/entity/{entity}/harm/model/{model}/section/01_no_poverty"/>
      <report-section id="biodiversity" title="Biodiversity" endpoint="/report/entity/{entity}/harm/model/{model}/section/15_life_on_land"/>
    </report-group>
    
    <report-group id="social">
      <h2>Social Impact</h2>
      <report-section id="human-rights" title="Human Rights" endpoint="/report/entity/{entity}/harm/model/{model}/section/16_peace_justice_and_strong_institutions"/>
      <report-section id="labor-practices" title="Labor Practices" endpoint="/report/entity/{entity}/harm/model/{model}/section/08_decent_work_and_economic_growth"/>
    </report-group>
    
    <report-group id="governance">
      <h2>Governance</h2>
      <report-section id="transparency" title="Transparency" endpoint="/report/entity/{entity}/transparency"/>
      <report-section id="reliability" title="Reliability" endpoint="/report/entity/{entity}/reliability"/>
    </report-group>
  </report-group>',
  '{"type":"doc","content":[{"type":"reportGroup","attrs":{"id":"esg-report"},"content":[{"type":"heading","attrs":{"level":1},"content":[{"type":"text","text":"ESG Report"}]},{"type":"reportSummary","attrs":{"id":"exec-summary","prompt":"Executive summary of the ESG performance","title":"Executive Summary","summarize":"environmental,social,governance"}},{"type":"reportGroup","attrs":{"id":"environmental"},"content":[{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Environmental Impact"}]},{"type":"reportSection","attrs":{"id":"climate-change","title":"Climate Change","endpoint":"/report/entity/{entity}/harm/model/{model}/section/01_no_poverty"}},{"type":"reportSection","attrs":{"id":"biodiversity","title":"Biodiversity","endpoint":"/report/entity/{entity}/harm/model/{model}/section/15_life_on_land"}}]},{"type":"reportGroup","attrs":{"id":"social"},"content":[{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Social Impact"}]},{"type":"reportSection","attrs":{"id":"human-rights","title":"Human Rights","endpoint":"/report/entity/{entity}/harm/model/{model}/section/16_peace_justice_and_strong_institutions"}},{"type":"reportSection","attrs":{"id":"labor-practices","title":"Labor Practices","endpoint":"/report/entity/{entity}/harm/model/{model}/section/08_decent_work_and_economic_growth"}}]},{"type":"reportGroup","attrs":{"id":"governance"},"content":[{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Governance"}]},{"type":"reportSection","attrs":{"id":"transparency","title":"Transparency","endpoint":"/report/entity/{entity}/transparency"}},{"type":"reportSection","attrs":{"id":"reliability","title":"Reliability","endpoint":"/report/entity/{entity}/reliability"}}]}]}]}',
  '{"template_type": "report", "model": "sdg", "sections": ["environmental", "social", "governance"]}',
  ARRAY['report', 'esg', 'sustainability'],
  'BarChart3'
),
(
  'EKO Report',
  'Standard EKO report template with sections and groups',
  'Reports',
  'global',
  '<report-group id="eko-report">
    <h1>EKO Report</h1>
    <report-summary id="exec-summary" prompt="Executive summary" title="Executive Summary" summarize="impact,reliability,transparency"/>
    
    <report-group id="impact-section">
      <h2>Impact Analysis</h2>
      <report-section id="harm-impact" title="Harm Impact" endpoint="/report/entity/[ENTITY_ID]/harm/model/sdg/section/13_climate_action"/>
      <report-section id="benefit-impact" title="Benefit Impact" endpoint="/report/entity/[ENTITY_ID]/benefit/model/sdg/section/13_climate_action"/>
    </report-group>
    
    <report-group id="reliability-section">
      <h2>Reliability Assessment</h2>
      <report-section id="transparency" title="Transparency Score" endpoint="/report/entity/[ENTITY_ID]/transparency"/>
      <report-section id="reliability" title="Reliability Analysis" endpoint="/report/entity/[ENTITY_ID]/reliability"/>
    </report-group>
  </report-group>',
  '{"type":"doc","content":[{"type":"reportGroup","attrs":{"id":"eko-report"},"content":[{"type":"heading","attrs":{"level":1},"content":[{"type":"text","text":"EKO Report"}]},{"type":"reportSummary","attrs":{"id":"exec-summary","prompt":"Executive summary","title":"Executive Summary","summarize":"impact,reliability,transparency"}},{"type":"reportGroup","attrs":{"id":"impact-section"},"content":[{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Impact Analysis"}]},{"type":"reportSection","attrs":{"id":"harm-impact","title":"Harm Impact","endpoint":"/report/entity/[ENTITY_ID]/harm/model/sdg/section/13_climate_action"}},{"type":"reportSection","attrs":{"id":"benefit-impact","title":"Benefit Impact","endpoint":"/report/entity/[ENTITY_ID]/benefit/model/sdg/section/13_climate_action"}}]},{"type":"reportGroup","attrs":{"id":"reliability-section"},"content":[{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Reliability Assessment"}]},{"type":"reportSection","attrs":{"id":"transparency","title":"Transparency Score","endpoint":"/report/entity/[ENTITY_ID]/transparency"}},{"type":"reportSection","attrs":{"id":"reliability","title":"Reliability Analysis","endpoint":"/report/entity/[ENTITY_ID]/reliability"}}]}]}]}',
  '{"template_type": "report", "model": "sdg", "sections": ["impact", "reliability"]}',
  ARRAY['report', 'eko', 'standard'],
  'FileText'
),
(
  'Meeting Notes',
  'Template for capturing meeting discussions and action items',
  'Business',
  'global',
  '<h1>Meeting Notes</h1>
  <p><strong>Date:</strong> [Date]</p>
  <p><strong>Attendees:</strong> [List attendees]</p>
  <p><strong>Agenda:</strong></p>
  <ul><li>[Agenda item 1]</li><li>[Agenda item 2]</li></ul>
  
  <h2>Discussion Points</h2>
  <p>[Key discussion points]</p>
  
  <h2>Action Items</h2>
  <ul><li>[ ] [Action item 1] - [Assignee] - [Due date]</li><li>[ ] [Action item 2] - [Assignee] - [Due date]</li></ul>
  
  <h2>Next Steps</h2>
  <p>[Next meeting date and agenda]</p>',
  '{"type":"doc","content":[{"type":"heading","attrs":{"level":1},"content":[{"type":"text","text":"Meeting Notes"}]},{"type":"paragraph","content":[{"type":"text","marks":[{"type":"bold"}],"text":"Date:"},{"type":"text","text":" [Date]"}]},{"type":"paragraph","content":[{"type":"text","marks":[{"type":"bold"}],"text":"Attendees:"},{"type":"text","text":" [List attendees]"}]},{"type":"paragraph","content":[{"type":"text","marks":[{"type":"bold"}],"text":"Agenda:"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"[Agenda item 1]"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"[Agenda item 2]"}]}]}]},{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Discussion Points"}]},{"type":"paragraph","content":[{"type":"text","text":"[Key discussion points]"}]},{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Action Items"}]},{"type":"bulletList","content":[{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"[ ] [Action item 1] - [Assignee] - [Due date]"}]}]},{"type":"listItem","content":[{"type":"paragraph","content":[{"type":"text","text":"[ ] [Action item 2] - [Assignee] - [Due date]"}]}]}]},{"type":"heading","attrs":{"level":2},"content":[{"type":"text","text":"Next Steps"}]},{"type":"paragraph","content":[{"type":"text","text":"[Next meeting date and agenda]"}]}]}',
  '{"template_type": "business"}',
  ARRAY['meeting', 'business', 'notes'],
  'Users'
);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_document_templates_updated_at 
  BEFORE UPDATE ON document_templates 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments
COMMENT ON TABLE document_templates IS 'Templates for creating new documents and reports';
COMMENT ON COLUMN document_templates.scope IS 'Visibility scope: global (all users), customer (single user), organisation (organisation members)';
COMMENT ON COLUMN document_templates.content IS 'HTML content of the template';
COMMENT ON COLUMN document_templates.data IS 'TipTap JSON representation of the template';
COMMENT ON COLUMN document_templates.metadata IS 'Additional template metadata like model type, sections, etc.';
