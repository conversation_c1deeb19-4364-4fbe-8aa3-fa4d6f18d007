'use client';

import { useEntity } from '@/components/context/entity/entity-context';
import { NavigationItem } from '@/components/page-header';

/**
 * Custom hook that provides navigation utilities with URL parameter preservation
 * This ensures that entity context parameters (entity, run, model, disclosures)
 * are preserved when navigating between pages
 */
export function useNavigationWithParams() {
  const entityContext = useEntity();

  /**
   * Creates a navigation item with preserved query parameters
   * @param label - The display label for the navigation item
   * @param href - The base href without query parameters
   * @returns NavigationItem with query parameters appended
   */
  const createNavItem = (label: string, href: string): NavigationItem => {
    const fullHref = entityContext.queryString
      ? `${href}?${entityContext.queryString}`
      : href;

    return {
      label,
      href: fullHref
    };
  };

  /**
   * Creates multiple navigation items with preserved query parameters
   * @param items - Array of {label, href} objects
   * @returns Array of NavigationItems with query parameters appended
   */
  const createNavItems = (items: Array<{label: string, href: string}>): NavigationItem[] => {
    return items.map(item => createNavItem(item.label, item.href));
  };

  /**
   * Creates a URL with preserved query parameters
   * @param baseUrl - The base URL without query parameters
   * @returns URL with query parameters appended
   */
  const createUrlWithParams = (baseUrl: string): string => {
    return entityContext.queryString 
      ? `${baseUrl}?${entityContext.queryString}` 
      : baseUrl;
  };

  return {
    createNavItem,
    createNavItems,
    createUrlWithParams,
    queryString: entityContext.queryString
  };
}
