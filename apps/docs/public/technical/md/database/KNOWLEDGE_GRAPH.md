# Analytics Database Structure Overview

![kg_base_entities.png](kg_base_entities.png)

## Database Overview

The analytics database contains the core knowledge graph for the Eko platform. It has **962,432 base entities**, **213,503 statements**, **73,201 documents**, and **5 virtual entities**. The database follows a knowledge graph pattern where entities are connected through statements extracted from documents.

The database consists of two main schema patterns:
- **Core Knowledge Graph (`kg_*`)**: Base entities, statements, documents, and their relationships
- **Analysis Tables (`ana_*`)**: Processed analysis results including effects, flags, claims, and predictions

## Core Entity Tables

### kg_base_entities
**Primary entity table** storing all real-world entities in the knowledge graph.

**Purpose**: Represents companies, people, organizations, concepts, and other entities extracted from documents.

**Key Fields**:
- `id`: Primary key (bigint)
- `eko_id`: Unique identifier following format `eko:{type}:{normalized_name}` 
- `name`: Entity name
- `type`: Entity type (255+ types including company, person, organization, concept, etc.)
- `canonical_id`: References canonical entity if this is a duplicate/variant
- `canonical`: Boolean indicating if this is the canonical entity
- `short_id`: Human-readable encoded ID
- `lei`: Legal Entity Identifier for companies
- `legal_name`, `common_name`: Various name forms
- `status`: active/deleted

**Entity Types**: The system recognizes 255+ entity types with companies (255,036), concepts (164,723), and persons (105,137) being the most common.

**Canonical Relationships**: Entities can be linked to canonical entities to handle duplicates and variants.

### kg_virt_entities
**Virtual entity aggregation table** for grouping related base entities.

**Purpose**: Creates logical groupings of base entities that represent the same real-world entity across different names, spellings, or contexts.

**Key Fields**:
- `id`: Primary key
- `name`: Virtual entity name
- `eko_id`: Virtual entity identifier
- `type`: Entity type
- `title`: Display title
- `description`: Description
- `entity_regex`: Regular expression for auto-mapping base entities
- `common_names`, `legal_names`, `leis`: Aggregated arrays from base entities

**Mapping**: Connected to base entities via `kg_virt_entity_map` table.

### kg_virt_entity_map
**Junction table** linking virtual entities to their constituent base entities.

**Purpose**: Maps which base entities belong to which virtual entities.

**Key Fields**:
- `virt_entity_id`: References kg_virt_entities
- `base_entity_id`: References kg_base_entities
- Unique constraint on the pair

## Document Tables

### kg_documents
**Document metadata table** storing information about source documents.

**Purpose**: Represents PDFs, reports, and other documents that statements are extracted from.

**Key Fields**:
- `id`: Primary key
- `name`: Document name
- `url`: Source URL
- `year`: Publication year
- `status`: Authorization status (pending/authorized/deleted/rejected)
- `research_categories`: Array of research categories
- `origin_domain`: Domain the document came from
- `credibility`: Credibility score
- `file_type`: Document type (PDF, etc.)

**Document Processing**: Documents are processed to extract pages and statements.

### kg_document_pages
**Document page content table** storing text content from document pages.

**Purpose**: Stores the actual text content extracted from each page of documents.

**Key Fields**:
- `id`: Primary key
- `doc_id`: References kg_documents
- `page_text`: Extracted text content
- `page`: Page number
- `text_type`: Type of text content

## Statement Tables

### kg_statements
**Core statement table** storing extracted statements from documents.

**Purpose**: Represents structured statements extracted from documents that describe actions, impacts, or relationships between entities.

**Key Fields**:
- `id`: Primary key
- `statement_text`: The actual statement text
- `source_text`: Original source text if different
- `doc_id`: References kg_documents
- `doc_page_id`: References kg_document_pages
- `subject_entities`: Array of entity IDs that are subjects
- `object_entities`: Array of entity IDs that are objects
- `company_id`: Primary company entity ID
- `statement_category`: Classification of statement
- `impact`: Impact description
- `action`: Action description
- `impact_value`: Numerical impact score
- `start_year`, `end_year`: Time period the statement covers
- ESG flags: `is_environmental`, `is_social`, `is_governance`, `is_animal_welfare`
- `is_impactful_action`: Whether this represents an impactful action
- `is_vague`: Whether the statement is vague
- `is_disclosure`: Whether this is a disclosure statement
- `model_json`: DEMISE model JSON
- Vector embeddings: `demise_embedding`, `text_embedding`, `domain_embedding`, `effect_embedding`

**Statement Processing**: Statements are processed through the DEMISE model to extract structured information and generate embeddings.

## Supporting Tables

### kg_time_periods
**Time period normalization table** for statement time references.

**Purpose**: Normalizes time periods referenced in statements.

**Key Fields**:
- `from_year`, `from_month`, `from_day`: Start date
- `to_year`, `to_month`, `to_day`: End date
- `precision`: Time precision (year/month/day)

### kg_locations
**Location entities table** for geographic references.

### kg_quantities
**Quantity entities table** for numerical references.

### kg_features
**Feature names table** for DEMISE model features.

**Purpose**: Stores feature names used in the DEMISE model for statement classification.

### kg_statement_features
**Statement feature values table** linking statements to their feature values.

### kg_domains
**Domain information table** storing metadata about web domains that are sources of documents.

**Purpose**: Manages domain-level information for credibility assessment and filtering.

**Key Fields**:
- `domain`: Domain name (primary key)
- `exact`: Whether domain matching is exact
- `crawl`: Whether domain should be crawled
- `credibility`: Credibility score (smallint, default 50)
- `domain_role`: Role of domain (kg_domain_role enum)
- `domain_category`: Category of domain (kg_domain_category enum)
- `description`: Domain description
- `entity_id`: Associated entity ID

### kg_model_sections
**Model section definitions table** for ethical model taxonomy.

**Purpose**: Defines sections and taxonomy for ethical models used in analysis.

**Key Fields**:
- `model`: Ethical model type (ethical_model enum)
- `section`: Section identifier
- `title`: Section title
- `description`: Section description
- `level`: Model level (model_level enum)
- `status`: Section status (kg_status enum)

## Relationships and Data Flow

### Entity Relationships
1. **Base Entities** can have canonical relationships (duplicates point to canonical)
2. **Virtual Entities** aggregate multiple base entities through the mapping table
3. **Statements** reference entities as subjects and objects

### Document Processing Flow
1. **Documents** are ingested and stored in `kg_documents`
2. **Pages** are extracted and stored in `kg_document_pages`
3. **Statements** are extracted from pages and stored in `kg_statements`
4. **Entities** referenced in statements are created/retrieved in `kg_base_entities`

### Statement Analysis
1. Statements are processed through the DEMISE model
2. Feature vectors are extracted and stored in `kg_statement_features`
3. Various embeddings are generated for semantic search
4. ESG categorization flags are set
5. Impact values are calculated

## Usage Patterns

### Entity Resolution
- Use canonical relationships to resolve duplicate entities
- Virtual entities provide high-level groupings for analysis
- Entity search supports fuzzy matching, regex, and web search

### Statement Analysis
- Statements can be filtered by entity, time period, and ESG categories
- Impact values allow ranking statements by significance
- Embeddings enable semantic similarity searches
- Feature vectors support clustering and classification

### Domain Integration
- Documents are linked to domains for credibility assessment
- Domain roles and categories affect statement filtering
- Statement credibility is influenced by source domain

## Key Indexes and Performance

The database includes extensive indexing for:
- Entity name searches (fuzzy, exact, regex)
- Statement filtering by entity, time, and ESG flags
- Vector similarity searches on embeddings
- Full-text search on statement content
- Document and page lookups

## Data Volumes
- **Base Entities**: 962,432 (dominated by companies, concepts, persons)
- **Statements**: 213,503 with full DEMISE processing
- **Documents**: 73,201 source documents
- **Virtual Entities**: 5 high-level groupings

The system is designed to handle large-scale knowledge extraction and analysis, with particular focus on ESG (Environmental, Social, Governance) content analysis and entity relationship mapping.

## Table Comments and Descriptions

### Table Comments
- **kg_base_entities**: "Stores all entities (companies, people, organizations) with canonical relationships for deduplication. Central to entity resolution and management across the system."
- **kg_statements**: "Central repository for extracted statements from documents. Contains rich metadata, embeddings, and ESG classification. Core to all analysis workflows."
- **kg_documents**: "Stores source documents with metadata. Foundation for all statement extraction and analysis."
- **kg_virt_entities**: "Virtual entities that group multiple base entities into logical business entities. Enables analysis at the business group level rather than individual entity level."

### Column Documentation
The database includes comprehensive column comments explaining the purpose and usage of each field. Key examples include:
- **kg_base_entities.canonical_id**: "References another entity ID that this entity is a duplicate of (for deduplication)"
- **kg_statements.demise_embedding**: "DEMISE model vector embedding (Domain, Effect, Magnitude, Impact, Scope, Entity)"
- **kg_statements.impact_value**: "Numerical impact value assessment"

## Tables

### kg_document_authors

**Entity Relationships**: The `kg_document_authors` table (90,483 records) links documents to their authors via entity IDs. The `doc_id` references `kg_documents.id`, and `entity_id` references `kg_base_entities.id`.

**What Entities Represent**: Authors are predominantly:
- **Persons** (44,230 records) - Individual authors
- **Organisations** (34,240 records) - Corporate entities, NGOs, institutions
- **Companies** (5,799 records) - Business entities
- **Government** (4,518 records) - Government bodies
- **Other types** - Including institutions, groups, countries, etc.

**Network Structure**: In a network analysis, nodes would be documents connecting to their author entities. Many documents have multiple authors, and the same entity can author multiple documents.

**Entity Name Mapping**: The mapping between entity_id and names is found in `kg_base_entities`:
```sql
SELECT id, name, type FROM kg_base_entities WHERE id = ?
```
Example: Entity ID 202191 maps to "WWF-UK" (organisation type).

### kg_documents

**Document Types**: Documents are diverse and include:
- **Disclosure documents** - Corporate sustainability reports, ESG disclosures
- **Impact studies** - Research on environmental/social impact
- **Scientific papers** - Academic research
- **Regulatory documents** - Government policies, regulations
- **Journalism** - News articles and media reports
- **Media content** - Various media publications

**ID Relationships**: Yes, `kg_documents.id` is the same as `doc_id` in `kg_document_authors`.

**Author Matching**: The `kg_documents.authors` column contains JSON arrays with author information including names and entity types. These authors DO have corresponding entity IDs that can be found by matching the names in `kg_base_entities`.

**Research Categories**: The categories (`impact`, `disclosure`, `scientific`, `journalism`, `regulatory`, `media`, `company_background`) describe the **type and purpose of the document itself**, not the authors. For example, if a document has category "journalism", it means the document is a journalistic piece, regardless of who wrote it.

### kg_statements

**ID Structure**: Correct - each `kg_statements.id` refers to a specific statement extracted from a `doc_id` and `doc_page_id`. These are different from document IDs.

**Authors Column**: Yes, the `authors` column contains arrays of entity IDs from `kg_base_entities`.

**Company_id Relationships**: The `company_id` field references `kg_base_entities.id` and represents the **subject company** the statement is about. This is often different from the author - for example, WWF-UK (author) might make a statement about Coca-Cola (company_id).

**Subject/Object Entities**: Yes, these are grammatical analysis results. `subject_entities` and `object_entities` contain arrays of entity IDs representing the grammatical subjects and objects of the statements, extracted through NLP processing.

### kg_entity_relations_map

**Entity ID References**: Both `from_entity_id` and `to_entity_id` reference `kg_base_entities.id`.

**Purpose**: Stores relationships between entities extracted from documents.

**Key Fields**:
- `from_entity_id`: Source entity ID
- `to_entity_id`: Target entity ID  
- `relationship_category`: Category of relationship (kg_relationship_category enum)
- `relationship_type`: Type of relationship (kg_entity_rel_type enum)
- `relationship_sub_type`: Sub-type specification
- `relationship_source`: Source of relationship information (kg_relationship_source enum)
- `relationship_data`: Additional relationship metadata (JSON)
- `canonical`: Whether this is a canonical relationship

**Relationship Types**: The system supports various relationship types including:
- **Business**: owns, manages, client_of, supplies
- **Organizational**: part_of, member_of, child_of
- **Conceptual**: is_a, type_of, instance_of
- **Geographical**: located_in, contains, adjacent_to

**Business Relations**: These represent various business relationships including ownership, partnerships, and organizational structures extracted from documents.

### kg_time_periods

**Content**: This table stores **normalized time periods referenced in statements**, not document creation dates. It includes:
- **Date ranges**: from_year/month/day to to_year/month/day
- **Precision levels**: year, month, or day precision
- **Statement linking**: The `id` is referenced by `kg_statements.time_period_id`

**Purpose**: When a statement mentions "from April 2021 to March 2022", this creates a time period record that can be reused across multiple statements.

###  kg_features

**Analysis Method**: Features are extracted using the **DEMISE model** (Domain, Effect, Magnitude, Impact, Scope, Entity), which is an LLM-based system for analyzing statements.

**Feature Examples**: Top features include:
- `statement.descriptive` (1,660,477 usages)
- `subject.entity_type.organizations` (648,123 usages)
- `impact.benefit_to_human_life` (395,726 usages)

**Subject/Object Entities**: Yes, the subject_entities and object_entities in statements are also extracted using NLP/LLM analysis, identifying grammatical relationships within the statement text.

### kg_virt_entities

**ID Structure**: Virtual entities have their own `id` system separate from base entities. They don't have entity_ids in the traditional sense but are linked to base entities through `kg_virt_entity_map`.

**Virtual Entity Concept**: Virtual entities are **logical groupings** of multiple base entities that represent the same real-world entity. For example:
- "Colgate" virtual entity groups 1,000 base entities (various spellings, subsidiaries, etc.)
- "John Lewis" virtual entity groups 86 base entities
- This enables analysis at the business group level rather than individual entity variants

## Recommended Documents for Understanding The DB

**High-Statement Documents** (rich content for analysis):
1. **Document ID 392**: "natureinthebalancewhatcompaniescandotorestorenaturalcapitalv-*.pdf" (60 statements) - Impact category
2. **Document ID 367**: "ceowatermandatesitewatertargetsguide-*.pdf" (54 statements) - Disclosure & Impact categories
3. **Document ID 3702**: "transitionplansbestpracticeexamplesricardoenergyenvironment-*.pdf" (6 statements) - Disclosure category

**Virtual Entity Focus**: 
- **Colgate** (ID 3) - Largest virtual entity with 1,000 base entities
- **John Lewis** (ID 5) - Well-documented retail partnership with 86 base entities

**Analysis Recommendations**:
- Focus on documents with "impact" and "disclosure" research categories for ESG analysis
- Use virtual entities for business group-level analysis
- Examine time periods 2021-2024 for recent sustainability trends    
