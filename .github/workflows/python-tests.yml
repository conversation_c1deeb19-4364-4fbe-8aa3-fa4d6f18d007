# .github/workflows/backend-ci.yml
name: Backend CI

on:
  #  push:
  #    paths:
  #      - 'backoffice/**'
  pull_request:
    paths:
      - 'backoffice/**'

jobs:
  tests:
    name: Run Python Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true

      - name: Install dependencies
        working-directory: backoffice
        run: |
          uv sync

      - name: Run pytest
        working-directory: backoffice
        run: |
          uv run python -m pytest tests -v

  type-check:
    name: Type Check
    runs-on: ubuntu-latest
    needs: tests
    # allow warnings but not block merge
    continue-on-error: true

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true

      - name: Install dependencies
        working-directory: backoffice
        run: |
          uv sync

      - name: Run type check (warning only)
        working-directory: backoffice
        run: |
          echo "⚠️ Running type check (warnings only)"
          uvx ty check . || true
