import { expect, Page } from '@playwright/test'

/**
 * Fixed version of TestUtils with authentication and performance improvements
 * This file can be copied to apps/customer/tests/helpers/tests/fixed-test-utils.ts
 */
export class FixedTestUtils {
  constructor(private page: Page) {
  }

  /**
   * Enhanced login with authentication state detection
   */
  async login(email = '<EMAIL>', password = 'test1_pass') {
    // Check if already authenticated by trying to access customer area
    try {
      await this.page.goto('/customer', { timeout: 10000 })
      await this.page.waitForLoadState('networkidle', { timeout: 5000 })
      
      const currentUrl = this.page.url()
      if (currentUrl.includes('/customer')) {
        console.log('User appears to already be authenticated, skipping login form')
        
        // Additional verification - check for user profile data
        const userAuthenticated = await this.page.evaluate(() => {
          // Check if there's user data indicating authentication
          return document.querySelector('[data-testid="user-dropdown"], .user-menu, button:has-text("<EMAIL>")') !== null
        }).catch(() => false)
        
        if (userAuthenticated) {
          console.log('Authentication confirmed via UI elements')
          return
        }
      }
    } catch (error) {
      console.log('Authentication check failed, proceeding with login form')
    }

    // Clear any potentially corrupted auth state
    await this.clearAuthState()

    // Retry navigation if server is still starting up
    let retries = 5
    while (retries > 0) {
      try {
        await this.page.goto('/login?next=%2Fcustomer', { timeout: 30000 })
        await this.page.waitForLoadState('networkidle', { timeout: 30000 })
        break
      } catch (error) {
        retries--
        if (retries === 0) throw error
        console.log(`Login navigation failed, retrying... (${retries} attempts left)`)
        await this.page.waitForTimeout(2000)
      }
    }

    // Check if login form is present - handle case where user is auto-redirected
    try {
      await this.page.waitForSelector('#email', { timeout: 5000 })
      await this.page.waitForSelector('#password', { timeout: 5000 })
      await this.page.waitForSelector('button[type="submit"]', { timeout: 5000 })
    } catch (error) {
      // No login form found - check if we've been redirected
      const finalUrl = this.page.url()
      if (finalUrl.includes('/customer')) {
        console.log('Redirected to customer area without login form - already authenticated')
        return
      }
      throw new Error(`No login form found and not on customer page. Current URL: ${finalUrl}`)
    }

    await this.page.fill('#email', email)
    await this.page.fill('#password', password)

    // Click submit and wait for navigation 
    const navigationPromise = this.page.waitForURL(/\/customer/, { timeout: 45000 })
    await this.page.click('button[type="submit"]')

    try {
      await navigationPromise
      console.log(`Login successful, redirected to: ${this.page.url()}`)
    } catch (error) {
      // Enhanced fallback check with more detailed logging
      const currentUrl = this.page.url()
      console.log(`Navigation timeout, checking current URL: ${currentUrl}`)

      // Check for error messages on the page
      const errorMessage = await this.page.locator('[role="alert"], .error-message, text=Could not authenticate').first().textContent().catch(() => null)
      if (errorMessage) {
        throw new Error(`Login failed with error: ${errorMessage}`)
      }

      // If we ended up on login page with error params, that's a failure
      if (currentUrl.includes('/login') && currentUrl.includes('message=')) {
        throw new Error(`Login failed - authentication rejected: ${currentUrl}`)
      }

      // If we're on any customer page, consider it successful
      if (currentUrl.includes('/customer')) {
        console.log(`Login successful (fallback detection), redirected to: ${currentUrl}`)
        return
      }

      // Otherwise it's a failure
      throw new Error(`Login failed - expected customer URL but got: ${currentUrl}`)
    }

    // Additional wait for page to stabilize after redirect
    await this.page.waitForLoadState('networkidle', { timeout: 15000 }).catch(() => {
      console.log('Network idle timeout after login, but proceeding...')
    })

    // Wait for DOM to fully initialize after login
    await this.page.waitForFunction(() => {
      return document.readyState === 'complete'
    }, { timeout: 5000 }).catch(() => {
      console.log('DOM initialization check failed after login, continuing...')
    })
  }

  /**
   * Clear authentication state
   */
  async clearAuthState() {
    try {
      // Clear browser storage
      await this.page.evaluate(() => {
        localStorage.clear()
        sessionStorage.clear()
        // Clear cookies
        document.cookie.split(";").forEach(function(c) { 
          document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/")
        })
      })
      
      // Try to sign out via API
      await this.page.goto('/api/auth/signout', { timeout: 5000 }).catch(() => {
        console.log('Logout endpoint not available')
      })
    } catch (error) {
      console.log('Failed to clear auth state:', error)
    }
  }

  /**
   * Create a new document from template
   */
  async createDocumentFromTemplate(templateName = 'Blank Document') {
    console.log(`Creating document from template: ${templateName}`)

    await this.page.goto('/customer/documents', { timeout: 90000 })
    await this.page.waitForLoadState('networkidle', { timeout: 60000 })
    await this.page.waitForTimeout(3000)

    // Wait for and click New Document button
    try {
      await this.page.waitForSelector('[data-testid="new-document-button"]', { timeout: 60000 })
      await this.page.click('[data-testid="new-document-button"]')
      console.log('Clicked New Document button via data-testid')
    } catch (error) {
      await this.page.click('button:has-text("New Document")')
      console.log('Clicked New Document button via text selector')
    }

    // Wait for template dialog
    await this.page.waitForSelector('[role="dialog"], [data-testid="template-dialog"]', { timeout: 45000 })
    console.log('Template dialog appeared')

    // Wait for templates to load
    await this.page.waitForTimeout(2000)

    // Find and click template
    const templateIdMap: Record<string, string> = {
      'EKO Report': 'esg-report',
      'Blank Document': 'blank',
      'ESG Report': 'esg-report',
    }

    const templateId = templateIdMap[templateName] || templateName.toLowerCase().replace(/\s+/g, '-')
    let templateLocator = this.page.locator(`[data-testid="template-${templateId}"]`)

    if (await templateLocator.count() === 0) {
      const actualTemplateName = templateName === 'EKO Report' ? 'ESG Report' : templateName
      templateLocator = this.page.locator('[data-testid^="template-"]').filter({ hasText: actualTemplateName })
    }

    await templateLocator.first().waitFor({ timeout: 30000 })
    await templateLocator.first().scrollIntoViewIfNeeded()
    await this.page.waitForTimeout(1000)
    await templateLocator.first().click({ force: true })
    console.log(`Clicked template: ${templateName}`)

    // Wait for navigation to document editor
    await this.page.waitForURL(/\/customer\/documents\/[a-f0-9-]+/, { timeout: 120000 })
    console.log('Navigated to document editor')

    await this.page.waitForTimeout(2000)
    await this.waitForEditor(60000)
    console.log('Editor loaded successfully')

    return this.getDocumentIdFromUrl()
  }

  /**
   * Get document ID from current URL
   */
  getDocumentIdFromUrl(): string {
    const url = this.page.url()
    const match = url.match(/\/documents\/([a-f0-9-]+)/)
    if (!match) {
      throw new Error('Could not extract document ID from URL')
    }
    return match[1]
  }

  /**
   * Wait for ProseMirror editor to be visible with configurable timeout
   */
  async waitForEditor(timeout = 60000) {
    await this.page.waitForLoadState('domcontentloaded', { timeout: 30000 })

    try {
      await this.page.waitForSelector('.loading, [data-testid="loading"]', { state: 'hidden', timeout: 5000 })
    } catch {
      console.log('No loading spinner found or timeout waiting for it to disappear')
    }

    try {
      await this.page.waitForSelector('[data-testid="eko-document-editor"]', { timeout: 30000 })
      console.log('Document editor component found')
    } catch {
      console.log('Document editor component not found, continuing anyway...')
    }

    await this.page.waitForFunction(() => {
      return !!(window as any).testEditor || document.querySelector('.ProseMirror')
    }, { timeout: 30000 })

    const editor = this.page.locator('.ProseMirror')
    await expect(editor).toBeVisible({ timeout: 10000 })

    await this.page.waitForFunction(() => {
      const proseMirror = document.querySelector('.ProseMirror') as HTMLElement
      return proseMirror && proseMirror.getAttribute('contenteditable') === 'true'
    }, { timeout: 10000 })

    console.log('Editor is ready and interactive')
    return editor
  }

  /**
   * Type text in the ProseMirror editor
   */
  async typeInEditor(text: string, timeout = 10000) {
    const editor = await this.waitForEditor(timeout)
    await editor.click()
    await this.page.keyboard.type(text)
    return editor
  }
}