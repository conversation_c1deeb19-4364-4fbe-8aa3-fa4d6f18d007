# Agentic Investigator: Comprehensive Design Document

## Executive Summary

The Agentic Investigator is a next-generation autonomous research system designed to conduct comprehensive ESG investigations of entities using advanced AI agents, persistent memory, and dynamic decision-making. This system represents an evolution from the existing analysis_v2 and crawl systems, integrating the best of both worlds with cutting-edge agentic AI capabilities.

## System Architecture Overview

### Core Design Philosophy

The Agentic Investigator follows five fundamental principles:

1. **Autonomous Investigation**: AI agents make intelligent decisions about investigation paths
2. **Persistent Memory**: Comprehensive tracking of discoveries, hypotheses, and evidence chains
3. **Dynamic Planning**: Investigation strategies evolve based on discovered information
4. **Multi-Source Integration**: Seamless coordination of web crawling, regulatory data, and document analysis
5. **Evidence-Based Reasoning**: All conclusions supported by verifiable evidence chains

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Agentic Investigator Core                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Investigation  │  │     Agent       │  │     Memory      │  │
│  │   Controller    │  │   Orchestra     │  │   Management    │  │
│  │                 │  │                 │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Data Sources   │  │   Knowledge     │  │    Analysis     │  │
│  │   & Tools       │  │     Graph       │  │    Pipeline     │  │
│  │                 │  │                 │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Crawl4AI      │  │   Regulatory    │  │   Document      │  │
│  │   Engine        │  │   Data APIs     │  │   Processing    │  │
│  │                 │  │                 │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Investigation Controller

The central orchestrator that manages the entire investigation lifecycle.

```python
class InvestigationController:
    """
    Central controller managing autonomous ESG investigations
    """
    
    def __init__(self):
        self.memory_manager = InvestigationMemoryManager()
        self.agent_orchestra = AgentOrchestra()
        self.knowledge_graph = EnhancedKnowledgeGraph()
        self.decision_engine = InvestigationDecisionEngine()
        
    async def investigate(self, entity: str, scope: ESGScope) -> InvestigationReport:
        """Execute full autonomous investigation"""
        investigation_id = await self.initialize_investigation(entity, scope)
        
        while not self.is_investigation_complete(investigation_id):
            # Dynamic decision making about next steps
            next_actions = await self.decision_engine.plan_next_actions(
                investigation_id, 
                self.memory_manager.get_current_state(investigation_id)
            )
            
            # Execute actions via agent orchestra
            results = await self.agent_orchestra.execute_actions(next_actions)
            
            # Update memory with findings
            await self.memory_manager.update_investigation_state(
                investigation_id, results
            )
            
            # Analyze and synthesize findings
            await self.analyze_and_synthesize(investigation_id, results)
            
        return await self.generate_final_report(investigation_id)
```

### 2. Agent Orchestra

Multi-agent system coordinating specialized investigation agents.

```python
class AgentOrchestra:
    """
    Manages and coordinates multiple specialized investigation agents
    """
    
    def __init__(self):
        self.agents = {
            'web_investigator': WebInvestigationAgent(),
            'regulatory_analyst': RegulatoryAnalysisAgent(),
            'document_analyst': DocumentAnalysisAgent(),
            'relationship_mapper': RelationshipMappingAgent(),
            'esg_analyst': ESGAnalysisAgent(),
            'synthesis_agent': SynthesisAgent()
        }
        
    async def execute_actions(self, actions: List[InvestigationAction]) -> List[ActionResult]:
        """Execute investigation actions using appropriate agents"""
        results = []
        
        for action in actions:
            agent = self.select_agent_for_action(action)
            result = await agent.execute(action)
            results.append(result)
            
        return results
```

### 3. Memory Management System

Advanced memory architecture combining episodic, semantic, and procedural memory.

```python
class InvestigationMemoryManager:
    """
    Comprehensive memory management for investigation processes
    """
    
    def __init__(self):
        self.episodic_memory = EpisodicMemoryStore()      # Specific investigation events
        self.semantic_memory = SemanticMemoryStore()      # Factual knowledge
        self.procedural_memory = ProceduralMemoryStore()  # Investigation strategies
        self.working_memory = WorkingMemoryStore()        # Current investigation state
        
    async def update_investigation_state(self, investigation_id: str, results: List[ActionResult]):
        """Update all memory stores with new findings"""
        
        # Store specific events and discoveries
        await self.episodic_memory.store_events(investigation_id, results)
        
        # Extract and store factual knowledge
        facts = await self.extract_facts(results)
        await self.semantic_memory.store_facts(investigation_id, facts)
        
        # Learn from successful investigation patterns
        patterns = await self.extract_patterns(results)
        await self.procedural_memory.update_strategies(patterns)
        
        # Update current working context
        await self.working_memory.update_context(investigation_id, results)
```

### 4. Decision Engine

AI-powered decision-making system that plans investigation strategies.

```python
class InvestigationDecisionEngine:
    """
    Intelligent decision-making engine for investigation planning
    """
    
    def __init__(self):
        self.strategy_llm = StrategicPlanningLLM()
        self.priority_ranker = PriorityRankingSystem()
        self.resource_optimizer = ResourceOptimizer()
        
    async def plan_next_actions(self, investigation_id: str, current_state: InvestigationState) -> List[InvestigationAction]:
        """Generate optimal next investigation actions"""
        
        # Analyze current knowledge gaps
        knowledge_gaps = await self.identify_knowledge_gaps(current_state)
        
        # Generate potential actions
        potential_actions = await self.generate_potential_actions(
            investigation_id, knowledge_gaps
        )
        
        # Prioritize actions based on impact and feasibility
        prioritized_actions = await self.priority_ranker.rank_actions(
            potential_actions, current_state
        )
        
        # Optimize resource allocation
        optimized_actions = await self.resource_optimizer.optimize_actions(
            prioritized_actions
        )
        
        return optimized_actions
```

## Data Sources and Tools Integration

### 1. Crawl4AI Engine

Advanced web crawling with agentic capabilities.

```python
class AgenticCrawl4AIEngine:
    """
    Crawl4AI-powered web investigation engine
    """
    
    def __init__(self):
        self.crawler = AsyncWebCrawler()
        self.content_analyzer = ContentAnalyzer()
        self.link_prioritizer = LinkPrioritizer()
        
    async def investigate_entity_web_presence(self, entity: str, context: InvestigationContext) -> WebInvestigationResult:
        """Comprehensive web investigation of entity"""
        
        # Start with Google search
        search_results = await self.perform_google_search(entity)
        
        # Analyze and prioritize links
        prioritized_links = await self.link_prioritizer.prioritize_links(
            search_results, context
        )
        
        # Crawl high-priority pages
        crawl_results = []
        for link in prioritized_links:
            result = await self.crawler.arun(
                url=link.url,
                extraction_strategy=LLMExtractionStrategy(
                    provider="openai/gpt-4",
                    api_token=settings.OPENAI_API_KEY,
                    instruction=f"Extract ESG-relevant information about {entity}"
                )
            )
            crawl_results.append(result)
            
        # Analyze content for new leads
        new_leads = await self.content_analyzer.extract_investigation_leads(crawl_results)
        
        return WebInvestigationResult(
            crawled_pages=crawl_results,
            new_leads=new_leads,
            entities_discovered=await self.extract_related_entities(crawl_results)
        )
```

### 2. Regulatory Data Integration

Enhanced integration with existing SEC, Companies House, and GLEIF systems.

```python
class RegulatoryDataEngine:
    """
    Comprehensive regulatory data investigation engine
    """
    
    def __init__(self):
        self.sec_client = SECDataClient()
        self.companies_house_client = CompaniesHouseClient()
        self.gleif_client = GLEIFClient()
        self.additional_sources = AdditionalRegulatorySourcesClient()
        
    async def investigate_regulatory_profile(self, entity: str, context: InvestigationContext) -> RegulatoryProfile:
        """Comprehensive regulatory investigation"""
        
        # Parallel investigation across all sources
        sec_data = await self.sec_client.comprehensive_investigation(entity)
        ch_data = await self.companies_house_client.comprehensive_investigation(entity)
        gleif_data = await self.gleif_client.comprehensive_investigation(entity)
        
        # Additional sources based on jurisdiction
        additional_data = await self.additional_sources.investigate_by_jurisdiction(
            entity, context.jurisdiction
        )
        
        # Synthesize findings
        return RegulatoryProfile(
            sec_profile=sec_data,
            companies_house_profile=ch_data,
            gleif_profile=gleif_data,
            additional_profiles=additional_data,
            relationships=await self.extract_regulatory_relationships(
                sec_data, ch_data, gleif_data, additional_data
            )
        )
```

### 3. Document Processing Pipeline

Advanced document analysis with ESG focus.

```python
class DocumentProcessingPipeline:
    """
    Advanced document processing for ESG investigations
    """
    
    def __init__(self):
        self.document_classifier = DocumentClassifier()
        self.content_extractor = ContentExtractor()
        self.esg_analyzer = ESGContentAnalyzer()
        self.claim_verifier = ClaimVerificationSystem()
        
    async def process_document(self, document: Document, context: InvestigationContext) -> DocumentAnalysis:
        """Comprehensive document analysis"""
        
        # Classify document type and relevance
        classification = await self.document_classifier.classify(document)
        
        if classification.esg_relevance < 0.3:
            return DocumentAnalysis(relevant=False, classification=classification)
            
        # Extract structured content
        structured_content = await self.content_extractor.extract_structured_content(document)
        
        # Analyze for ESG claims and commitments
        esg_analysis = await self.esg_analyzer.analyze_esg_content(structured_content)
        
        # Verify claims against known evidence
        claim_verification = await self.claim_verifier.verify_claims(
            esg_analysis.claims, context.entity
        )
        
        return DocumentAnalysis(
            relevant=True,
            classification=classification,
            structured_content=structured_content,
            esg_analysis=esg_analysis,
            claim_verification=claim_verification
        )
```

## Memory Architecture Details

### Episodic Memory Store

```python
class EpisodicMemoryStore:
    """
    Stores specific investigation events and experiences
    """
    
    def __init__(self):
        self.vector_db = VectorDatabase()  # For semantic search
        self.temporal_db = TemporalDatabase()  # For temporal queries
        
    async def store_events(self, investigation_id: str, events: List[InvestigationEvent]):
        """Store investigation events with full context"""
        
        for event in events:
            # Create rich event context
            event_context = InvestigationEventContext(
                investigation_id=investigation_id,
                timestamp=event.timestamp,
                agent=event.agent,
                action=event.action,
                result=event.result,
                context=event.context,
                outcome=event.outcome
            )
            
            # Store in vector database for semantic retrieval
            embedding = await self.create_event_embedding(event_context)
            await self.vector_db.store(
                id=event.id,
                embedding=embedding,
                metadata=event_context.dict()
            )
            
            # Store in temporal database for time-based queries
            await self.temporal_db.store(event_context)
    
    async def recall_similar_events(self, current_context: InvestigationContext) -> List[InvestigationEvent]:
        """Recall similar past investigation events"""
        
        query_embedding = await self.create_context_embedding(current_context)
        similar_events = await self.vector_db.search(
            query_embedding, 
            limit=10,
            filter_metadata={"investigation_type": current_context.investigation_type}
        )
        
        return [self.deserialize_event(event) for event in similar_events]
```

### Semantic Memory Store

```python
class SemanticMemoryStore:
    """
    Stores factual knowledge extracted from investigations
    """
    
    def __init__(self):
        self.knowledge_graph = Neo4jKnowledgeGraph()
        self.fact_database = FactDatabase()
        
    async def store_facts(self, investigation_id: str, facts: List[Fact]):
        """Store verified facts with evidence chains"""
        
        for fact in facts:
            # Create fact node with evidence
            fact_node = FactNode(
                fact=fact,
                evidence=fact.evidence,
                confidence=fact.confidence,
                source_investigation=investigation_id,
                timestamp=datetime.now()
            )
            
            # Store in knowledge graph
            await self.knowledge_graph.create_fact_node(fact_node)
            
            # Create relationships to entities
            for entity in fact.related_entities:
                await self.knowledge_graph.create_relationship(
                    fact_node, entity, fact.relationship_type
                )
            
            # Store in fact database for quick lookup
            await self.fact_database.store(fact_node)
```

### Procedural Memory Store

```python
class ProceduralMemoryStore:
    """
    Stores investigation strategies and learned patterns
    """
    
    def __init__(self):
        self.strategy_database = StrategyDatabase()
        self.pattern_matcher = PatternMatcher()
        
    async def update_strategies(self, patterns: List[InvestigationPattern]):
        """Update investigation strategies based on successful patterns"""
        
        for pattern in patterns:
            # Identify similar existing strategies
            similar_strategies = await self.pattern_matcher.find_similar_strategies(pattern)
            
            if similar_strategies:
                # Update existing strategy with new learnings
                await self.update_existing_strategy(similar_strategies[0], pattern)
            else:
                # Create new strategy
                new_strategy = InvestigationStrategy(
                    pattern=pattern,
                    success_rate=pattern.success_rate,
                    context=pattern.context,
                    actions=pattern.actions
                )
                await self.strategy_database.store(new_strategy)
    
    async def suggest_strategies(self, context: InvestigationContext) -> List[InvestigationStrategy]:
        """Suggest investigation strategies based on context"""
        
        # Find strategies with similar context
        similar_contexts = await self.pattern_matcher.find_similar_contexts(context)
        
        # Rank strategies by success rate and relevance
        ranked_strategies = await self.rank_strategies(similar_contexts, context)
        
        return ranked_strategies[:5]  # Return top 5 strategies
```

## Agent Definitions

### Web Investigation Agent

```python
class WebInvestigationAgent:
    """
    Specialized agent for web-based investigation
    """
    
    def __init__(self):
        self.crawl_engine = AgenticCrawl4AIEngine()
        self.search_optimizer = SearchOptimizer()
        self.content_analyzer = ContentAnalyzer()
        
    async def execute(self, action: WebInvestigationAction) -> WebInvestigationResult:
        """Execute web investigation action"""
        
        if action.type == "google_search":
            return await self.perform_google_search(action)
        elif action.type == "crawl_website":
            return await self.crawl_website(action)
        elif action.type == "analyze_content":
            return await self.analyze_content(action)
        elif action.type == "follow_links":
            return await self.follow_links(action)
        else:
            raise ValueError(f"Unknown action type: {action.type}")
    
    async def perform_google_search(self, action: WebInvestigationAction) -> WebInvestigationResult:
        """Perform intelligent Google search"""
        
        # Optimize search query based on context
        optimized_query = await self.search_optimizer.optimize_query(
            action.query, action.context
        )
        
        # Perform search
        search_results = await self.crawl_engine.perform_google_search(optimized_query)
        
        # Analyze and filter results
        filtered_results = await self.content_analyzer.filter_relevant_results(
            search_results, action.context
        )
        
        return WebInvestigationResult(
            search_query=optimized_query,
            results=filtered_results,
            new_leads=await self.extract_new_leads(filtered_results)
        )
```

### Regulatory Analysis Agent

```python
class RegulatoryAnalysisAgent:
    """
    Specialized agent for regulatory data analysis
    """
    
    def __init__(self):
        self.regulatory_engine = RegulatoryDataEngine()
        self.relationship_analyzer = RelationshipAnalyzer()
        self.compliance_checker = ComplianceChecker()
        
    async def execute(self, action: RegulatoryAnalysisAction) -> RegulatoryAnalysisResult:
        """Execute regulatory analysis action"""
        
        if action.type == "entity_lookup":
            return await self.lookup_entity(action)
        elif action.type == "relationship_mapping":
            return await self.map_relationships(action)
        elif action.type == "compliance_check":
            return await self.check_compliance(action)
        elif action.type == "filing_analysis":
            return await self.analyze_filings(action)
        else:
            raise ValueError(f"Unknown action type: {action.type}")
    
    async def lookup_entity(self, action: RegulatoryAnalysisAction) -> RegulatoryAnalysisResult:
        """Look up entity in regulatory databases"""
        
        # Comprehensive regulatory lookup
        regulatory_profile = await self.regulatory_engine.investigate_regulatory_profile(
            action.entity, action.context
        )
        
        # Analyze findings for new investigation leads
        new_leads = await self.extract_regulatory_leads(regulatory_profile)
        
        return RegulatoryAnalysisResult(
            entity=action.entity,
            regulatory_profile=regulatory_profile,
            new_leads=new_leads,
            relationships=regulatory_profile.relationships
        )
```

### ESG Analysis Agent

```python
class ESGAnalysisAgent:
    """
    Specialized agent for ESG analysis and synthesis
    """
    
    def __init__(self):
        self.esg_analyzer = ESGContentAnalyzer()
        self.claim_verifier = ClaimVerificationSystem()
        self.impact_assessor = ImpactAssessmentSystem()
        
    async def execute(self, action: ESGAnalysisAction) -> ESGAnalysisResult:
        """Execute ESG analysis action"""
        
        if action.type == "content_analysis":
            return await self.analyze_content(action)
        elif action.type == "claim_verification":
            return await self.verify_claims(action)
        elif action.type == "impact_assessment":
            return await self.assess_impact(action)
        elif action.type == "synthesis":
            return await self.synthesize_findings(action)
        else:
            raise ValueError(f"Unknown action type: {action.type}")
    
    async def analyze_content(self, action: ESGAnalysisAction) -> ESGAnalysisResult:
        """Analyze content for ESG insights"""
        
        # Extract ESG-relevant content
        esg_content = await self.esg_analyzer.extract_esg_content(action.content)
        
        # Classify ESG themes
        esg_themes = await self.esg_analyzer.classify_esg_themes(esg_content)
        
        # Extract claims and commitments
        claims = await self.esg_analyzer.extract_claims_and_commitments(esg_content)
        
        return ESGAnalysisResult(
            esg_content=esg_content,
            esg_themes=esg_themes,
            claims=claims,
            recommendations=await self.generate_recommendations(esg_themes, claims)
        )
```

## Integration with Existing Systems

### 1. Analysis_v2 Integration

```python
class Analysis_v2_Integration:
    """
    Integration layer with existing analysis_v2 system
    """
    
    def __init__(self):
        self.effect_analyzer = EffectAnalyzer()
        self.claims_analyzer = ClaimsAnalyzer()
        self.promises_analyzer = PromisesAnalyzer()
        
    async def enhance_with_agentic_findings(self, run_id: str, agentic_findings: List[AgenticFinding]):
        """Enhance analysis_v2 with agentic investigation findings"""
        
        # Convert agentic findings to analysis_v2 format
        effect_flags = await self.convert_to_effect_flags(agentic_findings)
        
        # Store in analysis_v2 database
        await self.store_effect_flags(run_id, effect_flags)
        
        # Run claims analysis on new findings
        await self.claims_analyzer.analyze_claims_from_agentic_findings(
            run_id, agentic_findings
        )
        
        # Run promises analysis on new findings
        await self.promises_analyzer.analyze_promises_from_agentic_findings(
            run_id, agentic_findings
        )
```

### 2. Crawl System Integration

```python
class CrawlSystemIntegration:
    """
    Integration layer with existing crawl system
    """
    
    def __init__(self):
        self.memory_manager = CrewMemoryManager()
        self.tool_registry = ToolRegistry()
        
    async def leverage_existing_memory(self, entity: str) -> ExistingMemoryContext:
        """Leverage existing crawl system memory"""
        
        # Load existing memory for entity
        existing_memory = await self.memory_manager.load_memory(entity)
        
        # Convert to agentic investigation context
        agentic_context = await self.convert_to_agentic_context(existing_memory)
        
        return ExistingMemoryContext(
            visited_urls=existing_memory.visited_urls,
            insights=existing_memory.insights,
            downloaded_files=existing_memory.downloaded_files,
            agentic_context=agentic_context
        )
    
    async def reuse_existing_tools(self) -> List[Tool]:
        """Reuse existing crawl system tools"""
        
        # Get existing tools
        existing_tools = await self.tool_registry.get_all_tools()
        
        # Convert to agentic format
        agentic_tools = []
        for tool in existing_tools:
            agentic_tool = await self.convert_tool_to_agentic(tool)
            agentic_tools.append(agentic_tool)
            
        return agentic_tools
```

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-4)
- **Core Architecture**: Implement Investigation Controller and Agent Orchestra
- **Memory System**: Build basic episodic and semantic memory stores
- **Crawl4AI Integration**: Integrate Crawl4AI engine with existing patterns
- **Database Schema**: Extend existing schema for agentic investigation tracking

### Phase 2: Agent Development (Weeks 5-8)
- **Web Investigation Agent**: Implement intelligent web crawling with Crawl4AI
- **Regulatory Analysis Agent**: Enhance existing SEC/Companies House integration
- **Document Analysis Agent**: Build advanced document processing pipeline
- **Basic Decision Engine**: Implement simple decision-making logic

### Phase 3: Advanced Features (Weeks 9-12)
- **Advanced Decision Engine**: Implement sophisticated planning and prioritization
- **Procedural Memory**: Add strategy learning and pattern recognition
- **Cross-Agent Coordination**: Implement advanced agent collaboration
- **Integration Layer**: Build seamless integration with analysis_v2

### Phase 4: Intelligence Enhancement (Weeks 13-16)
- **Dynamic Planning**: Implement adaptive investigation strategies
- **Learning Systems**: Add cross-investigation learning capabilities
- **Performance Optimization**: Optimize for speed and resource efficiency
- **Quality Assurance**: Implement comprehensive testing and validation

## Technical Requirements

### Core Libraries and Dependencies

```python
# Core agentic framework
crew-ai>=0.51.0
langchain>=0.1.0
langchain-community>=0.0.20

# Web crawling and processing
crawl4ai>=0.3.0
aiohttp>=3.8.0
beautifulsoup4>=4.12.0
playwright>=1.40.0

# Memory and vector databases
chromadb>=0.4.0
pinecone-client>=2.2.0
weaviate-client>=3.25.0
neo4j>=5.14.0

# Document processing
pypdf>=3.17.0
python-docx>=0.8.11
unstructured>=0.11.0

# AI/ML libraries
openai>=1.3.0
anthropic>=0.7.0
sentence-transformers>=2.2.0
torch>=2.1.0

# Existing dependencies
pydantic>=2.0.0
sqlalchemy>=2.0.0
loguru>=0.7.0
```

### Database Schema Extensions

```sql
-- Investigation tracking
CREATE TABLE agi_investigations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_id UUID REFERENCES kg_base_entities(id),
    investigation_type VARCHAR(50),
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    run_id UUID REFERENCES ana_runs(id)
);

-- Memory stores
CREATE TABLE agi_episodic_memory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    investigation_id UUID REFERENCES agi_investigations(id),
    event_type VARCHAR(50),
    timestamp TIMESTAMP,
    agent VARCHAR(50),
    action_data JSONB,
    result_data JSONB,
    embedding VECTOR(1536)
);

CREATE TABLE agi_semantic_memory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    investigation_id UUID REFERENCES agi_investigations(id),
    fact_type VARCHAR(50),
    fact_data JSONB,
    confidence FLOAT,
    evidence JSONB,
    embedding VECTOR(1536)
);

CREATE TABLE agi_procedural_memory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    strategy_name VARCHAR(100),
    context_pattern JSONB,
    actions JSONB,
    success_rate FLOAT,
    usage_count INTEGER DEFAULT 0
);

-- Investigation actions and results
CREATE TABLE agi_investigation_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    investigation_id UUID REFERENCES agi_investigations(id),
    agent VARCHAR(50),
    action_type VARCHAR(50),
    action_data JSONB,
    status VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

CREATE TABLE agi_investigation_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action_id UUID REFERENCES agi_investigation_actions(id),
    result_type VARCHAR(50),
    result_data JSONB,
    quality_score FLOAT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Configuration and Settings

```python
# eko/settings.py additions
class AgenticInvestigatorSettings:
    """Configuration for Agentic Investigator"""
    
    # Core settings
    MAX_INVESTIGATION_DEPTH: int = 5
    MAX_CONCURRENT_AGENTS: int = 3
    INVESTIGATION_TIMEOUT_HOURS: int = 24
    
    # Memory settings
    EPISODIC_MEMORY_RETENTION_DAYS: int = 365
    SEMANTIC_MEMORY_RETENTION_DAYS: int = 1825  # 5 years
    PROCEDURAL_MEMORY_RETENTION_DAYS: int = 3650  # 10 years
    
    # Crawl4AI settings
    CRAWL4AI_MAX_PAGES_PER_DOMAIN: int = 50
    CRAWL4AI_CRAWL_TIMEOUT_SECONDS: int = 300
    CRAWL4AI_MAX_CONCURRENT_CRAWLS: int = 5
    
    # LLM settings
    STRATEGY_LLM_MODEL: str = "gpt-4-turbo-preview"
    ANALYSIS_LLM_MODEL: str = "gpt-4-turbo-preview"
    SYNTHESIS_LLM_MODEL: str = "gpt-4-turbo-preview"
    
    # Quality thresholds
    MIN_CONFIDENCE_THRESHOLD: float = 0.7
    MIN_RELEVANCE_THRESHOLD: float = 0.6
    MIN_EVIDENCE_QUALITY: float = 0.8
```

## CLI Integration

### Command Implementation

```python
# cli/agentic_investigator.py
import asyncio
from typing import Optional
import click
from eko.agent.agentic_investigator import AgenticInvestigator
from eko.models.investigation import ESGScope, InvestigationConfig

@click.command()
@click.option('--entity', required=True, help='Entity to investigate')
@click.option('--scope', 
              type=click.Choice(['environmental', 'social', 'governance', 'full']),
              default='full',
              help='Investigation scope')
@click.option('--max-depth', default=3, help='Maximum investigation depth')
@click.option('--time-limit', default=24, help='Time limit in hours')
@click.option('--resume', is_flag=True, help='Resume previous investigation')
@click.option('--output-format', 
              type=click.Choice(['json', 'html', 'pdf']),
              default='html',
              help='Output format')
def crawl_agent(entity: str, scope: str, max_depth: int, time_limit: int, 
                resume: bool, output_format: str):
    """
    Run agentic investigation using Crawl4AI and advanced AI agents.
    
    This command performs autonomous ESG investigation of entities using:
    - Intelligent web crawling with Crawl4AI
    - Multi-source data integration (SEC, Companies House, etc.)
    - Advanced AI agents for analysis and synthesis
    - Persistent memory for long-running investigations
    """
    
    async def run_investigation():
        # Initialize investigator
        investigator = AgenticInvestigator()
        
        # Configure investigation
        config = InvestigationConfig(
            entity=entity,
            scope=ESGScope(scope),
            max_depth=max_depth,
            time_limit_hours=time_limit,
            resume=resume,
            output_format=output_format
        )
        
        # Run investigation
        result = await investigator.investigate(config)
        
        # Output results
        if output_format == 'json':
            click.echo(result.to_json())
        elif output_format == 'html':
            result.save_html_report()
            click.echo(f"Report saved to: {result.html_report_path}")
        elif output_format == 'pdf':
            result.save_pdf_report()
            click.echo(f"Report saved to: {result.pdf_report_path}")
    
    # Run async investigation
    asyncio.run(run_investigation())
```

## Testing Strategy

### Unit Tests

```python
# tests/test_agentic_investigator.py
import pytest
from unittest.mock import AsyncMock, MagicMock
from eko.agent.agentic_investigator import AgenticInvestigator
from eko.models.investigation import InvestigationConfig, ESGScope

@pytest.fixture
def mock_investigator():
    investigator = AgenticInvestigator()
    investigator.memory_manager = AsyncMock()
    investigator.agent_orchestra = AsyncMock()
    investigator.decision_engine = AsyncMock()
    return investigator

@pytest.mark.asyncio
async def test_investigation_lifecycle(mock_investigator):
    """Test complete investigation lifecycle"""
    
    # Setup
    config = InvestigationConfig(
        entity="Test Company",
        scope=ESGScope.FULL,
        max_depth=2,
        time_limit_hours=1
    )
    
    # Mock responses
    mock_investigator.decision_engine.plan_next_actions.return_value = [
        MockInvestigationAction(type="google_search", query="Test Company ESG")
    ]
    
    # Run investigation
    result = await mock_investigator.investigate(config)
    
    # Assertions
    assert result.entity == "Test Company"
    assert result.investigation_complete
    assert len(result.findings) > 0
    
    # Verify method calls
    mock_investigator.decision_engine.plan_next_actions.assert_called()
    mock_investigator.agent_orchestra.execute_actions.assert_called()
    mock_investigator.memory_manager.update_investigation_state.assert_called()
```

### Integration Tests

```python
# tests/integration/test_crawl4ai_integration.py
import pytest
from eko.agent.engines.crawl4ai_engine import AgenticCrawl4AIEngine

@pytest.mark.integration
@pytest.mark.asyncio
async def test_crawl4ai_basic_functionality():
    """Test basic Crawl4AI functionality"""
    
    engine = AgenticCrawl4AIEngine()
    
    # Test Google search
    search_results = await engine.perform_google_search("Tesla sustainability")
    assert len(search_results) > 0
    assert any("tesla" in result.title.lower() for result in search_results)
    
    # Test web crawling
    if search_results:
        crawl_result = await engine.crawl_website(search_results[0].url)
        assert crawl_result.content is not None
        assert len(crawl_result.content) > 0
```

### Performance Tests

```python
# tests/performance/test_memory_performance.py
import pytest
import asyncio
from eko.agent.memory.investigation_memory import InvestigationMemoryManager

@pytest.mark.performance
@pytest.mark.asyncio
async def test_memory_store_performance():
    """Test memory store performance with large datasets"""
    
    memory_manager = InvestigationMemoryManager()
    
    # Generate test data
    test_events = [generate_test_event(i) for i in range(1000)]
    
    # Measure storage time
    start_time = asyncio.get_event_loop().time()
    await memory_manager.episodic_memory.store_events("test_investigation", test_events)
    storage_time = asyncio.get_event_loop().time() - start_time
    
    # Performance assertions
    assert storage_time < 30.0  # Should complete in under 30 seconds
    
    # Measure retrieval time
    start_time = asyncio.get_event_loop().time()
    results = await memory_manager.episodic_memory.recall_similar_events(
        test_context
    )
    retrieval_time = asyncio.get_event_loop().time() - start_time
    
    # Performance assertions
    assert retrieval_time < 5.0  # Should complete in under 5 seconds
    assert len(results) > 0
```

## Monitoring and Observability

### Performance Metrics

```python
# eko/agent/monitoring/metrics.py
from typing import Dict, List
from dataclasses import dataclass
from datetime import datetime

@dataclass
class InvestigationMetrics:
    """Metrics for investigation performance tracking"""
    
    investigation_id: str
    start_time: datetime
    end_time: datetime
    total_duration_seconds: float
    
    # Agent performance
    agent_execution_times: Dict[str, float]
    agent_success_rates: Dict[str, float]
    
    # Memory performance
    memory_store_times: Dict[str, float]
    memory_retrieval_times: Dict[str, float]
    
    # Data source performance
    data_source_response_times: Dict[str, float]
    data_source_success_rates: Dict[str, float]
    
    # Quality metrics
    findings_quality_scores: List[float]
    evidence_quality_scores: List[float]
    
    # Cost metrics
    llm_token_usage: Dict[str, int]
    api_call_counts: Dict[str, int]

class MetricsCollector:
    """Collects and reports investigation metrics"""
    
    def __init__(self):
        self.metrics_store = MetricsStore()
        
    async def collect_metrics(self, investigation_id: str) -> InvestigationMetrics:
        """Collect comprehensive metrics for investigation"""
        
        # Gather metrics from various sources
        agent_metrics = await self.collect_agent_metrics(investigation_id)
        memory_metrics = await self.collect_memory_metrics(investigation_id)
        data_source_metrics = await self.collect_data_source_metrics(investigation_id)
        quality_metrics = await self.collect_quality_metrics(investigation_id)
        cost_metrics = await self.collect_cost_metrics(investigation_id)
        
        # Combine into comprehensive metrics
        metrics = InvestigationMetrics(
            investigation_id=investigation_id,
            **agent_metrics,
            **memory_metrics,
            **data_source_metrics,
            **quality_metrics,
            **cost_metrics
        )
        
        # Store metrics
        await self.metrics_store.store_metrics(metrics)
        
        return metrics
```

### Logging Integration

```python
# eko/agent/logging/investigation_logger.py
from loguru import logger
from typing import Any, Dict
import json

class InvestigationLogger:
    """Specialized logger for investigation processes"""
    
    def __init__(self, investigation_id: str):
        self.investigation_id = investigation_id
        self.logger = logger.bind(investigation_id=investigation_id)
        
    def log_investigation_start(self, config: InvestigationConfig):
        """Log investigation start"""
        self.logger.info(
            "Investigation started",
            extra={
                "event_type": "investigation_start",
                "entity": config.entity,
                "scope": config.scope,
                "config": config.dict()
            }
        )
    
    def log_agent_action(self, agent: str, action: str, result: Any):
        """Log agent action and result"""
        self.logger.info(
            f"Agent {agent} executed {action}",
            extra={
                "event_type": "agent_action",
                "agent": agent,
                "action": action,
                "result_summary": self._summarize_result(result)
            }
        )
    
    def log_memory_update(self, memory_type: str, update_data: Dict):
        """Log memory update"""
        self.logger.info(
            f"Memory updated: {memory_type}",
            extra={
                "event_type": "memory_update",
                "memory_type": memory_type,
                "update_summary": self._summarize_data(update_data)
            }
        )
    
    def log_decision_point(self, decision: str, reasoning: str, options: List[str]):
        """Log decision-making process"""
        self.logger.info(
            f"Decision made: {decision}",
            extra={
                "event_type": "decision_point",
                "decision": decision,
                "reasoning": reasoning,
                "options": options
            }
        )
```

## Security Considerations

### Data Protection

```python
# eko/agent/security/data_protection.py
from typing import Any, Dict
import hashlib
import json

class DataProtectionManager:
    """Manages data protection for investigation processes"""
    
    def __init__(self):
        self.sensitive_fields = {
            'personal_data', 'financial_data', 'api_keys', 'passwords',
            'email_addresses', 'phone_numbers', 'addresses'
        }
        
    def sanitize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize sensitive data before storage"""
        
        sanitized = {}
        for key, value in data.items():
            if key.lower() in self.sensitive_fields:
                sanitized[key] = self._hash_sensitive_data(value)
            elif isinstance(value, dict):
                sanitized[key] = self.sanitize_data(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self.sanitize_data(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                sanitized[key] = value
                
        return sanitized
    
    def _hash_sensitive_data(self, data: Any) -> str:
        """Hash sensitive data for storage"""
        data_str = json.dumps(data, sort_keys=True)
        return hashlib.sha256(data_str.encode()).hexdigest()
```

### Access Control

```python
# eko/agent/security/access_control.py
from typing import List, Optional
from enum import Enum

class InvestigationRole(Enum):
    ADMIN = "admin"
    INVESTIGATOR = "investigator"
    ANALYST = "analyst"
    VIEWER = "viewer"

class AccessControlManager:
    """Manages access control for investigation processes"""
    
    def __init__(self):
        self.role_permissions = {
            InvestigationRole.ADMIN: [
                'create_investigation', 'delete_investigation', 
                'modify_investigation', 'view_investigation',
                'access_raw_data', 'export_data'
            ],
            InvestigationRole.INVESTIGATOR: [
                'create_investigation', 'modify_investigation', 
                'view_investigation', 'access_raw_data'
            ],
            InvestigationRole.ANALYST: [
                'view_investigation', 'access_analysis_data'
            ],
            InvestigationRole.VIEWER: [
                'view_investigation'
            ]
        }
    
    def check_permission(self, user_role: InvestigationRole, action: str) -> bool:
        """Check if user has permission for action"""
        return action in self.role_permissions.get(user_role, [])
    
    def filter_data_by_role(self, data: Dict, user_role: InvestigationRole) -> Dict:
        """Filter data based on user role"""
        if user_role == InvestigationRole.ADMIN:
            return data
        elif user_role == InvestigationRole.INVESTIGATOR:
            return self._remove_sensitive_admin_data(data)
        elif user_role == InvestigationRole.ANALYST:
            return self._filter_for_analyst(data)
        else:  # VIEWER
            return self._filter_for_viewer(data)
```

## Conclusion

The Agentic Investigator represents a significant evolution in autonomous ESG investigation capabilities. By combining the strengths of existing systems (analysis_v2 and crawl) with cutting-edge agentic AI technologies, it provides:

1. **Autonomous Operation**: Intelligent agents that can make decisions and adapt strategies
2. **Comprehensive Memory**: Persistent memory systems that learn from investigations
3. **Multi-Source Integration**: Seamless coordination of web crawling, regulatory data, and document analysis
4. **Dynamic Planning**: Investigation strategies that evolve based on discoveries
5. **Quality Assurance**: Evidence-based reasoning with comprehensive audit trails

This system will enable more thorough, efficient, and intelligent ESG investigations while maintaining the robustness and reliability of the existing platform.

The implementation roadmap provides a clear path to deployment, with careful attention to integration, testing, and monitoring. The modular architecture ensures that components can be developed and deployed incrementally, reducing risk and allowing for continuous improvement.

With proper implementation, the Agentic Investigator will set a new standard for autonomous ESG research and analysis, providing unprecedented depth and accuracy in corporate sustainability investigations.