DROP VIEW IF EXISTS view_appsmith_chunks_per_issue;
CREATE VIEW view_appsmith_chunks_per_issue AS
select issue as x, count(*) as y
from _deprecated_kg_document_chunk_issue_map
         JOIN public._deprecated_kg_document_chunks krc on _deprecated_kg_document_chunk_issue_map.doc_chunk_id = krc.id
                                JOIN public.kg_document_pages krp on krp.id = krc.doc_page_id
         JOIN public.kg_documents kr on kr.id = _deprecated_kg_document_chunk_issue_map.doc_id
WHERE kr.status != 'deleted'
GROUP BY issue;


DROP VIEW IF EXISTS view_appsmith_chunks_per_model_section;
CREATE VIEW view_appsmith_chunks_per_model_section AS
select map.model, map.model_section as x, count(*) as y
from _deprecated_kg_document_chunk_issue_map im
         JOIN public._deprecated_kg_document_chunks krc on im.doc_chunk_id = krc.id
                                            JOIN public.kg_document_pages krp on krp.id = krc.doc_page_id
                                            JOIN public.kg_documents kr on kr.id = im.doc_id
         JOIN _deprecated_kg_issue_section_map map on map.issue = im.issue
WHERE kr.status != 'deleted'
GROUP BY map.model,  map.model_section;


DROP VIEW IF EXISTS view_appsmith_reports_per_issue;
CREATE VIEW view_appsmith_reports_per_issue AS
select issue as x, count(DISTINCT krp.doc_id) as y
from _deprecated_kg_document_chunk_issue_map
         JOIN public._deprecated_kg_document_chunks krc on _deprecated_kg_document_chunk_issue_map.doc_chunk_id = krc.id
                                JOIN public.kg_document_pages krp on krp.id = krc.doc_page_id
         JOIN public.kg_documents kr on kr.id = _deprecated_kg_document_chunk_issue_map.doc_id
WHERE kr.status != 'deleted'
GROUP BY issue;


DROP VIEW IF EXISTS view_appsmith_reports_per_model_section;
CREATE VIEW view_appsmith_reports_per_model_section AS
select map.model, map.model_section as x, count(DISTINCT krp.doc_id) as y
from _deprecated_kg_document_chunk_issue_map im
         JOIN public._deprecated_kg_document_chunks krc on im.doc_chunk_id = krc.id
                                            JOIN public.kg_document_pages krp on krp.id = krc.doc_page_id
                                            JOIN public.kg_documents kr on kr.id = im.doc_id
         JOIN _deprecated_kg_issue_section_map map on map.issue = im.issue
WHERE kr.status != 'deleted'
GROUP BY  map.model, map.model_section;
